import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { wishlistService, WishlistProperty } from '../services/wishlistService';
import { 
  Heart, 
  MapPin, 
  Star,
  Users,
  Bed,
  Bath,
  Wifi,
  Car,
  Utensils,
  Waves,
  Mountain,
  Eye,
  Trash2,
  AlertCircle,
  Search
} from 'lucide-react';

interface WishlistProps {
  className?: string;
  limit?: number;
  showHeader?: boolean;
}

export const Wishlist: React.FC<WishlistProps> = ({
  className,
  limit,
  showHeader = true
}) => {
  const navigate = useNavigate();
  const [properties, setProperties] = useState<WishlistProperty[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchWishlist();
  }, []);

  const fetchWishlist = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const wishlistData = await wishlistService.getWishlist();
      
      // Apply limit if specified
      const limitedData = limit ? wishlistData.slice(0, limit) : wishlistData;
      setProperties(limitedData);
    } catch (err) {
      console.error('Error fetching wishlist:', err);
      setError('Failed to load wishlist');
    } finally {
      setLoading(false);
    }
  };

  const handleRemoveFromWishlist = async (propertyId: string) => {
    try {
      const success = await wishlistService.removeFromWishlist(propertyId);
      if (success) {
        setProperties(prev => prev.filter(p => p.property_id !== propertyId));
      }
    } catch (err) {
      console.error('Error removing from wishlist:', err);
      setError('Failed to remove property from wishlist');
    }
  };

  const handleViewProperty = (propertyId: string) => {
    navigate(`/property/${propertyId}`);
  };

  const getAmenityIcon = (amenity: string) => {
    const amenityLower = amenity.toLowerCase();
    if (amenityLower.includes('wifi')) return <Wifi className="h-3 w-3" />;
    if (amenityLower.includes('parking') || amenityLower.includes('car')) return <Car className="h-3 w-3" />;
    if (amenityLower.includes('kitchen')) return <Utensils className="h-3 w-3" />;
    if (amenityLower.includes('beach') || amenityLower.includes('ocean')) return <Waves className="h-3 w-3" />;
    if (amenityLower.includes('mountain') || amenityLower.includes('view')) return <Mountain className="h-3 w-3" />;
    return <span className="text-xs">•</span>;
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="animate-pulse space-y-4">
            {showHeader && <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {[...Array(3)].map((_, index) => (
                <div key={index} className="space-y-3">
                  <div className="h-48 bg-gray-200 rounded-lg"></div>
                  <div className="h-4 bg-gray-200 rounded"></div>
                  <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={className}>
      {showHeader && (
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Heart className="h-5 w-5 text-red-500" />
              My Wishlist
              <Badge variant="secondary">{properties.length}</Badge>
            </CardTitle>
          </CardHeader>
        </Card>
      )}

      {error && (
        <div className="p-3 bg-red-50 border border-red-200 rounded-lg mb-4">
          <div className="flex items-center gap-2 text-red-700">
            <AlertCircle className="h-4 w-4" />
            <span className="text-sm">{error}</span>
          </div>
        </div>
      )}

      {properties.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <Heart className="h-16 w-16 mx-auto mb-4 text-gray-400" />
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              Your Wishlist is Empty
            </h3>
            <p className="text-gray-600 mb-6">
              Start exploring and save properties you love for later
            </p>
            <Button
              onClick={() => navigate('/search')}
              className="bg-sea-green-500 hover:bg-sea-green-600"
            >
              <Search className="h-4 w-4 mr-2" />
              Explore Properties
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {properties.map((property) => (
            <Card key={property.id} className="overflow-hidden hover:shadow-lg transition-shadow">
              <div className="relative">
                <img
                  src={property.images[0] || '/placeholder.svg'}
                  alt={property.title}
                  className="w-full h-48 object-cover"
                />
                <div className="absolute top-3 right-3">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleRemoveFromWishlist(property.property_id)}
                    className="bg-white/80 hover:bg-white text-red-500 hover:text-red-600 rounded-full p-2"
                  >
                    <Heart className="h-4 w-4 fill-current" />
                  </Button>
                </div>
                {!property.is_available && (
                  <div className="absolute top-3 left-3">
                    <Badge variant="secondary" className="bg-gray-800/80 text-white">
                      Not Available
                    </Badge>
                  </div>
                )}
              </div>

              <CardContent className="p-4">
                <div className="space-y-3">
                  {/* Property Title and Location */}
                  <div>
                    <h3 className="font-semibold text-gray-900 line-clamp-1">
                      {property.title}
                    </h3>
                    <div className="flex items-center gap-1 text-gray-600 text-sm">
                      <MapPin className="h-3 w-3" />
                      {property.location}
                    </div>
                  </div>

                  {/* Property Details */}
                  <div className="flex items-center gap-4 text-sm text-gray-600">
                    <div className="flex items-center gap-1">
                      <Users className="h-3 w-3" />
                      {property.max_guests}
                    </div>
                    <div className="flex items-center gap-1">
                      <Bed className="h-3 w-3" />
                      {property.bedrooms}
                    </div>
                    <div className="flex items-center gap-1">
                      <Bath className="h-3 w-3" />
                      {property.bathrooms}
                    </div>
                    <div className="flex items-center gap-1">
                      {wishlistService.getPropertyTypeIcon(property.property_type)}
                      <span className="text-xs">{property.property_type}</span>
                    </div>
                  </div>

                  {/* Rating and Reviews */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-1">
                      <Star className="h-4 w-4 text-yellow-400 fill-current" />
                      <span className="text-sm font-medium">
                        {wishlistService.formatRating(property.rating)}
                      </span>
                      <span className="text-sm text-gray-600">
                        ({property.review_count} reviews)
                      </span>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-semibold text-gray-900">
                        {wishlistService.formatPrice(property.price)}
                      </div>
                      <div className="text-xs text-gray-600">per night</div>
                    </div>
                  </div>

                  {/* Amenities */}
                  <div className="flex flex-wrap gap-1">
                    {property.amenities.slice(0, 4).map((amenity, index) => (
                      <div
                        key={index}
                        className="flex items-center gap-1 bg-gray-100 px-2 py-1 rounded text-xs text-gray-600"
                      >
                        {getAmenityIcon(amenity)}
                        <span>{amenity}</span>
                      </div>
                    ))}
                    {property.amenities.length > 4 && (
                      <div className="bg-gray-100 px-2 py-1 rounded text-xs text-gray-600">
                        +{property.amenities.length - 4} more
                      </div>
                    )}
                  </div>

                  {/* Host Info */}
                  <div className="text-sm text-gray-600">
                    Hosted by {property.host_name}
                  </div>

                  {/* Added Date */}
                  <div className="text-xs text-gray-500">
                    Added {new Date(property.added_at).toLocaleDateString()}
                  </div>

                  {/* Actions */}
                  <div className="flex items-center gap-2 pt-2">
                    <Button
                      onClick={() => handleViewProperty(property.property_id)}
                      className="flex-1 bg-sea-green-500 hover:bg-sea-green-600"
                      disabled={!property.is_available}
                    >
                      <Eye className="h-4 w-4 mr-2" />
                      {property.is_available ? 'View Property' : 'View Details'}
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleRemoveFromWishlist(property.property_id)}
                      className="text-red-600 border-red-200 hover:bg-red-50"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Show More Button */}
      {limit && properties.length >= limit && (
        <div className="text-center mt-6">
          <Button
            variant="outline"
            onClick={() => navigate('/user/dashboard?tab=wishlist')}
          >
            View All Saved Properties
          </Button>
        </div>
      )}
    </div>
  );
};
