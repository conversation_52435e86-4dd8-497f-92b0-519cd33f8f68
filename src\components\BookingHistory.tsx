import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { bookingService, Booking, BookingStats } from '../services/bookingService';
import { 
  Calendar, 
  MapPin, 
  Users, 
  CreditCard,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Star,
  MessageSquare,
  Phone,
  Mail,
  Eye,
  X
} from 'lucide-react';

interface BookingHistoryProps {
  className?: string;
  showUpcomingOnly?: boolean;
  limit?: number;
}

export const BookingHistory: React.FC<BookingHistoryProps> = ({
  className,
  showUpcomingOnly = false,
  limit
}) => {
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [stats, setStats] = useState<BookingStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedBooking, setSelectedBooking] = useState<Booking | null>(null);
  const [showDetails, setShowDetails] = useState(false);

  useEffect(() => {
    fetchBookings();
    fetchStats();
  }, [showUpcomingOnly]);

  const fetchBookings = async () => {
    try {
      setLoading(true);
      setError(null);
      
      let bookingData: Booking[];
      if (showUpcomingOnly) {
        bookingData = await bookingService.getUpcomingBookings();
      } else {
        bookingData = await bookingService.getUserBookings();
      }
      
      // Apply limit if specified
      if (limit && bookingData.length > limit) {
        bookingData = bookingData.slice(0, limit);
      }
      
      setBookings(bookingData);
    } catch (err) {
      console.error('Error fetching bookings:', err);
      setError('Failed to load bookings');
      // Use mock data for testing
      const mockBookings = bookingService.getMockBookings();
      setBookings(limit ? mockBookings.slice(0, limit) : mockBookings);
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const statsData = await bookingService.getBookingStats();
      setStats(statsData);
    } catch (err) {
      console.error('Error fetching booking stats:', err);
      // Use mock stats for testing
      setStats(bookingService.getMockStats());
    }
  };

  const handleCancelBooking = async (bookingId: string) => {
    try {
      const success = await bookingService.cancelBooking(bookingId, 'User requested cancellation');
      if (success) {
        await fetchBookings();
        setShowDetails(false);
      }
    } catch (err) {
      console.error('Error cancelling booking:', err);
      setError('Failed to cancel booking');
    }
  };

  const handleViewDetails = (booking: Booking) => {
    setSelectedBooking(booking);
    setShowDetails(true);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'confirmed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'cancelled':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-blue-500" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-500" />;
    }
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="animate-pulse space-y-4">
            <div className="h-6 bg-gray-200 rounded w-1/3"></div>
            {[...Array(3)].map((_, index) => (
              <div key={index} className="space-y-3">
                <div className="h-4 bg-gray-200 rounded"></div>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Stats Overview */}
      {stats && !showUpcomingOnly && (
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-gray-900">{stats.total_bookings}</div>
              <div className="text-sm text-gray-600">Total Bookings</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-sea-green-600">{stats.upcoming_trips}</div>
              <div className="text-sm text-gray-600">Upcoming Trips</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-blue-600">{stats.completed_trips}</div>
              <div className="text-sm text-gray-600">Completed</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-gray-900">
                R{stats.total_spent.toLocaleString()}
              </div>
              <div className="text-sm text-gray-600">Total Spent</div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Bookings List */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            {showUpcomingOnly ? 'Upcoming Trips' : 'Booking History'}
            <Badge variant="secondary">{bookings.length}</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {error && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-lg mb-4">
              <div className="flex items-center gap-2 text-red-700">
                <AlertCircle className="h-4 w-4" />
                <span className="text-sm">{error}</span>
              </div>
            </div>
          )}

          {bookings.length === 0 ? (
            <div className="text-center py-8">
              <Calendar className="h-12 w-12 mx-auto mb-4 text-gray-400" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {showUpcomingOnly ? 'No Upcoming Trips' : 'No Bookings Yet'}
              </h3>
              <p className="text-gray-600 mb-4">
                {showUpcomingOnly 
                  ? 'You don\'t have any upcoming trips planned.'
                  : 'Start exploring and book your first amazing stay!'
                }
              </p>
              <Button onClick={() => window.location.href = '/search'}>
                Explore Properties
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {bookings.map((booking) => (
                <div
                  key={booking.id}
                  className="border rounded-lg p-4 hover:shadow-md transition-shadow"
                >
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                    <div className="flex items-start gap-4">
                      <img
                        src={booking.property_image || '/placeholder.svg'}
                        alt={booking.property_title}
                        className="w-16 h-16 rounded-lg object-cover"
                      />
                      <div className="flex-1">
                        <h4 className="font-semibold text-gray-900 mb-1">
                          {booking.property_title}
                        </h4>
                        <div className="flex items-center gap-1 text-gray-600 text-sm mb-2">
                          <MapPin className="h-3 w-3" />
                          {booking.property_location}
                        </div>
                        <div className="flex items-center gap-4 text-sm text-gray-600">
                          <div className="flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            {bookingService.formatBookingDates(booking.check_in_date, booking.check_out_date)}
                          </div>
                          <div className="flex items-center gap-1">
                            <Users className="h-3 w-3" />
                            {booking.guest_count} guests
                          </div>
                          <div className="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            {bookingService.calculateNights(booking.check_in_date, booking.check_out_date)} nights
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="flex flex-col items-end gap-2">
                      <div className="text-right">
                        <div className="text-lg font-semibold text-gray-900">
                          R{booking.total_amount.toLocaleString()}
                        </div>
                        <div className="text-sm text-gray-600">Total</div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Badge className={bookingService.getBookingStatusColor(booking.booking_status)}>
                          {getStatusIcon(booking.booking_status)}
                          <span className="ml-1 capitalize">{booking.booking_status}</span>
                        </Badge>
                        <Badge className={bookingService.getPaymentStatusColor(booking.payment_status)}>
                          <CreditCard className="h-3 w-3 mr-1" />
                          <span className="capitalize">{booking.payment_status}</span>
                        </Badge>
                      </div>

                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleViewDetails(booking)}
                        >
                          <Eye className="h-3 w-3 mr-1" />
                          Details
                        </Button>
                        
                        {booking.can_review && (
                          <Button
                            variant="outline"
                            size="sm"
                            className="text-sea-green-600 border-sea-green-200 hover:bg-sea-green-50"
                          >
                            <Star className="h-3 w-3 mr-1" />
                            Review
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Booking Details Modal */}
      {showDetails && selectedBooking && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-gray-900">Booking Details</h2>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowDetails(false)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>

              <div className="space-y-6">
                {/* Property Info */}
                <div>
                  <h3 className="font-medium text-gray-900 mb-3">Property Information</h3>
                  <div className="flex items-start gap-4">
                    <img
                      src={selectedBooking.property_image || '/placeholder.svg'}
                      alt={selectedBooking.property_title}
                      className="w-24 h-24 rounded-lg object-cover"
                    />
                    <div>
                      <h4 className="font-semibold text-gray-900">{selectedBooking.property_title}</h4>
                      <p className="text-gray-600 flex items-center gap-1 mt-1">
                        <MapPin className="h-3 w-3" />
                        {selectedBooking.property_location}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Booking Details */}
                <div>
                  <h3 className="font-medium text-gray-900 mb-3">Booking Information</h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-700">Check-in</label>
                      <p className="text-gray-900">{new Date(selectedBooking.check_in_date).toLocaleDateString()}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-700">Check-out</label>
                      <p className="text-gray-900">{new Date(selectedBooking.check_out_date).toLocaleDateString()}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-700">Guests</label>
                      <p className="text-gray-900">{selectedBooking.guest_count}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-700">Nights</label>
                      <p className="text-gray-900">
                        {bookingService.calculateNights(selectedBooking.check_in_date, selectedBooking.check_out_date)}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Host Contact */}
                <div>
                  <h3 className="font-medium text-gray-900 mb-3">Host Contact</h3>
                  <div className="space-y-2">
                    <p className="text-gray-900">{selectedBooking.host_name}</p>
                    <div className="flex items-center gap-4">
                      <Button variant="outline" size="sm">
                        <Mail className="h-3 w-3 mr-1" />
                        Email Host
                      </Button>
                      <Button variant="outline" size="sm">
                        <MessageSquare className="h-3 w-3 mr-1" />
                        Message
                      </Button>
                    </div>
                  </div>
                </div>

                {/* Special Requests */}
                {selectedBooking.special_requests && (
                  <div>
                    <h3 className="font-medium text-gray-900 mb-3">Special Requests</h3>
                    <p className="text-gray-600 bg-gray-50 p-3 rounded-lg">
                      {selectedBooking.special_requests}
                    </p>
                  </div>
                )}

                {/* Actions */}
                <div className="flex items-center gap-3 pt-4 border-t">
                  {bookingService.canCancel(selectedBooking) && (
                    <Button
                      variant="outline"
                      onClick={() => handleCancelBooking(selectedBooking.id)}
                      className="text-red-600 border-red-200 hover:bg-red-50"
                    >
                      Cancel Booking
                    </Button>
                  )}
                  
                  {selectedBooking.can_review && (
                    <Button className="bg-sea-green-500 hover:bg-sea-green-600">
                      <Star className="h-4 w-4 mr-2" />
                      Write Review
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
