interface PaymentData {
  payment_id: string;
  payment_url: string;
  payment_data: {
    merchant_id: string;
    merchant_key: string;
    return_url: string;
    cancel_url: string;
    notify_url: string;
    name_first: string;
    name_last: string;
    email_address: string;
    cell_number: string;
    m_payment_id: string;
    amount: string;
    item_name: string;
    item_description: string;
    custom_str1: string;
    custom_str2: string;
    custom_str3: string;
    signature: string;
  };
  payment_reference: string;
}

interface Payment {
  id: string;
  booking_id: string;
  payment_reference: string;
  amount: number;
  platform_fee: number;
  host_amount: number;
  refund_amount: number;
  payment_method: 'payfast' | 'card' | 'eft' | 'bank_transfer';
  payment_status: 'pending' | 'completed' | 'failed' | 'refunded' | 'cancelled';
  payfast_payment_id?: string;
  payment_data?: any;
  notification_data?: any;
  created_at: string;
  updated_at: string;
  property_id?: string;
  guest_id?: string;
  check_in_date?: string;
  check_out_date?: string;
  booking_status?: string;
  property_title?: string;
  host_id?: string;
}

interface RefundData {
  refund_id: string;
  refund_amount: number;
  status: string;
}

interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

class PaymentService {
  private baseUrl = 'http://localhost/stayfinder/api/payments.php';

  private async makeRequest<T>(url: string, options: RequestInit = {}): Promise<ApiResponse<T>> {
    try {
      const token = localStorage.getItem('auth_token');
      const headers: HeadersInit = {
        'Content-Type': 'application/json',
        ...options.headers,
      };

      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const response = await fetch(url, {
        ...options,
        headers,
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Payment API error:', error);
      throw error;
    }
  }

  // Create payment for booking
  async createPayment(bookingId: string): Promise<PaymentData> {
    try {
      const params = new URLSearchParams({ action: 'create' });
      
      const response = await this.makeRequest<PaymentData>(`${this.baseUrl}?${params}`, {
        method: 'POST',
        body: JSON.stringify({ booking_id: bookingId }),
      });
      
      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to create payment');
      }
    } catch (error) {
      console.error('Error creating payment:', error);
      throw error;
    }
  }

  // Get payment details
  async getPayment(paymentId: string): Promise<Payment> {
    try {
      const params = new URLSearchParams({
        action: 'payment',
        payment_id: paymentId,
      });

      const response = await this.makeRequest<Payment>(`${this.baseUrl}?${params}`);
      
      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to fetch payment details');
      }
    } catch (error) {
      console.error('Error fetching payment:', error);
      // Return mock data for testing
      return this.getMockPayment(paymentId);
    }
  }

  // Process refund
  async processRefund(paymentId: string, refundAmount?: number): Promise<RefundData> {
    try {
      const params = new URLSearchParams({ action: 'refund' });
      
      const requestBody: any = { payment_id: paymentId };
      if (refundAmount) {
        requestBody.refund_amount = refundAmount;
      }
      
      const response = await this.makeRequest<RefundData>(`${this.baseUrl}?${params}`, {
        method: 'POST',
        body: JSON.stringify(requestBody),
      });
      
      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to process refund');
      }
    } catch (error) {
      console.error('Error processing refund:', error);
      throw error;
    }
  }

  // Redirect to PayFast payment page
  redirectToPayment(paymentData: PaymentData): void {
    // Create a form and submit it to PayFast
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = paymentData.payment_url;
    form.style.display = 'none';

    // Add all payment data as hidden inputs
    Object.entries(paymentData.payment_data).forEach(([key, value]) => {
      const input = document.createElement('input');
      input.type = 'hidden';
      input.name = key;
      input.value = value.toString();
      form.appendChild(input);
    });

    document.body.appendChild(form);
    form.submit();
  }

  // Helper methods
  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
      minimumFractionDigits: 2,
    }).format(amount);
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('en-ZA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  }

  formatDateTime(dateString: string): string {
    return new Date(dateString).toLocaleString('en-ZA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  }

  getPaymentStatusColor(status: string): string {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      case 'refunded':
        return 'bg-blue-100 text-blue-800';
      case 'cancelled':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  getPaymentStatusIcon(status: string): string {
    switch (status) {
      case 'completed':
        return '✓';
      case 'pending':
        return '⏳';
      case 'failed':
        return '✗';
      case 'refunded':
        return '↩';
      case 'cancelled':
        return '⊘';
      default:
        return '?';
    }
  }

  calculatePlatformFee(amount: number, feePercentage: number = 5): number {
    return Math.round(amount * (feePercentage / 100) * 100) / 100;
  }

  calculateHostAmount(amount: number, platformFee: number): number {
    return Math.round((amount - platformFee) * 100) / 100;
  }

  isRefundable(payment: Payment): boolean {
    return payment.payment_status === 'completed' && payment.refund_amount === 0;
  }

  canProcessRefund(payment: Payment, checkInDate: string): boolean {
    if (!this.isRefundable(payment)) {
      return false;
    }

    const checkIn = new Date(checkInDate);
    const now = new Date();
    const hoursUntilCheckIn = (checkIn.getTime() - now.getTime()) / (1000 * 60 * 60);

    // Allow refunds up to 24 hours before check-in
    return hoursUntilCheckIn > 24;
  }

  getRefundPolicy(checkInDate: string): {
    canRefund: boolean;
    refundPercentage: number;
    message: string;
  } {
    const checkIn = new Date(checkInDate);
    const now = new Date();
    const hoursUntilCheckIn = (checkIn.getTime() - now.getTime()) / (1000 * 60 * 60);

    if (hoursUntilCheckIn > 168) { // More than 7 days
      return {
        canRefund: true,
        refundPercentage: 100,
        message: 'Full refund available (more than 7 days before check-in)'
      };
    } else if (hoursUntilCheckIn > 48) { // 2-7 days
      return {
        canRefund: true,
        refundPercentage: 50,
        message: '50% refund available (2-7 days before check-in)'
      };
    } else if (hoursUntilCheckIn > 24) { // 1-2 days
      return {
        canRefund: true,
        refundPercentage: 25,
        message: '25% refund available (1-2 days before check-in)'
      };
    } else {
      return {
        canRefund: false,
        refundPercentage: 0,
        message: 'No refund available (less than 24 hours before check-in)'
      };
    }
  }

  // Mock data for testing
  getMockPayment(paymentId: string): Payment {
    return {
      id: paymentId,
      booking_id: 'booking-1',
      payment_reference: 'SF-TEST-001',
      amount: 1200.00,
      platform_fee: 60.00,
      host_amount: 1140.00,
      refund_amount: 0.00,
      payment_method: 'payfast',
      payment_status: 'completed',
      payfast_payment_id: 'pf_12345',
      created_at: '2024-06-20T10:00:00Z',
      updated_at: '2024-06-20T10:05:00Z',
      property_id: 'prop-1',
      guest_id: 'guest-1',
      check_in_date: '2024-06-25',
      check_out_date: '2024-06-28',
      booking_status: 'confirmed',
      property_title: 'Beachfront Villa in Margate',
      host_id: 'host-1'
    };
  }

  getMockPaymentData(bookingId: string): PaymentData {
    return {
      payment_id: 'payment-' + Date.now(),
      payment_url: 'https://sandbox.payfast.co.za/eng/process',
      payment_data: {
        merchant_id: '10000100',
        merchant_key: '46f0cd694581a',
        return_url: 'http://localhost:5173/payment/success',
        cancel_url: 'http://localhost:5173/payment/cancel',
        notify_url: 'http://localhost/stayfinder/api/payments.php?action=notify',
        name_first: 'John',
        name_last: 'Doe',
        email_address: '<EMAIL>',
        cell_number: '0821234567',
        m_payment_id: 'SF-' + bookingId + '-' + Date.now(),
        amount: '1200.00',
        item_name: 'StayFinder Booking - Test Property',
        item_description: 'Accommodation booking from 2024-06-25 to 2024-06-28',
        custom_str1: bookingId,
        custom_str2: '1140.00',
        custom_str3: '60.00',
        signature: 'test_signature_hash'
      },
      payment_reference: 'SF-' + bookingId + '-' + Date.now()
    };
  }
}

export const paymentService = new PaymentService();
export type { 
  PaymentData, 
  Payment, 
  RefundData, 
  ApiResponse 
};
