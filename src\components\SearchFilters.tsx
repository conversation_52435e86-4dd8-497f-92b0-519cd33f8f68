import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import {
  MapPin,
  DollarSign,
  Users,
  Home,
  Filter,
  X,
  Star,
  Bed,
  Bath,
  Wifi,
  Car,
  Waves,
  Calendar,
  ChevronDown,
  ChevronUp
} from 'lucide-react';

interface SearchFilters {
  location?: string;
  minPrice?: number;
  maxPrice?: number;
  guests?: number;
  propertyType?: string;
  bedrooms?: number;
  bathrooms?: number;
  amenities?: string[];
  rating?: number;
  instantBook?: boolean;
  checkIn?: string;
  checkOut?: string;
  radius?: number;
}

interface SearchFiltersProps {
  filters: SearchFilters;
  onFiltersChange: (filters: SearchFilters) => void;
  onSearch: () => void;
  onClear: () => void;
  loading?: boolean;
}

const PROPERTY_TYPES = [
  { value: 'apartment', label: 'Apartment' },
  { value: 'house', label: 'House' },
  { value: 'villa', label: 'Villa' },
  { value: 'cottage', label: 'Cottage' },
  { value: 'townhouse', label: 'Townhouse' },
  { value: 'guesthouse', label: 'Guesthouse' }
];

const POPULAR_LOCATIONS = [
  'Margate',
  'Scottburgh',
  'Hibberdene',
  'Port Shepstone',
  'Ramsgate',
  'Uvongo',
  'Shelly Beach',
  'Pennington'
];

const POPULAR_AMENITIES = [
  { id: 'wifi', label: 'WiFi', icon: Wifi },
  { id: 'parking', label: 'Free Parking', icon: Car },
  { id: 'pool', label: 'Swimming Pool', icon: Waves },
  { id: 'kitchen', label: 'Kitchen', icon: Home },
  { id: 'air_conditioning', label: 'Air Conditioning', icon: Home },
  { id: 'heating', label: 'Heating', icon: Home },
  { id: 'tv', label: 'TV', icon: Home },
  { id: 'washing_machine', label: 'Washing Machine', icon: Home }
];

export const SearchFilters: React.FC<SearchFiltersProps> = ({
  filters,
  onFiltersChange,
  onSearch,
  onClear,
  loading = false
}) => {
  const [showAdvanced, setShowAdvanced] = useState(false);

  const updateFilter = (key: keyof SearchFilters, value: any) => {
    onFiltersChange({
      ...filters,
      [key]: value || undefined
    });
  };

  const clearFilter = (key: keyof SearchFilters) => {
    const newFilters = { ...filters };
    delete newFilters[key];
    onFiltersChange(newFilters);
  };

  const toggleAmenity = (amenityId: string) => {
    const currentAmenities = filters.amenities || [];
    const updatedAmenities = currentAmenities.includes(amenityId)
      ? currentAmenities.filter(id => id !== amenityId)
      : [...currentAmenities, amenityId];
    updateFilter('amenities', updatedAmenities.length > 0 ? updatedAmenities : undefined);
  };

  const hasActiveFilters = Object.values(filters).some(value => value !== undefined && value !== '');

  const getActiveFiltersCount = () => {
    return Object.values(filters).filter(value => value !== undefined && value !== '').length;
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Search Filters
            {getActiveFiltersCount() > 0 && (
              <Badge variant="secondary">
                {getActiveFiltersCount()} active
              </Badge>
            )}
          </CardTitle>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowAdvanced(!showAdvanced)}
            >
              {showAdvanced ? 'Simple' : 'Advanced'}
            </Button>
            {hasActiveFilters && (
              <Button
                variant="outline"
                size="sm"
                onClick={onClear}
              >
                Clear All
              </Button>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Basic Filters */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Location */}
          <div className="space-y-2">
            <Label htmlFor="location" className="flex items-center gap-1">
              <MapPin className="h-4 w-4" />
              Location
            </Label>
            <div className="relative">
              <Input
                id="location"
                placeholder="Enter location..."
                value={filters.location || ''}
                onChange={(e) => updateFilter('location', e.target.value)}
                disabled={loading}
              />
              {filters.location && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="absolute right-1 top-1 h-6 w-6 p-0"
                  onClick={() => clearFilter('location')}
                >
                  <X className="h-3 w-3" />
                </Button>
              )}
            </div>
            {/* Popular locations */}
            <div className="flex flex-wrap gap-1">
              {POPULAR_LOCATIONS.slice(0, 4).map((location) => (
                <Badge
                  key={location}
                  variant="outline"
                  className="cursor-pointer text-xs"
                  onClick={() => updateFilter('location', location)}
                >
                  {location}
                </Badge>
              ))}
            </div>
          </div>

          {/* Guests */}
          <div className="space-y-2">
            <Label htmlFor="guests" className="flex items-center gap-1">
              <Users className="h-4 w-4" />
              Guests
            </Label>
            <Select
              value={filters.guests?.toString() || undefined}
              onValueChange={(value) => updateFilter('guests', value ? parseInt(value) : undefined)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Any" />
              </SelectTrigger>
              <SelectContent>
                {[1, 2, 3, 4, 5, 6, 7, 8].map((num) => (
                  <SelectItem key={num} value={num.toString()}>
                    {num} {num === 1 ? 'guest' : 'guests'}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Property Type */}
          <div className="space-y-2">
            <Label htmlFor="propertyType" className="flex items-center gap-1">
              <Home className="h-4 w-4" />
              Property Type
            </Label>
            <Select
              value={filters.propertyType || undefined}
              onValueChange={(value) => updateFilter('propertyType', value || undefined)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Any type" />
              </SelectTrigger>
              <SelectContent>
                {PROPERTY_TYPES.map((type) => (
                  <SelectItem key={type.value} value={type.value}>
                    {type.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Search Button */}
          <div className="space-y-2">
            <Label>&nbsp;</Label>
            <Button
              onClick={onSearch}
              disabled={loading}
              className="w-full bg-sea-green-500 hover:bg-sea-green-600"
            >
              {loading ? 'Searching...' : 'Search'}
            </Button>
          </div>
        </div>

        {/* Advanced Filters */}
        {showAdvanced && (
          <div className="border-t pt-4 space-y-4">
            <h4 className="font-medium text-gray-900">Price Range</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="minPrice" className="flex items-center gap-1">
                  <DollarSign className="h-4 w-4" />
                  Min Price (per night)
                </Label>
                <Input
                  id="minPrice"
                  type="number"
                  placeholder="R 0"
                  value={filters.minPrice || ''}
                  onChange={(e) => updateFilter('minPrice', e.target.value ? parseInt(e.target.value) : undefined)}
                  disabled={loading}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="maxPrice" className="flex items-center gap-1">
                  <DollarSign className="h-4 w-4" />
                  Max Price (per night)
                </Label>
                <Input
                  id="maxPrice"
                  type="number"
                  placeholder="R 10,000"
                  value={filters.maxPrice || ''}
                  onChange={(e) => updateFilter('maxPrice', e.target.value ? parseInt(e.target.value) : undefined)}
                  disabled={loading}
                />
              </div>
            </div>

            {/* Quick Price Filters */}
            <div className="space-y-2">
              <Label>Quick Price Filters</Label>
              <div className="flex flex-wrap gap-2">
                {[
                  { label: 'Under R500', max: 500 },
                  { label: 'R500 - R1000', min: 500, max: 1000 },
                  { label: 'R1000 - R2000', min: 1000, max: 2000 },
                  { label: 'R2000+', min: 2000 }
                ].map((range) => (
                  <Badge
                    key={range.label}
                    variant="outline"
                    className="cursor-pointer"
                    onClick={() => {
                      updateFilter('minPrice', range.min);
                      updateFilter('maxPrice', range.max);
                    }}
                  >
                    {range.label}
                  </Badge>
                ))}
              </div>
            </div>

            {/* Bedrooms and Bathrooms */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label className="text-sm font-medium">Bedrooms</Label>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => updateFilter('bedrooms', Math.max(0, (filters.bedrooms || 0) - 1))}
                    className="h-8 w-8 p-0"
                  >
                    -
                  </Button>
                  <span className="text-sm font-medium w-12 text-center">
                    {filters.bedrooms === 0 || !filters.bedrooms ? 'Any' : filters.bedrooms}
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => updateFilter('bedrooms', Math.min(10, (filters.bedrooms || 0) + 1))}
                    className="h-8 w-8 p-0"
                  >
                    +
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium">Bathrooms</Label>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => updateFilter('bathrooms', Math.max(0, (filters.bathrooms || 0) - 1))}
                    className="h-8 w-8 p-0"
                  >
                    -
                  </Button>
                  <span className="text-sm font-medium w-12 text-center">
                    {filters.bathrooms === 0 || !filters.bathrooms ? 'Any' : filters.bathrooms}
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => updateFilter('bathrooms', Math.min(10, (filters.bathrooms || 0) + 1))}
                    className="h-8 w-8 p-0"
                  >
                    +
                  </Button>
                </div>
              </div>
            </div>

            {/* Amenities */}
            <div className="space-y-3">
              <Label className="text-sm font-medium">Amenities</Label>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                {POPULAR_AMENITIES.map((amenity) => {
                  const isSelected = (filters.amenities || []).includes(amenity.id);
                  return (
                    <button
                      key={amenity.id}
                      onClick={() => toggleAmenity(amenity.id)}
                      className={`p-2 rounded-lg border text-xs transition-all flex items-center gap-2 ${
                        isSelected
                          ? 'border-sea-green-500 bg-sea-green-50 text-sea-green-700'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <amenity.icon className="h-3 w-3" />
                      {amenity.label}
                    </button>
                  );
                })}
              </div>
            </div>

            {/* Rating and Booking Options */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label className="text-sm font-medium">Minimum Rating</Label>
                <select
                  value={filters.rating || 0}
                  onChange={(e) => updateFilter('rating', parseFloat(e.target.value) || undefined)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                >
                  <option value={0}>Any Rating</option>
                  <option value={4.5}>4.5+ Stars</option>
                  <option value={4.0}>4.0+ Stars</option>
                  <option value={3.5}>3.5+ Stars</option>
                  <option value={3.0}>3.0+ Stars</option>
                </select>
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium">Booking Options</Label>
                <label className="flex items-center gap-2 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={filters.instantBook || false}
                    onChange={(e) => updateFilter('instantBook', e.target.checked || undefined)}
                    className="w-4 h-4 text-sea-green-600 border-gray-300 rounded focus:ring-sea-green-500"
                  />
                  <span className="text-sm">Instant Book Available</span>
                </label>
              </div>
            </div>
          </div>
        )}

        {/* Active Filters Display */}
        {hasActiveFilters && (
          <div className="border-t pt-4">
            <Label className="text-sm font-medium">Active Filters:</Label>
            <div className="flex flex-wrap gap-2 mt-2">
              {filters.location && (
                <Badge variant="secondary" className="flex items-center gap-1">
                  <MapPin className="h-3 w-3" />
                  {filters.location}
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-4 w-4 p-0 ml-1"
                    onClick={() => clearFilter('location')}
                  >
                    <X className="h-2 w-2" />
                  </Button>
                </Badge>
              )}
              {filters.guests && (
                <Badge variant="secondary" className="flex items-center gap-1">
                  <Users className="h-3 w-3" />
                  {filters.guests} guests
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-4 w-4 p-0 ml-1"
                    onClick={() => clearFilter('guests')}
                  >
                    <X className="h-2 w-2" />
                  </Button>
                </Badge>
              )}
              {filters.propertyType && (
                <Badge variant="secondary" className="flex items-center gap-1">
                  <Home className="h-3 w-3" />
                  {PROPERTY_TYPES.find(t => t.value === filters.propertyType)?.label}
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-4 w-4 p-0 ml-1"
                    onClick={() => clearFilter('propertyType')}
                  >
                    <X className="h-2 w-2" />
                  </Button>
                </Badge>
              )}
              {(filters.minPrice || filters.maxPrice) && (
                <Badge variant="secondary" className="flex items-center gap-1">
                  <DollarSign className="h-3 w-3" />
                  R{filters.minPrice || 0} - R{filters.maxPrice || '∞'}
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-4 w-4 p-0 ml-1"
                    onClick={() => {
                      clearFilter('minPrice');
                      clearFilter('maxPrice');
                    }}
                  >
                    <X className="h-2 w-2" />
                  </Button>
                </Badge>
              )}
              {filters.bedrooms && (
                <Badge variant="secondary" className="flex items-center gap-1">
                  <Bed className="h-3 w-3" />
                  {filters.bedrooms} bedroom{filters.bedrooms > 1 ? 's' : ''}
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-4 w-4 p-0 ml-1"
                    onClick={() => clearFilter('bedrooms')}
                  >
                    <X className="h-2 w-2" />
                  </Button>
                </Badge>
              )}
              {filters.bathrooms && (
                <Badge variant="secondary" className="flex items-center gap-1">
                  <Bath className="h-3 w-3" />
                  {filters.bathrooms} bathroom{filters.bathrooms > 1 ? 's' : ''}
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-4 w-4 p-0 ml-1"
                    onClick={() => clearFilter('bathrooms')}
                  >
                    <X className="h-2 w-2" />
                  </Button>
                </Badge>
              )}
              {filters.rating && (
                <Badge variant="secondary" className="flex items-center gap-1">
                  <Star className="h-3 w-3" />
                  {filters.rating}+ stars
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-4 w-4 p-0 ml-1"
                    onClick={() => clearFilter('rating')}
                  >
                    <X className="h-2 w-2" />
                  </Button>
                </Badge>
              )}
              {filters.instantBook && (
                <Badge variant="secondary" className="flex items-center gap-1">
                  Instant Book
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-4 w-4 p-0 ml-1"
                    onClick={() => clearFilter('instantBook')}
                  >
                    <X className="h-2 w-2" />
                  </Button>
                </Badge>
              )}
              {filters.amenities && filters.amenities.map(amenityId => {
                const amenity = POPULAR_AMENITIES.find(a => a.id === amenityId);
                return amenity ? (
                  <Badge key={amenityId} variant="secondary" className="flex items-center gap-1">
                    <amenity.icon className="h-3 w-3" />
                    {amenity.label}
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-4 w-4 p-0 ml-1"
                      onClick={() => toggleAmenity(amenityId)}
                    >
                      <X className="h-2 w-2" />
                    </Button>
                  </Badge>
                ) : null;
              })}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
