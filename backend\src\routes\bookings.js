const express = require('express');
const { body, validationResult } = require('express-validator');
const { v4: uuidv4 } = require('uuid');
const Booking = require('../models/Booking');
const { findOne } = require('../utils/database');
const { authenticateToken, requireBookingOwnershipOrAdmin } = require('../middleware/auth');
const logger = require('../utils/logger');

const router = express.Router();

// Create new booking
router.post('/', [
  authenticateToken,
  body('propertyId').notEmpty().withMessage('Property ID required'),
  body('checkInDate').isISO8601().withMessage('Valid check-in date required'),
  body('checkOutDate').isISO8601().withMessage('Valid check-out date required'),
  body('guestCount').isInt({ min: 1 }).withMessage('Guest count must be at least 1'),
  body('specialRequests').optional().trim()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { propertyId, checkInDate, checkOutDate, guestCount, specialRequests } = req.body;
    const guestId = req.user.id;

    // Validate dates
    const checkIn = new Date(checkInDate);
    const checkOut = new Date(checkOutDate);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (checkIn < today) {
      return res.status(400).json({
        error: 'Check-in date cannot be in the past'
      });
    }

    if (checkOut <= checkIn) {
      return res.status(400).json({
        error: 'Check-out date must be after check-in date'
      });
    }

    // Get property details and validate
    const property = await findOne(`
      SELECT id, title, price_per_night, cleaning_fee, max_guests, owner_id
      FROM properties
      WHERE id = ? AND status = 'active'
    `, [propertyId]);

    if (!property) {
      return res.status(404).json({
        error: 'Property not found or not available'
      });
    }

    // Check guest capacity
    if (guestCount > property.max_guests) {
      return res.status(400).json({
        error: `Property can accommodate maximum ${property.max_guests} guests`
      });
    }

    // Check availability using Booking model
    const conflictingBookings = await Booking.checkAvailability(propertyId, checkInDate, checkOutDate);

    if (conflictingBookings.length > 0) {
      return res.status(409).json({
        error: 'Property is not available for selected dates'
      });
    }

    // Calculate total amount using Booking model
    const costBreakdown = await Booking.calculateTotalAmount(propertyId, checkInDate, checkOutDate);

    // Create booking using Booking model
    const bookingId = uuidv4();
    const bookingData = {
      id: bookingId,
      property_id: propertyId,
      guest_id: guestId,
      check_in_date: checkInDate,
      check_out_date: checkOutDate,
      total_amount: costBreakdown.totalAmount,
      booking_status: 'pending',
      payment_status: 'pending',
      guest_count: guestCount,
      special_requests: specialRequests || null
    };

    await Booking.create(bookingData);

    // Get complete booking details for response
    const newBooking = await Booking.findById(bookingId);

    res.status(201).json({
      message: 'Booking created successfully',
      booking: {
        id: newBooking.id,
        propertyId: newBooking.property_id,
        propertyTitle: newBooking.property_title,
        propertyLocation: newBooking.property_location,
        checkInDate: newBooking.check_in_date,
        checkOutDate: newBooking.check_out_date,
        guestCount: newBooking.guest_count,
        totalAmount: parseFloat(newBooking.total_amount),
        bookingStatus: newBooking.booking_status,
        paymentStatus: newBooking.payment_status,
        specialRequests: newBooking.special_requests,
        costBreakdown: {
          nights: costBreakdown.nights,
          pricePerNight: costBreakdown.pricePerNight,
          accommodationCost: costBreakdown.accommodationCost,
          cleaningFee: costBreakdown.cleaningFee,
          totalAmount: costBreakdown.totalAmount
        },
        guest: {
          firstName: newBooking.guest_first_name,
          lastName: newBooking.guest_last_name
        },
        createdAt: newBooking.created_at
      }
    });

    logger.info(`Booking created: ${bookingId} for property ${propertyId} by user ${guestId}`);

  } catch (error) {
    logger.error('Booking creation error:', error);
    res.status(500).json({
      error: 'Failed to create booking',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
});

// Get user's bookings
router.get('/', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const userRole = req.user.role;

    const bookings = await Booking.findByUserId(userId, userRole);

    const formattedBookings = bookings.map(booking => ({
      id: booking.id,
      propertyId: booking.property_id,
      propertyTitle: booking.property_title,
      propertyLocation: booking.property_location,
      checkInDate: booking.check_in_date,
      checkOutDate: booking.check_out_date,
      guestCount: booking.guest_count,
      totalAmount: parseFloat(booking.total_amount),
      bookingStatus: booking.booking_status,
      paymentStatus: booking.payment_status,
      specialRequests: booking.special_requests,
      guest: {
        firstName: booking.guest_first_name,
        lastName: booking.guest_last_name
      },
      owner: {
        firstName: booking.owner_first_name,
        lastName: booking.owner_last_name
      },
      isOwner: booking.owner_id === userId,
      isGuest: booking.guest_id === userId,
      createdAt: booking.created_at,
      updatedAt: booking.updated_at
    }));

    res.json({
      bookings: formattedBookings
    });

    logger.info(`Bookings fetched for user ${userId}: ${formattedBookings.length} bookings`);

  } catch (error) {
    logger.error('Bookings fetch error:', error);
    res.status(500).json({
      error: 'Failed to fetch bookings',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
});

// Get single booking
router.get('/:id', [authenticateToken, requireBookingOwnershipOrAdmin], async (req, res) => {
  try {
    const { id } = req.params;

    const booking = await Booking.findById(id);

    if (!booking) {
      return res.status(404).json({
        error: 'Booking not found'
      });
    }

    const formattedBooking = {
      id: booking.id,
      property: {
        id: booking.property_id,
        title: booking.property_title,
        description: booking.property_description,
        location: booking.property_location
      },
      checkInDate: booking.check_in_date,
      checkOutDate: booking.check_out_date,
      guestCount: booking.guest_count,
      totalAmount: parseFloat(booking.total_amount),
      bookingStatus: booking.booking_status,
      paymentStatus: booking.payment_status,
      specialRequests: booking.special_requests,
      guest: {
        firstName: booking.guest_first_name,
        lastName: booking.guest_last_name,
        email: booking.guest_email,
        phone: booking.guest_phone
      },
      owner: {
        firstName: booking.owner_first_name,
        lastName: booking.owner_last_name,
        email: booking.owner_email,
        phone: booking.owner_phone
      },
      createdAt: booking.created_at,
      updatedAt: booking.updated_at
    };

    res.json({
      booking: formattedBooking
    });

    logger.info(`Booking details fetched: ${id}`);

  } catch (error) {
    logger.error('Booking fetch error:', error);
    res.status(500).json({
      error: 'Failed to fetch booking',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
});

// Update booking status
router.patch('/:id/status', [
  authenticateToken,
  requireBookingOwnershipOrAdmin,
  body('status').isIn(['pending', 'confirmed', 'cancelled', 'completed']).withMessage('Invalid status')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { id } = req.params;
    const { status } = req.body;

    const result = await Booking.updateStatus(id, status);

    if (result.affectedRows === 0) {
      return res.status(404).json({
        error: 'Booking not found'
      });
    }

    res.json({
      message: 'Booking status updated successfully',
      status
    });

    logger.info(`Booking status updated: ${id} -> ${status}`);

  } catch (error) {
    logger.error('Booking status update error:', error);
    res.status(500).json({
      error: 'Failed to update booking status',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
});

// Get upcoming bookings
router.get('/upcoming/list', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const userRole = req.user.role;

    const upcomingBookings = await Booking.getUpcomingBookings(userId, userRole);

    const formattedBookings = upcomingBookings.map(booking => ({
      id: booking.id,
      propertyId: booking.property_id,
      propertyTitle: booking.property_title,
      propertyLocation: booking.property_location,
      checkInDate: booking.check_in_date,
      checkOutDate: booking.check_out_date,
      guestCount: booking.guest_count,
      totalAmount: parseFloat(booking.total_amount),
      bookingStatus: booking.booking_status,
      paymentStatus: booking.payment_status,
      guest: {
        firstName: booking.guest_first_name,
        lastName: booking.guest_last_name
      },
      isOwner: booking.owner_id === userId,
      isGuest: booking.guest_id === userId,
      createdAt: booking.created_at
    }));

    res.json({
      upcomingBookings: formattedBookings
    });

    logger.info(`Upcoming bookings fetched for user ${userId}: ${formattedBookings.length} bookings`);

  } catch (error) {
    logger.error('Upcoming bookings fetch error:', error);
    res.status(500).json({
      error: 'Failed to fetch upcoming bookings',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
});

// Get booking statistics
router.get('/stats/summary', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const userRole = req.user.role;

    const stats = await Booking.getBookingStats(userId, userRole);

    res.json({
      stats: {
        totalBookings: parseInt(stats.total_bookings),
        confirmedBookings: parseInt(stats.confirmed_bookings),
        pendingBookings: parseInt(stats.pending_bookings),
        cancelledBookings: parseInt(stats.cancelled_bookings),
        completedBookings: parseInt(stats.completed_bookings),
        totalRevenue: parseFloat(stats.total_revenue || 0)
      }
    });

    logger.info(`Booking stats fetched for user ${userId}`);

  } catch (error) {
    logger.error('Booking stats fetch error:', error);
    res.status(500).json({
      error: 'Failed to fetch booking statistics',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
});

// Calculate booking cost (for frontend booking form)
router.post('/calculate-cost', [
  authenticateToken,
  body('propertyId').notEmpty().withMessage('Property ID required'),
  body('checkInDate').isISO8601().withMessage('Valid check-in date required'),
  body('checkOutDate').isISO8601().withMessage('Valid check-out date required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { propertyId, checkInDate, checkOutDate } = req.body;

    // Validate dates
    const checkIn = new Date(checkInDate);
    const checkOut = new Date(checkOutDate);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (checkIn < today) {
      return res.status(400).json({
        error: 'Check-in date cannot be in the past'
      });
    }

    if (checkOut <= checkIn) {
      return res.status(400).json({
        error: 'Check-out date must be after check-in date'
      });
    }

    // Calculate cost breakdown
    const costBreakdown = await Booking.calculateTotalAmount(propertyId, checkInDate, checkOutDate);

    res.json({
      costBreakdown: {
        nights: costBreakdown.nights,
        pricePerNight: costBreakdown.pricePerNight,
        accommodationCost: costBreakdown.accommodationCost,
        cleaningFee: costBreakdown.cleaningFee,
        totalAmount: costBreakdown.totalAmount
      }
    });

    logger.info(`Cost calculated for property ${propertyId}: R${costBreakdown.totalAmount}`);

  } catch (error) {
    logger.error('Cost calculation error:', error);
    res.status(500).json({
      error: 'Failed to calculate booking cost',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
});

module.exports = router;
