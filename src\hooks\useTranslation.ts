import { useState, useEffect, useCallback, createContext, useContext } from 'react';

// Supported languages
export type SupportedLanguage = 'en' | 'af' | 'zu' | 'xh' | 'st' | 'tn' | 'ss' | 've' | 'ts' | 'nr' | 'nso';

export interface LanguageInfo {
  code: SupportedLanguage;
  name: string;
  nativeName: string;
  flag: string;
  rtl: boolean;
}

// South African languages
export const supportedLanguages: Record<SupportedLanguage, LanguageInfo> = {
  en: { code: 'en', name: 'English', nativeName: 'English', flag: '🇿🇦', rtl: false },
  af: { code: 'af', name: 'Afrikaans', nativeName: 'Afrikaans', flag: '🇿🇦', rtl: false },
  zu: { code: 'zu', name: 'Zulu', nativeName: 'isiZulu', flag: '🇿🇦', rtl: false },
  xh: { code: 'xh', name: 'Xhosa', nativeName: 'isiXhosa', flag: '🇿🇦', rtl: false },
  st: { code: 'st', name: 'Sotho', nativeName: 'Sesotho', flag: '🇿🇦', rtl: false },
  tn: { code: 'tn', name: 'Tswana', nativeName: 'Setswana', flag: '🇿🇦', rtl: false },
  ss: { code: 'ss', name: 'Swati', nativeName: 'siSwati', flag: '🇿🇦', rtl: false },
  ve: { code: 've', name: 'Venda', nativeName: 'Tshivenḓa', flag: '🇿🇦', rtl: false },
  ts: { code: 'ts', name: 'Tsonga', nativeName: 'Xitsonga', flag: '🇿🇦', rtl: false },
  nr: { code: 'nr', name: 'Ndebele', nativeName: 'isiNdebele', flag: '🇿🇦', rtl: false },
  nso: { code: 'nso', name: 'Northern Sotho', nativeName: 'Sepedi', flag: '🇿🇦', rtl: false }
};

// Translation keys and default English translations
export interface TranslationKeys {
  // Common
  'common.loading': string;
  'common.error': string;
  'common.success': string;
  'common.cancel': string;
  'common.save': string;
  'common.delete': string;
  'common.edit': string;
  'common.view': string;
  'common.search': string;
  'common.filter': string;
  'common.sort': string;
  'common.next': string;
  'common.previous': string;
  'common.close': string;
  'common.open': string;
  'common.yes': string;
  'common.no': string;

  // Navigation
  'nav.home': string;
  'nav.search': string;
  'nav.wishlist': string;
  'nav.trips': string;
  'nav.messages': string;
  'nav.profile': string;
  'nav.help': string;
  'nav.logout': string;

  // Search
  'search.destination': string;
  'search.checkIn': string;
  'search.checkOut': string;
  'search.guests': string;
  'search.searchButton': string;
  'search.filters': string;
  'search.results': string;
  'search.noResults': string;
  'search.priceRange': string;
  'search.propertyType': string;
  'search.amenities': string;

  // Property
  'property.details': string;
  'property.amenities': string;
  'property.reviews': string;
  'property.location': string;
  'property.host': string;
  'property.policies': string;
  'property.availability': string;
  'property.book': string;
  'property.contact': string;
  'property.share': string;
  'property.save': string;
  'property.photos': string;

  // Booking
  'booking.summary': string;
  'booking.dates': string;
  'booking.guests': string;
  'booking.total': string;
  'booking.confirm': string;
  'booking.payment': string;
  'booking.success': string;
  'booking.failed': string;

  // User
  'user.profile': string;
  'user.settings': string;
  'user.notifications': string;
  'user.privacy': string;
  'user.security': string;
  'user.preferences': string;

  // Messages
  'messages.inbox': string;
  'messages.compose': string;
  'messages.reply': string;
  'messages.send': string;
  'messages.typing': string;
  'messages.online': string;
  'messages.offline': string;

  // Reviews
  'reviews.write': string;
  'reviews.rating': string;
  'reviews.comment': string;
  'reviews.submit': string;
  'reviews.helpful': string;
  'reviews.report': string;

  // Errors
  'error.network': string;
  'error.notFound': string;
  'error.unauthorized': string;
  'error.serverError': string;
  'error.validation': string;
}

// Default English translations
const defaultTranslations: TranslationKeys = {
  // Common
  'common.loading': 'Loading...',
  'common.error': 'Error',
  'common.success': 'Success',
  'common.cancel': 'Cancel',
  'common.save': 'Save',
  'common.delete': 'Delete',
  'common.edit': 'Edit',
  'common.view': 'View',
  'common.search': 'Search',
  'common.filter': 'Filter',
  'common.sort': 'Sort',
  'common.next': 'Next',
  'common.previous': 'Previous',
  'common.close': 'Close',
  'common.open': 'Open',
  'common.yes': 'Yes',
  'common.no': 'No',

  // Navigation
  'nav.home': 'Home',
  'nav.search': 'Search',
  'nav.wishlist': 'Wishlist',
  'nav.trips': 'Trips',
  'nav.messages': 'Messages',
  'nav.profile': 'Profile',
  'nav.help': 'Help',
  'nav.logout': 'Logout',

  // Search
  'search.destination': 'Where are you going?',
  'search.checkIn': 'Check-in',
  'search.checkOut': 'Check-out',
  'search.guests': 'Guests',
  'search.searchButton': 'Search',
  'search.filters': 'Filters',
  'search.results': 'Search Results',
  'search.noResults': 'No results found',
  'search.priceRange': 'Price Range',
  'search.propertyType': 'Property Type',
  'search.amenities': 'Amenities',

  // Property
  'property.details': 'Property Details',
  'property.amenities': 'Amenities',
  'property.reviews': 'Reviews',
  'property.location': 'Location',
  'property.host': 'Host',
  'property.policies': 'Policies',
  'property.availability': 'Availability',
  'property.book': 'Book Now',
  'property.contact': 'Contact Host',
  'property.share': 'Share',
  'property.save': 'Save',
  'property.photos': 'Photos',

  // Booking
  'booking.summary': 'Booking Summary',
  'booking.dates': 'Dates',
  'booking.guests': 'Guests',
  'booking.total': 'Total',
  'booking.confirm': 'Confirm Booking',
  'booking.payment': 'Payment',
  'booking.success': 'Booking Confirmed',
  'booking.failed': 'Booking Failed',

  // User
  'user.profile': 'Profile',
  'user.settings': 'Settings',
  'user.notifications': 'Notifications',
  'user.privacy': 'Privacy',
  'user.security': 'Security',
  'user.preferences': 'Preferences',

  // Messages
  'messages.inbox': 'Inbox',
  'messages.compose': 'Compose',
  'messages.reply': 'Reply',
  'messages.send': 'Send',
  'messages.typing': 'Typing...',
  'messages.online': 'Online',
  'messages.offline': 'Offline',

  // Reviews
  'reviews.write': 'Write Review',
  'reviews.rating': 'Rating',
  'reviews.comment': 'Comment',
  'reviews.submit': 'Submit Review',
  'reviews.helpful': 'Helpful',
  'reviews.report': 'Report',

  // Errors
  'error.network': 'Network error. Please check your connection.',
  'error.notFound': 'Page not found',
  'error.unauthorized': 'You are not authorized to access this page',
  'error.serverError': 'Server error. Please try again later.',
  'error.validation': 'Please check your input and try again'
};

// Sample translations for Afrikaans (basic examples)
const afrikaansTranslations: Partial<TranslationKeys> = {
  'common.loading': 'Laai...',
  'common.error': 'Fout',
  'common.success': 'Sukses',
  'common.cancel': 'Kanselleer',
  'common.save': 'Stoor',
  'common.search': 'Soek',
  'nav.home': 'Tuis',
  'nav.search': 'Soek',
  'nav.profile': 'Profiel',
  'search.destination': 'Waarheen gaan jy?',
  'search.guests': 'Gaste',
  'property.book': 'Bespreek Nou',
  'booking.total': 'Totaal'
};

// Translation storage
const translations: Record<SupportedLanguage, Partial<TranslationKeys>> = {
  en: defaultTranslations,
  af: afrikaansTranslations,
  zu: {}, // Would be populated with Zulu translations
  xh: {}, // Would be populated with Xhosa translations
  st: {}, // Would be populated with Sotho translations
  tn: {}, // Would be populated with Tswana translations
  ss: {}, // Would be populated with Swati translations
  ve: {}, // Would be populated with Venda translations
  ts: {}, // Would be populated with Tsonga translations
  nr: {}, // Would be populated with Ndebele translations
  nso: {} // Would be populated with Northern Sotho translations
};

// Translation context
interface TranslationContextType {
  language: SupportedLanguage;
  setLanguage: (language: SupportedLanguage) => void;
  t: (key: keyof TranslationKeys, params?: Record<string, string | number>) => string;
  languages: Record<SupportedLanguage, LanguageInfo>;
}

const TranslationContext = createContext<TranslationContextType | undefined>(undefined);

export const useTranslation = () => {
  const [language, setLanguageState] = useState<SupportedLanguage>(() => {
    // Get language from localStorage or browser preference
    const saved = localStorage.getItem('stayfinder_language');
    if (saved && saved in supportedLanguages) {
      return saved as SupportedLanguage;
    }
    
    // Detect browser language
    const browserLang = navigator.language.split('-')[0];
    if (browserLang in supportedLanguages) {
      return browserLang as SupportedLanguage;
    }
    
    return 'en'; // Default to English
  });

  const setLanguage = useCallback((newLanguage: SupportedLanguage) => {
    setLanguageState(newLanguage);
    localStorage.setItem('stayfinder_language', newLanguage);
    
    // Update document language and direction
    document.documentElement.lang = newLanguage;
    document.documentElement.dir = supportedLanguages[newLanguage].rtl ? 'rtl' : 'ltr';
  }, []);

  const t = useCallback((key: keyof TranslationKeys, params?: Record<string, string | number>): string => {
    // Get translation from current language or fall back to English
    const currentTranslations = translations[language];
    let translation = currentTranslations[key] || defaultTranslations[key] || key;

    // Replace parameters in translation
    if (params) {
      Object.entries(params).forEach(([paramKey, value]) => {
        translation = translation.replace(new RegExp(`{{${paramKey}}}`, 'g'), String(value));
      });
    }

    return translation;
  }, [language]);

  // Set initial document language
  useEffect(() => {
    document.documentElement.lang = language;
    document.documentElement.dir = supportedLanguages[language].rtl ? 'rtl' : 'ltr';
  }, [language]);

  return {
    language,
    setLanguage,
    t,
    languages: supportedLanguages
  };
};

// Language selector hook
export const useLanguageSelector = () => {
  const { language, setLanguage, languages } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);

  const currentLanguage = languages[language];
  const availableLanguages = Object.values(languages);

  const selectLanguage = useCallback((langCode: SupportedLanguage) => {
    setLanguage(langCode);
    setIsOpen(false);
  }, [setLanguage]);

  return {
    currentLanguage,
    availableLanguages,
    isOpen,
    setIsOpen,
    selectLanguage
  };
};

// Translation provider component
export const TranslationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const translation = useTranslation();
  
  return (
    <TranslationContext.Provider value={translation}>
      {children}
    </TranslationContext.Provider>
  );
};

// Hook to use translation context
export const useTranslationContext = () => {
  const context = useContext(TranslationContext);
  if (!context) {
    throw new Error('useTranslationContext must be used within a TranslationProvider');
  }
  return context;
};

// Utility functions
export const formatCurrency = (amount: number, language: SupportedLanguage): string => {
  const locale = language === 'af' ? 'af-ZA' : 'en-ZA';
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: 'ZAR'
  }).format(amount);
};

export const formatDate = (date: Date, language: SupportedLanguage): string => {
  const locale = language === 'af' ? 'af-ZA' : 'en-ZA';
  return new Intl.DateTimeFormat(locale, {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }).format(date);
};

export const formatNumber = (number: number, language: SupportedLanguage): string => {
  const locale = language === 'af' ? 'af-ZA' : 'en-ZA';
  return new Intl.NumberFormat(locale).format(number);
};

// Translation loading utility (for dynamic loading of translation files)
export const loadTranslations = async (language: SupportedLanguage): Promise<Partial<TranslationKeys>> => {
  try {
    // In a real implementation, this would load translation files from a server or CDN
    const response = await fetch(`/translations/${language}.json`);
    if (response.ok) {
      return await response.json();
    }
  } catch (error) {
    console.warn(`Failed to load translations for ${language}:`, error);
  }
  
  // Return existing translations as fallback
  return translations[language] || {};
};

// Translation validation utility
export const validateTranslations = (languageTranslations: Partial<TranslationKeys>): string[] => {
  const missingKeys: string[] = [];
  const requiredKeys = Object.keys(defaultTranslations) as (keyof TranslationKeys)[];
  
  requiredKeys.forEach(key => {
    if (!languageTranslations[key]) {
      missingKeys.push(key);
    }
  });
  
  return missingKeys;
};
