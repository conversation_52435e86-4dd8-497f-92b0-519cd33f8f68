-- Enhance users table for complete user profiles
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS phone VARCHAR(20),
ADD COLUMN IF NOT EXISTS date_of_birth DATE,
ADD COLUMN IF NOT EXISTS profile_picture VARCHAR(255),
ADD COLUMN IF NOT EXISTS bio TEXT,
ADD COLUMN IF NOT EXISTS location VARCHAR(255),
ADD COLUMN IF NOT EXISTS languages JSON,
ADD COLUMN IF NOT EXISTS occupation VARCHAR(255),
ADD COLUMN IF NOT EXISTS emergency_contact_name VARCHAR(255),
ADD COLUMN IF NOT EXISTS emergency_contact_phone VARCHAR(20),
ADD COLUMN IF NOT EXISTS email_verified BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS phone_verified BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS id_verified BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS id_document_path VARCHAR(255),
ADD COLUMN IF NOT EXISTS id_verification_status ENUM('pending', 'verified', 'rejected') DEFAULT NULL,
ADD COLUMN IF NOT EXISTS preferences JSON,
ADD COLUMN IF NOT EXISTS notification_settings JSON,
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_email_verified ON users(email_verified);
CREATE INDEX IF NOT EXISTS idx_users_phone_verified ON users(phone_verified);
CREATE INDEX IF NOT EXISTS idx_users_id_verified ON users(id_verified);
CREATE INDEX IF NOT EXISTS idx_users_location ON users(location);
CREATE INDEX IF NOT EXISTS idx_users_updated_at ON users(updated_at);

-- Update existing users with default values
UPDATE users 
SET 
    email_verified = TRUE,
    preferences = JSON_OBJECT(
        'currency', 'ZAR',
        'language', 'en',
        'timezone', 'Africa/Johannesburg',
        'marketing_emails', true,
        'booking_notifications', true,
        'review_notifications', true
    ),
    notification_settings = JSON_OBJECT(
        'email_notifications', true,
        'sms_notifications', false,
        'push_notifications', true,
        'marketing_communications', false,
        'booking_updates', true,
        'review_reminders', true,
        'price_alerts', false
    ),
    languages = JSON_ARRAY('English'),
    location = 'South Africa'
WHERE preferences IS NULL OR notification_settings IS NULL;

-- Sample data updates for testing
UPDATE users 
SET 
    phone = '+27123456789',
    bio = 'Travel enthusiast who loves exploring the beautiful South African coast.',
    occupation = 'Software Developer',
    location = 'Cape Town, South Africa'
WHERE email = '<EMAIL>';

UPDATE users 
SET 
    phone = '+27987654321',
    bio = 'Hospitality professional with a passion for providing exceptional guest experiences.',
    occupation = 'Hotel Manager',
    location = 'Durban, South Africa'
WHERE email = '<EMAIL>';
