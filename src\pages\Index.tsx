
import { Header } from '@/components/Header';
import { Hero } from '@/components/Hero';
import { FeaturedProperties } from '@/components/FeaturedProperties';
import { SmartRecommendations } from '@/components/SmartRecommendations';
import { FeedbackSection } from '@/components/FeedbackSection';
import { Footer } from '@/components/Footer';

const Index = () => {
  return (
    <div className="min-h-screen">
      <Header />
      <Hero />
      <FeaturedProperties />

      {/* Smart Recommendations */}
      <div className="container mx-auto px-4 py-12">
        <div className="space-y-12">
          {/* Trending Properties */}
          <SmartRecommendations
            type="trending"
            title="Trending Destinations"
            subtitle="Popular properties that guests are booking right now"
            limit={6}
          />

          {/* Personalized Recommendations (for logged-in users) */}
          <SmartRecommendations
            type="personalized"
            title="Recommended for You"
            subtitle="Based on your preferences and browsing history"
            limit={6}
          />

          {/* Recently Viewed (for logged-in users) */}
          <SmartRecommendations
            type="recently_viewed"
            title="Continue Exploring"
            subtitle="Properties you've recently viewed"
            limit={4}
          />
        </div>
      </div>

      <FeedbackSection />
      <Footer />
    </div>
  );
};

export default Index;
