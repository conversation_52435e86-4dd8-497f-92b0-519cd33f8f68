import React, { useState } from 'react';
import { HostDashboard } from '@/components/HostDashboard';
import { PropertyCreationWizard } from '@/components/PropertyCreationWizard';
import { PropertyAnalytics } from '@/components/PropertyAnalytics';
import { ImageUploadComponent } from '@/components/ImageUploadComponent';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Header } from '@/components/Header';
import { 
  PageTransition, 
  SlideIn, 
  StaggeredAnimation 
} from '@/components/ui/page-transitions';
import { EnhancedBreadcrumb } from '@/components/ui/enhanced-breadcrumb';
import { 
  Home, 
  Settings, 
  BarChart3, 
  Camera, 
  Plus,
  Users,
  DollarSign,
  Calendar,
  Star,
  Award,
  Shield,
  Zap,
  Target,
  Building
} from 'lucide-react';

export const PropertyManagementDemo: React.FC = () => {
  const [selectedDemo, setSelectedDemo] = useState<'dashboard' | 'wizard' | 'analytics' | 'upload'>('dashboard');
  const [sampleImages, setSampleImages] = useState<any[]>([]);

  const demoSections = [
    {
      id: 'dashboard',
      title: 'Host Dashboard',
      description: 'Comprehensive property management and analytics overview',
      icon: BarChart3,
      color: 'from-blue-500 to-blue-600'
    },
    {
      id: 'wizard',
      title: 'Property Creation Wizard',
      description: 'Step-by-step property listing creation process',
      icon: Plus,
      color: 'from-green-500 to-green-600'
    },
    {
      id: 'analytics',
      title: 'Property Analytics',
      description: 'Detailed performance metrics and insights',
      icon: BarChart3,
      color: 'from-purple-500 to-purple-600'
    },
    {
      id: 'upload',
      title: 'Image Upload System',
      description: 'Advanced image management with drag-and-drop',
      icon: Camera,
      color: 'from-orange-500 to-orange-600'
    }
  ];

  return (
    <PageTransition>
      <div className="min-h-screen bg-gray-50">
        <Header />
        
        <div className="container mx-auto px-4 py-8">
          <SlideIn direction="up" delay={100}>
            <div className="mb-8">
              <EnhancedBreadcrumb 
                items={[
                  { label: 'Home', href: '/', icon: <Home className="h-4 w-4" /> },
                  { label: 'Property Management Demo', current: true }
                ]}
              />
              <div className="text-center mt-8">
                <div className="inline-flex items-center gap-2 bg-gradient-to-r from-green-100 to-blue-100 rounded-full px-6 py-3 mb-6">
                  <Building className="h-5 w-5 text-green-600" />
                  <span className="text-sm font-semibold text-green-700">Property Management Tools</span>
                  <Badge className="bg-green-500 text-white text-xs">Host Features</Badge>
                </div>
                <h1 className="text-5xl font-bold text-gray-900 mb-4">
                  Property Management Suite
                </h1>
                <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                  Comprehensive tools for hosts to manage properties, track performance, 
                  and optimize their rental business
                </p>
              </div>
            </div>
          </SlideIn>

          {/* Demo Section Selector */}
          <SlideIn direction="up" delay={200}>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
              <StaggeredAnimation delay={100}>
                {demoSections.map((section) => {
                  const Icon = section.icon;
                  return (
                    <Card 
                      key={section.id}
                      className={`cursor-pointer transition-all duration-300 border-2 ${
                        selectedDemo === section.id 
                          ? 'border-blue-500 shadow-lg scale-105' 
                          : 'border-gray-200 hover:border-blue-300 hover:shadow-md'
                      }`}
                      onClick={() => setSelectedDemo(section.id as any)}
                    >
                      <CardContent className="p-6 text-center">
                        <div className={`w-16 h-16 bg-gradient-to-br ${section.color} rounded-2xl flex items-center justify-center mx-auto mb-4`}>
                          <Icon className="h-8 w-8 text-white" />
                        </div>
                        <h3 className="text-lg font-bold text-gray-900 mb-2">{section.title}</h3>
                        <p className="text-gray-600 text-sm">{section.description}</p>
                      </CardContent>
                    </Card>
                  );
                })}
              </StaggeredAnimation>
            </div>
          </SlideIn>

          {/* Demo Content */}
          <SlideIn direction="up" delay={300}>
            <div className="space-y-8">
              {/* Host Dashboard Demo */}
              {selectedDemo === 'dashboard' && (
                <div>
                  <Card className="border-0 shadow-lg mb-6">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <BarChart3 className="h-5 w-5 text-blue-600" />
                        Host Dashboard
                      </CardTitle>
                      <p className="text-gray-600">
                        Complete property management interface with analytics, bookings, and performance metrics
                      </p>
                    </CardHeader>
                  </Card>
                  
                  <HostDashboard hostId="demo-host" />
                </div>
              )}

              {/* Property Creation Wizard Demo */}
              {selectedDemo === 'wizard' && (
                <div>
                  <Card className="border-0 shadow-lg mb-6">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Plus className="h-5 w-5 text-green-600" />
                        Property Creation Wizard
                      </CardTitle>
                      <p className="text-gray-600">
                        Step-by-step guided process for creating new property listings
                      </p>
                    </CardHeader>
                  </Card>

                  <PropertyCreationWizard
                    onComplete={(propertyData) => {
                      console.log('Demo property created:', propertyData);
                      alert('Property created successfully! (Demo mode)');
                    }}
                    onCancel={() => {
                      console.log('Property creation cancelled');
                    }}
                  />
                </div>
              )}

              {/* Property Analytics Demo */}
              {selectedDemo === 'analytics' && (
                <div>
                  <Card className="border-0 shadow-lg mb-6">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <BarChart3 className="h-5 w-5 text-purple-600" />
                        Property Analytics
                      </CardTitle>
                      <p className="text-gray-600">
                        Comprehensive performance analytics and business insights
                      </p>
                    </CardHeader>
                  </Card>

                  <PropertyAnalytics 
                    propertyId="demo-property"
                    propertyTitle="Luxury Beachfront Villa - Camps Bay"
                  />
                </div>
              )}

              {/* Image Upload Demo */}
              {selectedDemo === 'upload' && (
                <div>
                  <Card className="border-0 shadow-lg mb-6">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Camera className="h-5 w-5 text-orange-600" />
                        Image Upload System
                      </CardTitle>
                      <p className="text-gray-600">
                        Advanced image management with drag-and-drop, captions, and organization
                      </p>
                    </CardHeader>
                  </Card>

                  <ImageUploadComponent
                    images={sampleImages}
                    onImagesChange={setSampleImages}
                    maxImages={20}
                    maxFileSize={10}
                    showCaptions={true}
                    showTags={true}
                    allowReordering={true}
                  />
                </div>
              )}
            </div>
          </SlideIn>

          {/* Features Summary */}
          <SlideIn direction="up" delay={400}>
            <Card className="border-0 shadow-lg mt-12">
              <CardHeader>
                <CardTitle className="text-2xl text-center">Property Management Features Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  {[
                    { icon: BarChart3, title: 'Analytics', count: '15+', description: 'Performance metrics' },
                    { icon: Plus, title: 'Creation Tools', count: '7', description: 'Step wizard process' },
                    { icon: Camera, title: 'Image Management', count: '20', description: 'Photos per property' },
                    { icon: Shield, title: 'Host Protection', count: '100%', description: 'Secure platform' }
                  ].map((stat, index) => (
                    <div key={index} className="text-center">
                      <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-blue-500 rounded-2xl flex items-center justify-center mx-auto mb-4">
                        <stat.icon className="h-8 w-8 text-white" />
                      </div>
                      <div className="text-3xl font-bold text-gray-900 mb-1">{stat.count}</div>
                      <div className="text-lg font-semibold text-gray-700 mb-1">{stat.title}</div>
                      <div className="text-sm text-gray-600">{stat.description}</div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </SlideIn>

          {/* Host Benefits */}
          <SlideIn direction="up" delay={500}>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-12">
              {[
                {
                  icon: DollarSign,
                  title: 'Maximize Revenue',
                  description: 'Advanced pricing tools and analytics to optimize your earnings',
                  color: 'from-green-500 to-green-600'
                },
                {
                  icon: Users,
                  title: 'Guest Management',
                  description: 'Streamlined booking and communication tools',
                  color: 'from-blue-500 to-blue-600'
                },
                {
                  icon: Star,
                  title: 'Quality Assurance',
                  description: 'Tools to maintain high standards and excellent reviews',
                  color: 'from-yellow-500 to-yellow-600'
                },
                {
                  icon: Calendar,
                  title: 'Smart Scheduling',
                  description: 'Automated calendar management and availability optimization',
                  color: 'from-purple-500 to-purple-600'
                },
                {
                  icon: Target,
                  title: 'Performance Tracking',
                  description: 'Detailed insights into property performance and market trends',
                  color: 'from-red-500 to-red-600'
                },
                {
                  icon: Zap,
                  title: 'Instant Updates',
                  description: 'Real-time notifications and automated responses',
                  color: 'from-indigo-500 to-indigo-600'
                }
              ].map((benefit, index) => (
                <Card key={index} className="border-0 shadow-lg hover:shadow-xl transition-shadow duration-300">
                  <CardContent className="p-6">
                    <div className={`w-12 h-12 bg-gradient-to-br ${benefit.color} rounded-xl flex items-center justify-center mb-4`}>
                      <benefit.icon className="h-6 w-6 text-white" />
                    </div>
                    <h3 className="text-lg font-bold text-gray-900 mb-2">{benefit.title}</h3>
                    <p className="text-gray-600">{benefit.description}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </SlideIn>
        </div>
      </div>
    </PageTransition>
  );
};
