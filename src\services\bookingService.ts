interface Booking {
  id: string;
  property_id: string;
  property_title: string;
  property_location: string;
  property_image: string;
  check_in_date: string;
  check_out_date: string;
  total_amount: number;
  booking_status: 'pending' | 'confirmed' | 'cancelled' | 'completed';
  payment_status: 'pending' | 'paid' | 'refunded';
  guest_count: number;
  special_requests?: string;
  created_at: string;
  updated_at: string;
  host_name: string;
  host_email: string;
  can_review: boolean;
  review_id?: string;
}

interface BookingStats {
  total_bookings: number;
  upcoming_trips: number;
  completed_trips: number;
  cancelled_bookings: number;
  total_spent: number;
}

interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

class BookingService {
  private baseUrl = 'http://localhost:3001/api/bookings';

  private async makeRequest<T>(url: string, options: RequestInit = {}): Promise<ApiResponse<T>> {
    try {
      const token = localStorage.getItem('auth_token');
      const headers: HeadersInit = {
        'Content-Type': 'application/json',
        ...options.headers,
      };

      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const response = await fetch(url, {
        ...options,
        headers,
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Booking API error:', error);
      throw error;
    }
  }

  async getUserBookings(): Promise<Booking[]> {
    try {
      const response = await this.makeRequest<Booking[]>(`${this.baseUrl}/user`);
      
      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to fetch bookings');
      }
    } catch (error) {
      console.error('Error fetching user bookings:', error);
      throw error;
    }
  }

  async getUpcomingBookings(): Promise<Booking[]> {
    try {
      const response = await this.makeRequest<Booking[]>(`${this.baseUrl}/user/upcoming`);
      
      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to fetch upcoming bookings');
      }
    } catch (error) {
      console.error('Error fetching upcoming bookings:', error);
      throw error;
    }
  }

  async getBookingHistory(): Promise<Booking[]> {
    try {
      const response = await this.makeRequest<Booking[]>(`${this.baseUrl}/user/history`);
      
      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to fetch booking history');
      }
    } catch (error) {
      console.error('Error fetching booking history:', error);
      throw error;
    }
  }

  async getBookingStats(): Promise<BookingStats> {
    try {
      const response = await this.makeRequest<BookingStats>(`${this.baseUrl}/user/stats`);
      
      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to fetch booking stats');
      }
    } catch (error) {
      console.error('Error fetching booking stats:', error);
      throw error;
    }
  }

  async cancelBooking(bookingId: string, reason?: string): Promise<boolean> {
    try {
      const response = await this.makeRequest<void>(`${this.baseUrl}/${bookingId}/cancel`, {
        method: 'POST',
        body: JSON.stringify({ reason }),
      });
      
      return response.success;
    } catch (error) {
      console.error('Error cancelling booking:', error);
      throw error;
    }
  }

  async getBookingDetails(bookingId: string): Promise<Booking> {
    try {
      const response = await this.makeRequest<Booking>(`${this.baseUrl}/${bookingId}`);
      
      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to fetch booking details');
      }
    } catch (error) {
      console.error('Error fetching booking details:', error);
      throw error;
    }
  }

  // Helper methods
  getBookingStatusColor(status: string): string {
    switch (status) {
      case 'confirmed':
        return 'bg-green-100 text-green-700 border-green-200';
      case 'pending':
        return 'bg-yellow-100 text-yellow-700 border-yellow-200';
      case 'cancelled':
        return 'bg-red-100 text-red-700 border-red-200';
      case 'completed':
        return 'bg-blue-100 text-blue-700 border-blue-200';
      default:
        return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  }

  getPaymentStatusColor(status: string): string {
    switch (status) {
      case 'paid':
        return 'bg-green-100 text-green-700 border-green-200';
      case 'pending':
        return 'bg-yellow-100 text-yellow-700 border-yellow-200';
      case 'refunded':
        return 'bg-blue-100 text-blue-700 border-blue-200';
      default:
        return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  }

  formatBookingDates(checkIn: string, checkOut: string): string {
    const checkInDate = new Date(checkIn);
    const checkOutDate = new Date(checkOut);
    
    const options: Intl.DateTimeFormatOptions = { 
      month: 'short', 
      day: 'numeric',
      year: checkInDate.getFullYear() !== new Date().getFullYear() ? 'numeric' : undefined
    };
    
    return `${checkInDate.toLocaleDateString('en-US', options)} - ${checkOutDate.toLocaleDateString('en-US', options)}`;
  }

  calculateNights(checkIn: string, checkOut: string): number {
    const checkInDate = new Date(checkIn);
    const checkOutDate = new Date(checkOut);
    const timeDiff = checkOutDate.getTime() - checkInDate.getTime();
    return Math.ceil(timeDiff / (1000 * 3600 * 24));
  }

  isUpcoming(checkIn: string): boolean {
    const checkInDate = new Date(checkIn);
    const today = new Date();
    return checkInDate > today;
  }

  canCancel(booking: Booking): boolean {
    const checkInDate = new Date(booking.check_in_date);
    const today = new Date();
    const daysUntilCheckIn = Math.ceil((checkInDate.getTime() - today.getTime()) / (1000 * 3600 * 24));
    
    return booking.booking_status === 'confirmed' && 
           booking.payment_status === 'paid' && 
           daysUntilCheckIn > 1; // Can cancel up to 1 day before check-in
  }

  // Mock data for testing when API is not available
  getMockBookings(): Booking[] {
    return [
      {
        id: 'booking-1',
        property_id: 'prop-1',
        property_title: 'Luxury Villa in Margate',
        property_location: 'Margate, KwaZulu-Natal',
        property_image: '/placeholder.svg',
        check_in_date: '2024-07-15',
        check_out_date: '2024-07-20',
        total_amount: 2500.00,
        booking_status: 'confirmed',
        payment_status: 'paid',
        guest_count: 4,
        special_requests: 'Late check-in requested',
        created_at: '2024-06-15T10:30:00Z',
        updated_at: '2024-06-15T10:30:00Z',
        host_name: 'Jane Smith',
        host_email: '<EMAIL>',
        can_review: false,
      },
      {
        id: 'booking-2',
        property_id: 'prop-2',
        property_title: 'Beachfront Cottage in Hibberdene',
        property_location: 'Hibberdene, KwaZulu-Natal',
        property_image: '/placeholder.svg',
        check_in_date: '2024-05-10',
        check_out_date: '2024-05-15',
        total_amount: 1800.00,
        booking_status: 'completed',
        payment_status: 'paid',
        guest_count: 2,
        created_at: '2024-04-20T14:15:00Z',
        updated_at: '2024-05-15T11:00:00Z',
        host_name: 'Mike Johnson',
        host_email: '<EMAIL>',
        can_review: true,
      },
      {
        id: 'booking-3',
        property_id: 'prop-3',
        property_title: 'Mountain Retreat in Drakensberg',
        property_location: 'Drakensberg, KwaZulu-Natal',
        property_image: '/placeholder.svg',
        check_in_date: '2024-08-01',
        check_out_date: '2024-08-05',
        total_amount: 3200.00,
        booking_status: 'pending',
        payment_status: 'pending',
        guest_count: 6,
        special_requests: 'Vegetarian meals requested',
        created_at: '2024-06-20T09:45:00Z',
        updated_at: '2024-06-20T09:45:00Z',
        host_name: 'Sarah Wilson',
        host_email: '<EMAIL>',
        can_review: false,
      }
    ];
  }

  getMockStats(): BookingStats {
    return {
      total_bookings: 8,
      upcoming_trips: 2,
      completed_trips: 5,
      cancelled_bookings: 1,
      total_spent: 15750.00
    };
  }
}

export const bookingService = new BookingService();
export type { Booking, BookingStats, ApiResponse };
