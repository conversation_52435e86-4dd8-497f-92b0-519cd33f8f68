import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useErrorReporting } from './ErrorCapture';
import { useDebugSystem } from './DebugSystemProvider';
import { Bug, AlertTriangle, Info, Zap, Play, Settings } from 'lucide-react';

export const DebugSystemDemo: React.FC = () => {
  const [demoCount, setDemoCount] = useState(0);
  const { reportError, reportWarning, reportInfo } = useErrorReporting();
  const { isEnabled, isVisible, togglePanel, config } = useDebugSystem();

  // Component that will throw an error
  const ErrorComponent: React.FC<{ shouldError: boolean }> = ({ shouldError }) => {
    if (shouldError) {
      throw new Error('Demo React component error - this is intentional for testing');
    }
    return <div className="text-green-600">Component rendered successfully!</div>;
  };

  const [showErrorComponent, setShowErrorComponent] = useState(false);

  const triggerConsoleError = () => {
    console.error('Demo console error - this is intentional for testing', {
      demoCount,
      timestamp: new Date().toISOString(),
      userAction: 'triggerConsoleError'
    });
    setDemoCount(prev => prev + 1);
  };

  const triggerConsoleWarning = () => {
    console.warn('Demo console warning - this is intentional for testing', {
      demoCount,
      timestamp: new Date().toISOString(),
      userAction: 'triggerConsoleWarning'
    });
    setDemoCount(prev => prev + 1);
  };

  const triggerConsoleInfo = () => {
    console.info('Demo console info - this is intentional for testing', {
      demoCount,
      timestamp: new Date().toISOString(),
      userAction: 'triggerConsoleInfo'
    });
    setDemoCount(prev => prev + 1);
  };

  const triggerConsoleDebug = () => {
    console.debug('Demo console debug - this is intentional for testing', {
      demoCount,
      timestamp: new Date().toISOString(),
      userAction: 'triggerConsoleDebug'
    });
    setDemoCount(prev => prev + 1);
  };

  const triggerManualError = () => {
    reportError(
      new Error(`Demo manual error #${demoCount + 1} - this is intentional for testing`),
      'demo-manual-error'
    );
    setDemoCount(prev => prev + 1);
  };

  const triggerManualWarning = () => {
    reportWarning(
      `Demo manual warning #${demoCount + 1} - this is intentional for testing`,
      'demo-manual-warning'
    );
    setDemoCount(prev => prev + 1);
  };

  const triggerManualInfo = () => {
    reportInfo(
      `Demo manual info #${demoCount + 1} - this is intentional for testing`,
      'demo-manual-info'
    );
    setDemoCount(prev => prev + 1);
  };

  const triggerGlobalError = () => {
    // Trigger a global error
    setTimeout(() => {
      throw new Error(`Demo global error #${demoCount + 1} - this is intentional for testing`);
    }, 100);
    setDemoCount(prev => prev + 1);
  };

  const triggerPromiseRejection = () => {
    // Trigger an unhandled promise rejection
    Promise.reject(new Error(`Demo promise rejection #${demoCount + 1} - this is intentional for testing`));
    setDemoCount(prev => prev + 1);
  };

  const triggerReactError = () => {
    setShowErrorComponent(true);
    setDemoCount(prev => prev + 1);
  };

  const resetErrorComponent = () => {
    setShowErrorComponent(false);
  };

  if (!isVisible) {
    return (
      <Card className="w-full max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bug className="h-5 w-5 text-gray-400" />
            Debug System Demo
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Settings className="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <p className="text-gray-600 mb-4">
              Debug system is not visible. This could be because:
            </p>
            <ul className="text-sm text-gray-500 space-y-1 mb-6">
              <li>• Debug system is disabled</li>
              <li>• You don't have admin privileges</li>
              <li>• Production mode is active without explicit enabling</li>
            </ul>
            <p className="text-sm text-gray-500">
              Check the admin dashboard to configure debug system settings.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="w-full max-w-4xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bug className="h-5 w-5 text-sea-green-600" />
            Debug System Demo & Testing
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Status */}
            <div className="flex items-center gap-4">
              <Badge className={isEnabled ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}>
                {isEnabled ? "Enabled" : "Disabled"}
              </Badge>
              <Badge className="bg-blue-100 text-blue-800">
                Demo Count: {demoCount}
              </Badge>
              <Button
                variant="outline"
                size="sm"
                onClick={togglePanel}
                className="ml-auto"
              >
                <Play className="h-4 w-4 mr-1" />
                Toggle Debug Panel
              </Button>
            </div>

            {/* Instructions */}
            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h4 className="font-medium text-blue-900 mb-2">How to Test:</h4>
              <ol className="text-sm text-blue-800 space-y-1">
                <li>1. Click the buttons below to trigger different types of errors</li>
                <li>2. Use <kbd className="px-1 py-0.5 bg-blue-200 rounded text-xs">Ctrl+Shift+D</kbd> to toggle the debug panel</li>
                <li>3. Observe how errors are captured and displayed in the debug panel</li>
                <li>4. Test the copy, dismiss, and export functionality</li>
                <li>5. Try the search and filter features</li>
              </ol>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Console Errors */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-red-500" />
            Console Error Testing
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            <Button
              variant="outline"
              onClick={triggerConsoleError}
              className="flex items-center gap-2 border-red-200 hover:bg-red-50"
            >
              <Bug className="h-4 w-4 text-red-500" />
              Console Error
            </Button>
            
            <Button
              variant="outline"
              onClick={triggerConsoleWarning}
              className="flex items-center gap-2 border-yellow-200 hover:bg-yellow-50"
            >
              <AlertTriangle className="h-4 w-4 text-yellow-500" />
              Console Warning
            </Button>
            
            <Button
              variant="outline"
              onClick={triggerConsoleInfo}
              className="flex items-center gap-2 border-blue-200 hover:bg-blue-50"
            >
              <Info className="h-4 w-4 text-blue-500" />
              Console Info
            </Button>
            
            <Button
              variant="outline"
              onClick={triggerConsoleDebug}
              className="flex items-center gap-2 border-purple-200 hover:bg-purple-50"
            >
              <Zap className="h-4 w-4 text-purple-500" />
              Console Debug
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Manual Reporting */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5 text-blue-500" />
            Manual Error Reporting
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            <Button
              variant="outline"
              onClick={triggerManualError}
              className="flex items-center gap-2 border-red-200 hover:bg-red-50"
            >
              <Bug className="h-4 w-4 text-red-500" />
              Manual Error
            </Button>
            
            <Button
              variant="outline"
              onClick={triggerManualWarning}
              className="flex items-center gap-2 border-yellow-200 hover:bg-yellow-50"
            >
              <AlertTriangle className="h-4 w-4 text-yellow-500" />
              Manual Warning
            </Button>
            
            <Button
              variant="outline"
              onClick={triggerManualInfo}
              className="flex items-center gap-2 border-blue-200 hover:bg-blue-50"
            >
              <Info className="h-4 w-4 text-blue-500" />
              Manual Info
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Global Errors */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5 text-orange-500" />
            Global Error Testing
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            <Button
              variant="outline"
              onClick={triggerGlobalError}
              className="flex items-center gap-2 border-orange-200 hover:bg-orange-50"
            >
              <Bug className="h-4 w-4 text-orange-500" />
              Global Error
            </Button>
            
            <Button
              variant="outline"
              onClick={triggerPromiseRejection}
              className="flex items-center gap-2 border-red-200 hover:bg-red-50"
            >
              <AlertTriangle className="h-4 w-4 text-red-500" />
              Promise Rejection
            </Button>
            
            <Button
              variant="outline"
              onClick={triggerReactError}
              className="flex items-center gap-2 border-purple-200 hover:bg-purple-50"
            >
              <Zap className="h-4 w-4 text-purple-500" />
              React Error
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* React Error Component */}
      {showErrorComponent && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center gap-2">
                <Bug className="h-5 w-5 text-red-500" />
                React Error Boundary Test
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={resetErrorComponent}
              >
                Reset Component
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ErrorComponent shouldError={true} />
          </CardContent>
        </Card>
      )}

      {/* Configuration Info */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5 text-gray-500" />
            Current Configuration
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
            <div>
              <span className="font-medium">Enabled:</span>
              <Badge className={config.enabled ? "ml-2 bg-green-100 text-green-800" : "ml-2 bg-red-100 text-red-800"}>
                {config.enabled ? "Yes" : "No"}
              </Badge>
            </div>
            <div>
              <span className="font-medium">Admin Only:</span>
              <Badge className={config.adminOnly ? "ml-2 bg-blue-100 text-blue-800" : "ml-2 bg-gray-100 text-gray-800"}>
                {config.adminOnly ? "Yes" : "No"}
              </Badge>
            </div>
            <div>
              <span className="font-medium">Max Errors:</span>
              <Badge className="ml-2 bg-gray-100 text-gray-800">
                {config.maxErrors}
              </Badge>
            </div>
            <div>
              <span className="font-medium">Auto Capture:</span>
              <Badge className={config.autoCapture ? "ml-2 bg-green-100 text-green-800" : "ml-2 bg-red-100 text-red-800"}>
                {config.autoCapture ? "Yes" : "No"}
              </Badge>
            </div>
            <div>
              <span className="font-medium">Production:</span>
              <Badge className={config.showInProduction ? "ml-2 bg-yellow-100 text-yellow-800" : "ml-2 bg-gray-100 text-gray-800"}>
                {config.showInProduction ? "Enabled" : "Disabled"}
              </Badge>
            </div>
            <div>
              <span className="font-medium">Shortcuts:</span>
              <Badge className={config.keyboardShortcuts ? "ml-2 bg-green-100 text-green-800" : "ml-2 bg-red-100 text-red-800"}>
                {config.keyboardShortcuts ? "Yes" : "No"}
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
