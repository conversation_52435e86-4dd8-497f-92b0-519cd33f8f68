import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  Calendar, 
  User, 
  Phone, 
  Mail, 
  MapPin,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Search,
  Filter,
  Eye,
  MessageSquare,
  DollarSign
} from 'lucide-react';
import { format, parseISO, differenceInDays } from 'date-fns';

interface HostBooking {
  id: string;
  propertyId: string;
  propertyTitle: string;
  propertyLocation: string;
  guestId: string;
  guestName: string;
  guestEmail: string;
  guestPhone?: string;
  checkInDate: string;
  checkOutDate: string;
  guestCount: number;
  totalAmount: number;
  bookingStatus: 'pending' | 'confirmed' | 'cancelled' | 'completed';
  paymentStatus: 'pending' | 'paid' | 'refunded';
  specialRequests?: string;
  createdAt: string;
  updatedAt: string;
}

interface HostBookingManagementProps {
  bookings: HostBooking[];
  onAcceptBooking: (bookingId: string) => void;
  onDeclineBooking: (bookingId: string) => void;
  onContactGuest: (booking: HostBooking) => void;
  onViewBookingDetails: (bookingId: string) => void;
}

export const HostBookingManagement: React.FC<HostBookingManagementProps> = ({
  bookings,
  onAcceptBooking,
  onDeclineBooking,
  onContactGuest,
  onViewBookingDetails
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'pending' | 'confirmed' | 'cancelled' | 'completed'>('all');
  const [sortBy, setSortBy] = useState<'checkIn' | 'created' | 'amount'>('checkIn');

  const filteredBookings = bookings
    .filter(booking => {
      const matchesSearch = booking.guestName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           booking.propertyTitle.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           booking.guestEmail.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesStatus = statusFilter === 'all' || booking.bookingStatus === statusFilter;
      return matchesSearch && matchesStatus;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'checkIn':
          return new Date(a.checkInDate).getTime() - new Date(b.checkInDate).getTime();
        case 'created':
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        case 'amount':
          return b.totalAmount - a.totalAmount;
        default:
          return 0;
      }
    });

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'confirmed':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-600" />;
      case 'cancelled':
        return <XCircle className="h-4 w-4 text-red-600" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-blue-600" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'cancelled':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'completed':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'paid':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'refunded':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const calculateNights = (checkIn: string, checkOut: string) => {
    return differenceInDays(parseISO(checkOut), parseISO(checkIn));
  };

  const isUpcoming = (checkIn: string) => {
    return new Date(checkIn) > new Date();
  };

  const handleAcceptBooking = (bookingId: string) => {
    const confirmed = window.confirm('Are you sure you want to accept this booking?');
    if (confirmed) {
      onAcceptBooking(bookingId);
    }
  };

  const handleDeclineBooking = (bookingId: string) => {
    const reason = window.prompt('Please provide a reason for declining this booking (optional):');
    if (reason !== null) { // User didn't cancel the prompt
      onDeclineBooking(bookingId);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Calendar className="h-5 w-5" />
          Booking Management
        </CardTitle>

        {/* Filters and Search */}
        <div className="flex flex-col sm:flex-row gap-4 mt-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search bookings..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value as any)}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm"
          >
            <option value="all">All Status</option>
            <option value="pending">Pending</option>
            <option value="confirmed">Confirmed</option>
            <option value="completed">Completed</option>
            <option value="cancelled">Cancelled</option>
          </select>

          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as any)}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm"
          >
            <option value="checkIn">Sort by Check-in</option>
            <option value="created">Sort by Created</option>
            <option value="amount">Sort by Amount</option>
          </select>
        </div>
      </CardHeader>

      <CardContent>
        {filteredBookings.length === 0 ? (
          <div className="text-center py-12">
            <Calendar className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {searchTerm || statusFilter !== 'all' ? 'No bookings found' : 'No bookings yet'}
            </h3>
            <p className="text-gray-600">
              {searchTerm || statusFilter !== 'all' 
                ? 'Try adjusting your search or filters'
                : 'Bookings will appear here when guests book your properties'
              }
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {filteredBookings.map((booking) => (
              <div key={booking.id} className="border rounded-lg p-6 hover:bg-gray-50 transition-colors">
                <div className="flex flex-col lg:flex-row lg:items-start gap-4">
                  {/* Booking Header */}
                  <div className="flex-1">
                    <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-2 mb-4">
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900">{booking.propertyTitle}</h3>
                        <div className="flex items-center gap-1 text-gray-600 mb-1">
                          <MapPin className="h-4 w-4" />
                          <span className="text-sm">{booking.propertyLocation}</span>
                        </div>
                        <div className="flex items-center gap-1 text-gray-600">
                          <User className="h-4 w-4" />
                          <span className="text-sm font-medium">{booking.guestName}</span>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className={getStatusColor(booking.bookingStatus)}>
                          <span className="flex items-center gap-1">
                            {getStatusIcon(booking.bookingStatus)}
                            {booking.bookingStatus}
                          </span>
                        </Badge>
                        <Badge variant="outline" className={getPaymentStatusColor(booking.paymentStatus)}>
                          {booking.paymentStatus}
                        </Badge>
                      </div>
                    </div>

                    {/* Booking Details */}
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                      <div>
                        <p className="text-xs text-gray-600 mb-1">Check-in</p>
                        <p className="font-medium">{format(parseISO(booking.checkInDate), 'MMM dd, yyyy')}</p>
                        <p className="text-xs text-gray-500">{isUpcoming(booking.checkInDate) ? 'Upcoming' : 'Past'}</p>
                      </div>
                      
                      <div>
                        <p className="text-xs text-gray-600 mb-1">Check-out</p>
                        <p className="font-medium">{format(parseISO(booking.checkOutDate), 'MMM dd, yyyy')}</p>
                        <p className="text-xs text-gray-500">{calculateNights(booking.checkInDate, booking.checkOutDate)} nights</p>
                      </div>
                      
                      <div>
                        <p className="text-xs text-gray-600 mb-1">Guests</p>
                        <p className="font-medium">{booking.guestCount} guest{booking.guestCount !== 1 ? 's' : ''}</p>
                      </div>
                      
                      <div>
                        <p className="text-xs text-gray-600 mb-1">Total Amount</p>
                        <p className="font-medium text-lg">R{booking.totalAmount.toLocaleString()}</p>
                      </div>
                    </div>

                    {/* Guest Contact Info */}
                    <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600 mb-4">
                      <div className="flex items-center gap-1">
                        <Mail className="h-3 w-3" />
                        <span>{booking.guestEmail}</span>
                      </div>
                      {booking.guestPhone && (
                        <div className="flex items-center gap-1">
                          <Phone className="h-3 w-3" />
                          <span>{booking.guestPhone}</span>
                        </div>
                      )}
                    </div>

                    {/* Special Requests */}
                    {booking.specialRequests && (
                      <div className="mb-4">
                        <p className="text-xs text-gray-600 mb-1">Special Requests</p>
                        <p className="text-sm text-gray-800 bg-gray-50 p-2 rounded">{booking.specialRequests}</p>
                      </div>
                    )}

                    {/* Actions */}
                    <div className="flex flex-wrap gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onViewBookingDetails(booking.id)}
                      >
                        <Eye className="h-3 w-3 mr-1" />
                        View Details
                      </Button>
                      
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onContactGuest(booking)}
                      >
                        <MessageSquare className="h-3 w-3 mr-1" />
                        Contact Guest
                      </Button>

                      {booking.bookingStatus === 'pending' && (
                        <>
                          <Button
                            size="sm"
                            onClick={() => handleAcceptBooking(booking.id)}
                            className="bg-green-600 hover:bg-green-700 text-white"
                          >
                            <CheckCircle className="h-3 w-3 mr-1" />
                            Accept
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDeclineBooking(booking.id)}
                            className="border-red-300 text-red-700 hover:bg-red-50"
                          >
                            <XCircle className="h-3 w-3 mr-1" />
                            Decline
                          </Button>
                        </>
                      )}
                    </div>

                    {/* Booking Timeline */}
                    <div className="mt-4 pt-4 border-t text-xs text-gray-500">
                      <span>Booked on {format(parseISO(booking.createdAt), 'MMM dd, yyyy')}</span>
                      {booking.updatedAt !== booking.createdAt && (
                        <span> • Updated {format(parseISO(booking.updatedAt), 'MMM dd, yyyy')}</span>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};
