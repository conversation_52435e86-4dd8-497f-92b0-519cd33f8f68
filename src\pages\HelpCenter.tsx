import React, { useState } from 'react';
import { Search, Book, MessageCircle, Phone, Mail, ChevronRight, Star, Clock, User, Home, CreditCard, Shield, HelpCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';

interface HelpArticle {
  id: string;
  title: string;
  content: string;
  category: string;
  tags: string[];
  views: number;
  helpful: number;
  lastUpdated: Date;
  featured: boolean;
}

interface HelpCategory {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  articleCount: number;
  color: string;
}

export const HelpCenter: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [selectedArticle, setSelectedArticle] = useState<HelpArticle | null>(null);

  const categories: HelpCategory[] = [
    {
      id: 'getting-started',
      name: 'Getting Started',
      description: 'Learn the basics of using StayFinder',
      icon: <Star className="h-6 w-6" />,
      articleCount: 8,
      color: 'bg-blue-100 text-blue-800'
    },
    {
      id: 'booking',
      name: 'Booking & Reservations',
      description: 'How to book and manage your stays',
      icon: <Home className="h-6 w-6" />,
      articleCount: 12,
      color: 'bg-green-100 text-green-800'
    },
    {
      id: 'payments',
      name: 'Payments & Billing',
      description: 'Payment methods, billing, and refunds',
      icon: <CreditCard className="h-6 w-6" />,
      articleCount: 6,
      color: 'bg-purple-100 text-purple-800'
    },
    {
      id: 'hosting',
      name: 'Hosting',
      description: 'Guide for property owners and hosts',
      icon: <User className="h-6 w-6" />,
      articleCount: 15,
      color: 'bg-orange-100 text-orange-800'
    },
    {
      id: 'safety',
      name: 'Safety & Security',
      description: 'Stay safe and secure on our platform',
      icon: <Shield className="h-6 w-6" />,
      articleCount: 7,
      color: 'bg-red-100 text-red-800'
    },
    {
      id: 'account',
      name: 'Account & Profile',
      description: 'Manage your account and personal information',
      icon: <User className="h-6 w-6" />,
      articleCount: 9,
      color: 'bg-yellow-100 text-yellow-800'
    }
  ];

  const articles: HelpArticle[] = [
    {
      id: '1',
      title: 'How to create your first booking',
      content: `# How to create your first booking

Welcome to StayFinder! Booking your first accommodation is easy and straightforward. Follow these simple steps:

## Step 1: Search for Properties
1. Enter your destination in the search bar
2. Select your check-in and check-out dates
3. Choose the number of guests
4. Click "Search" to see available properties

## Step 2: Browse and Filter
- Use filters to narrow down your options (price, amenities, property type)
- View property photos and read descriptions
- Check reviews from previous guests
- Compare different properties using our comparison tool

## Step 3: Select Your Property
- Click on a property that interests you
- Review all details including location, amenities, and house rules
- Check the exact pricing breakdown
- Read the cancellation policy

## Step 4: Make Your Booking
1. Click "Book Now"
2. Enter your personal details
3. Choose your payment method
4. Review your booking summary
5. Confirm your reservation

## Step 5: After Booking
- You'll receive a confirmation email
- The host will be notified of your booking
- You can message the host through our platform
- Check your booking details in your dashboard

## Tips for First-Time Bookers
- Read reviews carefully to understand what to expect
- Contact the host if you have specific questions
- Arrive during the specified check-in hours
- Respect the property and house rules

Need more help? Contact our support team anytime!`,
      category: 'getting-started',
      tags: ['booking', 'first-time', 'tutorial'],
      views: 1250,
      helpful: 89,
      lastUpdated: new Date('2024-12-20'),
      featured: true
    },
    {
      id: '2',
      title: 'Understanding our cancellation policies',
      content: `# Understanding our cancellation policies

StayFinder offers flexible cancellation options to protect both guests and hosts. Here's what you need to know:

## Types of Cancellation Policies

### Flexible
- Full refund if cancelled 24 hours before check-in
- 50% refund if cancelled within 24 hours
- Best for uncertain travel plans

### Moderate
- Full refund if cancelled 5 days before check-in
- 50% refund if cancelled 2-5 days before
- No refund if cancelled within 48 hours

### Strict
- Full refund if cancelled 14 days before check-in
- 50% refund if cancelled 7-14 days before
- No refund if cancelled within 7 days

## How to Cancel Your Booking
1. Go to your dashboard
2. Find your booking under "Upcoming Trips"
3. Click "Cancel Booking"
4. Select your reason for cancellation
5. Confirm the cancellation

## Refund Timeline
- Credit card refunds: 5-10 business days
- Bank transfers: 3-5 business days
- Digital wallets: 1-3 business days

## Special Circumstances
We understand that sometimes unexpected events occur. Contact our support team if you need to cancel due to:
- Medical emergencies
- Natural disasters
- Government travel restrictions
- Other extenuating circumstances

Our team will review your case and may offer additional flexibility.`,
      category: 'booking',
      tags: ['cancellation', 'refund', 'policy'],
      views: 890,
      helpful: 76,
      lastUpdated: new Date('2024-12-18'),
      featured: true
    },
    {
      id: '3',
      title: 'How to become a host on StayFinder',
      content: `# How to become a host on StayFinder

Ready to start earning by hosting guests? Here's your complete guide to becoming a successful StayFinder host:

## Getting Started

### Step 1: Create Your Host Account
1. Click "Become a Host" on our homepage
2. Complete the host verification process
3. Provide necessary identification documents
4. Set up your payment information

### Step 2: List Your Property
1. Use our Property Creation Wizard
2. Add high-quality photos (minimum 5 photos)
3. Write a compelling description
4. Set your pricing and availability
5. Define your house rules

## Property Requirements
- Must be legally allowed to rent your space
- Property should be clean and well-maintained
- Basic amenities should be provided
- Safety features must be in place

## Setting Your Price
- Research similar properties in your area
- Consider seasonal demand fluctuations
- Use our Smart Pricing tool for optimization
- Factor in cleaning and service fees

## Creating Great Listings
### Photography Tips
- Use natural lighting when possible
- Show all rooms and important features
- Include exterior shots and neighborhood views
- Keep photos current and accurate

### Writing Descriptions
- Be honest and detailed about your space
- Highlight unique features and amenities
- Mention nearby attractions and transportation
- Set clear expectations for guests

## Managing Bookings
- Respond to inquiries within 24 hours
- Keep your calendar updated
- Communicate clearly with guests
- Provide check-in instructions

## Host Responsibilities
- Maintain a clean and safe environment
- Provide accurate listing information
- Be responsive to guest needs
- Follow local laws and regulations

## Maximizing Your Success
- Maintain high ratings through excellent service
- Offer competitive pricing
- Keep your listing updated
- Respond quickly to messages

Ready to start hosting? Begin your journey today!`,
      category: 'hosting',
      tags: ['hosting', 'getting-started', 'listing'],
      views: 2100,
      helpful: 156,
      lastUpdated: new Date('2024-12-22'),
      featured: true
    }
  ];

  const featuredArticles = articles.filter(article => article.featured);

  const filteredArticles = articles.filter(article => {
    const matchesSearch = searchQuery === '' || 
      article.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      article.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
      article.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    
    const matchesCategory = !selectedCategory || article.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-ZA', { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    });
  };

  if (selectedArticle) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 py-8">
          <Button 
            variant="ghost" 
            onClick={() => setSelectedArticle(null)}
            className="mb-6"
          >
            ← Back to Help Center
          </Button>
          
          <Card>
            <CardHeader>
              <div className="flex items-center gap-2 mb-2">
                <Badge className={categories.find(c => c.id === selectedArticle.category)?.color}>
                  {categories.find(c => c.id === selectedArticle.category)?.name}
                </Badge>
                <span className="text-sm text-gray-500">
                  Updated {formatDate(selectedArticle.lastUpdated)}
                </span>
              </div>
              <CardTitle className="text-2xl">{selectedArticle.title}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="prose max-w-none">
                <div dangerouslySetInnerHTML={{ 
                  __html: selectedArticle.content.replace(/\n/g, '<br>').replace(/## /g, '<h2>').replace(/### /g, '<h3>') 
                }} />
              </div>
              
              <Separator className="my-8" />
              
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4 text-sm text-gray-600">
                  <span className="flex items-center gap-1">
                    <Clock className="h-4 w-4" />
                    {selectedArticle.views} views
                  </span>
                  <span className="flex items-center gap-1">
                    <Star className="h-4 w-4" />
                    {selectedArticle.helpful} found this helpful
                  </span>
                </div>
                
                <div className="flex gap-2">
                  <Button variant="outline" size="sm">
                    👍 Helpful
                  </Button>
                  <Button variant="outline" size="sm">
                    👎 Not helpful
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="max-w-6xl mx-auto px-4 py-12 text-center">
          <h1 className="text-4xl font-bold mb-4">How can we help you?</h1>
          <p className="text-gray-600 mb-8 max-w-2xl mx-auto">
            Find answers to common questions, learn how to use StayFinder, and get the support you need.
          </p>
          
          <div className="max-w-2xl mx-auto relative">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            <Input
              placeholder="Search for help articles..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-12 py-3 text-lg"
            />
          </div>
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-4 py-8">
        <Tabs defaultValue="browse" className="space-y-8">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="browse">Browse Topics</TabsTrigger>
            <TabsTrigger value="featured">Featured Articles</TabsTrigger>
            <TabsTrigger value="contact">Contact Support</TabsTrigger>
          </TabsList>

          <TabsContent value="browse" className="space-y-8">
            {/* Categories */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {categories.map((category) => (
                <Card 
                  key={category.id}
                  className="cursor-pointer hover:shadow-md transition-shadow"
                  onClick={() => setSelectedCategory(category.id)}
                >
                  <CardContent className="p-6">
                    <div className="flex items-center gap-3 mb-3">
                      <div className={cn("p-2 rounded-lg", category.color)}>
                        {category.icon}
                      </div>
                      <div>
                        <h3 className="font-semibold">{category.name}</h3>
                        <p className="text-sm text-gray-600">{category.articleCount} articles</p>
                      </div>
                    </div>
                    <p className="text-sm text-gray-600">{category.description}</p>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Articles */}
            {(selectedCategory || searchQuery) && (
              <div>
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-2xl font-bold">
                    {selectedCategory 
                      ? categories.find(c => c.id === selectedCategory)?.name 
                      : 'Search Results'
                    }
                  </h2>
                  {selectedCategory && (
                    <Button variant="ghost" onClick={() => setSelectedCategory(null)}>
                      View All Categories
                    </Button>
                  )}
                </div>

                <div className="space-y-4">
                  {filteredArticles.map((article) => (
                    <Card 
                      key={article.id}
                      className="cursor-pointer hover:shadow-md transition-shadow"
                      onClick={() => setSelectedArticle(article)}
                    >
                      <CardContent className="p-6">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <h3 className="font-semibold mb-2">{article.title}</h3>
                            <div className="flex items-center gap-4 text-sm text-gray-600 mb-3">
                              <span className="flex items-center gap-1">
                                <Clock className="h-4 w-4" />
                                {article.views} views
                              </span>
                              <span className="flex items-center gap-1">
                                <Star className="h-4 w-4" />
                                {article.helpful} helpful
                              </span>
                              <span>Updated {formatDate(article.lastUpdated)}</span>
                            </div>
                            <div className="flex flex-wrap gap-1">
                              {article.tags.map((tag) => (
                                <Badge key={tag} variant="secondary" className="text-xs">
                                  {tag}
                                </Badge>
                              ))}
                            </div>
                          </div>
                          <ChevronRight className="h-5 w-5 text-gray-400" />
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="featured" className="space-y-6">
            <h2 className="text-2xl font-bold">Featured Articles</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {featuredArticles.map((article) => (
                <Card 
                  key={article.id}
                  className="cursor-pointer hover:shadow-md transition-shadow"
                  onClick={() => setSelectedArticle(article)}
                >
                  <CardContent className="p-6">
                    <Badge className="mb-3">Featured</Badge>
                    <h3 className="font-semibold mb-2">{article.title}</h3>
                    <p className="text-sm text-gray-600 mb-3">
                      {article.content.substring(0, 150)}...
                    </p>
                    <div className="flex items-center gap-4 text-sm text-gray-600">
                      <span className="flex items-center gap-1">
                        <Clock className="h-4 w-4" />
                        {article.views} views
                      </span>
                      <span className="flex items-center gap-1">
                        <Star className="h-4 w-4" />
                        {article.helpful} helpful
                      </span>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="contact" className="space-y-6">
            <h2 className="text-2xl font-bold">Contact Support</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <Card>
                <CardContent className="p-6 text-center">
                  <MessageCircle className="h-12 w-12 mx-auto mb-4 text-sea-green-600" />
                  <h3 className="font-semibold mb-2">Live Chat</h3>
                  <p className="text-sm text-gray-600 mb-4">
                    Get instant help from our support team
                  </p>
                  <Button className="w-full">Start Chat</Button>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6 text-center">
                  <Mail className="h-12 w-12 mx-auto mb-4 text-sea-green-600" />
                  <h3 className="font-semibold mb-2">Email Support</h3>
                  <p className="text-sm text-gray-600 mb-4">
                    Send us an email and we'll respond within 24 hours
                  </p>
                  <Button variant="outline" className="w-full">
                    Send Email
                  </Button>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6 text-center">
                  <Phone className="h-12 w-12 mx-auto mb-4 text-sea-green-600" />
                  <h3 className="font-semibold mb-2">Phone Support</h3>
                  <p className="text-sm text-gray-600 mb-4">
                    Call us for urgent issues (9 AM - 6 PM)
                  </p>
                  <Button variant="outline" className="w-full">
                    +27 21 123 4567
                  </Button>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Frequently Asked Questions</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <details className="border rounded-lg p-4">
                    <summary className="cursor-pointer font-medium">
                      How do I cancel my booking?
                    </summary>
                    <p className="mt-2 text-sm text-gray-600">
                      You can cancel your booking through your dashboard. Go to "My Trips" and click on the booking you want to cancel.
                    </p>
                  </details>
                  
                  <details className="border rounded-lg p-4">
                    <summary className="cursor-pointer font-medium">
                      When will I be charged for my booking?
                    </summary>
                    <p className="mt-2 text-sm text-gray-600">
                      Payment is processed immediately upon booking confirmation. You'll receive a receipt via email.
                    </p>
                  </details>
                  
                  <details className="border rounded-lg p-4">
                    <summary className="cursor-pointer font-medium">
                      How do I contact my host?
                    </summary>
                    <p className="mt-2 text-sm text-gray-600">
                      You can message your host through our platform. Go to your booking details and click "Message Host".
                    </p>
                  </details>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};
