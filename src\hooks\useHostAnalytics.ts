import { useState, useEffect, useCallback } from 'react';
import { HostAnalyticsService, AnalyticsSummary, HostMetrics, PropertyMetrics } from '@/services/hostAnalyticsService';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';

export const useHostAnalytics = (dateRange?: { start: string; end: string }) => {
  const [summary, setSummary] = useState<AnalyticsSummary | null>(null);
  const [hostMetrics, setHostMetrics] = useState<HostMetrics[]>([]);
  const [propertyMetrics, setPropertyMetrics] = useState<PropertyMetrics[]>([]);
  const [topProperties, setTopProperties] = useState<PropertyMetrics[]>([]);
  const [loading, setLoading] = useState(false);
  const { user } = useAuth();
  const { toast } = useToast();

  // Default to last 30 days if no date range provided
  const getDefaultDateRange = () => {
    const end = new Date();
    const start = new Date();
    start.setDate(start.getDate() - 30);
    
    return {
      start: start.toISOString().split('T')[0],
      end: end.toISOString().split('T')[0],
    };
  };

  const currentRange = dateRange || getDefaultDateRange();

  // Get previous period for comparison
  const getPreviousPeriod = (start: string, end: string) => {
    const startDate = new Date(start);
    const endDate = new Date(end);
    const daysDiff = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
    
    const prevEnd = new Date(startDate);
    prevEnd.setDate(prevEnd.getDate() - 1);
    
    const prevStart = new Date(prevEnd);
    prevStart.setDate(prevStart.getDate() - daysDiff);
    
    return {
      start: prevStart.toISOString().split('T')[0],
      end: prevEnd.toISOString().split('T')[0],
    };
  };

  // Load analytics data
  const loadAnalytics = useCallback(async () => {
    if (!user?.id) return;

    try {
      setLoading(true);
      
      const previousPeriod = getPreviousPeriod(currentRange.start, currentRange.end);

      // Load all analytics data in parallel
      const [
        summaryData,
        hostMetricsData,
        propertyMetricsData,
        topPropertiesData
      ] = await Promise.all([
        HostAnalyticsService.getAnalyticsSummary(
          user.id,
          currentRange.start,
          currentRange.end,
          previousPeriod.start,
          previousPeriod.end
        ),
        HostAnalyticsService.getHostMetrics(user.id, currentRange.start, currentRange.end),
        HostAnalyticsService.getPropertyMetrics(user.id, currentRange.start, currentRange.end),
        HostAnalyticsService.getTopPerformingProperties(user.id, currentRange.start, currentRange.end, 5)
      ]);

      setSummary(summaryData);
      setHostMetrics(hostMetricsData);
      setPropertyMetrics(propertyMetricsData);
      setTopProperties(topPropertiesData);

    } catch (error) {
      console.error('Failed to load analytics:', error);
      toast({
        title: "Error",
        description: "Failed to load analytics data",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, [user?.id, currentRange.start, currentRange.end, toast]);

  // Load data when user or date range changes
  useEffect(() => {
    if (user?.id) {
      loadAnalytics();
    }
  }, [user?.id, loadAnalytics]);

  // Calculate daily metrics for today (useful for real-time updates)
  const updateTodayMetrics = async (): Promise<boolean> => {
    if (!user?.id) return false;

    try {
      const today = new Date().toISOString().split('T')[0];
      await HostAnalyticsService.calculateDailyMetrics(user.id, today);
      
      // Reload analytics after update
      await loadAnalytics();
      
      toast({
        title: "Metrics updated",
        description: "Your analytics have been refreshed",
      });
      
      return true;
    } catch (error) {
      console.error('Failed to update today metrics:', error);
      toast({
        title: "Error",
        description: "Failed to update metrics",
        variant: "destructive",
      });
      return false;
    }
  };

  // Get metrics for a specific property
  const getPropertyMetrics = (propertyId: string): PropertyMetrics[] => {
    return propertyMetrics.filter(pm => pm.property_id === propertyId);
  };

  // Get total metrics across all properties
  const getTotalMetrics = () => {
    return propertyMetrics.reduce(
      (acc, pm) => ({
        totalViews: acc.totalViews + pm.views_count,
        totalBookings: acc.totalBookings + pm.bookings_count,
        totalRevenue: acc.totalRevenue + pm.revenue,
        averageOccupancy: acc.averageOccupancy + pm.occupancy_rate,
        propertyCount: acc.propertyCount + 1,
      }),
      {
        totalViews: 0,
        totalBookings: 0,
        totalRevenue: 0,
        averageOccupancy: 0,
        propertyCount: 0,
      }
    );
  };

  // Get chart data for views over time
  const getViewsChartData = () => {
    return hostMetrics.map(hm => ({
      date: hm.metric_date,
      views: hm.total_views,
      bookings: hm.total_bookings,
      revenue: hm.total_revenue,
    }));
  };

  // Get property performance comparison
  const getPropertyComparison = () => {
    const propertyGroups = propertyMetrics.reduce((acc, pm) => {
      if (!acc[pm.property_id]) {
        acc[pm.property_id] = {
          property: pm.property,
          totalViews: 0,
          totalBookings: 0,
          totalRevenue: 0,
          averageOccupancy: 0,
          dataPoints: 0,
        };
      }
      
      acc[pm.property_id].totalViews += pm.views_count;
      acc[pm.property_id].totalBookings += pm.bookings_count;
      acc[pm.property_id].totalRevenue += pm.revenue;
      acc[pm.property_id].averageOccupancy += pm.occupancy_rate;
      acc[pm.property_id].dataPoints += 1;
      
      return acc;
    }, {} as Record<string, any>);

    return Object.values(propertyGroups).map((group: any) => ({
      ...group,
      averageOccupancy: group.dataPoints > 0 ? group.averageOccupancy / group.dataPoints : 0,
    }));
  };

  return {
    // Data
    summary,
    hostMetrics,
    propertyMetrics,
    topProperties,
    loading,
    
    // Actions
    loadAnalytics,
    updateTodayMetrics,
    
    // Computed data
    getPropertyMetrics,
    getTotalMetrics,
    getViewsChartData,
    getPropertyComparison,
    
    // Date range
    dateRange: currentRange,
  };
};
