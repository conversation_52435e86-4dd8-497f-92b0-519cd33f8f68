# 🏗️ StayFinder Database Schema

**Database:** PostgreSQL (Supabase)  
**Version:** 1.0  
**Created:** July 16, 2025

---

## 📋 Schema Overview

The StayFinder database schema is designed for a comprehensive holiday rental platform serving all of South Africa. It supports multi-user types (guests, hosts, admins), property management, booking systems, reviews, and real-time messaging across all provinces and cities.

### 🎯 Core Entities
- **Users** - Authentication and profile management
- **Properties** - Holiday rental listings
- **Bookings** - Reservation management
- **Reviews** - Property rating system
- **Messages** - Host-guest communication
- **Amenities** - Property features
- **Property Images** - Media management
- **Booking Payments** - Payment tracking
- **Notifications** - System notifications

---

## 📊 Entity Relationship Overview

```
Users (1) ←→ (M) Properties (Host relationship)
Users (1) ←→ (M) Bookings (Guest relationship)
Properties (1) ←→ (M) Bookings
Properties (1) ←→ (M) Reviews
Properties (1) ←→ (M) Property_Images
Properties (M) ←→ (M) Amenities (via Property_Amenities)
Bookings (1) ←→ (M) Booking_Payments
Users (1) ←→ (M) Messages (Sender)
Users (1) ←→ (M) Messages (Recipient)
Users (1) ←→ (M) Notifications
```

---

## 🗂️ Table Definitions

### 1. **users** - User Management
**Purpose:** Stores all user information including guests, hosts, and administrators

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | Unique user identifier |
| auth_user_id | UUID | UNIQUE, REFERENCES auth.users(id) | Supabase auth user ID |
| email | VARCHAR(255) | UNIQUE, NOT NULL | User email address |
| first_name | VARCHAR(100) | NOT NULL | User's first name |
| last_name | VARCHAR(100) | NOT NULL | User's last name |
| phone | VARCHAR(20) | | Phone number |
| date_of_birth | DATE | | Date of birth |
| profile_image_url | TEXT | | Profile picture URL |
| bio | TEXT | | User biography |
| user_type | user_type_enum | DEFAULT 'guest' | User role (guest/host/admin) |
| is_verified | BOOLEAN | DEFAULT FALSE | Email verification status |
| is_active | BOOLEAN | DEFAULT TRUE | Account active status |
| address_line1 | VARCHAR(255) | | Street address |
| address_line2 | VARCHAR(255) | | Additional address info |
| city | VARCHAR(100) | | City |
| province | VARCHAR(100) | | Province/State |
| postal_code | VARCHAR(20) | | Postal/ZIP code |
| country | VARCHAR(100) | DEFAULT 'South Africa' | Country |
| emergency_contact_name | VARCHAR(200) | | Emergency contact |
| emergency_contact_phone | VARCHAR(20) | | Emergency contact phone |
| preferences | JSONB | DEFAULT '{}' | User preferences |
| created_at | TIMESTAMPTZ | DEFAULT NOW() | Record creation time |
| updated_at | TIMESTAMPTZ | DEFAULT NOW() | Last update time |

**Indexes:**
- `idx_users_email` - Email lookup
- `idx_users_auth_user_id` - Auth integration
- `idx_users_user_type` - Role-based queries

**Enums:**
```sql
CREATE TYPE user_type_enum AS ENUM ('guest', 'host', 'admin');
```

---

### 2. **properties** - Property Listings
**Purpose:** Stores holiday rental property information

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | Unique property identifier |
| host_id | UUID | NOT NULL, REFERENCES users(id) ON DELETE CASCADE | Property owner |
| title | VARCHAR(255) | NOT NULL | Property title |
| description | TEXT | NOT NULL | Detailed description |
| property_type | property_type_enum | NOT NULL | Type of property |
| address_line1 | VARCHAR(255) | NOT NULL | Street address |
| address_line2 | VARCHAR(255) | | Additional address |
| city | VARCHAR(100) | NOT NULL | City |
| province | VARCHAR(100) | NOT NULL | Province |
| postal_code | VARCHAR(20) | NOT NULL | Postal code |
| country | VARCHAR(100) | DEFAULT 'South Africa' | Country |
| latitude | DECIMAL(10,8) | | GPS latitude |
| longitude | DECIMAL(11,8) | | GPS longitude |
| max_guests | INTEGER | NOT NULL, CHECK (max_guests > 0) | Maximum occupancy |
| bedrooms | INTEGER | NOT NULL, CHECK (bedrooms >= 0) | Number of bedrooms |
| bathrooms | DECIMAL(3,1) | NOT NULL, CHECK (bathrooms > 0) | Number of bathrooms |
| beds | INTEGER | NOT NULL, CHECK (beds > 0) | Number of beds |
| property_size_sqm | INTEGER | | Property size in square meters |
| price_per_night | DECIMAL(10,2) | NOT NULL, CHECK (price_per_night > 0) | Nightly rate |
| cleaning_fee | DECIMAL(10,2) | DEFAULT 0, CHECK (cleaning_fee >= 0) | Cleaning fee |
| security_deposit | DECIMAL(10,2) | DEFAULT 0, CHECK (security_deposit >= 0) | Security deposit |
| minimum_stay_nights | INTEGER | DEFAULT 1, CHECK (minimum_stay_nights > 0) | Minimum booking nights |
| maximum_stay_nights | INTEGER | CHECK (maximum_stay_nights >= minimum_stay_nights) | Maximum booking nights |
| check_in_time | TIME | DEFAULT '15:00' | Standard check-in time |
| check_out_time | TIME | DEFAULT '11:00' | Standard check-out time |
| house_rules | TEXT[] | DEFAULT '{}' | Array of house rules |
| cancellation_policy | cancellation_policy_enum | DEFAULT 'moderate' | Cancellation terms |
| instant_book | BOOLEAN | DEFAULT FALSE | Allow instant booking |
| status | property_status_enum | DEFAULT 'draft' | Property status |
| featured | BOOLEAN | DEFAULT FALSE | Featured property flag |
| average_rating | DECIMAL(3,2) | DEFAULT 0, CHECK (average_rating >= 0 AND average_rating <= 5) | Average review rating |
| total_reviews | INTEGER | DEFAULT 0, CHECK (total_reviews >= 0) | Total review count |
| views_count | INTEGER | DEFAULT 0 | Property view counter |
| booking_count | INTEGER | DEFAULT 0 | Total bookings |
| last_booked_at | TIMESTAMPTZ | | Last booking date |
| search_vector | TSVECTOR | | Full-text search vector |
| created_at | TIMESTAMPTZ | DEFAULT NOW() | Record creation time |
| updated_at | TIMESTAMPTZ | DEFAULT NOW() | Last update time |

**Indexes:**
- `idx_properties_host_id` - Host properties lookup
- `idx_properties_location` - Location-based search
- `idx_properties_price` - Price range queries
- `idx_properties_status` - Active properties
- `idx_properties_search` - Full-text search (GIN)
- `idx_properties_coordinates` - Geospatial queries (GIST)
- `idx_properties_featured` - Featured properties

**Enums:**
```sql
CREATE TYPE property_type_enum AS ENUM ('apartment', 'house', 'villa', 'cottage', 'guesthouse', 'townhouse', 'studio', 'loft');
CREATE TYPE property_status_enum AS ENUM ('draft', 'active', 'inactive', 'suspended', 'archived');
CREATE TYPE cancellation_policy_enum AS ENUM ('flexible', 'moderate', 'strict', 'super_strict');
```

---

### 3. **amenities** - Property Features
**Purpose:** Master list of available property amenities

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | Unique amenity identifier |
| name | VARCHAR(100) | UNIQUE, NOT NULL | Amenity name |
| description | TEXT | | Amenity description |
| category | amenity_category_enum | NOT NULL | Amenity category |
| icon | VARCHAR(50) | | Icon identifier |
| is_active | BOOLEAN | DEFAULT TRUE | Amenity availability |
| sort_order | INTEGER | DEFAULT 0 | Display order |
| created_at | TIMESTAMPTZ | DEFAULT NOW() | Record creation time |

**Enums:**
```sql
CREATE TYPE amenity_category_enum AS ENUM ('basic', 'kitchen', 'bathroom', 'entertainment', 'outdoor', 'safety', 'accessibility');
```

---

### 4. **property_amenities** - Property-Amenity Junction
**Purpose:** Links properties to their available amenities

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| property_id | UUID | REFERENCES properties(id) ON DELETE CASCADE | Property reference |
| amenity_id | UUID | REFERENCES amenities(id) ON DELETE CASCADE | Amenity reference |
| created_at | TIMESTAMPTZ | DEFAULT NOW() | Record creation time |

**Primary Key:** `(property_id, amenity_id)`

---

### 5. **property_images** - Property Media
**Purpose:** Stores property photos and media files

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | Unique image identifier |
| property_id | UUID | NOT NULL, REFERENCES properties(id) ON DELETE CASCADE | Property reference |
| image_url | TEXT | NOT NULL | Image storage URL |
| alt_text | VARCHAR(255) | | Image alt text |
| caption | TEXT | | Image caption |
| sort_order | INTEGER | DEFAULT 0 | Display order |
| is_primary | BOOLEAN | DEFAULT FALSE | Primary image flag |
| file_size | INTEGER | | File size in bytes |
| width | INTEGER | | Image width |
| height | INTEGER | | Image height |
| format | VARCHAR(10) | | Image format (jpg, png, webp) |
| created_at | TIMESTAMPTZ | DEFAULT NOW() | Record creation time |

**Indexes:**
- `idx_property_images_property_id` - Property images lookup
- `idx_property_images_primary` - Primary image queries

---

### 6. **bookings** - Reservation Management
**Purpose:** Manages property bookings and reservations

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | Unique booking identifier |
| property_id | UUID | NOT NULL, REFERENCES properties(id) ON DELETE RESTRICT | Property reference |
| guest_id | UUID | NOT NULL, REFERENCES users(id) ON DELETE RESTRICT | Guest reference |
| booking_reference | VARCHAR(20) | UNIQUE, NOT NULL | Human-readable booking ref |
| check_in_date | DATE | NOT NULL | Check-in date |
| check_out_date | DATE | NOT NULL, CHECK (check_out_date > check_in_date) | Check-out date |
| nights | INTEGER | NOT NULL, CHECK (nights > 0) | Number of nights |
| guest_count | INTEGER | NOT NULL, CHECK (guest_count > 0) | Number of guests |
| adults | INTEGER | NOT NULL, CHECK (adults > 0) | Number of adults |
| children | INTEGER | DEFAULT 0, CHECK (children >= 0) | Number of children |
| infants | INTEGER | DEFAULT 0, CHECK (infants >= 0) | Number of infants |
| subtotal | DECIMAL(10,2) | NOT NULL, CHECK (subtotal > 0) | Accommodation subtotal |
| cleaning_fee | DECIMAL(10,2) | DEFAULT 0, CHECK (cleaning_fee >= 0) | Cleaning fee |
| service_fee | DECIMAL(10,2) | DEFAULT 0, CHECK (service_fee >= 0) | Platform service fee |
| taxes | DECIMAL(10,2) | DEFAULT 0, CHECK (taxes >= 0) | Applicable taxes |
| total_amount | DECIMAL(10,2) | NOT NULL, CHECK (total_amount > 0) | Total booking amount |
| currency | VARCHAR(3) | DEFAULT 'ZAR' | Currency code |
| status | booking_status_enum | DEFAULT 'pending' | Booking status |
| payment_status | payment_status_enum | DEFAULT 'pending' | Payment status |
| special_requests | TEXT | | Guest special requests |
| host_notes | TEXT | | Host private notes |
| check_in_instructions | TEXT | | Check-in instructions |
| cancellation_reason | TEXT | | Cancellation reason |
| cancelled_at | TIMESTAMPTZ | | Cancellation timestamp |
| cancelled_by | UUID | REFERENCES users(id) | Who cancelled |
| confirmed_at | TIMESTAMPTZ | | Confirmation timestamp |
| checked_in_at | TIMESTAMPTZ | | Actual check-in time |
| checked_out_at | TIMESTAMPTZ | | Actual check-out time |
| created_at | TIMESTAMPTZ | DEFAULT NOW() | Record creation time |
| updated_at | TIMESTAMPTZ | DEFAULT NOW() | Last update time |

**Indexes:**
- `idx_bookings_property_id` - Property bookings
- `idx_bookings_guest_id` - Guest bookings
- `idx_bookings_dates` - Date range queries
- `idx_bookings_status` - Status filtering
- `idx_bookings_reference` - Reference lookup

**Enums:**
```sql
CREATE TYPE booking_status_enum AS ENUM ('pending', 'confirmed', 'checked_in', 'checked_out', 'cancelled', 'no_show', 'completed');
CREATE TYPE payment_status_enum AS ENUM ('pending', 'paid', 'partially_paid', 'failed', 'refunded', 'partially_refunded');
```

---

### 7. **booking_payments** - Payment Tracking
**Purpose:** Tracks payment transactions for bookings

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | Unique payment identifier |
| booking_id | UUID | NOT NULL, REFERENCES bookings(id) ON DELETE CASCADE | Booking reference |
| payment_intent_id | VARCHAR(255) | | Stripe payment intent ID |
| amount | DECIMAL(10,2) | NOT NULL, CHECK (amount > 0) | Payment amount |
| currency | VARCHAR(3) | DEFAULT 'ZAR' | Currency code |
| payment_method | payment_method_enum | NOT NULL | Payment method used |
| status | payment_transaction_status_enum | DEFAULT 'pending' | Transaction status |
| transaction_id | VARCHAR(255) | | External transaction ID |
| gateway_response | JSONB | | Payment gateway response |
| failure_reason | TEXT | | Failure reason if applicable |
| processed_at | TIMESTAMPTZ | | Processing timestamp |
| created_at | TIMESTAMPTZ | DEFAULT NOW() | Record creation time |

**Enums:**
```sql
CREATE TYPE payment_method_enum AS ENUM ('credit_card', 'debit_card', 'bank_transfer', 'paypal', 'apple_pay', 'google_pay');
CREATE TYPE payment_transaction_status_enum AS ENUM ('pending', 'processing', 'succeeded', 'failed', 'cancelled', 'refunded');
```

---

### 8. **reviews** - Property Reviews & Ratings
**Purpose:** Manages guest reviews and ratings for properties

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | Unique review identifier |
| property_id | UUID | NOT NULL, REFERENCES properties(id) ON DELETE CASCADE | Property reference |
| booking_id | UUID | UNIQUE, NOT NULL, REFERENCES bookings(id) ON DELETE CASCADE | Booking reference |
| guest_id | UUID | NOT NULL, REFERENCES users(id) ON DELETE CASCADE | Reviewer reference |
| overall_rating | INTEGER | NOT NULL, CHECK (overall_rating >= 1 AND overall_rating <= 5) | Overall rating (1-5) |
| cleanliness_rating | INTEGER | CHECK (cleanliness_rating >= 1 AND cleanliness_rating <= 5) | Cleanliness rating |
| accuracy_rating | INTEGER | CHECK (accuracy_rating >= 1 AND accuracy_rating <= 5) | Accuracy rating |
| check_in_rating | INTEGER | CHECK (check_in_rating >= 1 AND check_in_rating <= 5) | Check-in rating |
| communication_rating | INTEGER | CHECK (communication_rating >= 1 AND communication_rating <= 5) | Communication rating |
| location_rating | INTEGER | CHECK (location_rating >= 1 AND location_rating <= 5) | Location rating |
| value_rating | INTEGER | CHECK (value_rating >= 1 AND value_rating <= 5) | Value for money rating |
| title | VARCHAR(255) | | Review title |
| comment | TEXT | | Review comment |
| pros | TEXT[] | DEFAULT '{}' | Positive aspects |
| cons | TEXT[] | DEFAULT '{}' | Negative aspects |
| would_recommend | BOOLEAN | | Would recommend property |
| status | review_status_enum | DEFAULT 'pending' | Review status |
| host_response | TEXT | | Host response to review |
| host_responded_at | TIMESTAMPTZ | | Host response timestamp |
| is_featured | BOOLEAN | DEFAULT FALSE | Featured review flag |
| helpful_count | INTEGER | DEFAULT 0 | Helpful votes count |
| created_at | TIMESTAMPTZ | DEFAULT NOW() | Record creation time |
| updated_at | TIMESTAMPTZ | DEFAULT NOW() | Last update time |

**Indexes:**
- `idx_reviews_property_id` - Property reviews
- `idx_reviews_guest_id` - Guest reviews
- `idx_reviews_booking_id` - Booking review lookup
- `idx_reviews_rating` - Rating-based queries
- `idx_reviews_status` - Status filtering

**Enums:**
```sql
CREATE TYPE review_status_enum AS ENUM ('pending', 'published', 'hidden', 'flagged');
```

---

### 9. **messages** - Communication System
**Purpose:** Handles communication between hosts and guests

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | Unique message identifier |
| conversation_id | UUID | NOT NULL | Conversation thread ID |
| sender_id | UUID | NOT NULL, REFERENCES users(id) ON DELETE CASCADE | Message sender |
| recipient_id | UUID | NOT NULL, REFERENCES users(id) ON DELETE CASCADE | Message recipient |
| property_id | UUID | REFERENCES properties(id) ON DELETE SET NULL | Related property |
| booking_id | UUID | REFERENCES bookings(id) ON DELETE SET NULL | Related booking |
| message_type | message_type_enum | DEFAULT 'text' | Message type |
| subject | VARCHAR(255) | | Message subject |
| content | TEXT | NOT NULL | Message content |
| attachments | JSONB | DEFAULT '[]' | File attachments |
| is_read | BOOLEAN | DEFAULT FALSE | Read status |
| read_at | TIMESTAMPTZ | | Read timestamp |
| is_system_message | BOOLEAN | DEFAULT FALSE | System generated message |
| priority | message_priority_enum | DEFAULT 'normal' | Message priority |
| parent_message_id | UUID | REFERENCES messages(id) | Reply to message |
| created_at | TIMESTAMPTZ | DEFAULT NOW() | Record creation time |
| updated_at | TIMESTAMPTZ | DEFAULT NOW() | Last update time |

**Indexes:**
- `idx_messages_conversation_id` - Conversation messages
- `idx_messages_sender_id` - Sender messages
- `idx_messages_recipient_id` - Recipient messages
- `idx_messages_property_id` - Property-related messages
- `idx_messages_booking_id` - Booking-related messages
- `idx_messages_read_status` - Unread messages

**Enums:**
```sql
CREATE TYPE message_type_enum AS ENUM ('text', 'image', 'document', 'system', 'booking_request', 'booking_confirmation');
CREATE TYPE message_priority_enum AS ENUM ('low', 'normal', 'high', 'urgent');
```

---

### 10. **notifications** - System Notifications
**Purpose:** Manages system notifications for users

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | Unique notification identifier |
| user_id | UUID | NOT NULL, REFERENCES users(id) ON DELETE CASCADE | Notification recipient |
| type | notification_type_enum | NOT NULL | Notification type |
| title | VARCHAR(255) | NOT NULL | Notification title |
| message | TEXT | NOT NULL | Notification message |
| data | JSONB | DEFAULT '{}' | Additional notification data |
| related_entity_type | VARCHAR(50) | | Related entity type |
| related_entity_id | UUID | | Related entity ID |
| is_read | BOOLEAN | DEFAULT FALSE | Read status |
| read_at | TIMESTAMPTZ | | Read timestamp |
| action_url | TEXT | | Action URL |
| expires_at | TIMESTAMPTZ | | Expiration timestamp |
| created_at | TIMESTAMPTZ | DEFAULT NOW() | Record creation time |

**Indexes:**
- `idx_notifications_user_id` - User notifications
- `idx_notifications_type` - Type-based queries
- `idx_notifications_read_status` - Unread notifications
- `idx_notifications_created_at` - Recent notifications

**Enums:**
```sql
CREATE TYPE notification_type_enum AS ENUM ('booking_request', 'booking_confirmed', 'booking_cancelled', 'payment_received', 'review_received', 'message_received', 'property_approved', 'system_announcement');
```

---

### 11. **user_preferences** - User Settings
**Purpose:** Stores user preferences and settings

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| user_id | UUID | PRIMARY KEY, REFERENCES users(id) ON DELETE CASCADE | User reference |
| email_notifications | BOOLEAN | DEFAULT TRUE | Email notifications enabled |
| sms_notifications | BOOLEAN | DEFAULT FALSE | SMS notifications enabled |
| push_notifications | BOOLEAN | DEFAULT TRUE | Push notifications enabled |
| marketing_emails | BOOLEAN | DEFAULT FALSE | Marketing emails enabled |
| language | VARCHAR(10) | DEFAULT 'en' | Preferred language |
| currency | VARCHAR(3) | DEFAULT 'ZAR' | Preferred currency |
| timezone | VARCHAR(50) | DEFAULT 'Africa/Johannesburg' | User timezone (supports all SA timezones) |
| notification_settings | JSONB | DEFAULT '{}' | Detailed notification preferences |
| privacy_settings | JSONB | DEFAULT '{}' | Privacy preferences |
| created_at | TIMESTAMPTZ | DEFAULT NOW() | Record creation time |
| updated_at | TIMESTAMPTZ | DEFAULT NOW() | Last update time |

---

### 12. **property_availability** - Availability Calendar
**Purpose:** Manages property availability and blocked dates

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | Unique availability identifier |
| property_id | UUID | NOT NULL, REFERENCES properties(id) ON DELETE CASCADE | Property reference |
| date | DATE | NOT NULL | Specific date |
| is_available | BOOLEAN | DEFAULT TRUE | Availability status |
| price_override | DECIMAL(10,2) | | Custom price for date |
| minimum_stay_override | INTEGER | | Custom minimum stay |
| reason | availability_reason_enum | | Reason for unavailability |
| notes | TEXT | | Additional notes |
| created_at | TIMESTAMPTZ | DEFAULT NOW() | Record creation time |

**Primary Key:** `(property_id, date)`

**Indexes:**
- `idx_property_availability_property_date` - Property availability lookup
- `idx_property_availability_date_range` - Date range queries

**Enums:**
```sql
CREATE TYPE availability_reason_enum AS ENUM ('booked', 'blocked', 'maintenance', 'personal_use', 'seasonal_closure');
```

---

## 🔧 Database Functions & Triggers

### Automatic Timestamp Updates
```sql
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';
```

### Search Vector Updates
```sql
CREATE OR REPLACE FUNCTION update_property_search_vector()
RETURNS TRIGGER AS $$
BEGIN
    NEW.search_vector := to_tsvector('english',
        COALESCE(NEW.title, '') || ' ' ||
        COALESCE(NEW.description, '') || ' ' ||
        COALESCE(NEW.city, '') || ' ' ||
        COALESCE(NEW.province, '')
    );
    RETURN NEW;
END;
$$ language 'plpgsql';
```

### Booking Reference Generation
```sql
CREATE OR REPLACE FUNCTION generate_booking_reference()
RETURNS TRIGGER AS $$
BEGIN
    NEW.booking_reference := 'SF' || TO_CHAR(NOW(), 'YYYYMMDD') || LPAD(NEXTVAL('booking_reference_seq')::TEXT, 6, '0');
    RETURN NEW;
END;
$$ language 'plpgsql';
```

---

## 📈 Performance Considerations

### Partitioning Strategy
- **bookings** table: Partition by check_in_date (monthly)
- **messages** table: Partition by created_at (monthly)
- **notifications** table: Partition by created_at (monthly)

### Indexing Strategy
- **Composite indexes** for common query patterns
- **Partial indexes** for filtered queries
- **GIN indexes** for JSONB and array columns
- **GIST indexes** for geospatial queries

### Caching Strategy
- **Property listings** - Cache for 15 minutes
- **User sessions** - Cache for 1 hour
- **Search results** - Cache for 5 minutes
- **Static data** (amenities) - Cache for 24 hours
