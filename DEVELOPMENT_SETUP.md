# Development Setup Guide
## KZN South Coast StayFinder - Local Development with <PERSON>AM<PERSON>

### 🚀 Quick Start

Since you have XAMPP running, we can set up a full-stack development environment locally.

### 📋 Prerequisites Checklist

✅ **XAMPP Control Panel** - Running (Apache: Port 80, MySQL: Port 3306)  
✅ **Node.js** - Required for React frontend  
✅ **Git** - For version control  
⚠️ **Backend API** - Needs to be created  

### 🗄️ Database Setup

#### 1. Create Database via phpMyAdmin

1. Open **phpMyAdmin**: http://localhost/phpmyadmin
2. Create new database: `stayfinder_dev`
3. Set collation: `utf8mb4_unicode_ci`

#### 2. Database Schema Creation

```sql
-- Users table
CREATE TABLE users (
    id VARCHAR(36) PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHA<PERSON>(100) NOT NULL,
    last_name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
    phone VARCHAR(20),
    role ENUM('guest', 'host', 'admin') DEFAULT 'guest',
    email_verified BOOLEAN DEFAULT FALSE,
    created_at TIMES<PERSON>MP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Properties table
CREATE TABLE properties (
    id VARCHAR(36) PRIMARY KEY,
    owner_id VARCHAR(36) NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    property_type ENUM('apartment', 'house', 'villa', 'guesthouse', 'cottage') NOT NULL,
    location VARCHAR(255) NOT NULL,
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    max_guests INT NOT NULL,
    bedrooms INT NOT NULL,
    bathrooms INT NOT NULL,
    price_per_night DECIMAL(10, 2) NOT NULL,
    cleaning_fee DECIMAL(10, 2) DEFAULT 0,
    amenities JSON,
    house_rules TEXT,
    check_in_time TIME DEFAULT '15:00:00',
    check_out_time TIME DEFAULT '11:00:00',
    status ENUM('active', 'inactive', 'pending') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Property images table
CREATE TABLE property_images (
    id VARCHAR(36) PRIMARY KEY,
    property_id VARCHAR(36) NOT NULL,
    image_url VARCHAR(500) NOT NULL,
    alt_text VARCHAR(255),
    is_primary BOOLEAN DEFAULT FALSE,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (property_id) REFERENCES properties(id) ON DELETE CASCADE
);

-- Bookings table
CREATE TABLE bookings (
    id VARCHAR(36) PRIMARY KEY,
    property_id VARCHAR(36) NOT NULL,
    guest_id VARCHAR(36) NOT NULL,
    check_in_date DATE NOT NULL,
    check_out_date DATE NOT NULL,
    total_amount DECIMAL(10, 2) NOT NULL,
    booking_status ENUM('pending', 'confirmed', 'cancelled', 'completed') DEFAULT 'pending',
    payment_status ENUM('pending', 'paid', 'refunded') DEFAULT 'pending',
    guest_count INT NOT NULL,
    special_requests TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (property_id) REFERENCES properties(id) ON DELETE CASCADE,
    FOREIGN KEY (guest_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Reviews table
CREATE TABLE reviews (
    id VARCHAR(36) PRIMARY KEY,
    booking_id VARCHAR(36) NOT NULL,
    reviewer_id VARCHAR(36) NOT NULL,
    property_id VARCHAR(36) NOT NULL,
    rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
    comment TEXT,
    response TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (booking_id) REFERENCES bookings(id) ON DELETE CASCADE,
    FOREIGN KEY (reviewer_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (property_id) REFERENCES properties(id) ON DELETE CASCADE
);

-- Indexes for performance
CREATE INDEX idx_properties_location ON properties(location);
CREATE INDEX idx_properties_price ON properties(price_per_night);
CREATE INDEX idx_properties_status ON properties(status);
CREATE INDEX idx_bookings_dates ON bookings(check_in_date, check_out_date);
CREATE INDEX idx_bookings_status ON bookings(booking_status);
CREATE INDEX idx_reviews_property ON reviews(property_id);
CREATE INDEX idx_reviews_rating ON reviews(rating);
```

### 🔧 Backend API Setup

#### 1. Create Backend Directory Structure

```bash
# In your project root, create backend folder
mkdir backend
cd backend

# Initialize Node.js project
npm init -y

# Install dependencies
npm install express cors helmet morgan dotenv bcryptjs jsonwebtoken
npm install mysql2 uuid multer express-validator express-rate-limit
npm install --save-dev nodemon @types/node typescript ts-node
npm install --save-dev @types/express @types/cors @types/bcryptjs @types/jsonwebtoken
```

#### 2. Backend Project Structure

```
backend/
├── src/
│   ├── controllers/
│   │   ├── authController.js
│   │   ├── propertyController.js
│   │   ├── bookingController.js
│   │   └── reviewController.js
│   ├── middleware/
│   │   ├── auth.js
│   │   ├── validation.js
│   │   └── upload.js
│   ├── models/
│   │   ├── User.js
│   │   ├── Property.js
│   │   ├── Booking.js
│   │   └── Review.js
│   ├── routes/
│   │   ├── auth.js
│   │   ├── properties.js
│   │   ├── bookings.js
│   │   └── reviews.js
│   ├── utils/
│   │   ├── database.js
│   │   ├── helpers.js
│   │   └── constants.js
│   └── app.js
├── uploads/
│   └── properties/
├── .env
├── .gitignore
├── package.json
└── server.js
```

### 🌐 Environment Configuration

#### Backend .env file

```env
# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_NAME=stayfinder_dev
DB_USER=root
DB_PASSWORD=

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=24h
JWT_REFRESH_SECRET=your-refresh-token-secret
JWT_REFRESH_EXPIRES_IN=7d

# Server Configuration
PORT=3001
NODE_ENV=development
CORS_ORIGIN=http://localhost:5173

# File Upload Configuration
MAX_FILE_SIZE=5242880
UPLOAD_PATH=./uploads

# Email Configuration (for future use)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
```

### 🔄 Development Workflow

#### 1. Start Development Servers

**Terminal 1 - Frontend (React)**
```bash
# In project root
npm run dev
# Runs on http://localhost:5173
```

**Terminal 2 - Backend (Node.js)**
```bash
# In backend folder
npm run dev
# Runs on http://localhost:3001
```

**Terminal 3 - Database**
```bash
# XAMPP MySQL already running on localhost:3306
# Access phpMyAdmin: http://localhost/phpmyadmin
```

#### 2. API Testing

Use tools like:
- **Postman** - For API endpoint testing
- **Thunder Client** (VS Code extension) - Lightweight API testing
- **curl** - Command line testing

### 📡 Frontend-Backend Integration

#### Update Vite Config for API Proxy

```typescript
// vite.config.ts
export default defineConfig({
  server: {
    host: "::",
    port: 5173,
    proxy: {
      '/api': {
        target: 'http://localhost:3001',
        changeOrigin: true,
        secure: false,
      }
    }
  },
  // ... rest of config
});
```

#### API Service Layer

```typescript
// src/services/api.ts
const API_BASE_URL = '/api';

export const apiClient = {
  // Auth endpoints
  login: (credentials: LoginCredentials) => 
    fetch(`${API_BASE_URL}/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(credentials)
    }),
  
  // Property endpoints
  getProperties: (filters?: PropertyFilters) => 
    fetch(`${API_BASE_URL}/properties${filters ? '?' + new URLSearchParams(filters) : ''}`),
  
  // Booking endpoints
  createBooking: (bookingData: BookingData) =>
    fetch(`${API_BASE_URL}/bookings`, {
      method: 'POST',
      headers: { 
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      },
      body: JSON.stringify(bookingData)
    })
};
```

### 🧪 Sample Data for Testing

#### Insert Test Data

```sql
-- Test users
INSERT INTO users (id, email, password_hash, first_name, last_name, role) VALUES
('user-1', '<EMAIL>', '$2b$12$hash', 'John', 'Doe', 'guest'),
('user-2', '<EMAIL>', '$2b$12$hash', 'Jane', 'Smith', 'host'),
('user-3', '<EMAIL>', '$2b$12$hash', 'Admin', 'User', 'admin');

-- Test properties
INSERT INTO properties (id, owner_id, title, description, property_type, location, max_guests, bedrooms, bathrooms, price_per_night, status) VALUES
('prop-1', 'user-2', 'Beachfront Villa Margate', 'Stunning oceanfront villa with private beach access', 'villa', 'Margate', 8, 4, 3, 2500.00, 'active'),
('prop-2', 'user-2', 'Cozy Cottage Scottburgh', 'Charming cottage near the beach', 'cottage', 'Scottburgh', 4, 2, 1, 800.00, 'active'),
('prop-3', 'user-2', 'Luxury Apartment Hibberdene', 'Modern apartment with sea views', 'apartment', 'Hibberdene', 6, 3, 2, 1200.00, 'active');
```

### 🔍 Next Steps

1. **Create Backend API** - Follow the structure outlined above
2. **Implement Authentication** - JWT-based auth system
3. **Connect Frontend** - Update React components to use real API
4. **Test Integration** - Ensure frontend and backend communicate properly
5. **Add File Upload** - Property image upload functionality
6. **Implement Search** - Real search with database queries

### 🛠️ Useful Development Commands

```bash
# Frontend
npm run dev          # Start development server
npm run build        # Build for production
npm run lint         # Run ESLint

# Backend (when created)
npm run dev          # Start with nodemon
npm run start        # Start production server
npm run test         # Run tests

# Database
mysql -u root -p     # Connect to MySQL CLI
```

This setup gives you a solid foundation for full-stack development with your existing XAMPP environment!
