import React from 'react';
import { <PERSON>, Moon, Monitor } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useTheme } from '@/contexts/ThemeContext';
import { cn } from '@/lib/utils';

interface ThemeToggleProps {
  variant?: 'button' | 'dropdown';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  showLabel?: boolean;
}

export const ThemeToggle: React.FC<ThemeToggleProps> = ({
  variant = 'button',
  size = 'md',
  className,
  showLabel = false
}) => {
  const { theme, actualTheme, setTheme, toggleTheme } = useTheme();

  const getIcon = (themeValue: string, isActive = false) => {
    const iconSize = size === 'sm' ? 'h-4 w-4' : size === 'lg' ? 'h-6 w-6' : 'h-5 w-5';
    const iconClass = cn(iconSize, isActive && 'text-sea-green-500');

    switch (themeValue) {
      case 'light':
        return <Sun className={iconClass} />;
      case 'dark':
        return <Moon className={iconClass} />;
      case 'system':
        return <Monitor className={iconClass} />;
      default:
        return actualTheme === 'dark' ? <Moon className={iconClass} /> : <Sun className={iconClass} />;
    }
  };

  const getLabel = (themeValue: string) => {
    switch (themeValue) {
      case 'light':
        return 'Light';
      case 'dark':
        return 'Dark';
      case 'system':
        return 'System';
      default:
        return 'Theme';
    }
  };

  if (variant === 'dropdown') {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size={size}
            className={cn(
              "relative",
              className
            )}
            aria-label="Toggle theme"
          >
            {getIcon(theme)}
            {showLabel && (
              <span className="ml-2 hidden sm:inline">
                {getLabel(theme)}
              </span>
            )}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="min-w-[120px]">
          <DropdownMenuItem
            onClick={() => setTheme('light')}
            className={cn(
              "flex items-center gap-2 cursor-pointer",
              theme === 'light' && "bg-sea-green-50 text-sea-green-700 dark:bg-sea-green-900 dark:text-sea-green-300"
            )}
          >
            {getIcon('light', theme === 'light')}
            <span>Light</span>
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={() => setTheme('dark')}
            className={cn(
              "flex items-center gap-2 cursor-pointer",
              theme === 'dark' && "bg-sea-green-50 text-sea-green-700 dark:bg-sea-green-900 dark:text-sea-green-300"
            )}
          >
            {getIcon('dark', theme === 'dark')}
            <span>Dark</span>
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={() => setTheme('system')}
            className={cn(
              "flex items-center gap-2 cursor-pointer",
              theme === 'system' && "bg-sea-green-50 text-sea-green-700 dark:bg-sea-green-900 dark:text-sea-green-300"
            )}
          >
            {getIcon('system', theme === 'system')}
            <span>System</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    );
  }

  return (
    <Button
      variant="ghost"
      size={size}
      onClick={toggleTheme}
      className={cn(
        "relative transition-all duration-200",
        className
      )}
      aria-label={`Switch to ${actualTheme === 'light' ? 'dark' : 'light'} mode`}
    >
      <div className="relative">
        {/* Light mode icon */}
        <Sun
          className={cn(
            size === 'sm' ? 'h-4 w-4' : size === 'lg' ? 'h-6 w-6' : 'h-5 w-5',
            "transition-all duration-300",
            actualTheme === 'dark' 
              ? "rotate-90 scale-0 opacity-0" 
              : "rotate-0 scale-100 opacity-100"
          )}
        />
        
        {/* Dark mode icon */}
        <Moon
          className={cn(
            size === 'sm' ? 'h-4 w-4' : size === 'lg' ? 'h-6 w-6' : 'h-5 w-5',
            "absolute inset-0 transition-all duration-300",
            actualTheme === 'dark' 
              ? "rotate-0 scale-100 opacity-100" 
              : "rotate-90 scale-0 opacity-0"
          )}
        />
      </div>
      
      {showLabel && (
        <span className="ml-2 hidden sm:inline">
          {actualTheme === 'light' ? 'Dark' : 'Light'} Mode
        </span>
      )}
    </Button>
  );
};

// Compact theme toggle for mobile
export const CompactThemeToggle: React.FC<{ className?: string }> = ({ className }) => {
  const { actualTheme, toggleTheme } = useTheme();

  return (
    <button
      onClick={toggleTheme}
      className={cn(
        "p-2 rounded-lg transition-colors duration-200",
        "bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700",
        "text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100",
        className
      )}
      aria-label={`Switch to ${actualTheme === 'light' ? 'dark' : 'light'} mode`}
    >
      {actualTheme === 'dark' ? (
        <Sun className="h-5 w-5" />
      ) : (
        <Moon className="h-5 w-5" />
      )}
    </button>
  );
};

// Theme indicator for showing current theme
export const ThemeIndicator: React.FC<{ className?: string }> = ({ className }) => {
  const { theme, actualTheme } = useTheme();

  return (
    <div className={cn("flex items-center gap-2 text-sm", className)}>
      {getIcon(theme)}
      <span className="text-gray-600 dark:text-gray-400">
        {theme === 'system' ? `System (${actualTheme})` : theme}
      </span>
    </div>
  );
};

// Animated theme toggle with smooth transitions
export const AnimatedThemeToggle: React.FC<{ className?: string }> = ({ className }) => {
  const { actualTheme, toggleTheme } = useTheme();

  return (
    <button
      onClick={toggleTheme}
      className={cn(
        "relative w-14 h-8 rounded-full transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-sea-green-500 focus:ring-offset-2",
        actualTheme === 'dark' 
          ? "bg-gray-700" 
          : "bg-yellow-200",
        className
      )}
      aria-label={`Switch to ${actualTheme === 'light' ? 'dark' : 'light'} mode`}
    >
      {/* Toggle circle */}
      <div
        className={cn(
          "absolute top-1 w-6 h-6 rounded-full transition-all duration-300 flex items-center justify-center",
          actualTheme === 'dark'
            ? "left-7 bg-gray-900"
            : "left-1 bg-yellow-400"
        )}
      >
        {actualTheme === 'dark' ? (
          <Moon className="h-3 w-3 text-yellow-300" />
        ) : (
          <Sun className="h-3 w-3 text-yellow-600" />
        )}
      </div>
      
      {/* Background stars for dark mode */}
      {actualTheme === 'dark' && (
        <div className="absolute inset-0 flex items-center justify-start pl-2">
          <div className="flex gap-1">
            <div className="w-1 h-1 bg-yellow-300 rounded-full opacity-60"></div>
            <div className="w-0.5 h-0.5 bg-yellow-300 rounded-full opacity-40"></div>
          </div>
        </div>
      )}
    </button>
  );
};
