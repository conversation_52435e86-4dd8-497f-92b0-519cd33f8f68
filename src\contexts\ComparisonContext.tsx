import React, { createContext, useContext, useState, useCallback } from 'react';

export interface Property {
  id: string;
  title: string;
  location: string;
  price: number;
  rating: number;
  reviewCount: number;
  images: string[];
  amenities: string[];
  bedrooms: number;
  bathrooms: number;
  guests: number;
  propertyType: string;
  host: {
    name: string;
    avatar?: string;
    verified: boolean;
  };
  features: {
    wifi: boolean;
    parking: boolean;
    pool: boolean;
    kitchen: boolean;
    airConditioning: boolean;
    heating: boolean;
    tv: boolean;
    washer: boolean;
    dryer: boolean;
    gym: boolean;
    spa: boolean;
    petFriendly: boolean;
  };
  policies: {
    checkIn: string;
    checkOut: string;
    cancellation: string;
    smoking: boolean;
    parties: boolean;
  };
}

interface ComparisonContextType {
  comparedProperties: Property[];
  addToComparison: (property: Property) => void;
  removeFromComparison: (propertyId: string) => void;
  clearComparison: () => void;
  isInComparison: (propertyId: string) => boolean;
  canAddMore: boolean;
  maxProperties: number;
}

const ComparisonContext = createContext<ComparisonContextType | undefined>(undefined);

export const useComparison = () => {
  const context = useContext(ComparisonContext);
  if (context === undefined) {
    throw new Error('useComparison must be used within a ComparisonProvider');
  }
  return context;
};

interface ComparisonProviderProps {
  children: React.ReactNode;
  maxProperties?: number;
}

export const ComparisonProvider: React.FC<ComparisonProviderProps> = ({
  children,
  maxProperties = 3
}) => {
  const [comparedProperties, setComparedProperties] = useState<Property[]>([]);

  const addToComparison = useCallback((property: Property) => {
    setComparedProperties(prev => {
      // Check if property is already in comparison
      if (prev.some(p => p.id === property.id)) {
        return prev;
      }
      
      // Check if we've reached the maximum
      if (prev.length >= maxProperties) {
        // Remove the oldest property and add the new one
        return [...prev.slice(1), property];
      }
      
      return [...prev, property];
    });
  }, [maxProperties]);

  const removeFromComparison = useCallback((propertyId: string) => {
    setComparedProperties(prev => prev.filter(p => p.id !== propertyId));
  }, []);

  const clearComparison = useCallback(() => {
    setComparedProperties([]);
  }, []);

  const isInComparison = useCallback((propertyId: string) => {
    return comparedProperties.some(p => p.id === propertyId);
  }, [comparedProperties]);

  const canAddMore = comparedProperties.length < maxProperties;

  const value: ComparisonContextType = {
    comparedProperties,
    addToComparison,
    removeFromComparison,
    clearComparison,
    isInComparison,
    canAddMore,
    maxProperties
  };

  return (
    <ComparisonContext.Provider value={value}>
      {children}
    </ComparisonContext.Provider>
  );
};
