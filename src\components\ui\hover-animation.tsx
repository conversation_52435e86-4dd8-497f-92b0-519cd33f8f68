import React from 'react';
import { cn } from '@/lib/utils';

interface HoverAnimationProps {
  children: React.ReactNode;
  type?: 'scale' | 'lift' | 'rotate' | 'glow' | 'bounce';
  intensity?: 'subtle' | 'normal' | 'strong';
  className?: string;
}

export const HoverAnimation: React.FC<HoverAnimationProps> = ({
  children,
  type = 'scale',
  intensity = 'normal',
  className = ''
}) => {
  const getHoverClasses = () => {
    const baseClasses = 'transition-all duration-300 ease-out';
    
    const intensityMap = {
      subtle: {
        scale: 'hover:scale-105',
        lift: 'hover:-translate-y-1 hover:shadow-md',
        rotate: 'hover:rotate-1',
        glow: 'hover:shadow-lg hover:shadow-blue-500/25',
        bounce: 'hover:animate-bounce'
      },
      normal: {
        scale: 'hover:scale-110',
        lift: 'hover:-translate-y-2 hover:shadow-lg',
        rotate: 'hover:rotate-2',
        glow: 'hover:shadow-xl hover:shadow-blue-500/30',
        bounce: 'hover:animate-bounce'
      },
      strong: {
        scale: 'hover:scale-125',
        lift: 'hover:-translate-y-3 hover:shadow-xl',
        rotate: 'hover:rotate-3',
        glow: 'hover:shadow-2xl hover:shadow-blue-500/40',
        bounce: 'hover:animate-bounce'
      }
    };

    return `${baseClasses} ${intensityMap[intensity][type]}`;
  };

  return (
    <div className={cn(getHoverClasses(), className)}>
      {children}
    </div>
  );
};
