// Authentication API Service
const API_BASE_URL = 'http://localhost:3001/api';

class AuthService {
  // Get authentication token from localStorage
  getAuthToken() {
    return localStorage.getItem('stayfinder_token');
  }

  // Set authentication token in localStorage
  setAuthToken(token) {
    if (token) {
      localStorage.setItem('stayfinder_token', token);
    } else {
      localStorage.removeItem('stayfinder_token');
    }
  }

  // Get user data from localStorage
  getUser() {
    const userData = localStorage.getItem('stayfinder_user');
    return userData ? JSON.parse(userData) : null;
  }

  // Set user data in localStorage
  setUser(user) {
    if (user) {
      localStorage.setItem('stayfinder_user', JSON.stringify(user));
    } else {
      localStorage.removeItem('stayfinder_user');
    }
  }

  // Check if user is authenticated
  isAuthenticated() {
    const token = this.getAuthToken();
    const user = this.getUser();
    return !!(token && user);
  }

  // Get authorization headers
  getAuthHeaders() {
    const token = this.getAuthToken();
    return {
      'Content-Type': 'application/json',
      ...(token && { 'Authorization': `Bearer ${token}` })
    };
  }

  // Register new user
  async register(userData) {
    try {
      const response = await fetch(`${API_BASE_URL}/auth/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email: userData.email,
          password: userData.password,
          firstName: userData.firstName,
          lastName: userData.lastName,
          phone: userData.phone
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      // Store token and user data
      this.setAuthToken(data.token);
      this.setUser(data.user);

      return {
        success: true,
        user: data.user,
        token: data.token,
        message: data.message
      };
    } catch (error) {
      console.error('Registration error:', error);
      throw error;
    }
  }

  // Login user
  async login(credentials) {
    try {
      const response = await fetch(`${API_BASE_URL}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email: credentials.email,
          password: credentials.password
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      // Store token and user data
      this.setAuthToken(data.token);
      this.setUser(data.user);

      return {
        success: true,
        user: data.user,
        token: data.token,
        message: data.message
      };
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  }

  // Logout user
  async logout() {
    try {
      // Clear local storage
      this.setAuthToken(null);
      this.setUser(null);

      // Optional: Call logout endpoint if it exists
      const token = this.getAuthToken();
      if (token) {
        await fetch(`${API_BASE_URL}/auth/logout`, {
          method: 'POST',
          headers: this.getAuthHeaders()
        });
      }

      return { success: true };
    } catch (error) {
      console.error('Logout error:', error);
      // Even if the API call fails, clear local storage
      this.setAuthToken(null);
      this.setUser(null);
      return { success: true };
    }
  }

  // Get user profile
  async getProfile() {
    try {
      const response = await fetch(`${API_BASE_URL}/auth/profile`, {
        headers: this.getAuthHeaders()
      });

      if (!response.ok) {
        if (response.status === 401) {
          // Token is invalid, logout user
          this.logout();
          throw new Error('Session expired. Please login again.');
        }
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      // Update user data in localStorage
      this.setUser(data.user);

      return data.user;
    } catch (error) {
      console.error('Profile fetch error:', error);
      throw error;
    }
  }

  // Update user profile
  async updateProfile(profileData) {
    try {
      const response = await fetch(`${API_BASE_URL}/auth/profile`, {
        method: 'PUT',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(profileData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      // Update user data in localStorage
      this.setUser(data.user);

      return data.user;
    } catch (error) {
      console.error('Profile update error:', error);
      throw error;
    }
  }

  // Change password
  async changePassword(passwordData) {
    try {
      const response = await fetch(`${API_BASE_URL}/auth/change-password`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify({
          currentPassword: passwordData.currentPassword,
          newPassword: passwordData.newPassword
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Password change error:', error);
      throw error;
    }
  }

  // Validate token and refresh user data
  async validateToken() {
    try {
      if (!this.getAuthToken()) {
        return false;
      }

      const user = await this.getProfile();
      return !!user;
    } catch (error) {
      console.error('Token validation error:', error);
      this.logout();
      return false;
    }
  }

  // Transform user data for frontend
  transformUser(backendUser) {
    return {
      id: backendUser.id,
      email: backendUser.email,
      firstName: backendUser.firstName || backendUser.first_name,
      lastName: backendUser.lastName || backendUser.last_name,
      phone: backendUser.phone,
      role: backendUser.role,
      emailVerified: backendUser.emailVerified || backendUser.email_verified,
      createdAt: backendUser.createdAt || backendUser.created_at,
      updatedAt: backendUser.updatedAt || backendUser.updated_at
    };
  }

  // Get user's full name
  getUserFullName(user = null) {
    const currentUser = user || this.getUser();
    if (!currentUser) return '';
    return `${currentUser.firstName} ${currentUser.lastName}`.trim();
  }

  // Get user's initials
  getUserInitials(user = null) {
    const currentUser = user || this.getUser();
    if (!currentUser) return '';
    const firstName = currentUser.firstName || '';
    const lastName = currentUser.lastName || '';
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  }

  // Check if user has specific role
  hasRole(role, user = null) {
    const currentUser = user || this.getUser();
    if (!currentUser) return false;
    return currentUser.role === role;
  }

  // Check if user is admin
  isAdmin(user = null) {
    return this.hasRole('admin', user);
  }

  // Check if user is host
  isHost(user = null) {
    return this.hasRole('host', user);
  }

  // Check if user is guest
  isGuest(user = null) {
    return this.hasRole('guest', user);
  }
}

export default new AuthService();
