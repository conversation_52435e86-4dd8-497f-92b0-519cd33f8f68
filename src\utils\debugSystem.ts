import { CapturedError } from '@/hooks/useErrorCapture';

// Debug system configuration
export interface DebugSystemConfig {
  enabled: boolean;
  adminOnly: boolean;
  maxErrors: number;
  autoCapture: boolean;
  showInProduction: boolean;
  keyboardShortcuts: boolean;
}

// Default configuration
export const DEFAULT_DEBUG_CONFIG: DebugSystemConfig = {
  enabled: false,
  adminOnly: true,
  maxErrors: 100,
  autoCapture: true,
  showInProduction: false,
  keyboardShortcuts: true
};

// Local storage keys
export const DEBUG_STORAGE_KEYS = {
  CONFIG: 'stayfinder_debug_config',
  PANEL_STATE: 'stayfinder_debug_panel_state',
  PANEL_POSITION: 'stayfinder_debug_panel_position',
  PANEL_SIZE: 'stayfinder_debug_panel_size'
} as const;

// Debug system manager class
export class DebugSystemManager {
  private static instance: DebugSystemManager;
  private config: DebugSystemConfig;
  private listeners: Set<(config: DebugSystemConfig) => void> = new Set();

  private constructor() {
    this.config = this.loadConfig();
    this.setupKeyboardShortcuts();
  }

  public static getInstance(): DebugSystemManager {
    if (!DebugSystemManager.instance) {
      DebugSystemManager.instance = new DebugSystemManager();
    }
    return DebugSystemManager.instance;
  }

  // Load configuration from localStorage
  private loadConfig(): DebugSystemConfig {
    try {
      const stored = localStorage.getItem(DEBUG_STORAGE_KEYS.CONFIG);
      if (stored) {
        const parsed = JSON.parse(stored);
        return { ...DEFAULT_DEBUG_CONFIG, ...parsed };
      }
    } catch (error) {
      console.warn('Failed to load debug system config:', error);
    }
    return { ...DEFAULT_DEBUG_CONFIG };
  }

  // Save configuration to localStorage
  private saveConfig(): void {
    try {
      localStorage.setItem(DEBUG_STORAGE_KEYS.CONFIG, JSON.stringify(this.config));
      this.notifyListeners();
    } catch (error) {
      console.warn('Failed to save debug system config:', error);
    }
  }

  // Setup keyboard shortcuts
  private setupKeyboardShortcuts(): void {
    if (typeof window === 'undefined') return;

    const handleKeyDown = (event: KeyboardEvent) => {
      if (!this.config.keyboardShortcuts) return;

      // Ctrl+Shift+D to toggle debug panel
      if (event.ctrlKey && event.shiftKey && event.key === 'D') {
        event.preventDefault();
        this.toggleEnabled();
      }

      // Ctrl+Shift+C to clear all errors
      if (event.ctrlKey && event.shiftKey && event.key === 'C') {
        event.preventDefault();
        this.clearAllErrors();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
  }

  // Get current configuration
  public getConfig(): DebugSystemConfig {
    return { ...this.config };
  }

  // Update configuration
  public updateConfig(updates: Partial<DebugSystemConfig>): void {
    this.config = { ...this.config, ...updates };
    this.saveConfig();
  }

  // Toggle debug system enabled state
  public toggleEnabled(): void {
    this.updateConfig({ enabled: !this.config.enabled });
  }

  // Check if debug system should be visible
  public isVisible(userRole?: string): boolean {
    // Don't show in production unless explicitly enabled
    if (process.env.NODE_ENV === 'production' && !this.config.showInProduction) {
      return false;
    }

    // Check if enabled
    if (!this.config.enabled) {
      return false;
    }

    // Check admin-only restriction
    if (this.config.adminOnly && userRole !== 'admin') {
      return false;
    }

    return true;
  }

  // Add configuration change listener
  public addListener(listener: (config: DebugSystemConfig) => void): void {
    this.listeners.add(listener);
  }

  // Remove configuration change listener
  public removeListener(listener: (config: DebugSystemConfig) => void): void {
    this.listeners.delete(listener);
  }

  // Notify all listeners of configuration changes
  private notifyListeners(): void {
    this.listeners.forEach(listener => listener(this.config));
  }

  // Clear all errors (placeholder for integration with error capture)
  private clearAllErrors(): void {
    // This will be connected to the actual error clearing function
    window.dispatchEvent(new CustomEvent('debug-clear-all-errors'));
  }
}

// Panel state management
export interface PanelState {
  isOpen: boolean;
  isMinimized: boolean;
  activeTab: 'errors' | 'console' | 'network' | 'performance';
  filters: {
    type: CapturedError['type'] | 'all';
    search: string;
    source: string | 'all';
  };
}

export const DEFAULT_PANEL_STATE: PanelState = {
  isOpen: false,
  isMinimized: false,
  activeTab: 'errors',
  filters: {
    type: 'all',
    search: '',
    source: 'all'
  }
};

// Panel position and size management
export interface PanelGeometry {
  x: number;
  y: number;
  width: number;
  height: number;
}

export const DEFAULT_PANEL_GEOMETRY: PanelGeometry = {
  x: 20,
  y: 20,
  width: 400,
  height: 600
};

// Utility functions for panel state management
export const savePanelState = (state: PanelState): void => {
  try {
    localStorage.setItem(DEBUG_STORAGE_KEYS.PANEL_STATE, JSON.stringify(state));
  } catch (error) {
    console.warn('Failed to save panel state:', error);
  }
};

export const loadPanelState = (): PanelState => {
  try {
    const stored = localStorage.getItem(DEBUG_STORAGE_KEYS.PANEL_STATE);
    if (stored) {
      const parsed = JSON.parse(stored);
      return { ...DEFAULT_PANEL_STATE, ...parsed };
    }
  } catch (error) {
    console.warn('Failed to load panel state:', error);
  }
  return { ...DEFAULT_PANEL_STATE };
};

export const savePanelGeometry = (geometry: PanelGeometry): void => {
  try {
    localStorage.setItem(DEBUG_STORAGE_KEYS.PANEL_POSITION, JSON.stringify(geometry));
  } catch (error) {
    console.warn('Failed to save panel geometry:', error);
  }
};

export const loadPanelGeometry = (): PanelGeometry => {
  try {
    const stored = localStorage.getItem(DEBUG_STORAGE_KEYS.PANEL_POSITION);
    if (stored) {
      const parsed = JSON.parse(stored);
      return { ...DEFAULT_PANEL_GEOMETRY, ...parsed };
    }
  } catch (error) {
    console.warn('Failed to load panel geometry:', error);
  }
  return { ...DEFAULT_PANEL_GEOMETRY };
};

// Error formatting utilities
export const formatErrorForDisplay = (error: CapturedError): string => {
  const timestamp = error.timestamp.toLocaleTimeString();
  const location = error.filename ? ` at ${error.filename}:${error.line}:${error.column}` : '';
  return `[${timestamp}] ${error.type.toUpperCase()}: ${error.message}${location}`;
};

export const formatErrorForExport = (error: CapturedError): object => {
  return {
    id: error.id,
    type: error.type,
    message: error.message,
    timestamp: error.timestamp.toISOString(),
    source: error.source,
    filename: error.filename,
    line: error.line,
    column: error.column,
    stack: error.stack,
    componentStack: error.componentStack,
    userAgent: error.userAgent,
    url: error.url
  };
};

// Error filtering utilities
export const filterErrors = (
  errors: CapturedError[],
  filters: PanelState['filters']
): CapturedError[] => {
  return errors.filter(error => {
    // Type filter
    if (filters.type !== 'all' && error.type !== filters.type) {
      return false;
    }

    // Source filter
    if (filters.source !== 'all' && error.source !== filters.source) {
      return false;
    }

    // Search filter
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      const searchableText = [
        error.message,
        error.filename,
        error.source,
        error.stack,
        error.componentStack
      ].filter(Boolean).join(' ').toLowerCase();

      if (!searchableText.includes(searchLower)) {
        return false;
      }
    }

    return true;
  });
};

// Performance monitoring utilities
export const measurePerformance = (name: string, fn: () => void): number => {
  const start = performance.now();
  fn();
  const end = performance.now();
  const duration = end - start;
  
  console.debug(`[Debug System] ${name} took ${duration.toFixed(2)}ms`);
  return duration;
};

// Memory usage utilities
export const getMemoryUsage = (): { used: number; total: number } | null => {
  if ('memory' in performance) {
    const memory = (performance as any).memory;
    return {
      used: memory.usedJSHeapSize,
      total: memory.totalJSHeapSize
    };
  }
  return null;
};

// Export singleton instance
export const debugSystemManager = DebugSystemManager.getInstance();
