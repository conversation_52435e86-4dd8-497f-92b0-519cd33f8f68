import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { ImageUpload } from '../ImageUpload';
import { 
  Camera, 
  Image as ImageIcon,
  Lightbulb,
  CheckCircle
} from 'lucide-react';

interface PropertyImagesProps {
  data: {
    images: string[];
  };
  errors: Record<string, string>;
  onChange: (updates: any) => void;
}

export const PropertyImages: React.FC<PropertyImagesProps> = ({
  data,
  errors,
  onChange
}) => {
  // Convert string URLs to ImageFile format for ImageUpload component
  const convertToImageFiles = () => {
    return (data.images || []).map((url, index) => ({
      id: `existing-${index}`,
      file: new File([], `image-${index + 1}.jpg`),
      preview: url,
      isPrimary: index === 0,
      uploading: false
    }));
  };

  // Handle image changes from ImageUpload component
  const handleImagesChange = (imageFiles: any[]) => {
    const imageUrls = imageFiles.map(file => file.preview);
    onChange({ images: imageUrls });
  };

  const getImageCount = () => {
    return (data.images || []).length;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          Add photos of your property
        </h2>
        <p className="text-gray-600">
          Great photos help your listing stand out and attract more guests
        </p>
        {getImageCount() > 0 && (
          <p className="text-sm text-sea-green-600 mt-2">
            {getImageCount()} photo{getImageCount() !== 1 ? 's' : ''} uploaded
          </p>
        )}
      </div>

      {/* Image Upload */}
      <ImageUpload
        images={convertToImageFiles()}
        onImagesChange={handleImagesChange}
        maxImages={10}
        maxFileSize={5}
        acceptedTypes={['image/jpeg', 'image/jpg', 'image/png', 'image/webp']}
      />

      {/* Photo Requirements */}
      <Card className="bg-blue-50 border-blue-200">
        <CardContent className="p-6">
          <h3 className="font-semibold text-blue-900 mb-4 flex items-center gap-2">
            <Camera className="h-5 w-5" />
            Photo Requirements & Tips
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium text-blue-900 mb-3">Required Photos</h4>
              <ul className="space-y-2 text-sm text-blue-800">
                <li className="flex items-start gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                  <span>At least 5 high-quality photos</span>
                </li>
                <li className="flex items-start gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                  <span>Main living areas (living room, kitchen)</span>
                </li>
                <li className="flex items-start gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                  <span>All bedrooms and bathrooms</span>
                </li>
                <li className="flex items-start gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                  <span>Exterior view of the property</span>
                </li>
                <li className="flex items-start gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                  <span>Any special amenities (pool, garden, etc.)</span>
                </li>
              </ul>
            </div>

            <div>
              <h4 className="font-medium text-blue-900 mb-3">Photo Tips</h4>
              <ul className="space-y-2 text-sm text-blue-800">
                <li className="flex items-start gap-2">
                  <Lightbulb className="h-4 w-4 text-yellow-600 mt-0.5 flex-shrink-0" />
                  <span>Use natural lighting when possible</span>
                </li>
                <li className="flex items-start gap-2">
                  <Lightbulb className="h-4 w-4 text-yellow-600 mt-0.5 flex-shrink-0" />
                  <span>Keep spaces clean and clutter-free</span>
                </li>
                <li className="flex items-start gap-2">
                  <Lightbulb className="h-4 w-4 text-yellow-600 mt-0.5 flex-shrink-0" />
                  <span>Take photos from multiple angles</span>
                </li>
                <li className="flex items-start gap-2">
                  <Lightbulb className="h-4 w-4 text-yellow-600 mt-0.5 flex-shrink-0" />
                  <span>Highlight unique features and views</span>
                </li>
                <li className="flex items-start gap-2">
                  <Lightbulb className="h-4 w-4 text-yellow-600 mt-0.5 flex-shrink-0" />
                  <span>Consider hiring a professional photographer</span>
                </li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Photo Guidelines */}
      <Card className="bg-yellow-50 border-yellow-200">
        <CardContent className="p-6">
          <h3 className="font-semibold text-yellow-900 mb-4 flex items-center gap-2">
            <ImageIcon className="h-5 w-5" />
            Photo Guidelines
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium text-yellow-900 mb-3">✅ Do</h4>
              <ul className="space-y-1 text-sm text-yellow-800">
                <li>• Use high-resolution images (at least 1024x683 pixels)</li>
                <li>• Show the space as guests will see it</li>
                <li>• Include photos of amenities you've listed</li>
                <li>• Take horizontal (landscape) photos when possible</li>
                <li>• Ensure photos are well-lit and in focus</li>
                <li>• Show the actual space, not staged photos</li>
              </ul>
            </div>

            <div>
              <h4 className="font-medium text-yellow-900 mb-3">❌ Don't</h4>
              <ul className="space-y-1 text-sm text-yellow-800">
                <li>• Use photos with people in them</li>
                <li>• Include personal items or clutter</li>
                <li>• Use heavily filtered or edited photos</li>
                <li>• Show spaces that guests can't access</li>
                <li>• Include photos of other properties</li>
                <li>• Use blurry or dark photos</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Photo Order Information */}
      {getImageCount() > 1 && (
        <Card className="bg-green-50 border-green-200">
          <CardContent className="p-4">
            <h4 className="font-medium text-green-900 mb-2">📸 Photo Order</h4>
            <p className="text-sm text-green-800">
              Your first photo will be the main image shown in search results. Make sure it's your best shot that showcases the most appealing aspect of your property. You can reorder photos by dragging them in the gallery above.
            </p>
          </CardContent>
        </Card>
      )}

      {/* Minimum Photos Warning */}
      {getImageCount() < 5 && (
        <Card className="bg-orange-50 border-orange-200">
          <CardContent className="p-4">
            <h4 className="font-medium text-orange-900 mb-2">⚠️ Minimum Photos Required</h4>
            <p className="text-sm text-orange-800">
              You need at least 5 photos to create a compelling listing. Properties with more photos typically receive {' '}
              <strong>40% more bookings</strong> than those with fewer photos.
            </p>
            <p className="text-sm text-orange-700 mt-2">
              Current photos: <strong>{getImageCount()}/5 minimum</strong>
            </p>
          </CardContent>
        </Card>
      )}

      {/* Success Message */}
      {getImageCount() >= 5 && (
        <Card className="bg-green-50 border-green-200">
          <CardContent className="p-4">
            <h4 className="font-medium text-green-900 mb-2 flex items-center gap-2">
              <CheckCircle className="h-4 w-4" />
              Great job!
            </h4>
            <p className="text-sm text-green-800">
              You've uploaded {getImageCount()} photos. Your listing is looking great! 
              {getImageCount() >= 8 && ' You have an excellent photo collection that will attract more guests.'}
            </p>
          </CardContent>
        </Card>
      )}

      {/* Professional Photography Offer */}
      <Card className="bg-purple-50 border-purple-200">
        <CardContent className="p-6">
          <h3 className="font-semibold text-purple-900 mb-3">📷 Professional Photography</h3>
          <p className="text-sm text-purple-800 mb-4">
            Want to make your listing stand out even more? Professional photos can increase bookings by up to 40% and allow you to charge higher rates.
          </p>
          <div className="flex flex-wrap gap-3">
            <button className="px-4 py-2 bg-purple-600 text-white rounded-lg text-sm font-medium hover:bg-purple-700 transition-colors">
              Find Photographers
            </button>
            <button className="px-4 py-2 border border-purple-300 text-purple-700 rounded-lg text-sm font-medium hover:bg-purple-100 transition-colors">
              Learn More
            </button>
          </div>
        </CardContent>
      </Card>

      {/* Error Display */}
      {errors.images && (
        <Card className="bg-red-50 border-red-200">
          <CardContent className="p-4">
            <p className="text-red-700 text-sm">{errors.images}</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
