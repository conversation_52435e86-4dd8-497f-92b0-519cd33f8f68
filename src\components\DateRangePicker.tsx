import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Calendar, 
  ChevronLeft, 
  ChevronRight,
  X
} from 'lucide-react';

interface DateRange {
  checkIn?: string;
  checkOut?: string;
}

interface DateRangePickerProps {
  value: DateRange;
  onChange: (dateRange: DateRange) => void;
  className?: string;
  placeholder?: {
    checkIn?: string;
    checkOut?: string;
  };
}

export const DateRangePicker: React.FC<DateRangePickerProps> = ({
  value,
  onChange,
  className,
  placeholder = {
    checkIn: 'Check-in date',
    checkOut: 'Check-out date'
  }
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [currentMonth, setCurrentMonth] = useState(new Date());

  const today = new Date();
  const minDate = today.toISOString().split('T')[0];

  const handleDateChange = (field: 'checkIn' | 'checkOut', date: string) => {
    const newRange = { ...value, [field]: date };
    
    // Auto-adjust dates if needed
    if (field === 'checkIn' && value.checkOut && date >= value.checkOut) {
      const nextDay = new Date(date);
      nextDay.setDate(nextDay.getDate() + 1);
      newRange.checkOut = nextDay.toISOString().split('T')[0];
    }
    
    onChange(newRange);
  };

  const clearDates = () => {
    onChange({ checkIn: undefined, checkOut: undefined });
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const getDaysBetween = () => {
    if (!value.checkIn || !value.checkOut) return 0;
    const checkIn = new Date(value.checkIn);
    const checkOut = new Date(value.checkOut);
    const diffTime = checkOut.getTime() - checkIn.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  const getQuickDateOptions = () => {
    const options = [];
    const today = new Date();
    
    // This weekend
    const thisWeekend = new Date(today);
    thisWeekend.setDate(today.getDate() + (6 - today.getDay())); // Saturday
    const weekendEnd = new Date(thisWeekend);
    weekendEnd.setDate(thisWeekend.getDate() + 1); // Sunday
    
    options.push({
      label: 'This Weekend',
      checkIn: thisWeekend.toISOString().split('T')[0],
      checkOut: weekendEnd.toISOString().split('T')[0]
    });

    // Next weekend
    const nextWeekend = new Date(thisWeekend);
    nextWeekend.setDate(thisWeekend.getDate() + 7);
    const nextWeekendEnd = new Date(nextWeekend);
    nextWeekendEnd.setDate(nextWeekend.getDate() + 1);
    
    options.push({
      label: 'Next Weekend',
      checkIn: nextWeekend.toISOString().split('T')[0],
      checkOut: nextWeekendEnd.toISOString().split('T')[0]
    });

    // Next week (7 days)
    const nextWeek = new Date(today);
    nextWeek.setDate(today.getDate() + 7);
    const nextWeekEnd = new Date(nextWeek);
    nextWeekEnd.setDate(nextWeek.getDate() + 7);
    
    options.push({
      label: 'Next Week',
      checkIn: nextWeek.toISOString().split('T')[0],
      checkOut: nextWeekEnd.toISOString().split('T')[0]
    });

    return options;
  };

  return (
    <div className={className}>
      <div className="space-y-3">
        {/* Date Inputs */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          <div className="space-y-1">
            <Label className="text-sm font-medium">Check-in</Label>
            <div className="relative">
              <Input
                type="date"
                value={value.checkIn || ''}
                onChange={(e) => handleDateChange('checkIn', e.target.value)}
                min={minDate}
                placeholder={placeholder.checkIn}
                className="pr-8"
              />
              <Calendar className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
            </div>
          </div>

          <div className="space-y-1">
            <Label className="text-sm font-medium">Check-out</Label>
            <div className="relative">
              <Input
                type="date"
                value={value.checkOut || ''}
                onChange={(e) => handleDateChange('checkOut', e.target.value)}
                min={value.checkIn || minDate}
                placeholder={placeholder.checkOut}
                className="pr-8"
              />
              <Calendar className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
            </div>
          </div>
        </div>

        {/* Date Summary */}
        {value.checkIn && value.checkOut && (
          <div className="flex items-center justify-between p-3 bg-sea-green-50 rounded-lg border border-sea-green-200">
            <div className="text-sm">
              <span className="font-medium text-sea-green-900">
                {formatDate(value.checkIn)} → {formatDate(value.checkOut)}
              </span>
              <span className="text-sea-green-700 ml-2">
                ({getDaysBetween()} night{getDaysBetween() !== 1 ? 's' : ''})
              </span>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={clearDates}
              className="h-6 w-6 p-0 text-sea-green-600 hover:text-sea-green-800"
            >
              <X className="h-3 w-3" />
            </Button>
          </div>
        )}

        {/* Quick Date Options */}
        <div className="space-y-2">
          <Label className="text-sm font-medium">Quick Select</Label>
          <div className="flex flex-wrap gap-2">
            {getQuickDateOptions().map((option) => (
              <Button
                key={option.label}
                variant="outline"
                size="sm"
                onClick={() => onChange({
                  checkIn: option.checkIn,
                  checkOut: option.checkOut
                })}
                className="text-xs"
              >
                {option.label}
              </Button>
            ))}
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                const today = new Date();
                const tomorrow = new Date(today);
                tomorrow.setDate(today.getDate() + 1);
                const dayAfter = new Date(today);
                dayAfter.setDate(today.getDate() + 2);
                
                onChange({
                  checkIn: tomorrow.toISOString().split('T')[0],
                  checkOut: dayAfter.toISOString().split('T')[0]
                });
              }}
              className="text-xs"
            >
              Tomorrow
            </Button>
          </div>
        </div>

        {/* Flexible Dates Option */}
        <div className="pt-2 border-t">
          <label className="flex items-center gap-2 cursor-pointer">
            <input
              type="checkbox"
              className="w-4 h-4 text-sea-green-600 border-gray-300 rounded focus:ring-sea-green-500"
            />
            <span className="text-sm text-gray-600">I'm flexible with dates</span>
          </label>
          <p className="text-xs text-gray-500 mt-1">
            Show properties available within ±3 days of selected dates
          </p>
        </div>
      </div>
    </div>
  );
};
