-- <PERSON>reate saved searches and search history tables for advanced search functionality
CREATE TABLE IF NOT EXISTS saved_searches (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    name VARCHAR(255) NOT NULL,
    search_criteria JSON NOT NULL,
    notification_enabled B<PERSON>OLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

CREATE TABLE IF NOT EXISTS search_history (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NULL,
    search_criteria JSON NOT NULL,
    results_count INT DEFAULT 0,
    search_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    
    FOR<PERSON>G<PERSON>EY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

CREATE TABLE IF NOT EXISTS search_suggestions (
    id VARCHAR(36) PRIMARY KEY,
    term VARCHAR(255) NOT NULL,
    category ENUM('location', 'property_type', 'amenity', 'general') DEFAULT 'general',
    search_count INT DEFAULT 1,
    last_searched TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_term_category (term, category)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_saved_searches_user_id ON saved_searches(user_id);
CREATE INDEX IF NOT EXISTS idx_saved_searches_created_at ON saved_searches(created_at);

CREATE INDEX IF NOT EXISTS idx_search_history_user_id ON search_history(user_id);
CREATE INDEX IF NOT EXISTS idx_search_history_timestamp ON search_history(search_timestamp);

CREATE INDEX IF NOT EXISTS idx_search_suggestions_term ON search_suggestions(term);
CREATE INDEX IF NOT EXISTS idx_search_suggestions_category ON search_suggestions(category);
CREATE INDEX IF NOT EXISTS idx_search_suggestions_count ON search_suggestions(search_count);

-- Insert sample saved searches for testing
INSERT IGNORE INTO saved_searches (
    id, user_id, name, search_criteria, notification_enabled, created_at
) VALUES 
(
    'search-1',
    (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1),
    'Beach Houses in Margate',
    '{"location": "Margate", "propertyType": "house", "amenities": ["pool", "beach_access"], "maxPrice": 2000}',
    TRUE,
    '2024-06-20 10:00:00'
),
(
    'search-2',
    (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1),
    'Family Villas with Pool',
    '{"guests": 6, "propertyType": "villa", "amenities": ["pool", "wifi", "parking"], "bedrooms": 3}',
    FALSE,
    '2024-06-19 15:30:00'
),
(
    'search-3',
    (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1),
    'Budget Apartments',
    '{"propertyType": "apartment", "maxPrice": 800, "rating": 4.0}',
    TRUE,
    '2024-06-18 09:15:00'
);

-- Insert sample search history
INSERT IGNORE INTO search_history (
    id, user_id, search_criteria, results_count, search_timestamp
) VALUES 
(
    'history-1',
    (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1),
    '{"location": "Durban", "guests": 2, "checkIn": "2024-07-01", "checkOut": "2024-07-05"}',
    15,
    '2024-06-21 14:30:00'
),
(
    'history-2',
    (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1),
    '{"location": "Port Shepstone", "propertyType": "cottage", "amenities": ["wifi"]}',
    8,
    '2024-06-21 12:15:00'
),
(
    'history-3',
    (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1),
    '{"location": "Scottburgh", "minPrice": 500, "maxPrice": 1500}',
    12,
    '2024-06-21 10:45:00'
);

-- Insert sample search suggestions
INSERT IGNORE INTO search_suggestions (
    id, term, category, search_count, last_searched
) VALUES 
('suggest-1', 'Margate', 'location', 45, '2024-06-21 15:00:00'),
('suggest-2', 'Durban', 'location', 38, '2024-06-21 14:30:00'),
('suggest-3', 'Port Shepstone', 'location', 22, '2024-06-21 12:15:00'),
('suggest-4', 'Scottburgh', 'location', 18, '2024-06-21 10:45:00'),
('suggest-5', 'Amanzimtoti', 'location', 15, '2024-06-20 16:20:00'),
('suggest-6', 'villa', 'property_type', 32, '2024-06-21 13:45:00'),
('suggest-7', 'cottage', 'property_type', 28, '2024-06-21 12:15:00'),
('suggest-8', 'apartment', 'property_type', 25, '2024-06-21 11:30:00'),
('suggest-9', 'pool', 'amenity', 42, '2024-06-21 14:00:00'),
('suggest-10', 'beach access', 'amenity', 35, '2024-06-21 13:20:00'),
('suggest-11', 'wifi', 'amenity', 30, '2024-06-21 12:15:00'),
('suggest-12', 'parking', 'amenity', 28, '2024-06-21 11:45:00');
