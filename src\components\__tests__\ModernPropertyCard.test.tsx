import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, userEvent, setupCommonMocks } from '@/test/utils/test-utils';
import { ModernPropertyCard } from '../ModernPropertyCard';

const mockProperty = {
  id: '1',
  title: 'Luxury Beachfront Villa',
  description: 'Beautiful villa with ocean views',
  location: 'Camps Bay, Cape Town',
  price: 2500,
  maxGuests: 8,
  bedrooms: 4,
  bathrooms: 3,
  images: [
    'https://images.unsplash.com/photo-1564013799919-ab600027ffc6',
    'https://images.unsplash.com/photo-1522708323590-d24dbb6b0267'
  ],
  amenities: ['wifi', 'pool', 'parking'],
  rating: 4.9,
  reviewCount: 18,
  hostId: '2',
  createdAt: '2024-01-01T00:00:00.000Z',
  updatedAt: '2024-01-01T00:00:00.000Z'
};

describe('ModernPropertyCard Component', () => {
  beforeEach(() => {
    setupCommonMocks();
  });

  it('renders property information correctly', () => {
    render(<ModernPropertyCard property={mockProperty} />);
    
    expect(screen.getByText('Luxury Beachfront Villa')).toBeInTheDocument();
    expect(screen.getByText('Camps Bay, Cape Town')).toBeInTheDocument();
    expect(screen.getByText('R2,500')).toBeInTheDocument();
    expect(screen.getByText('8 guests')).toBeInTheDocument();
    expect(screen.getByText('4 bed')).toBeInTheDocument();
    expect(screen.getByText('3 bath')).toBeInTheDocument();
  });

  it('displays rating and review count', () => {
    render(<ModernPropertyCard property={mockProperty} />);
    
    expect(screen.getByText('4.9')).toBeInTheDocument();
    expect(screen.getByText('(18 reviews)')).toBeInTheDocument();
  });

  it('shows amenities', () => {
    render(<ModernPropertyCard property={mockProperty} />);
    
    // Check for amenity icons or text
    expect(screen.getByText(/wifi/i)).toBeInTheDocument();
    expect(screen.getByText(/pool/i)).toBeInTheDocument();
    expect(screen.getByText(/parking/i)).toBeInTheDocument();
  });

  it('handles image navigation', async () => {
    const user = userEvent.setup();
    render(<ModernPropertyCard property={mockProperty} />);
    
    // Should show first image initially
    const images = screen.getAllByRole('img');
    expect(images[0]).toHaveAttribute('src', mockProperty.images[0]);
    
    // Find and click next button
    const nextButton = screen.getByRole('button', { name: /next/i });
    await user.click(nextButton);
    
    // Should show second image
    expect(images[0]).toHaveAttribute('src', mockProperty.images[1]);
  });

  it('handles favorite toggle', async () => {
    const user = userEvent.setup();
    render(<ModernPropertyCard property={mockProperty} />);
    
    const favoriteButton = screen.getByRole('button', { name: /favorite/i });
    await user.click(favoriteButton);
    
    // Should toggle favorite state (implementation dependent)
    expect(favoriteButton).toBeInTheDocument();
  });

  it('handles share functionality', async () => {
    const user = userEvent.setup();
    render(<ModernPropertyCard property={mockProperty} />);
    
    const shareButton = screen.getByRole('button', { name: /share/i });
    await user.click(shareButton);
    
    // Should trigger share functionality
    expect(shareButton).toBeInTheDocument();
  });

  it('renders with compact variant', () => {
    render(<ModernPropertyCard property={mockProperty} variant="compact" />);
    
    // Should still show essential information
    expect(screen.getByText('Luxury Beachfront Villa')).toBeInTheDocument();
    expect(screen.getByText('R2,500')).toBeInTheDocument();
  });

  it('renders with featured variant', () => {
    render(<ModernPropertyCard property={mockProperty} variant="featured" />);
    
    // Should show featured styling
    expect(screen.getByText('Luxury Beachfront Villa')).toBeInTheDocument();
  });

  it('handles click to view property details', async () => {
    const onPropertyClick = vi.fn();
    const user = userEvent.setup();
    
    render(<ModernPropertyCard property={mockProperty} onPropertyClick={onPropertyClick} />);
    
    const propertyCard = screen.getByRole('article') || screen.getByTestId('property-card');
    await user.click(propertyCard);
    
    expect(onPropertyClick).toHaveBeenCalledWith(mockProperty);
  });

  it('displays property without images gracefully', () => {
    const propertyWithoutImages = { ...mockProperty, images: [] };
    
    render(<ModernPropertyCard property={propertyWithoutImages} />);
    
    // Should still render other information
    expect(screen.getByText('Luxury Beachfront Villa')).toBeInTheDocument();
  });

  it('handles loading state', () => {
    render(<ModernPropertyCard property={mockProperty} loading />);
    
    // Should show loading indicators
    expect(screen.getByTestId('property-card-loading') || screen.getByText(/loading/i)).toBeInTheDocument();
  });

  it('displays price per night correctly', () => {
    render(<ModernPropertyCard property={mockProperty} />);
    
    expect(screen.getByText(/per night/i)).toBeInTheDocument();
  });

  it('shows property type badge', () => {
    const propertyWithType = { ...mockProperty, propertyType: 'Villa' };
    
    render(<ModernPropertyCard property={propertyWithType} />);
    
    expect(screen.getByText('Villa')).toBeInTheDocument();
  });

  it('handles keyboard navigation', async () => {
    const user = userEvent.setup();
    render(<ModernPropertyCard property={mockProperty} />);
    
    const propertyCard = screen.getByRole('article') || screen.getByTestId('property-card');
    
    // Should be focusable
    propertyCard.focus();
    expect(propertyCard).toHaveFocus();
    
    // Should handle Enter key
    await user.keyboard('{Enter}');
    // Implementation dependent behavior
  });

  it('displays availability status', () => {
    const availableProperty = { ...mockProperty, available: true };
    
    render(<ModernPropertyCard property={availableProperty} />);
    
    // Should show availability indicator
    expect(screen.getByText(/available/i) || screen.getByTestId('availability-indicator')).toBeInTheDocument();
  });

  it('shows discount badge when applicable', () => {
    const discountedProperty = { ...mockProperty, discount: 20 };
    
    render(<ModernPropertyCard property={discountedProperty} />);
    
    expect(screen.getByText(/20% off/i) || screen.getByTestId('discount-badge')).toBeInTheDocument();
  });

  it('handles error state gracefully', () => {
    const invalidProperty = { ...mockProperty, title: '' };
    
    expect(() => {
      render(<ModernPropertyCard property={invalidProperty} />);
    }).not.toThrow();
  });

  it('maintains accessibility standards', () => {
    render(<ModernPropertyCard property={mockProperty} />);
    
    // Should have proper ARIA labels
    const propertyCard = screen.getByRole('article') || screen.getByTestId('property-card');
    expect(propertyCard).toHaveAttribute('aria-label', expect.stringContaining('Luxury Beachfront Villa'));
    
    // Images should have alt text
    const images = screen.getAllByRole('img');
    images.forEach(img => {
      expect(img).toHaveAttribute('alt');
    });
  });

  it('displays host information when provided', () => {
    const propertyWithHost = { 
      ...mockProperty, 
      host: { 
        id: '2', 
        name: 'John Doe', 
        avatar: 'https://example.com/avatar.jpg' 
      } 
    };
    
    render(<ModernPropertyCard property={propertyWithHost} />);
    
    expect(screen.getByText('John Doe')).toBeInTheDocument();
  });
});
