import React from 'react';
import { <PERSON> } from '@/components/Hero';
import { UltraModernPropertyCard } from '@/components/UltraModernPropertyCard';
import { ModernFooter } from '@/components/ModernFooter';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  PageTransition, 
  SlideIn, 
  StaggeredAnimation 
} from '@/components/ui/page-transitions';
import { EnhancedBreadcrumb } from '@/components/ui/enhanced-breadcrumb';
import { Home, Sparkles, Zap, Star, Award } from 'lucide-react';

export const ModernUIDemo: React.FC = () => {
  // Sample properties for demonstration
  const sampleProperties = [
    {
      id: '1',
      title: 'Luxury Beachfront Villa',
      location: 'Camps Bay, Cape Town',
      price: 2500,
      rating: 4.9,
      reviewCount: 127,
      images: [
        'https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=800&h=600&fit=crop',
        'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&h=600&fit=crop',
        'https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=800&h=600&fit=crop'
      ],
      bedrooms: 4,
      bathrooms: 3,
      guests: 8,
      amenities: ['WiFi', 'Pool', 'Kitchen', 'Parking', 'Ocean View'],
      host: {
        name: 'Sarah Johnson',
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face',
        verified: true,
        superhost: true
      },
      available: true,
      special: 'Featured',
      instantBook: true,
      trending: true,
      newListing: false
    },
    {
      id: '2',
      title: 'Modern City Apartment',
      location: 'Sandton, Johannesburg',
      price: 850,
      rating: 4.7,
      reviewCount: 89,
      images: [
        'https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?w=800&h=600&fit=crop',
        'https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=800&h=600&fit=crop'
      ],
      bedrooms: 2,
      bathrooms: 2,
      guests: 4,
      amenities: ['WiFi', 'Gym', 'Kitchen', 'Parking'],
      host: {
        name: 'Michael Chen',
        verified: true,
        superhost: false
      },
      available: true,
      instantBook: false,
      trending: false,
      newListing: true
    },
    {
      id: '3',
      title: 'Safari Lodge Experience',
      location: 'Kruger National Park',
      price: 1200,
      rating: 4.8,
      reviewCount: 156,
      images: [
        'https://images.unsplash.com/photo-1516026672322-bc52d61a55d5?w=800&h=600&fit=crop',
        'https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=800&h=600&fit=crop'
      ],
      bedrooms: 1,
      bathrooms: 1,
      guests: 2,
      amenities: ['WiFi', 'Restaurant', 'Game Drives', 'Spa'],
      host: {
        name: 'David Williams',
        verified: true,
        superhost: true
      },
      available: true,
      instantBook: true,
      trending: true,
      newListing: false
    }
  ];

  return (
    <PageTransition>
      <div className="min-h-screen bg-gray-50">
        {/* Enhanced Hero Section */}
        <Hero />

        <div className="container mx-auto px-4 py-16">
          <SlideIn direction="up" delay={100}>
            <div className="mb-12">
              <EnhancedBreadcrumb 
                items={[
                  { label: 'Home', href: '/', icon: <Home className="h-4 w-4" /> },
                  { label: 'Modern UI Demo', current: true }
                ]}
              />
              <div className="text-center mt-8">
                <div className="inline-flex items-center gap-2 bg-gradient-to-r from-sea-green-100 to-ocean-blue-100 rounded-full px-6 py-3 mb-6">
                  <Sparkles className="h-5 w-5 text-sea-green-600" />
                  <span className="text-sm font-semibold text-sea-green-700">Modern UI Showcase</span>
                  <Badge className="bg-sea-green-500 text-white text-xs">New</Badge>
                </div>
                <h1 className="text-5xl font-bold text-gray-900 mb-4">
                  Modern UI Upgrades
                </h1>
                <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                  Experience the enhanced StayFinder interface with modern animations, 
                  sophisticated gradients, and ultra-modern property cards
                </p>
              </div>
            </div>
          </SlideIn>

          {/* Features Overview */}
          <SlideIn direction="up" delay={200}>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
              <StaggeredAnimation delay={100}>
                {[
                  {
                    icon: Zap,
                    title: 'Enhanced Hero',
                    description: 'Floating elements, animated stats, modern gradients',
                    color: 'from-yellow-500 to-orange-500'
                  },
                  {
                    icon: Star,
                    title: 'Ultra-Modern Cards',
                    description: 'Advanced image galleries, metadata, interactions',
                    color: 'from-sea-green-500 to-ocean-blue-500'
                  },
                  {
                    icon: Sparkles,
                    title: 'Smooth Animations',
                    description: 'Staggered animations, hover effects, transitions',
                    color: 'from-purple-500 to-pink-500'
                  },
                  {
                    icon: Award,
                    title: 'Visual Polish',
                    description: 'Enhanced spacing, typography, hierarchy',
                    color: 'from-blue-500 to-indigo-500'
                  }
                ].map((feature, index) => {
                  const Icon = feature.icon;
                  return (
                    <Card key={index} className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 group">
                      <CardContent className="p-6 text-center">
                        <div className={`w-16 h-16 bg-gradient-to-br ${feature.color} rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300`}>
                          <Icon className="h-8 w-8 text-white" />
                        </div>
                        <h3 className="text-lg font-bold text-gray-900 mb-2">{feature.title}</h3>
                        <p className="text-gray-600 text-sm">{feature.description}</p>
                      </CardContent>
                    </Card>
                  );
                })}
              </StaggeredAnimation>
            </div>
          </SlideIn>

          {/* Ultra-Modern Property Cards Showcase */}
          <SlideIn direction="up" delay={300}>
            <div className="mb-16">
              <div className="text-center mb-12">
                <h2 className="text-4xl font-bold text-gray-900 mb-4">
                  Ultra-Modern Property Cards
                </h2>
                <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                  Experience our redesigned property cards with advanced image galleries, 
                  rich metadata, and sophisticated interactions
                </p>
              </div>

              {/* Featured Property Card */}
              <div className="mb-12">
                <h3 className="text-2xl font-bold text-gray-900 mb-6 text-center">Featured Layout</h3>
                <div className="max-w-4xl mx-auto">
                  <UltraModernPropertyCard 
                    property={sampleProperties[0]} 
                    variant="featured"
                    showHost={true}
                    onLike={(id) => console.log('Liked:', id)}
                    onShare={(property) => console.log('Shared:', property)}
                    onView={(property) => console.log('Viewed:', property)}
                  />
                </div>
              </div>

              {/* Grid Property Cards */}
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6 text-center">Grid Layout</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                  <StaggeredAnimation delay={150}>
                    {sampleProperties.map((property) => (
                      <UltraModernPropertyCard 
                        key={property.id}
                        property={property} 
                        variant="grid"
                        showHost={false}
                        onLike={(id) => console.log('Liked:', id)}
                        onShare={(property) => console.log('Shared:', property)}
                        onView={(property) => console.log('Viewed:', property)}
                      />
                    ))}
                  </StaggeredAnimation>
                </div>
              </div>
            </div>
          </SlideIn>

          {/* Enhancement Details */}
          <SlideIn direction="up" delay={400}>
            <Card className="border-0 shadow-lg mb-16">
              <CardHeader>
                <CardTitle className="text-2xl text-center">Modern UI Enhancement Details</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  <div>
                    <h4 className="text-lg font-bold text-gray-900 mb-4">Hero Section Enhancements</h4>
                    <ul className="space-y-2 text-gray-600">
                      <li>• Floating background elements with animations</li>
                      <li>• Enhanced gradient overlays and effects</li>
                      <li>• Animated statistics cards with hover effects</li>
                      <li>• Improved search bar with backdrop blur</li>
                      <li>• Staggered animations for destination buttons</li>
                      <li>• Modern CTA buttons with scale animations</li>
                    </ul>
                  </div>
                  
                  <div>
                    <h4 className="text-lg font-bold text-gray-900 mb-4">Property Card Features</h4>
                    <ul className="space-y-2 text-gray-600">
                      <li>• Advanced image galleries with navigation</li>
                      <li>• Rich metadata display with tags and credits</li>
                      <li>• Host information with verification badges</li>
                      <li>• Interactive amenities with icons</li>
                      <li>• Hover animations and micro-interactions</li>
                      <li>• Multiple layout variants (featured, grid, compact)</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </SlideIn>
        </div>

        {/* Modern Footer */}
        <ModernFooter />
      </div>
    </PageTransition>
  );
};
