
import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { MapPin, Loader2, ExternalLink } from 'lucide-react';
import propertiesService from '../services/properties';

interface Property {
  id: string;
  title: string;
  location: string;
  price: number;
  images: string[];
  description: string;
  propertyType?: string;
  maxGuests?: number;
  bedrooms?: number;
  bathrooms?: number;
  averageRating?: number;
  reviewCount?: number;
}

interface SearchResultsProps {
  searchQuery: string;
  onClose: () => void;
}

export const SearchResults = ({ searchQuery, onClose }: SearchResultsProps) => {
  const [properties, setProperties] = useState<Property[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();

  const handleViewAllResults = () => {
    const searchParams = new URLSearchParams();
    searchParams.set('location', searchQuery);
    navigate(`/search?${searchParams.toString()}`);
    onClose();
  };

  // Fetch properties from service
  useEffect(() => {
    const fetchProperties = async () => {
      if (!searchQuery.trim()) return;

      setLoading(true);
      setError(null);

      try {
        const result = await propertiesService.searchByLocation(searchQuery, 1, 12);

        if (result && result.properties) {
          setProperties(result.properties);
        } else {
          setProperties([]);
        }
      } catch (err) {
        console.error('Search error:', err);
        setError('Failed to fetch properties. Please try again.');
        setProperties([]);
      } finally {
        setLoading(false);
      }
    };

    fetchProperties();
  }, [searchQuery]);

  if (!searchQuery) return null;

  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-start justify-center pt-20">
      <div className="bg-white rounded-lg max-w-6xl w-full mx-4 max-h-[80vh] overflow-y-auto">
        <div className="p-6 border-b">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-bold text-gray-900">
              Search Results for "{searchQuery}"
            </h2>
            <div className="flex gap-2">
              <Button variant="outline" onClick={handleViewAllResults}>
                <ExternalLink className="h-4 w-4 mr-2" />
                View All Results
              </Button>
              <Button variant="outline" onClick={onClose}>
                Close
              </Button>
            </div>
          </div>
          {!loading && !error && (
            <p className="text-gray-600 mt-2">
              Found {properties.length} properties
            </p>
          )}
        </div>

        <div className="p-6 space-y-8">
          {/* Loading State */}
          {loading && (
            <div className="text-center py-12">
              <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-sea-green-600" />
              <p className="text-gray-600">Searching for properties...</p>
            </div>
          )}

          {/* Error State */}
          {error && (
            <div className="text-center py-12">
              <p className="text-red-600 text-lg mb-4">{error}</p>
              <Button
                onClick={() => window.location.reload()}
                variant="outline"
              >
                Try Again
              </Button>
            </div>
          )}

          {/* Results */}
          {!loading && !error && properties.length > 0 && (
            <div>
              <div className="flex items-center mb-4">
                <MapPin className="h-5 w-5 text-sea-green-600 mr-2" />
                <h3 className="text-xl font-semibold text-gray-800">Available Properties</h3>
                <Badge variant="outline" className="ml-2">
                  {properties.length} properties
                </Badge>
              </div>

              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                {properties.map((property) => (
                  <div key={property.id} className="bg-white p-4 rounded-lg shadow border hover:shadow-lg transition-shadow">
                    <img
                      src={property.images[0]}
                      alt={property.title}
                      className="w-full h-48 object-cover rounded mb-3"
                    />
                    <div className="space-y-2">
                      <h4 className="font-semibold text-lg">{property.title}</h4>
                      <p className="text-gray-600 text-sm">{property.location}</p>
                      <div className="flex items-center justify-between">
                        <p className="text-xl font-bold text-blue-600">R{property.price.toLocaleString()}/night</p>
                        <Badge variant="outline" className="text-xs">{property.propertyType}</Badge>
                      </div>
                      <div className="flex items-center text-sm text-gray-500 space-x-4">
                        <span>{property.maxGuests} guests</span>
                        <span>{property.bedrooms} beds</span>
                        <span>{property.bathrooms} baths</span>
                      </div>
                      <div className="flex items-center text-sm">
                        <span className="text-yellow-500">★</span>
                        <span className="ml-1">{property.averageRating} ({property.reviewCount} reviews)</span>
                      </div>
                      <p className="text-sm text-gray-500 line-clamp-2">{property.description}</p>
                      <div className="pt-2">
                        <Button className="w-full bg-gradient-to-r from-sea-green-500 to-ocean-blue-500 hover:from-sea-green-600 hover:to-ocean-blue-600 text-white">
                          View Details
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* No Results */}
          {!loading && !error && properties.length === 0 && (
            <div className="text-center py-12">
              <div className="mb-4">
                <MapPin className="h-12 w-12 mx-auto text-gray-300 mb-4" />
                <p className="text-gray-500 text-lg mb-2">
                  No properties found for "{searchQuery}"
                </p>
                <p className="text-gray-400 text-sm">
                  Try searching for "Cape Town" or "Stellenbosch" to see available properties.
                </p>
              </div>
              <Button variant="outline" onClick={onClose}>
                Close Search
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
