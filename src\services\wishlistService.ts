interface WishlistProperty {
  id: string;
  property_id: string;
  title: string;
  location: string;
  price: number;
  images: string[];
  rating: number;
  review_count: number;
  property_type: string;
  max_guests: number;
  bedrooms: number;
  bathrooms: number;
  amenities: string[];
  added_at: string;
  is_available: boolean;
  host_name: string;
}

interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

class WishlistService {
  private baseUrl = 'http://localhost:3001/api/wishlist';

  private async makeRequest<T>(url: string, options: RequestInit = {}): Promise<ApiResponse<T>> {
    try {
      const token = localStorage.getItem('auth_token');
      const headers: HeadersInit = {
        'Content-Type': 'application/json',
        ...options.headers,
      };

      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const response = await fetch(url, {
        ...options,
        headers,
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Wishlist API error:', error);
      throw error;
    }
  }

  async getWishlist(): Promise<WishlistProperty[]> {
    try {
      const response = await this.makeRequest<WishlistProperty[]>(this.baseUrl);
      
      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to fetch wishlist');
      }
    } catch (error) {
      console.error('Error fetching wishlist:', error);
      // Return mock data for testing
      return this.getMockWishlist();
    }
  }

  async addToWishlist(propertyId: string): Promise<boolean> {
    try {
      const response = await this.makeRequest<void>(this.baseUrl, {
        method: 'POST',
        body: JSON.stringify({ property_id: propertyId }),
      });
      
      return response.success;
    } catch (error) {
      console.error('Error adding to wishlist:', error);
      throw error;
    }
  }

  async removeFromWishlist(propertyId: string): Promise<boolean> {
    try {
      const response = await this.makeRequest<void>(`${this.baseUrl}/${propertyId}`, {
        method: 'DELETE',
      });
      
      return response.success;
    } catch (error) {
      console.error('Error removing from wishlist:', error);
      throw error;
    }
  }

  async isInWishlist(propertyId: string): Promise<boolean> {
    try {
      const response = await this.makeRequest<{ is_in_wishlist: boolean }>(`${this.baseUrl}/check/${propertyId}`);
      
      if (response.success && response.data) {
        return response.data.is_in_wishlist;
      }
      return false;
    } catch (error) {
      console.error('Error checking wishlist status:', error);
      return false;
    }
  }

  async getWishlistCount(): Promise<number> {
    try {
      const response = await this.makeRequest<{ count: number }>(`${this.baseUrl}/count`);
      
      if (response.success && response.data) {
        return response.data.count;
      }
      return 0;
    } catch (error) {
      console.error('Error fetching wishlist count:', error);
      return 0;
    }
  }

  // Helper methods
  formatPrice(price: number): string {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
      minimumFractionDigits: 0,
    }).format(price);
  }

  formatRating(rating: number): string {
    return rating.toFixed(1);
  }

  getPropertyTypeIcon(type: string): string {
    switch (type.toLowerCase()) {
      case 'villa':
        return '🏖️';
      case 'cottage':
        return '🏡';
      case 'apartment':
        return '🏢';
      case 'house':
        return '🏠';
      case 'cabin':
        return '🏕️';
      default:
        return '🏨';
    }
  }

  // Mock data for testing
  getMockWishlist(): WishlistProperty[] {
    return [
      {
        id: 'wishlist-1',
        property_id: 'prop-1',
        title: 'Luxury Villa in Margate',
        location: 'Margate, KwaZulu-Natal',
        price: 500,
        images: ['/placeholder.svg'],
        rating: 4.8,
        review_count: 24,
        property_type: 'Villa',
        max_guests: 8,
        bedrooms: 4,
        bathrooms: 3,
        amenities: ['Pool', 'WiFi', 'Kitchen', 'Parking', 'Beach Access'],
        added_at: '2024-06-15T10:30:00Z',
        is_available: true,
        host_name: 'Jane Smith',
      },
      {
        id: 'wishlist-2',
        property_id: 'prop-2',
        title: 'Beachfront Cottage in Hibberdene',
        location: 'Hibberdene, KwaZulu-Natal',
        price: 350,
        images: ['/placeholder.svg'],
        rating: 4.6,
        review_count: 18,
        property_type: 'Cottage',
        max_guests: 4,
        bedrooms: 2,
        bathrooms: 2,
        amenities: ['Beach Access', 'WiFi', 'Kitchen', 'Braai Area'],
        added_at: '2024-06-10T14:20:00Z',
        is_available: true,
        host_name: 'Mike Johnson',
      },
      {
        id: 'wishlist-3',
        property_id: 'prop-3',
        title: 'Mountain Retreat in Drakensberg',
        location: 'Drakensberg, KwaZulu-Natal',
        price: 800,
        images: ['/placeholder.svg'],
        rating: 4.9,
        review_count: 31,
        property_type: 'Cabin',
        max_guests: 12,
        bedrooms: 6,
        bathrooms: 4,
        amenities: ['Mountain Views', 'Fireplace', 'WiFi', 'Kitchen', 'Hiking Trails'],
        added_at: '2024-06-05T09:15:00Z',
        is_available: false,
        host_name: 'Sarah Wilson',
      },
      {
        id: 'wishlist-4',
        property_id: 'prop-4',
        title: 'Modern Apartment in Durban',
        location: 'Durban, KwaZulu-Natal',
        price: 280,
        images: ['/placeholder.svg'],
        rating: 4.4,
        review_count: 12,
        property_type: 'Apartment',
        max_guests: 2,
        bedrooms: 1,
        bathrooms: 1,
        amenities: ['City Views', 'WiFi', 'Kitchen', 'Gym Access'],
        added_at: '2024-06-01T16:45:00Z',
        is_available: true,
        host_name: 'David Brown',
      },
      {
        id: 'wishlist-5',
        property_id: 'prop-5',
        title: 'Coastal House in Scottburgh',
        location: 'Scottburgh, KwaZulu-Natal',
        price: 450,
        images: ['/placeholder.svg'],
        rating: 4.7,
        review_count: 22,
        property_type: 'House',
        max_guests: 6,
        bedrooms: 3,
        bathrooms: 2,
        amenities: ['Ocean Views', 'Pool', 'WiFi', 'Kitchen', 'Beach Access'],
        added_at: '2024-05-28T11:30:00Z',
        is_available: true,
        host_name: 'Lisa Taylor',
      }
    ];
  }

  // Local storage fallback for wishlist management
  private getLocalWishlist(): string[] {
    try {
      const wishlist = localStorage.getItem('user_wishlist');
      return wishlist ? JSON.parse(wishlist) : [];
    } catch (error) {
      console.error('Error reading local wishlist:', error);
      return [];
    }
  }

  private saveLocalWishlist(wishlist: string[]): void {
    try {
      localStorage.setItem('user_wishlist', JSON.stringify(wishlist));
    } catch (error) {
      console.error('Error saving local wishlist:', error);
    }
  }

  addToLocalWishlist(propertyId: string): boolean {
    try {
      const wishlist = this.getLocalWishlist();
      if (!wishlist.includes(propertyId)) {
        wishlist.push(propertyId);
        this.saveLocalWishlist(wishlist);
      }
      return true;
    } catch (error) {
      console.error('Error adding to local wishlist:', error);
      return false;
    }
  }

  removeFromLocalWishlist(propertyId: string): boolean {
    try {
      const wishlist = this.getLocalWishlist();
      const updatedWishlist = wishlist.filter(id => id !== propertyId);
      this.saveLocalWishlist(updatedWishlist);
      return true;
    } catch (error) {
      console.error('Error removing from local wishlist:', error);
      return false;
    }
  }

  isInLocalWishlist(propertyId: string): boolean {
    try {
      const wishlist = this.getLocalWishlist();
      return wishlist.includes(propertyId);
    } catch (error) {
      console.error('Error checking local wishlist:', error);
      return false;
    }
  }
}

export const wishlistService = new WishlistService();
export type { WishlistProperty, ApiResponse };
