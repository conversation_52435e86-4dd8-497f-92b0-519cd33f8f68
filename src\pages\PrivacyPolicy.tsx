import React from 'react';
import { Shield, Eye, Lock, Database, Users, Globe, Calendar, AlertCircle } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';

export const PrivacyPolicy: React.FC = () => {
  const lastUpdated = "December 27, 2024";

  const sections = [
    {
      title: "1. Information We Collect",
      icon: Database,
      content: `We collect information you provide directly to us, such as when you create an account, make a booking, or contact us. This includes:

      Personal Information:
      • Name, email address, phone number
      • Profile photo and bio
      • Payment information (processed securely by our payment partners)
      • Government-issued ID for verification purposes

      Booking Information:
      • Property preferences and search history
      • Booking details and communication with hosts
      • Reviews and ratings you provide

      Automatically Collected Information:
      • Device information (IP address, browser type, operating system)
      • Usage data (pages visited, time spent, features used)
      • Location data (with your permission)
      • Cookies and similar tracking technologies`
    },
    {
      title: "2. How We Use Your Information",
      icon: Eye,
      content: `We use the information we collect to:

      Provide Our Services:
      • Process bookings and facilitate communication between guests and hosts
      • Verify your identity and prevent fraud
      • Provide customer support and respond to your inquiries
      • Send booking confirmations and important service updates

      Improve Our Platform:
      • Analyze usage patterns to enhance user experience
      • Develop new features and services
      • Conduct research and analytics
      • Personalize content and recommendations

      Marketing and Communication:
      • Send promotional emails and newsletters (with your consent)
      • Display targeted advertisements
      • Conduct surveys and market research
      • Inform you about new features and services`
    },
    {
      title: "3. Information Sharing and Disclosure",
      icon: Users,
      content: `We may share your information in the following circumstances:

      With Hosts and Guests:
      • Basic profile information to facilitate bookings
      • Contact information after a confirmed booking
      • Reviews and ratings (publicly visible)

      With Service Providers:
      • Payment processors for secure transaction handling
      • Cloud storage providers for data hosting
      • Analytics providers for usage insights
      • Customer support tools for service delivery

      Legal Requirements:
      • When required by law or legal process
      • To protect our rights, property, or safety
      • To prevent fraud or illegal activities
      • In connection with business transfers or mergers

      We do not sell your personal information to third parties for their marketing purposes.`
    },
    {
      title: "4. Data Security",
      icon: Lock,
      content: `We implement appropriate technical and organizational measures to protect your personal information:

      Technical Safeguards:
      • Encryption of data in transit and at rest
      • Secure servers and databases
      • Regular security audits and penetration testing
      • Multi-factor authentication for sensitive operations

      Organizational Measures:
      • Limited access to personal information on a need-to-know basis
      • Regular employee training on data protection
      • Incident response procedures
      • Compliance with industry security standards

      While we strive to protect your information, no method of transmission over the internet or electronic storage is 100% secure. We cannot guarantee absolute security.`
    },
    {
      title: "5. Your Rights and Choices",
      icon: Shield,
      content: `You have the following rights regarding your personal information:

      Access and Portability:
      • Request a copy of your personal information
      • Download your data in a portable format
      • View your account information and settings

      Correction and Updates:
      • Update your profile and account information
      • Correct inaccurate or incomplete data
      • Modify your communication preferences

      Deletion and Restriction:
      • Request deletion of your account and data
      • Restrict processing of your information
      • Object to certain uses of your data

      Marketing Communications:
      • Opt out of promotional emails and newsletters
      • Manage notification preferences
      • Control targeted advertising

      To exercise these rights, please contact <NAME_EMAIL>.`
    },
    {
      title: "6. Cookies and Tracking Technologies",
      icon: Globe,
      content: `We use cookies and similar technologies to enhance your experience:

      Essential Cookies:
      • Required for basic platform functionality
      • Authentication and security
      • Shopping cart and booking process

      Analytics Cookies:
      • Google Analytics for usage insights
      • Performance monitoring
      • Error tracking and debugging

      Marketing Cookies:
      • Targeted advertising
      • Social media integration
      • Conversion tracking

      You can control cookies through your browser settings. However, disabling certain cookies may affect platform functionality.`
    },
    {
      title: "7. International Data Transfers",
      icon: Globe,
      content: `StayFinder is based in South Africa, and your information may be transferred to and processed in countries other than your own. We ensure appropriate safeguards are in place:

      • Standard contractual clauses approved by relevant authorities
      • Adequacy decisions for certain countries
      • Certification schemes and codes of conduct
      • Your explicit consent where required

      We only transfer data to countries and organizations that provide adequate protection for your personal information.`
    },
    {
      title: "8. Data Retention",
      icon: Calendar,
      content: `We retain your personal information for as long as necessary to provide our services and comply with legal obligations:

      Account Information:
      • Retained while your account is active
      • Deleted within 30 days of account closure (unless required for legal purposes)

      Booking Information:
      • Retained for 7 years for tax and legal compliance
      • Transaction records kept for fraud prevention

      Marketing Data:
      • Retained until you opt out or withdraw consent
      • Automatically deleted after 3 years of inactivity

      Legal Requirements:
      • Some data may be retained longer to comply with legal obligations
      • Court orders or regulatory requirements may extend retention periods`
    },
    {
      title: "9. Children's Privacy",
      icon: Shield,
      content: `StayFinder is not intended for children under 18 years of age. We do not knowingly collect personal information from children under 18. If you are a parent or guardian and believe your child has provided us with personal information, please contact us immediately.

      If we become aware that we have collected personal information from a child under 18, we will take steps to delete such information from our systems.`
    },
    {
      title: "10. Changes to This Privacy Policy",
      icon: AlertCircle,
      content: `We may update this Privacy Policy from time to time to reflect changes in our practices or applicable laws. We will:

      • Post the updated policy on our website
      • Notify you by email for material changes
      • Provide at least 30 days notice before changes take effect
      • Update the "Last Updated" date at the top of this policy

      Your continued use of our services after the effective date constitutes acceptance of the updated Privacy Policy.`
    },
    {
      title: "11. Contact Us",
      icon: Users,
      content: `If you have any questions, concerns, or requests regarding this Privacy Policy or our data practices, please contact us:

      Privacy Officer
      Email: <EMAIL>
      Phone: +27 21 123 4567
      Address: 123 Long Street, Cape Town, 8001, South Africa

      Data Protection Officer (for EU residents)
      Email: <EMAIL>

      We will respond to your inquiry within 30 days of receipt.`
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-sea-green-600 to-ocean-blue-600 text-white py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <Shield className="h-16 w-16 mx-auto mb-6 opacity-90" />
            <h1 className="text-4xl font-bold mb-4">Privacy Policy</h1>
            <p className="text-xl opacity-90 mb-6">
              Your privacy is important to us. Learn how we collect, use, and protect your information.
            </p>
            <div className="flex items-center justify-center gap-4 text-sm">
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                <span>Last updated: {lastUpdated}</span>
              </div>
              <Badge variant="secondary" className="bg-white/20 text-white">
                GDPR Compliant
              </Badge>
            </div>
          </div>
        </div>
      </section>

      <div className="container mx-auto px-4 py-12">
        <div className="max-w-4xl mx-auto">
          {/* Important Notice */}
          <Card className="mb-8 border-blue-200 bg-blue-50">
            <CardContent className="p-6">
              <div className="flex items-start gap-3">
                <Shield className="h-6 w-6 text-blue-600 flex-shrink-0 mt-0.5" />
                <div>
                  <h3 className="font-semibold text-blue-800 mb-2">Your Privacy Rights</h3>
                  <p className="text-blue-700 text-sm leading-relaxed">
                    This Privacy Policy explains how StayFinder collects, uses, and protects your personal information. 
                    We are committed to transparency and giving you control over your data. You have the right to access, 
                    correct, or delete your personal information at any time.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Quick Summary */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Eye className="h-5 w-5" />
                Privacy at a Glance
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-3 gap-4">
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <Lock className="h-8 w-8 mx-auto mb-2 text-green-600" />
                  <h4 className="font-semibold text-green-800">Secure</h4>
                  <p className="text-sm text-green-700">Your data is encrypted and protected</p>
                </div>
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <Users className="h-8 w-8 mx-auto mb-2 text-blue-600" />
                  <h4 className="font-semibold text-blue-800">Transparent</h4>
                  <p className="text-sm text-blue-700">Clear about what we collect and why</p>
                </div>
                <div className="text-center p-4 bg-purple-50 rounded-lg">
                  <Shield className="h-8 w-8 mx-auto mb-2 text-purple-600" />
                  <h4 className="font-semibold text-purple-800">Your Control</h4>
                  <p className="text-sm text-purple-700">You decide how your data is used</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Privacy Sections */}
          <div className="space-y-6">
            {sections.map((section, index) => {
              const IconComponent = section.icon;
              return (
                <Card key={index} id={`section-${index + 1}`}>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-3 text-xl text-gray-900">
                      <div className="w-10 h-10 bg-sea-green-100 rounded-lg flex items-center justify-center">
                        <IconComponent className="h-5 w-5 text-sea-green-600" />
                      </div>
                      {section.title}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="prose prose-gray max-w-none">
                      {section.content.split('\n').map((paragraph, pIndex) => {
                        if (paragraph.trim().startsWith('•')) {
                          return (
                            <ul key={pIndex} className="list-disc list-inside ml-4 mb-4">
                              <li className="text-gray-700 leading-relaxed">
                                {paragraph.trim().substring(1).trim()}
                              </li>
                            </ul>
                          );
                        } else if (paragraph.trim().endsWith(':')) {
                          return (
                            <h4 key={pIndex} className="font-semibold text-gray-800 mt-4 mb-2">
                              {paragraph.trim()}
                            </h4>
                          );
                        } else if (paragraph.trim()) {
                          return (
                            <p key={pIndex} className="text-gray-700 leading-relaxed mb-4">
                              {paragraph.trim()}
                            </p>
                          );
                        }
                        return null;
                      })}
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>

          {/* Footer */}
          <Card className="mt-12 bg-gradient-to-r from-sea-green-50 to-ocean-blue-50 border-sea-green-200">
            <CardContent className="p-8 text-center">
              <Shield className="h-12 w-12 mx-auto mb-4 text-sea-green-600" />
              <h3 className="text-xl font-semibold mb-4">Questions about your privacy?</h3>
              <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
                We're committed to protecting your privacy and being transparent about our data practices. 
                If you have any questions or concerns, our privacy team is here to help.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <a
                  href="mailto:<EMAIL>"
                  className="inline-flex items-center justify-center px-6 py-3 bg-sea-green-600 text-white rounded-lg hover:bg-sea-green-700 transition-colors"
                >
                  Contact Privacy Team
                </a>
                <a
                  href="/terms-of-service"
                  className="inline-flex items-center justify-center px-6 py-3 border border-sea-green-600 text-sea-green-600 rounded-lg hover:bg-sea-green-50 transition-colors"
                >
                  View Terms of Service
                </a>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};
