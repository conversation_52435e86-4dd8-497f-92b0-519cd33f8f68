import React, { useState, useRef } from 'react';
import { Camera, Upload, X, User, Check, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { toast } from '@/components/ui/use-toast';

interface ProfilePhotoUploadProps {
  currentPhoto?: string;
  onPhotoChange: (photo: string | null) => void;
  className?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  allowRemove?: boolean;
  maxFileSize?: number; // in MB
}

export const ProfilePhotoUpload: React.FC<ProfilePhotoUploadProps> = ({
  currentPhoto,
  onPhotoChange,
  className,
  size = 'lg',
  allowRemove = true,
  maxFileSize = 5
}) => {
  const [isUploading, setIsUploading] = useState(false);
  const [dragActive, setDragActive] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(currentPhoto || null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const sizeClasses = {
    sm: 'w-16 h-16',
    md: 'w-24 h-24',
    lg: 'w-32 h-32',
    xl: 'w-40 h-40'
  };

  const iconSizes = {
    sm: 'h-6 w-6',
    md: 'h-8 w-8',
    lg: 'h-12 w-12',
    xl: 'h-16 w-16'
  };

  const validateFile = (file: File): boolean => {
    // Check file type
    if (!file.type.startsWith('image/')) {
      toast({
        title: "Invalid file type",
        description: "Please select an image file (JPG, PNG, GIF, WebP)",
        variant: "destructive"
      });
      return false;
    }

    // Check file size
    if (file.size > maxFileSize * 1024 * 1024) {
      toast({
        title: "File too large",
        description: `Please select an image smaller than ${maxFileSize}MB`,
        variant: "destructive"
      });
      return false;
    }

    return true;
  };

  const processFile = async (file: File) => {
    if (!validateFile(file)) return;

    setIsUploading(true);

    try {
      // Create preview URL
      const url = URL.createObjectURL(file);
      setPreviewUrl(url);

      // In a real app, you would upload to a server here
      // For now, we'll simulate an upload delay
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Convert to base64 for demo purposes
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        onPhotoChange(result);
        toast({
          title: "Photo uploaded successfully",
          description: "Your profile photo has been updated",
        });
      };
      reader.readAsDataURL(file);

    } catch (error) {
      console.error('Upload error:', error);
      toast({
        title: "Upload failed",
        description: "Failed to upload photo. Please try again.",
        variant: "destructive"
      });
      setPreviewUrl(currentPhoto || null);
    } finally {
      setIsUploading(false);
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      processFile(file);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragActive(false);

    const file = e.dataTransfer.files?.[0];
    if (file) {
      processFile(file);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setDragActive(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragActive(false);
  };

  const handleRemovePhoto = () => {
    setPreviewUrl(null);
    onPhotoChange(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
    toast({
      title: "Photo removed",
      description: "Your profile photo has been removed",
    });
  };

  const openFileDialog = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className={cn("relative", className)}>
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileSelect}
        className="hidden"
      />

      <div className="flex flex-col items-center space-y-4">
        {/* Photo Display/Upload Area */}
        <div
          className={cn(
            sizeClasses[size],
            "relative rounded-full overflow-hidden border-4 border-gray-200 bg-gray-50 transition-all duration-200",
            dragActive && "border-sea-green-500 bg-sea-green-50",
            "hover:border-gray-300 cursor-pointer group"
          )}
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onClick={openFileDialog}
        >
          {previewUrl ? (
            <>
              <img
                src={previewUrl}
                alt="Profile"
                className="w-full h-full object-cover"
              />
              
              {/* Overlay on hover */}
              <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center">
                <Camera className={cn(iconSizes[size], "text-white")} />
              </div>
            </>
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              {isUploading ? (
                <div className="animate-spin rounded-full border-2 border-sea-green-500 border-t-transparent w-8 h-8" />
              ) : (
                <User className={cn(iconSizes[size], "text-gray-400")} />
              )}
            </div>
          )}

          {/* Upload indicator */}
          {isUploading && (
            <div className="absolute inset-0 bg-white/80 flex items-center justify-center">
              <div className="text-center">
                <div className="animate-spin rounded-full border-2 border-sea-green-500 border-t-transparent w-6 h-6 mx-auto mb-2" />
                <p className="text-xs text-gray-600">Uploading...</p>
              </div>
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex items-center space-x-2">
          <Button
            onClick={openFileDialog}
            variant="outline"
            size="sm"
            disabled={isUploading}
            className="flex items-center gap-2"
          >
            <Upload className="h-4 w-4" />
            {previewUrl ? 'Change Photo' : 'Upload Photo'}
          </Button>

          {allowRemove && previewUrl && (
            <Button
              onClick={handleRemovePhoto}
              variant="outline"
              size="sm"
              disabled={isUploading}
              className="flex items-center gap-2 text-red-600 hover:text-red-700 hover:bg-red-50"
            >
              <X className="h-4 w-4" />
              Remove
            </Button>
          )}
        </div>

        {/* Upload Instructions */}
        <div className="text-center text-sm text-gray-500 max-w-xs">
          <p>Click to upload or drag and drop</p>
          <p className="text-xs mt-1">
            JPG, PNG, GIF or WebP (max {maxFileSize}MB)
          </p>
        </div>
      </div>
    </div>
  );
};

// Compact version for smaller spaces
export const CompactProfilePhotoUpload: React.FC<{
  currentPhoto?: string;
  onPhotoChange: (photo: string | null) => void;
  className?: string;
}> = ({ currentPhoto, onPhotoChange, className }) => {
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    if (!file.type.startsWith('image/')) {
      toast({
        title: "Invalid file type",
        description: "Please select an image file",
        variant: "destructive"
      });
      return;
    }

    setIsUploading(true);

    try {
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        onPhotoChange(result);
        toast({
          title: "Photo updated",
          description: "Your profile photo has been updated",
        });
      };
      reader.readAsDataURL(file);
    } catch (error) {
      toast({
        title: "Upload failed",
        description: "Failed to upload photo",
        variant: "destructive"
      });
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className={cn("relative", className)}>
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileSelect}
        className="hidden"
      />

      <div className="relative w-20 h-20 rounded-full overflow-hidden border-2 border-gray-200 bg-gray-50 cursor-pointer hover:border-gray-300 transition-colors">
        {currentPhoto ? (
          <img
            src={currentPhoto}
            alt="Profile"
            className="w-full h-full object-cover"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center">
            <User className="h-8 w-8 text-gray-400" />
          </div>
        )}

        <button
          onClick={() => fileInputRef.current?.click()}
          disabled={isUploading}
          className="absolute inset-0 bg-black/50 opacity-0 hover:opacity-100 transition-opacity duration-200 flex items-center justify-center"
        >
          {isUploading ? (
            <div className="animate-spin rounded-full border-2 border-white border-t-transparent w-5 h-5" />
          ) : (
            <Camera className="h-5 w-5 text-white" />
          )}
        </button>
      </div>
    </div>
  );
};
