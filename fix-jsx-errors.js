#!/usr/bin/env node

/**
 * Fix JSX Syntax Errors in StayFinder Frontend
 * Addresses the compilation errors preventing the frontend from starting
 */

import fs from 'fs';
import path from 'path';

// ANSI color codes
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, colors.green);
}

function logError(message) {
  log(`❌ ${message}`, colors.red);
}

function logWarning(message) {
  log(`⚠️  ${message}`, colors.yellow);
}

function logInfo(message) {
  log(`ℹ️  ${message}`, colors.blue);
}

// Fix functions for each file
const fixes = [
  {
    file: 'src/components/Hero.tsx',
    description: 'Fix JSX comment syntax',
    fix: (content) => {
      // Fix JSX comment syntax
      return content.replace(
        /\s*\/\/ Fallback destinations if API fails/g,
        '\n                          {/* Fallback destinations if API fails */}'
      );
    }
  },
  {
    file: 'src/components/MapSearch.tsx',
    description: 'Fix SlideIn tag closure',
    fix: (content) => {
      // Ensure SlideIn tag is properly closed
      if (!content.includes('</SlideIn>')) {
        return content.replace(
          /(\s*<\/div>\s*)\);(\s*)$/,
          '$1</SlideIn>\n  );\n$2'
        );
      }
      return content;
    }
  },
  {
    file: 'src/components/PropertyAnalytics.tsx',
    description: 'Fix SlideIn tag closure',
    fix: (content) => {
      // Ensure SlideIn tag is properly closed
      if (!content.includes('</SlideIn>')) {
        return content.replace(
          /(\s*<\/div>\s*)\);(\s*)$/,
          '$1</SlideIn>\n  );\n$2'
        );
      }
      return content;
    }
  },
  {
    file: 'src/pages/UserDashboard.tsx',
    description: 'Fix conditional structure and tag closures',
    fix: (content) => {
      // Fix the structure issues
      let fixed = content;
      
      // Fix missing opening brace for conditional
      fixed = fixed.replace(
        /(\s*{\/\* Account Management \*\/}\s*<div className="grid)/,
        '$1'
      );
      
      // Ensure proper closing structure
      fixed = fixed.replace(
        /(\s*<\/div>\s*)\)\}/g,
        '$1)}'
      );
      
      return fixed;
    }
  }
];

async function fixJSXErrors() {
  log('🔧 FIXING JSX SYNTAX ERRORS', colors.bright);
  log('='.repeat(50), colors.cyan);
  
  let totalFixes = 0;
  let successfulFixes = 0;
  
  for (const fix of fixes) {
    totalFixes++;
    logInfo(`Fixing ${fix.file}...`);
    
    try {
      const filePath = fix.file;
      
      if (!fs.existsSync(filePath)) {
        logWarning(`File not found: ${filePath}`);
        continue;
      }
      
      const originalContent = fs.readFileSync(filePath, 'utf8');
      const fixedContent = fix.fix(originalContent);
      
      if (originalContent !== fixedContent) {
        fs.writeFileSync(filePath, fixedContent, 'utf8');
        logSuccess(`${fix.description} - Fixed`);
        successfulFixes++;
      } else {
        logInfo(`${fix.description} - No changes needed`);
        successfulFixes++;
      }
      
    } catch (error) {
      logError(`Failed to fix ${fix.file}: ${error.message}`);
    }
  }
  
  // Generate report
  log('\n' + '='.repeat(50), colors.cyan);
  log('📊 JSX FIXES REPORT', colors.bright);
  log('='.repeat(50), colors.cyan);
  
  logInfo(`Files processed: ${totalFixes}`);
  logInfo(`Successful fixes: ${successfulFixes}`);
  
  if (successfulFixes === totalFixes) {
    logSuccess('✨ All JSX errors have been fixed!');
    log('\n🎯 Next Steps:', colors.blue);
    log('1. Try starting the frontend server again', colors.blue);
    log('2. Run: npm run dev', colors.blue);
    log('3. Check for any remaining compilation errors', colors.blue);
  } else {
    logWarning('⚠️  Some fixes may need manual attention');
    log('\n🔧 Manual Steps:', colors.yellow);
    log('1. Check the files that failed to fix', colors.yellow);
    log('2. Manually correct any remaining syntax errors', colors.yellow);
    log('3. Ensure all JSX tags are properly closed', colors.yellow);
  }
  
  return successfulFixes === totalFixes;
}

// Additional manual fixes that might be needed
function suggestManualFixes() {
  log('\n📋 COMMON JSX ISSUES TO CHECK:', colors.cyan);
  log('1. Ensure all JSX comments use {/* */} syntax', colors.blue);
  log('2. Check that all opening tags have matching closing tags', colors.blue);
  log('3. Verify conditional statements have proper braces', colors.blue);
  log('4. Make sure all JSX expressions are properly closed', colors.blue);
  log('5. Check for unterminated strings or regular expressions', colors.blue);
}

// Main function
async function main() {
  const success = await fixJSXErrors();
  suggestManualFixes();
  
  process.exit(success ? 0 : 1);
}

// Run the fixes
main().catch(error => {
  logError(`Fix script error: ${error.message}`);
  console.error(error);
  process.exit(1);
});
