interface UserProfile {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone?: string;
  date_of_birth?: string;
  profile_picture?: string;
  bio?: string;
  location?: string;
  languages?: string[];
  occupation?: string;
  emergency_contact_name?: string;
  emergency_contact_phone?: string;
  email_verified: boolean;
  phone_verified: boolean;
  id_verified: boolean;
  created_at: string;
  preferences?: {
    currency?: string;
    language?: string;
    timezone?: string;
    marketing_emails?: boolean;
    booking_notifications?: boolean;
    review_notifications?: boolean;
  };
  notification_settings?: {
    email_notifications?: boolean;
    sms_notifications?: boolean;
    push_notifications?: boolean;
    marketing_communications?: boolean;
    booking_updates?: boolean;
    review_reminders?: boolean;
    price_alerts?: boolean;
  };
  total_bookings: number;
  total_reviews: number;
  average_rating: number;
}

interface UserProfileUpdate {
  first_name?: string;
  last_name?: string;
  phone?: string;
  date_of_birth?: string;
  bio?: string;
  location?: string;
  languages?: string[];
  occupation?: string;
  emergency_contact_name?: string;
  emergency_contact_phone?: string;
  preferences?: {
    currency?: string;
    language?: string;
    timezone?: string;
    marketing_emails?: boolean;
    booking_notifications?: boolean;
    review_notifications?: boolean;
  };
  notification_settings?: {
    email_notifications?: boolean;
    sms_notifications?: boolean;
    push_notifications?: boolean;
    marketing_communications?: boolean;
    booking_updates?: boolean;
    review_reminders?: boolean;
    price_alerts?: boolean;
  };
}

interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

class UserProfileService {
  private baseUrl = 'http://localhost:3001/api/user-profile';

  private async makeRequest<T>(url: string, options: RequestInit = {}): Promise<ApiResponse<T>> {
    try {
      const token = localStorage.getItem('auth_token');
      const headers: HeadersInit = {
        ...options.headers,
      };

      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      // Don't set Content-Type for FormData (file uploads)
      if (!(options.body instanceof FormData)) {
        headers['Content-Type'] = 'application/json';
      }

      const response = await fetch(url, {
        ...options,
        headers,
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('User Profile API error:', error);
      throw error;
    }
  }

  async getUserProfile(): Promise<UserProfile> {
    try {
      const response = await this.makeRequest<UserProfile>(this.baseUrl);
      
      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to fetch user profile');
      }
    } catch (error) {
      console.error('Error fetching user profile:', error);
      throw error;
    }
  }

  async updateUserProfile(updates: UserProfileUpdate): Promise<UserProfile> {
    try {
      const response = await this.makeRequest<UserProfile>(this.baseUrl, {
        method: 'PUT',
        body: JSON.stringify(updates),
      });
      
      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to update user profile');
      }
    } catch (error) {
      console.error('Error updating user profile:', error);
      throw error;
    }
  }

  async uploadProfilePicture(file: File): Promise<string> {
    try {
      const formData = new FormData();
      formData.append('profile_picture', file);

      const response = await this.makeRequest<{ profile_picture_url: string }>(
        `${this.baseUrl}?action=upload_profile_picture`,
        {
          method: 'POST',
          body: formData,
        }
      );
      
      if (response.success && response.data) {
        return response.data.profile_picture_url;
      } else {
        throw new Error(response.error || 'Failed to upload profile picture');
      }
    } catch (error) {
      console.error('Error uploading profile picture:', error);
      throw error;
    }
  }

  async uploadIdDocument(file: File): Promise<string> {
    try {
      const formData = new FormData();
      formData.append('id_document', file);

      const response = await this.makeRequest<{ id_document_path: string }>(
        `${this.baseUrl}?action=upload_id_document`,
        {
          method: 'POST',
          body: formData,
        }
      );
      
      if (response.success && response.data) {
        return response.data.id_document_path;
      } else {
        throw new Error(response.error || 'Failed to upload ID document');
      }
    } catch (error) {
      console.error('Error uploading ID document:', error);
      throw error;
    }
  }

  async verifyEmail(verificationCode: string): Promise<boolean> {
    try {
      const response = await this.makeRequest<void>(
        `${this.baseUrl}?action=verify_email`,
        {
          method: 'POST',
          body: JSON.stringify({ verification_code: verificationCode }),
        }
      );
      
      return response.success;
    } catch (error) {
      console.error('Error verifying email:', error);
      throw error;
    }
  }

  async verifyPhone(verificationCode: string): Promise<boolean> {
    try {
      const response = await this.makeRequest<void>(
        `${this.baseUrl}?action=verify_phone`,
        {
          method: 'POST',
          body: JSON.stringify({ verification_code: verificationCode }),
        }
      );
      
      return response.success;
    } catch (error) {
      console.error('Error verifying phone:', error);
      throw error;
    }
  }

  async sendEmailVerification(): Promise<boolean> {
    try {
      // In a real implementation, this would trigger an email
      // For now, we'll just return true
      console.log('Email verification sent (mock)');
      return true;
    } catch (error) {
      console.error('Error sending email verification:', error);
      throw error;
    }
  }

  async sendPhoneVerification(): Promise<boolean> {
    try {
      // In a real implementation, this would trigger an SMS
      // For now, we'll just return true
      console.log('Phone verification sent (mock)');
      return true;
    } catch (error) {
      console.error('Error sending phone verification:', error);
      throw error;
    }
  }

  validateProfileData(data: UserProfileUpdate): { isValid: boolean; errors: Record<string, string> } {
    const errors: Record<string, string> = {};

    if (data.first_name && data.first_name.trim().length < 2) {
      errors.first_name = 'First name must be at least 2 characters long';
    }

    if (data.last_name && data.last_name.trim().length < 2) {
      errors.last_name = 'Last name must be at least 2 characters long';
    }

    if (data.phone && !/^\+?[1-9]\d{1,14}$/.test(data.phone.replace(/\s/g, ''))) {
      errors.phone = 'Please enter a valid phone number';
    }

    if (data.date_of_birth) {
      const birthDate = new Date(data.date_of_birth);
      const today = new Date();
      const age = today.getFullYear() - birthDate.getFullYear();
      
      if (age < 18) {
        errors.date_of_birth = 'You must be at least 18 years old';
      }
      
      if (age > 120) {
        errors.date_of_birth = 'Please enter a valid birth date';
      }
    }

    if (data.bio && data.bio.length > 500) {
      errors.bio = 'Bio must be less than 500 characters';
    }

    if (data.emergency_contact_phone && !/^\+?[1-9]\d{1,14}$/.test(data.emergency_contact_phone.replace(/\s/g, ''))) {
      errors.emergency_contact_phone = 'Please enter a valid emergency contact phone number';
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  }

  validateFile(file: File, type: 'profile_picture' | 'id_document'): { isValid: boolean; error?: string } {
    if (type === 'profile_picture') {
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
      const maxSize = 5 * 1024 * 1024; // 5MB

      if (!allowedTypes.includes(file.type)) {
        return { isValid: false, error: 'Only JPEG, PNG, and WebP images are allowed' };
      }

      if (file.size > maxSize) {
        return { isValid: false, error: 'File size must be less than 5MB' };
      }
    } else if (type === 'id_document') {
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'];
      const maxSize = 10 * 1024 * 1024; // 10MB

      if (!allowedTypes.includes(file.type)) {
        return { isValid: false, error: 'Only JPEG, PNG, and PDF files are allowed' };
      }

      if (file.size > maxSize) {
        return { isValid: false, error: 'File size must be less than 10MB' };
      }
    }

    return { isValid: true };
  }
}

export const userProfileService = new UserProfileService();
export type { UserProfile, UserProfileUpdate, ApiResponse };
