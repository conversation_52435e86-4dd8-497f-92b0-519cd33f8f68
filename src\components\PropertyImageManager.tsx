import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ImageUpload } from './ImageUpload';
import { 
  Image as ImageIcon, 
  Star, 
  Trash2, 
  Edit, 
  Move,
  Eye,
  Upload,
  Download,
  RotateCcw,
  Crop
} from 'lucide-react';

interface PropertyImage {
  id: string;
  url: string;
  alt: string;
  isPrimary: boolean;
  size: number;
  uploadedAt: string;
}

interface PropertyImageManagerProps {
  propertyId: string;
  propertyTitle: string;
  images: PropertyImage[];
  onImagesUpdate: (images: PropertyImage[]) => void;
  maxImages?: number;
  className?: string;
}

export const PropertyImageManager: React.FC<PropertyImageManagerProps> = ({
  propertyId,
  propertyTitle,
  images,
  onImagesUpdate,
  maxImages = 10,
  className
}) => {
  const [isUploading, setIsUploading] = useState(false);
  const [selectedImages, setSelectedImages] = useState<string[]>([]);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  // Convert PropertyImage to ImageFile format for ImageUpload component
  const convertToImageFiles = (propertyImages: PropertyImage[]) => {
    return propertyImages.map(img => ({
      id: img.id,
      file: new File([], img.alt || 'image'), // Placeholder file
      preview: img.url,
      isPrimary: img.isPrimary,
      uploading: false
    }));
  };

  // Handle new image uploads
  const handleImageUpload = async (imageFiles: any[]) => {
    setIsUploading(true);
    try {
      // TODO: Implement actual image upload to server
      // For now, simulate upload with local preview URLs
      const newImages: PropertyImage[] = imageFiles
        .filter(file => !images.find(img => img.id === file.id))
        .map(file => ({
          id: file.id,
          url: file.preview,
          alt: file.file.name,
          isPrimary: file.isPrimary,
          size: file.file.size,
          uploadedAt: new Date().toISOString()
        }));

      onImagesUpdate([...images, ...newImages]);
    } catch (error) {
      console.error('Error uploading images:', error);
      alert('Failed to upload images. Please try again.');
    } finally {
      setIsUploading(false);
    }
  };

  const setPrimaryImage = (imageId: string) => {
    const updatedImages = images.map(img => ({
      ...img,
      isPrimary: img.id === imageId
    }));
    onImagesUpdate(updatedImages);
  };

  const deleteImage = (imageId: string) => {
    const confirmed = window.confirm('Are you sure you want to delete this image? This action cannot be undone.');
    if (!confirmed) return;

    const updatedImages = images.filter(img => img.id !== imageId);
    
    // If we deleted the primary image, make the first remaining image primary
    if (updatedImages.length > 0 && !updatedImages.some(img => img.isPrimary)) {
      updatedImages[0].isPrimary = true;
    }
    
    onImagesUpdate(updatedImages);
  };

  const deleteSelectedImages = () => {
    if (selectedImages.length === 0) return;
    
    const confirmed = window.confirm(
      `Are you sure you want to delete ${selectedImages.length} selected image(s)? This action cannot be undone.`
    );
    if (!confirmed) return;

    const updatedImages = images.filter(img => !selectedImages.includes(img.id));
    
    // If we deleted the primary image, make the first remaining image primary
    if (updatedImages.length > 0 && !updatedImages.some(img => img.isPrimary)) {
      updatedImages[0].isPrimary = true;
    }
    
    onImagesUpdate(updatedImages);
    setSelectedImages([]);
  };

  const toggleImageSelection = (imageId: string) => {
    setSelectedImages(prev => 
      prev.includes(imageId) 
        ? prev.filter(id => id !== imageId)
        : [...prev, imageId]
    );
  };

  const selectAllImages = () => {
    setSelectedImages(images.map(img => img.id));
  };

  const clearSelection = () => {
    setSelectedImages([]);
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <ImageIcon className="h-5 w-5" />
              Property Images
              {images.length > 0 && (
                <Badge variant="secondary">{images.length}/{maxImages}</Badge>
              )}
            </CardTitle>
            
            <div className="flex items-center gap-2">
              {selectedImages.length > 0 && (
                <>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={clearSelection}
                  >
                    Clear ({selectedImages.length})
                  </Button>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={deleteSelectedImages}
                  >
                    <Trash2 className="h-3 w-3 mr-1" />
                    Delete Selected
                  </Button>
                </>
              )}
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
              >
                {viewMode === 'grid' ? 'List View' : 'Grid View'}
              </Button>
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Image Upload */}
          <ImageUpload
            images={convertToImageFiles(images)}
            onImagesChange={handleImageUpload}
            maxImages={maxImages}
            maxFileSize={5}
          />

          {/* Image Management */}
          {images.length > 0 && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium">Manage Images</h3>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={selectedImages.length === images.length ? clearSelection : selectAllImages}
                  >
                    {selectedImages.length === images.length ? 'Deselect All' : 'Select All'}
                  </Button>
                </div>
              </div>

              {viewMode === 'grid' ? (
                /* Grid View */
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {images.map((image) => (
                    <div
                      key={image.id}
                      className={`relative group border-2 rounded-lg overflow-hidden transition-all ${
                        selectedImages.includes(image.id)
                          ? 'border-sea-green-500 ring-2 ring-sea-green-200'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      {/* Selection Checkbox */}
                      <div className="absolute top-2 left-2 z-10">
                        <input
                          type="checkbox"
                          checked={selectedImages.includes(image.id)}
                          onChange={() => toggleImageSelection(image.id)}
                          className="w-4 h-4 text-sea-green-600 bg-white border-gray-300 rounded focus:ring-sea-green-500"
                        />
                      </div>

                      {/* Primary Badge */}
                      {image.isPrimary && (
                        <div className="absolute top-2 right-2 z-10 bg-yellow-500 text-white px-2 py-1 rounded text-xs font-medium">
                          Primary
                        </div>
                      )}

                      {/* Image */}
                      <div className="aspect-square relative">
                        <img
                          src={image.url}
                          alt={image.alt}
                          className="w-full h-full object-cover"
                        />
                        
                        {/* Hover Controls */}
                        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-200 flex items-center justify-center">
                          <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex gap-1">
                            <Button
                              size="sm"
                              variant="secondary"
                              onClick={() => setPrimaryImage(image.id)}
                              className="h-8 w-8 p-0"
                              title="Set as primary"
                            >
                              <Star className={`h-3 w-3 ${image.isPrimary ? 'fill-yellow-400 text-yellow-400' : ''}`} />
                            </Button>
                            
                            <Button
                              size="sm"
                              variant="secondary"
                              onClick={() => window.open(image.url, '_blank')}
                              className="h-8 w-8 p-0"
                              title="View full size"
                            >
                              <Eye className="h-3 w-3" />
                            </Button>
                            
                            <Button
                              size="sm"
                              variant="destructive"
                              onClick={() => deleteImage(image.id)}
                              className="h-8 w-8 p-0"
                              title="Delete image"
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      </div>

                      {/* Image Info */}
                      <div className="p-2 bg-gray-50">
                        <p className="text-xs text-gray-600 truncate" title={image.alt}>
                          {image.alt}
                        </p>
                        <p className="text-xs text-gray-500">
                          {formatFileSize(image.size)} • {formatDate(image.uploadedAt)}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                /* List View */
                <div className="space-y-2">
                  {images.map((image) => (
                    <div
                      key={image.id}
                      className={`flex items-center gap-4 p-3 border rounded-lg transition-all ${
                        selectedImages.includes(image.id)
                          ? 'border-sea-green-500 bg-sea-green-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      {/* Selection Checkbox */}
                      <input
                        type="checkbox"
                        checked={selectedImages.includes(image.id)}
                        onChange={() => toggleImageSelection(image.id)}
                        className="w-4 h-4 text-sea-green-600 bg-white border-gray-300 rounded focus:ring-sea-green-500"
                      />

                      {/* Thumbnail */}
                      <div className="w-16 h-16 rounded overflow-hidden flex-shrink-0">
                        <img
                          src={image.url}
                          alt={image.alt}
                          className="w-full h-full object-cover"
                        />
                      </div>

                      {/* Image Info */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <p className="font-medium text-gray-900 truncate">{image.alt}</p>
                          {image.isPrimary && (
                            <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                              Primary
                            </Badge>
                          )}
                        </div>
                        <p className="text-sm text-gray-600">
                          {formatFileSize(image.size)} • Uploaded {formatDate(image.uploadedAt)}
                        </p>
                      </div>

                      {/* Actions */}
                      <div className="flex items-center gap-1">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => setPrimaryImage(image.id)}
                          title="Set as primary"
                        >
                          <Star className={`h-3 w-3 ${image.isPrimary ? 'fill-yellow-400 text-yellow-400' : ''}`} />
                        </Button>
                        
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => window.open(image.url, '_blank')}
                          title="View full size"
                        >
                          <Eye className="h-3 w-3" />
                        </Button>
                        
                        <Button
                          size="sm"
                          variant="destructive"
                          onClick={() => deleteImage(image.id)}
                          title="Delete image"
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
