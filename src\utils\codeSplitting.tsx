import React, { Suspense, lazy, ComponentType } from 'react';
import { Loader2, AlertCircle } from 'lucide-react';

// Loading component for code splitting
export const LoadingSpinner: React.FC<{ message?: string }> = ({ 
  message = "Loading..." 
}) => (
  <div className="flex items-center justify-center min-h-[200px] w-full">
    <div className="text-center">
      <Loader2 className="h-8 w-8 animate-spin text-sea-green-500 mx-auto mb-3" />
      <p className="text-gray-600 text-sm">{message}</p>
    </div>
  </div>
);

// Error boundary for code splitting
interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

export class CodeSplitErrorBoundary extends React.Component<
  React.PropsWithChildren<{ fallback?: React.ComponentType<{ error?: Error }> }>,
  ErrorBoundaryState
> {
  constructor(props: React.PropsWithChildren<{ fallback?: React.ComponentType<{ error?: Error }> }>) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Code splitting error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      const FallbackComponent = this.props.fallback || DefaultErrorFallback;
      return <FallbackComponent error={this.state.error} />;
    }

    return this.props.children;
  }
}

// Default error fallback component
const DefaultErrorFallback: React.FC<{ error?: Error }> = ({ error }) => (
  <div className="flex items-center justify-center min-h-[200px] w-full">
    <div className="text-center max-w-md mx-auto p-6">
      <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
      <h3 className="text-lg font-semibold text-gray-900 mb-2">
        Failed to load component
      </h3>
      <p className="text-gray-600 text-sm mb-4">
        There was an error loading this part of the application.
      </p>
      <button
        onClick={() => window.location.reload()}
        className="px-4 py-2 bg-sea-green-500 text-white rounded-lg hover:bg-sea-green-600 transition-colors"
      >
        Reload Page
      </button>
      {process.env.NODE_ENV === 'development' && error && (
        <details className="mt-4 text-left">
          <summary className="cursor-pointer text-sm text-gray-500">
            Error Details
          </summary>
          <pre className="mt-2 text-xs text-red-600 bg-red-50 p-2 rounded overflow-auto">
            {error.message}
            {error.stack}
          </pre>
        </details>
      )}
    </div>
  </div>
);

// Higher-order component for lazy loading with error boundary
export function withCodeSplitting<T extends ComponentType<any>>(
  importFunc: () => Promise<{ default: T }>,
  options: {
    fallback?: React.ComponentType;
    errorFallback?: React.ComponentType<{ error?: Error }>;
    loadingMessage?: string;
  } = {}
) {
  const LazyComponent = lazy(importFunc);
  
  const WrappedComponent: React.FC<React.ComponentProps<T>> = (props) => {
    const LoadingComponent = options.fallback || (() => (
      <LoadingSpinner message={options.loadingMessage} />
    ));

    return (
      <CodeSplitErrorBoundary fallback={options.errorFallback}>
        <Suspense fallback={<LoadingComponent />}>
          <LazyComponent {...props} />
        </Suspense>
      </CodeSplitErrorBoundary>
    );
  };

  WrappedComponent.displayName = `withCodeSplitting(${LazyComponent.displayName || 'Component'})`;
  
  return WrappedComponent;
}

// Preload function for critical routes
export function preloadComponent(importFunc: () => Promise<{ default: ComponentType<any> }>) {
  return importFunc();
}

// Route-based code splitting helpers
export const createLazyRoute = (
  importFunc: () => Promise<{ default: ComponentType<any> }>,
  loadingMessage?: string
) => {
  return withCodeSplitting(importFunc, { loadingMessage });
};

// Lazy load components with retry logic
export function lazyWithRetry<T extends ComponentType<any>>(
  importFunc: () => Promise<{ default: T }>,
  maxRetries: number = 3
) {
  return lazy(() => {
    return new Promise<{ default: T }>((resolve, reject) => {
      let retries = 0;
      
      const attemptImport = () => {
        importFunc()
          .then(resolve)
          .catch((error) => {
            if (retries < maxRetries) {
              retries++;
              console.warn(`Import failed, retrying... (${retries}/${maxRetries})`, error);
              setTimeout(attemptImport, 1000 * retries); // Exponential backoff
            } else {
              reject(error);
            }
          });
      };
      
      attemptImport();
    });
  });
}

// Performance monitoring for code splitting
export class CodeSplitPerformanceMonitor {
  private static instance: CodeSplitPerformanceMonitor;
  private loadTimes: Map<string, number> = new Map();
  private loadCounts: Map<string, number> = new Map();

  static getInstance(): CodeSplitPerformanceMonitor {
    if (!CodeSplitPerformanceMonitor.instance) {
      CodeSplitPerformanceMonitor.instance = new CodeSplitPerformanceMonitor();
    }
    return CodeSplitPerformanceMonitor.instance;
  }

  startTiming(componentName: string): void {
    const startTime = performance.now();
    this.loadTimes.set(`${componentName}_start`, startTime);
  }

  endTiming(componentName: string): void {
    const endTime = performance.now();
    const startTime = this.loadTimes.get(`${componentName}_start`);
    
    if (startTime) {
      const loadTime = endTime - startTime;
      this.loadTimes.set(componentName, loadTime);
      
      const currentCount = this.loadCounts.get(componentName) || 0;
      this.loadCounts.set(componentName, currentCount + 1);
      
      console.log(`Component ${componentName} loaded in ${loadTime.toFixed(2)}ms`);
    }
  }

  getStats(): { componentName: string; avgLoadTime: number; loadCount: number }[] {
    const stats: { componentName: string; avgLoadTime: number; loadCount: number }[] = [];
    
    this.loadCounts.forEach((count, componentName) => {
      const loadTime = this.loadTimes.get(componentName) || 0;
      stats.push({
        componentName,
        avgLoadTime: loadTime,
        loadCount: count
      });
    });
    
    return stats;
  }

  reset(): void {
    this.loadTimes.clear();
    this.loadCounts.clear();
  }
}

// Enhanced lazy loading with performance monitoring
export function lazyWithMonitoring<T extends ComponentType<any>>(
  importFunc: () => Promise<{ default: T }>,
  componentName: string
) {
  const monitor = CodeSplitPerformanceMonitor.getInstance();
  
  return lazy(() => {
    monitor.startTiming(componentName);
    
    return importFunc().then((module) => {
      monitor.endTiming(componentName);
      return module;
    });
  });
}

// Bundle analyzer helper (development only)
export const analyzeBundleSize = () => {
  if (process.env.NODE_ENV === 'development') {
    // This would integrate with webpack-bundle-analyzer or similar
    console.log('Bundle analysis would run here in development mode');
  }
};

// Prefetch critical components
export const prefetchCriticalComponents = () => {
  // Prefetch components that are likely to be needed soon
  const criticalComponents = [
    () => import('@/pages/Search'),
    () => import('@/pages/PropertyDetail'),
    () => import('@/components/PropertyCard'),
  ];

  criticalComponents.forEach((importFunc, index) => {
    setTimeout(() => {
      importFunc().catch(console.error);
    }, index * 100); // Stagger the prefetching
  });
};

// Resource hints for better performance
export const addResourceHints = () => {
  // Add DNS prefetch for external domains
  const domains = [
    'images.unsplash.com',
    'cdn.stayfinder.com',
    'api.stayfinder.com'
  ];

  domains.forEach(domain => {
    const link = document.createElement('link');
    link.rel = 'dns-prefetch';
    link.href = `//${domain}`;
    document.head.appendChild(link);
  });

  // Add preconnect for critical resources
  const criticalDomains = ['api.stayfinder.com'];
  
  criticalDomains.forEach(domain => {
    const link = document.createElement('link');
    link.rel = 'preconnect';
    link.href = `//${domain}`;
    document.head.appendChild(link);
  });
};

// Initialize performance optimizations
export const initializePerformanceOptimizations = () => {
  // Add resource hints
  addResourceHints();
  
  // Prefetch critical components after initial load
  if (document.readyState === 'complete') {
    prefetchCriticalComponents();
  } else {
    window.addEventListener('load', prefetchCriticalComponents);
  }
  
  // Log performance stats in development
  if (process.env.NODE_ENV === 'development') {
    setTimeout(() => {
      const monitor = CodeSplitPerformanceMonitor.getInstance();
      console.table(monitor.getStats());
    }, 5000);
  }
};
