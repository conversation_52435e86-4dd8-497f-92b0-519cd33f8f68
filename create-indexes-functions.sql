-- ============================================================================
-- StayFinder Database Indexes, Functions & Triggers
-- PostgreSQL/Supabase Compatible
-- Version: 1.0
-- Created: July 16, 2025
-- ============================================================================

-- ============================================================================
-- INDEXES FOR PERFORMANCE OPTIMIZATION
-- ============================================================================

-- Users table indexes
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_auth_user_id ON users(auth_user_id);
CREATE INDEX idx_users_user_type ON users(user_type);
CREATE INDEX idx_users_is_active ON users(is_active) WHERE is_active = TRUE;

-- Properties table indexes
CREATE INDEX idx_properties_host_id ON properties(host_id);
CREATE INDEX idx_properties_status ON properties(status) WHERE status = 'active';
CREATE INDEX idx_properties_featured ON properties(featured) WHERE featured = TRUE;
CREATE INDEX idx_properties_city ON properties(city);
CREATE INDEX idx_properties_province ON properties(province);
CREATE INDEX idx_properties_price ON properties(price_per_night);
CREATE INDEX idx_properties_max_guests ON properties(max_guests);
CREATE INDEX idx_properties_bedrooms ON properties(bedrooms);
CREATE INDEX idx_properties_property_type ON properties(property_type);
CREATE INDEX idx_properties_instant_book ON properties(instant_book) WHERE instant_book = TRUE;
CREATE INDEX idx_properties_average_rating ON properties(average_rating);
CREATE INDEX idx_properties_created_at ON properties(created_at);

-- Composite indexes for common queries
CREATE INDEX idx_properties_location_price ON properties(city, province, price_per_night) WHERE status = 'active';
CREATE INDEX idx_properties_guests_bedrooms ON properties(max_guests, bedrooms) WHERE status = 'active';

-- Full-text search index
CREATE INDEX idx_properties_search ON properties USING GIN(search_vector);

-- Geospatial index for location-based searches
CREATE INDEX idx_properties_coordinates ON properties USING GIST(ST_Point(longitude, latitude)) WHERE latitude IS NOT NULL AND longitude IS NOT NULL;

-- Property amenities indexes
CREATE INDEX idx_property_amenities_property_id ON property_amenities(property_id);
CREATE INDEX idx_property_amenities_amenity_id ON property_amenities(amenity_id);

-- Property images indexes
CREATE INDEX idx_property_images_property_id ON property_images(property_id);
CREATE INDEX idx_property_images_primary ON property_images(property_id, is_primary) WHERE is_primary = TRUE;
CREATE INDEX idx_property_images_sort_order ON property_images(property_id, sort_order);

-- Bookings table indexes
CREATE INDEX idx_bookings_property_id ON bookings(property_id);
CREATE INDEX idx_bookings_guest_id ON bookings(guest_id);
CREATE INDEX idx_bookings_reference ON bookings(booking_reference);
CREATE INDEX idx_bookings_status ON bookings(status);
CREATE INDEX idx_bookings_payment_status ON bookings(payment_status);
CREATE INDEX idx_bookings_check_in_date ON bookings(check_in_date);
CREATE INDEX idx_bookings_check_out_date ON bookings(check_out_date);
CREATE INDEX idx_bookings_created_at ON bookings(created_at);

-- Composite indexes for date range queries
CREATE INDEX idx_bookings_property_dates ON bookings(property_id, check_in_date, check_out_date);
CREATE INDEX idx_bookings_dates_status ON bookings(check_in_date, check_out_date, status);

-- Booking payments indexes
CREATE INDEX idx_booking_payments_booking_id ON booking_payments(booking_id);
CREATE INDEX idx_booking_payments_status ON booking_payments(status);
CREATE INDEX idx_booking_payments_payment_intent ON booking_payments(payment_intent_id);
CREATE INDEX idx_booking_payments_transaction_id ON booking_payments(transaction_id);

-- Reviews table indexes
CREATE INDEX idx_reviews_property_id ON reviews(property_id);
CREATE INDEX idx_reviews_guest_id ON reviews(guest_id);
CREATE INDEX idx_reviews_booking_id ON reviews(booking_id);
CREATE INDEX idx_reviews_status ON reviews(status) WHERE status = 'published';
CREATE INDEX idx_reviews_overall_rating ON reviews(overall_rating);
CREATE INDEX idx_reviews_created_at ON reviews(created_at);
CREATE INDEX idx_reviews_featured ON reviews(is_featured) WHERE is_featured = TRUE;

-- Messages table indexes
CREATE INDEX idx_messages_conversation_id ON messages(conversation_id);
CREATE INDEX idx_messages_sender_id ON messages(sender_id);
CREATE INDEX idx_messages_recipient_id ON messages(recipient_id);
CREATE INDEX idx_messages_property_id ON messages(property_id);
CREATE INDEX idx_messages_booking_id ON messages(booking_id);
CREATE INDEX idx_messages_is_read ON messages(recipient_id, is_read) WHERE is_read = FALSE;
CREATE INDEX idx_messages_created_at ON messages(created_at);

-- Notifications table indexes
CREATE INDEX idx_notifications_user_id ON notifications(user_id);
CREATE INDEX idx_notifications_type ON notifications(type);
CREATE INDEX idx_notifications_is_read ON notifications(user_id, is_read) WHERE is_read = FALSE;
CREATE INDEX idx_notifications_created_at ON notifications(created_at);
CREATE INDEX idx_notifications_expires_at ON notifications(expires_at) WHERE expires_at IS NOT NULL;

-- Property availability indexes
CREATE INDEX idx_property_availability_property_date ON property_availability(property_id, date);
CREATE INDEX idx_property_availability_date_range ON property_availability(date, is_available);
CREATE INDEX idx_property_availability_property_available ON property_availability(property_id, is_available) WHERE is_available = TRUE;

-- Amenities indexes
CREATE INDEX idx_amenities_category ON amenities(category);
CREATE INDEX idx_amenities_active ON amenities(is_active) WHERE is_active = TRUE;
CREATE INDEX idx_amenities_sort_order ON amenities(sort_order);

-- ============================================================================
-- FUNCTIONS AND TRIGGERS
-- ============================================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Function to update property search vector
CREATE OR REPLACE FUNCTION update_property_search_vector()
RETURNS TRIGGER AS $$
BEGIN
    NEW.search_vector := to_tsvector('english', 
        COALESCE(NEW.title, '') || ' ' ||
        COALESCE(NEW.description, '') || ' ' ||
        COALESCE(NEW.city, '') || ' ' ||
        COALESCE(NEW.province, '') || ' ' ||
        COALESCE(array_to_string(NEW.house_rules, ' '), '')
    );
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Function to generate booking reference
CREATE OR REPLACE FUNCTION generate_booking_reference()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.booking_reference IS NULL THEN
        NEW.booking_reference := 'SF' || TO_CHAR(NOW(), 'YYYYMMDD') || LPAD(NEXTVAL('booking_reference_seq')::TEXT, 6, '0');
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Function to calculate booking nights
CREATE OR REPLACE FUNCTION calculate_booking_nights()
RETURNS TRIGGER AS $$
BEGIN
    NEW.nights := NEW.check_out_date - NEW.check_in_date;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Function to update property statistics
CREATE OR REPLACE FUNCTION update_property_stats()
RETURNS TRIGGER AS $$
DECLARE
    avg_rating DECIMAL(3,2);
    review_count INTEGER;
BEGIN
    -- Update average rating and review count
    SELECT 
        COALESCE(AVG(overall_rating), 0),
        COUNT(*)
    INTO avg_rating, review_count
    FROM reviews 
    WHERE property_id = NEW.property_id AND status = 'published';
    
    UPDATE properties 
    SET 
        average_rating = avg_rating,
        total_reviews = review_count,
        updated_at = NOW()
    WHERE id = NEW.property_id;
    
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Function to update booking count
CREATE OR REPLACE FUNCTION update_booking_count()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.status = 'completed' AND (OLD.status IS NULL OR OLD.status != 'completed') THEN
        UPDATE properties 
        SET 
            booking_count = booking_count + 1,
            last_booked_at = NEW.check_out_date,
            updated_at = NOW()
        WHERE id = NEW.property_id;
    END IF;
    
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Function to prevent overlapping bookings
CREATE OR REPLACE FUNCTION prevent_booking_overlap()
RETURNS TRIGGER AS $$
DECLARE
    overlap_count INTEGER;
BEGIN
    -- Check for overlapping bookings
    SELECT COUNT(*)
    INTO overlap_count
    FROM bookings
    WHERE property_id = NEW.property_id
      AND id != COALESCE(NEW.id, '00000000-0000-0000-0000-000000000000'::UUID)
      AND status NOT IN ('cancelled', 'no_show')
      AND (
          (NEW.check_in_date >= check_in_date AND NEW.check_in_date < check_out_date) OR
          (NEW.check_out_date > check_in_date AND NEW.check_out_date <= check_out_date) OR
          (NEW.check_in_date <= check_in_date AND NEW.check_out_date >= check_out_date)
      );
    
    IF overlap_count > 0 THEN
        RAISE EXCEPTION 'Booking dates overlap with existing reservation';
    END IF;
    
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Function to create conversation ID for messages
CREATE OR REPLACE FUNCTION generate_conversation_id()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.conversation_id IS NULL THEN
        -- Generate conversation ID based on participants and property/booking
        NEW.conversation_id := gen_random_uuid();
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- ============================================================================
-- TRIGGER ASSIGNMENTS
-- ============================================================================

-- Updated_at triggers
CREATE TRIGGER trigger_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER trigger_properties_updated_at BEFORE UPDATE ON properties FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER trigger_bookings_updated_at BEFORE UPDATE ON bookings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER trigger_reviews_updated_at BEFORE UPDATE ON reviews FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER trigger_messages_updated_at BEFORE UPDATE ON messages FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER trigger_user_preferences_updated_at BEFORE UPDATE ON user_preferences FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Property search vector trigger
CREATE TRIGGER trigger_properties_search_vector BEFORE INSERT OR UPDATE ON properties FOR EACH ROW EXECUTE FUNCTION update_property_search_vector();

-- Booking reference generation trigger
CREATE TRIGGER trigger_booking_reference BEFORE INSERT ON bookings FOR EACH ROW EXECUTE FUNCTION generate_booking_reference();

-- Booking nights calculation trigger
CREATE TRIGGER trigger_booking_nights BEFORE INSERT OR UPDATE ON bookings FOR EACH ROW EXECUTE FUNCTION calculate_booking_nights();

-- Property statistics update trigger
CREATE TRIGGER trigger_property_stats AFTER INSERT OR UPDATE ON reviews FOR EACH ROW EXECUTE FUNCTION update_property_stats();

-- Booking count update trigger
CREATE TRIGGER trigger_booking_count AFTER UPDATE ON bookings FOR EACH ROW EXECUTE FUNCTION update_booking_count();

-- Booking overlap prevention trigger
CREATE TRIGGER trigger_prevent_overlap BEFORE INSERT OR UPDATE ON bookings FOR EACH ROW EXECUTE FUNCTION prevent_booking_overlap();

-- Message conversation ID trigger
CREATE TRIGGER trigger_conversation_id BEFORE INSERT ON messages FOR EACH ROW EXECUTE FUNCTION generate_conversation_id();

-- ============================================================================
-- UTILITY FUNCTIONS
-- ============================================================================

-- Function to get available properties for date range
CREATE OR REPLACE FUNCTION get_available_properties(
    p_check_in DATE,
    p_check_out DATE,
    p_guests INTEGER DEFAULT NULL,
    p_city VARCHAR DEFAULT NULL,
    p_min_price DECIMAL DEFAULT NULL,
    p_max_price DECIMAL DEFAULT NULL
)
RETURNS TABLE (
    property_id UUID,
    title VARCHAR,
    city VARCHAR,
    price_per_night DECIMAL,
    max_guests INTEGER,
    bedrooms INTEGER,
    average_rating DECIMAL,
    total_reviews INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id,
        p.title,
        p.city,
        p.price_per_night,
        p.max_guests,
        p.bedrooms,
        p.average_rating,
        p.total_reviews
    FROM properties p
    WHERE p.status = 'active'
      AND (p_guests IS NULL OR p.max_guests >= p_guests)
      AND (p_city IS NULL OR p.city ILIKE '%' || p_city || '%')
      AND (p_min_price IS NULL OR p.price_per_night >= p_min_price)
      AND (p_max_price IS NULL OR p.price_per_night <= p_max_price)
      AND NOT EXISTS (
          SELECT 1 FROM bookings b
          WHERE b.property_id = p.id
            AND b.status NOT IN ('cancelled', 'no_show')
            AND (
                (p_check_in >= b.check_in_date AND p_check_in < b.check_out_date) OR
                (p_check_out > b.check_in_date AND p_check_out <= b.check_out_date) OR
                (p_check_in <= b.check_in_date AND p_check_out >= b.check_out_date)
            )
      )
    ORDER BY p.featured DESC, p.average_rating DESC, p.created_at DESC;
END;
$$ LANGUAGE plpgsql;
