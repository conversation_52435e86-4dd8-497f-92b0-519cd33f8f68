import React, { useState } from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { StarRating } from './StarRating';
import { useReviews } from '../contexts/ReviewsContext';
import { useAuth } from '../contexts/AuthContext';
import { 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  Calendar,
  MapPin,
  User
} from 'lucide-react';
import { format, parseISO } from 'date-fns';

interface ReviewCardProps {
  review: {
    id: string;
    propertyId: string;
    bookingId: string;
    guestId: string;
    rating: number;
    comment: string;
    createdAt: string;
    updatedAt: string;
    guest?: {
      firstName: string;
      lastName: string;
      email: string;
    };
    property?: {
      title: string;
      location: string;
    };
    booking?: {
      checkInDate: string;
      checkOutDate: string;
    };
  };
  showProperty?: boolean;
  showActions?: boolean;
  onEdit?: (review: any) => void;
  onDelete?: (reviewId: string) => void;
}

export const ReviewCard: React.FC<ReviewCardProps> = ({
  review,
  showProperty = false,
  showActions = false,
  onEdit,
  onDelete
}) => {
  const [showMenu, setShowMenu] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  
  const { deleteReview, submitting } = useReviews();
  const { user } = useAuth();

  const isOwner = user?.id === review.guestId;
  const canEdit = isOwner && showActions;
  const canDelete = isOwner && showActions;

  const handleEdit = () => {
    if (onEdit) {
      onEdit(review);
    }
    setShowMenu(false);
  };

  const handleDelete = async () => {
    if (!canDelete) return;

    const confirmed = window.confirm('Are you sure you want to delete this review? This action cannot be undone.');
    if (!confirmed) return;

    setIsDeleting(true);
    try {
      await deleteReview(review.id);
      if (onDelete) {
        onDelete(review.id);
      }
    } catch (error) {
      console.error('Failed to delete review:', error);
      alert('Failed to delete review. Please try again.');
    } finally {
      setIsDeleting(false);
      setShowMenu(false);
    }
  };

  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  const formatStayDates = () => {
    if (!review.booking) return null;
    
    const checkIn = parseISO(review.booking.checkInDate);
    const checkOut = parseISO(review.booking.checkOutDate);
    
    return `${format(checkIn, 'MMM dd')} - ${format(checkOut, 'MMM dd, yyyy')}`;
  };

  return (
    <Card className="hover:shadow-md transition-shadow duration-200">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            {/* User Avatar */}
            <div className="w-10 h-10 bg-sea-green-500 rounded-full flex items-center justify-center text-white font-medium">
              {review.guest ? getInitials(review.guest.firstName, review.guest.lastName) : <User className="h-5 w-5" />}
            </div>
            
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-1">
                <h4 className="font-medium text-gray-900">
                  {review.guest ? `${review.guest.firstName} ${review.guest.lastName}` : 'Anonymous'}
                </h4>
                {isOwner && (
                  <Badge variant="outline" className="text-xs">
                    Your Review
                  </Badge>
                )}
              </div>
              
              <div className="flex items-center gap-2">
                <StarRating rating={review.rating} readonly size="sm" />
                <span className="text-sm text-gray-500">
                  {format(parseISO(review.createdAt), 'MMM dd, yyyy')}
                </span>
              </div>
            </div>
          </div>

          {/* Actions Menu */}
          {(canEdit || canDelete) && (
            <div className="relative">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowMenu(!showMenu)}
                className="h-8 w-8 p-0"
              >
                <MoreHorizontal className="h-4 w-4" />
              </Button>
              
              {showMenu && (
                <div className="absolute right-0 top-8 bg-white border rounded-lg shadow-lg py-1 z-10 min-w-[120px]">
                  {canEdit && (
                    <button
                      onClick={handleEdit}
                      className="w-full px-3 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2"
                    >
                      <Edit className="h-3 w-3" />
                      Edit
                    </button>
                  )}
                  {canDelete && (
                    <button
                      onClick={handleDelete}
                      disabled={isDeleting || submitting}
                      className="w-full px-3 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2 text-red-600"
                    >
                      <Trash2 className="h-3 w-3" />
                      {isDeleting ? 'Deleting...' : 'Delete'}
                    </button>
                  )}
                </div>
              )}
            </div>
          )}
        </div>
      </CardHeader>

      <CardContent className="pt-0 space-y-3">
        {/* Property Information (if showing) */}
        {showProperty && review.property && (
          <div className="bg-gray-50 p-3 rounded-lg">
            <h5 className="font-medium text-gray-900 mb-1">{review.property.title}</h5>
            <div className="flex items-center gap-4 text-sm text-gray-600">
              <div className="flex items-center gap-1">
                <MapPin className="h-3 w-3" />
                <span>{review.property.location}</span>
              </div>
              {review.booking && (
                <div className="flex items-center gap-1">
                  <Calendar className="h-3 w-3" />
                  <span>{formatStayDates()}</span>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Review Comment */}
        <div className="text-gray-700 leading-relaxed">
          {review.comment}
        </div>

        {/* Stay Information (if not showing property) */}
        {!showProperty && review.booking && (
          <div className="text-sm text-gray-500 flex items-center gap-1">
            <Calendar className="h-3 w-3" />
            <span>Stayed {formatStayDates()}</span>
          </div>
        )}

        {/* Updated indicator */}
        {review.updatedAt !== review.createdAt && (
          <div className="text-xs text-gray-500">
            Updated {format(parseISO(review.updatedAt), 'MMM dd, yyyy')}
          </div>
        )}
      </CardContent>

      {/* Click outside to close menu */}
      {showMenu && (
        <div
          className="fixed inset-0 z-5"
          onClick={() => setShowMenu(false)}
        />
      )}
    </Card>
  );
};
