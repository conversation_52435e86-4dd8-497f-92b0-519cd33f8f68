import React from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  CheckCircle, 
  Home, 
  MapPin, 
  Users, 
  Bed, 
  Bath,
  DollarSign,
  Clock,
  Shield,
  Star,
  Loader2,
  AlertCircle
} from 'lucide-react';

interface PropertyVerificationProps {
  data: {
    title: string;
    description: string;
    propertyType: string;
    location: string;
    address: string;
    maxGuests: number;
    bedrooms: number;
    bathrooms: number;
    amenities: string[];
    checkInTime: string;
    checkOutTime: string;
    houseRules: string[];
    cancellationPolicy: string;
    basePrice: number;
    cleaningFee: number;
    weeklyDiscount: number;
    monthlyDiscount: number;
  };
  errors: Record<string, string>;
  onSubmit: () => void;
  isSubmitting: boolean;
}

const AMENITY_LABELS: Record<string, string> = {
  wifi: 'WiFi',
  parking: 'Free Parking',
  kitchen: 'Kitchen',
  tv: 'TV',
  air_conditioning: 'Air Conditioning',
  heating: 'Heating',
  pool: 'Swimming Pool',
  hot_tub: 'Hot Tub',
  garden: 'Garden',
  balcony: 'Balcony/Patio',
  fireplace: 'Fireplace',
  gym: 'Gym/Fitness',
  washing_machine: 'Washing Machine',
  dryer: 'Dryer',
  dishwasher: 'Dishwasher',
  coffee_maker: 'Coffee Maker',
  microwave: 'Microwave',
  refrigerator: 'Refrigerator',
  netflix: 'Netflix',
  sound_system: 'Sound System',
  games: 'Board Games',
  books: 'Books',
  workspace: 'Dedicated Workspace',
  phone: 'Phone',
  baby_friendly: 'Baby Friendly',
  high_chair: 'High Chair',
  crib: 'Crib',
  toys: 'Children\'s Toys',
  child_safety: 'Child Safety Features',
  family_friendly: 'Family Friendly',
  pets_allowed: 'Pets Allowed',
  smoking_allowed: 'Smoking Allowed',
  events_allowed: 'Events Allowed',
  long_term_stays: 'Long-term Stays',
  security_cameras: 'Security Cameras (exterior)',
  smoke_detector: 'Smoke Detector',
  carbon_monoxide: 'Carbon Monoxide Detector',
  first_aid: 'First Aid Kit',
  fire_extinguisher: 'Fire Extinguisher',
  security_system: 'Security System'
};

const CANCELLATION_POLICY_LABELS: Record<string, string> = {
  flexible: 'Flexible',
  moderate: 'Moderate',
  strict: 'Strict',
  super_strict: 'Super Strict'
};

export const PropertyVerification: React.FC<PropertyVerificationProps> = ({
  data,
  errors,
  onSubmit,
  isSubmitting
}) => {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatTime = (time: string) => {
    if (!time) return '';
    return new Date(`2000-01-01T${time}`).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  };

  const getAmenityLabel = (amenityId: string) => {
    return AMENITY_LABELS[amenityId] || amenityId;
  };

  const calculateTotalPrice = (nights: number) => {
    let total = data.basePrice * nights;
    
    if (nights >= 7 && data.weeklyDiscount > 0) {
      total = total - (total * data.weeklyDiscount / 100);
    } else if (nights >= 30 && data.monthlyDiscount > 0) {
      total = total - (total * data.monthlyDiscount / 100);
    }
    
    return total + (data.cleaningFee || 0);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          Review your listing
        </h2>
        <p className="text-gray-600">
          Please review all details before submitting your property for approval
        </p>
      </div>

      {/* Property Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Home className="h-5 w-5" />
            Property Overview
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">{data.title}</h3>
            <div className="flex items-center gap-4 text-gray-600 mb-3">
              <div className="flex items-center gap-1">
                <MapPin className="h-4 w-4" />
                <span>{data.location}</span>
              </div>
              <Badge variant="outline">{data.propertyType}</Badge>
            </div>
            <p className="text-gray-700 leading-relaxed">{data.description}</p>
          </div>

          <div className="grid grid-cols-3 gap-4 pt-4 border-t">
            <div className="text-center">
              <div className="flex items-center justify-center gap-1 mb-1">
                <Users className="h-4 w-4 text-gray-600" />
                <span className="font-medium">{data.maxGuests}</span>
              </div>
              <p className="text-sm text-gray-600">Guests</p>
            </div>
            <div className="text-center">
              <div className="flex items-center justify-center gap-1 mb-1">
                <Bed className="h-4 w-4 text-gray-600" />
                <span className="font-medium">{data.bedrooms}</span>
              </div>
              <p className="text-sm text-gray-600">Bedrooms</p>
            </div>
            <div className="text-center">
              <div className="flex items-center justify-center gap-1 mb-1">
                <Bath className="h-4 w-4 text-gray-600" />
                <span className="font-medium">{data.bathrooms}</span>
              </div>
              <p className="text-sm text-gray-600">Bathrooms</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Amenities */}
      {data.amenities.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Star className="h-5 w-5" />
              Amenities ({data.amenities.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {data.amenities.map((amenityId) => (
                <Badge key={amenityId} variant="secondary" className="text-xs">
                  {getAmenityLabel(amenityId)}
                </Badge>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* House Rules & Policies */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            House Rules & Policies
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium text-gray-900 mb-2 flex items-center gap-2">
                <Clock className="h-4 w-4" />
                Check-in & Check-out
              </h4>
              <div className="text-sm text-gray-600 space-y-1">
                <p>Check-in: {formatTime(data.checkInTime)}</p>
                <p>Check-out: {formatTime(data.checkOutTime)}</p>
              </div>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Cancellation Policy</h4>
              <Badge variant="outline">
                {CANCELLATION_POLICY_LABELS[data.cancellationPolicy] || data.cancellationPolicy}
              </Badge>
            </div>
          </div>

          {data.houseRules.length > 0 && (
            <div>
              <h4 className="font-medium text-gray-900 mb-2">House Rules</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                {data.houseRules.map((rule, index) => (
                  <li key={index} className="flex items-center gap-2">
                    <CheckCircle className="h-3 w-3 text-green-600 flex-shrink-0" />
                    {rule}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Pricing */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            Pricing
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Base Pricing</h4>
                <div className="text-sm space-y-1">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Per night:</span>
                    <span className="font-medium">{formatCurrency(data.basePrice)}</span>
                  </div>
                  {data.cleaningFee > 0 && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Cleaning fee:</span>
                      <span className="font-medium">{formatCurrency(data.cleaningFee)}</span>
                    </div>
                  )}
                </div>
              </div>

              <div>
                <h4 className="font-medium text-gray-900 mb-2">Discounts</h4>
                <div className="text-sm space-y-1">
                  {data.weeklyDiscount > 0 ? (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Weekly (7+ nights):</span>
                      <span className="font-medium text-green-600">{data.weeklyDiscount}% off</span>
                    </div>
                  ) : (
                    <div className="text-gray-500">No weekly discount</div>
                  )}
                  {data.monthlyDiscount > 0 ? (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Monthly (30+ nights):</span>
                      <span className="font-medium text-green-600">{data.monthlyDiscount}% off</span>
                    </div>
                  ) : (
                    <div className="text-gray-500">No monthly discount</div>
                  )}
                </div>
              </div>
            </div>

            {/* Pricing Examples */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="font-medium text-gray-900 mb-3">Pricing Examples</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-3 text-sm">
                <div className="text-center p-2 bg-white rounded border">
                  <p className="text-gray-600">1 night</p>
                  <p className="font-bold">{formatCurrency(calculateTotalPrice(1))}</p>
                </div>
                <div className="text-center p-2 bg-white rounded border">
                  <p className="text-gray-600">3 nights</p>
                  <p className="font-bold">{formatCurrency(calculateTotalPrice(3))}</p>
                </div>
                <div className="text-center p-2 bg-white rounded border">
                  <p className="text-gray-600">7 nights</p>
                  <p className="font-bold">{formatCurrency(calculateTotalPrice(7))}</p>
                  {data.weeklyDiscount > 0 && (
                    <p className="text-xs text-green-600">With discount</p>
                  )}
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Submit Section */}
      <Card className="bg-sea-green-50 border-sea-green-200">
        <CardContent className="p-6">
          <div className="text-center space-y-4">
            <div>
              <h3 className="text-lg font-semibold text-sea-green-900 mb-2">
                Ready to submit your listing?
              </h3>
              <p className="text-sea-green-700">
                Your property will be reviewed and activated within 24-48 hours
              </p>
            </div>

            {errors.submit && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex items-center gap-2 text-red-700">
                  <AlertCircle className="h-4 w-4" />
                  <span>{errors.submit}</span>
                </div>
              </div>
            )}

            <Button
              onClick={onSubmit}
              disabled={isSubmitting}
              className="bg-sea-green-600 hover:bg-sea-green-700 text-white px-8 py-3 text-lg"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                  Creating Property...
                </>
              ) : (
                <>
                  <CheckCircle className="mr-2 h-5 w-5" />
                  Submit for Review
                </>
              )}
            </Button>

            <p className="text-xs text-gray-600">
              By submitting, you agree to our Terms of Service and Host Guidelines
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Next Steps */}
      <Card className="bg-blue-50 border-blue-200">
        <CardContent className="p-4">
          <h4 className="font-medium text-blue-900 mb-2">📋 What happens next?</h4>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• Your listing will be reviewed by our team</li>
            <li>• We'll verify the information and check for completeness</li>
            <li>• You'll receive an email confirmation once approved</li>
            <li>• Your property will go live and start receiving bookings</li>
            <li>• You can manage your listing from the Host Dashboard</li>
          </ul>
        </CardContent>
      </Card>
    </div>
  );
};
