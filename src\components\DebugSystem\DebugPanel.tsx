import React, { useState, useEffect, useCallback, useRef } from 'react';
import { X, Minimize2, Maximize2, Copy, Trash2, Download, Search, Filter, Bug, AlertTriangle, Info, Zap, RefreshCw } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useErrorCapture, CapturedError } from '@/hooks/useErrorCapture';
import { 
  debugSystemManager, 
  PanelState, 
  PanelGeometry,
  savePanelState,
  loadPanelState,
  savePanelGeometry,
  loadPanelGeometry,
  filterErrors,
  formatErrorForDisplay
} from '@/utils/debugSystem';
import { cn } from '@/lib/utils';

interface DebugPanelProps {
  userRole?: string;
  onClose?: () => void;
}

export const DebugPanel: React.FC<DebugPanelProps> = ({ userRole, onClose }) => {
  const {
    errors,
    isCapturing,
    clearErrors,
    dismissError,
    getErrorCounts,
    copyError,
    copyAllErrors,
    exportErrors
  } = useErrorCapture();

  const [panelState, setPanelState] = useState<PanelState>(loadPanelState);
  const [panelGeometry, setPanelGeometry] = useState<PanelGeometry>(loadPanelGeometry);
  const [isDragging, setIsDragging] = useState(false);
  const [isResizing, setIsResizing] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });

  const panelRef = useRef<HTMLDivElement>(null);
  const dragHandleRef = useRef<HTMLDivElement>(null);

  // Check if debug system should be visible
  const isVisible = debugSystemManager.isVisible(userRole);

  // Filter errors based on current filters
  const filteredErrors = filterErrors(errors, panelState.filters);
  const errorCounts = getErrorCounts();

  // Save panel state when it changes
  useEffect(() => {
    savePanelState(panelState);
  }, [panelState]);

  // Save panel geometry when it changes
  useEffect(() => {
    savePanelGeometry(panelGeometry);
  }, [panelGeometry]);

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!panelState.isOpen) return;

      // Escape to close panel
      if (event.key === 'Escape') {
        updatePanelState({ isOpen: false });
      }

      // Ctrl+A to select all (for copy all)
      if (event.ctrlKey && event.key === 'a' && event.target === document.body) {
        event.preventDefault();
        copyAllErrors();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [panelState.isOpen, copyAllErrors]);

  // Listen for debug system events
  useEffect(() => {
    const handleClearAll = () => clearErrors();
    
    window.addEventListener('debug-clear-all-errors', handleClearAll);
    return () => window.removeEventListener('debug-clear-all-errors', handleClearAll);
  }, [clearErrors]);

  const updatePanelState = useCallback((updates: Partial<PanelState>) => {
    setPanelState(prev => ({ ...prev, ...updates }));
  }, []);

  const updatePanelGeometry = useCallback((updates: Partial<PanelGeometry>) => {
    setPanelGeometry(prev => ({ ...prev, ...updates }));
  }, []);

  // Handle panel dragging
  const handleMouseDown = useCallback((event: React.MouseEvent) => {
    if (event.target !== dragHandleRef.current) return;
    
    setIsDragging(true);
    setDragOffset({
      x: event.clientX - panelGeometry.x,
      y: event.clientY - panelGeometry.y
    });
  }, [panelGeometry]);

  const handleMouseMove = useCallback((event: MouseEvent) => {
    if (!isDragging) return;

    const newX = Math.max(0, Math.min(window.innerWidth - panelGeometry.width, event.clientX - dragOffset.x));
    const newY = Math.max(0, Math.min(window.innerHeight - panelGeometry.height, event.clientY - dragOffset.y));

    updatePanelGeometry({ x: newX, y: newY });
  }, [isDragging, dragOffset, panelGeometry, updatePanelGeometry]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
    setIsResizing(false);
  }, []);

  // Setup mouse event listeners
  useEffect(() => {
    if (isDragging || isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, isResizing, handleMouseMove, handleMouseUp]);

  // Handle error actions
  const handleCopyError = async (error: CapturedError) => {
    const success = await copyError(error);
    if (success) {
      // Show toast notification (you can integrate with your toast system)
      console.log('Error copied to clipboard');
    }
  };

  const handleCopyAllErrors = async () => {
    const success = await copyAllErrors();
    if (success) {
      console.log('All errors copied to clipboard');
    }
  };

  const handleExportErrors = () => {
    const exportData = exportErrors();
    const blob = new Blob([exportData], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `stayfinder-debug-${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const getErrorIcon = (type: CapturedError['type']) => {
    switch (type) {
      case 'error':
        return <Bug className="h-4 w-4 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'info':
        return <Info className="h-4 w-4 text-blue-500" />;
      case 'debug':
        return <Zap className="h-4 w-4 text-purple-500" />;
      default:
        return <Info className="h-4 w-4 text-gray-500" />;
    }
  };

  const getErrorTypeColor = (type: CapturedError['type']) => {
    switch (type) {
      case 'error':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'info':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'debug':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  if (!isVisible) {
    return null;
  }

  return (
    <div
      ref={panelRef}
      className={cn(
        "fixed bg-white border border-gray-300 rounded-lg shadow-2xl z-[9999] overflow-hidden",
        "transition-all duration-200 ease-in-out",
        panelState.isMinimized ? "h-12" : "",
        isDragging ? "cursor-move" : ""
      )}
      style={{
        left: panelGeometry.x,
        top: panelGeometry.y,
        width: panelState.isMinimized ? 200 : panelGeometry.width,
        height: panelState.isMinimized ? 48 : panelGeometry.height,
        minWidth: 300,
        minHeight: 200,
        maxWidth: '90vw',
        maxHeight: '90vh'
      }}
    >
      {/* Header */}
      <div
        ref={dragHandleRef}
        className="flex items-center justify-between p-3 bg-gray-50 border-b border-gray-200 cursor-move select-none"
        onMouseDown={handleMouseDown}
      >
        <div className="flex items-center gap-2">
          <Bug className="h-5 w-5 text-sea-green-600" />
          <span className="font-semibold text-gray-900">Debug System</span>
          {isCapturing && (
            <Badge className="bg-green-100 text-green-800 text-xs">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse mr-1" />
              Capturing
            </Badge>
          )}
        </div>

        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => updatePanelState({ isMinimized: !panelState.isMinimized })}
            className="h-8 w-8 p-0"
          >
            {panelState.isMinimized ? <Maximize2 className="h-4 w-4" /> : <Minimize2 className="h-4 w-4" />}
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              updatePanelState({ isOpen: false });
              onClose?.();
            }}
            className="h-8 w-8 p-0"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Content */}
      {!panelState.isMinimized && (
        <div className="flex flex-col h-full">
          {/* Toolbar */}
          <div className="p-3 border-b border-gray-200 space-y-3">
            {/* Search and Filters */}
            <div className="flex gap-2">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search errors..."
                  value={panelState.filters.search}
                  onChange={(e) => updatePanelState({
                    filters: { ...panelState.filters, search: e.target.value }
                  })}
                  className="pl-10 h-8"
                />
              </div>
              
              <Select
                value={panelState.filters.type}
                onValueChange={(value) => updatePanelState({
                  filters: { ...panelState.filters, type: value as any }
                })}
              >
                <SelectTrigger className="w-32 h-8">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="error">Errors</SelectItem>
                  <SelectItem value="warning">Warnings</SelectItem>
                  <SelectItem value="info">Info</SelectItem>
                  <SelectItem value="debug">Debug</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center justify-between">
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCopyAllErrors}
                  disabled={errors.length === 0}
                  className="h-8"
                >
                  <Copy className="h-3 w-3 mr-1" />
                  Copy All
                </Button>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={clearErrors}
                  disabled={errors.length === 0}
                  className="h-8"
                >
                  <Trash2 className="h-3 w-3 mr-1" />
                  Clear
                </Button>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleExportErrors}
                  disabled={errors.length === 0}
                  className="h-8"
                >
                  <Download className="h-3 w-3 mr-1" />
                  Export
                </Button>
              </div>

              {/* Error Counts */}
              <div className="flex gap-1">
                {errorCounts.error > 0 && (
                  <Badge className="bg-red-100 text-red-800 text-xs">
                    {errorCounts.error} errors
                  </Badge>
                )}
                {errorCounts.warning > 0 && (
                  <Badge className="bg-yellow-100 text-yellow-800 text-xs">
                    {errorCounts.warning} warnings
                  </Badge>
                )}
                {errorCounts.info > 0 && (
                  <Badge className="bg-blue-100 text-blue-800 text-xs">
                    {errorCounts.info} info
                  </Badge>
                )}
              </div>
            </div>
          </div>

          {/* Error List */}
          <div className="flex-1 overflow-y-auto p-3 space-y-2">
            {filteredErrors.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                {errors.length === 0 ? (
                  <div>
                    <Bug className="h-12 w-12 mx-auto mb-3 text-gray-300" />
                    <p>No errors captured yet</p>
                    <p className="text-sm">Errors will appear here as they occur</p>
                  </div>
                ) : (
                  <div>
                    <Filter className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                    <p>No errors match current filters</p>
                  </div>
                )}
              </div>
            ) : (
              filteredErrors.map((error) => (
                <div
                  key={error.id}
                  className={cn(
                    "p-3 border rounded-lg",
                    getErrorTypeColor(error.type)
                  )}
                >
                  <div className="flex items-start justify-between gap-2 mb-2">
                    <div className="flex items-center gap-2 min-w-0 flex-1">
                      {getErrorIcon(error.type)}
                      <span className="font-medium text-sm truncate">
                        {error.message}
                      </span>
                    </div>
                    
                    <div className="flex gap-1 flex-shrink-0">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleCopyError(error)}
                        className="h-6 w-6 p-0"
                      >
                        <Copy className="h-3 w-3" />
                      </Button>
                      
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => dismissError(error.id)}
                        className="h-6 w-6 p-0"
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>

                  <div className="text-xs text-gray-600 space-y-1">
                    <div>
                      {error.timestamp.toLocaleTimeString()} • {error.source}
                      {error.filename && (
                        <span> • {error.filename}:{error.line}:{error.column}</span>
                      )}
                    </div>
                    
                    {error.stack && (
                      <details className="mt-2">
                        <summary className="cursor-pointer hover:text-gray-800">
                          Stack Trace
                        </summary>
                        <pre className="mt-1 p-2 bg-white/50 rounded text-xs overflow-x-auto whitespace-pre-wrap">
                          {error.stack}
                        </pre>
                      </details>
                    )}
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      )}

      {/* Resize Handle */}
      {!panelState.isMinimized && (
        <div
          className="absolute bottom-0 right-0 w-4 h-4 cursor-se-resize"
          onMouseDown={(e) => {
            e.preventDefault();
            setIsResizing(true);
          }}
        >
          <div className="absolute bottom-1 right-1 w-2 h-2 border-r-2 border-b-2 border-gray-400" />
        </div>
      )}
    </div>
  );
};
