import { useState, useEffect } from 'react';
import { ModernPropertyCard } from './ModernPropertyCard';
import { Button } from '@/components/ui/button';
import { Loader2, Sparkles, ArrowRight } from 'lucide-react';
import propertiesService from '../services/properties';

const sampleProperties = [
  {
    id: '1',
    title: 'Oceanfront Villa Margate',
    location: 'Margate, KZN South Coast',
    price: 2500,
    images: [
      'https://images.unsplash.com/photo-1721322800607-8c38375eef04?w=400&h=300&fit=crop',
      'https://images.unsplash.com/photo-1649972904349-6e44c42644a7?w=400&h=300&fit=crop'
    ],
    description: 'Stunning 4-bedroom villa with direct beach access, private pool, and panoramic ocean views. Perfect for families and groups looking for luxury accommodation.',
    agent: {
      name: '<PERSON>',
      phone: '+27 82 555 0123',
      email: '<EMAIL>',
      photo: 'https://images.unsplash.com/photo-1494790108755-2616b612b647?w=100&h=100&fit=crop&crop=face'
    },
    available: true,
    special: '20% Off Weekly Stays'
  },
  {
    id: '2',
    title: 'Cozy Beachside Cottage',
    location: 'Scottburgh, KZN South Coast',
    price: 1200,
    images: [
      'https://images.unsplash.com/photo-1721322800607-8c38375eef04?w=400&h=300&fit=crop'
    ],
    description: 'Charming 2-bedroom cottage just steps from the beach. Recently renovated with modern amenities while maintaining its coastal charm.',
    agent: {
      name: 'Michael Thompson',
      phone: '+27 83 444 5678',
      email: '<EMAIL>'
    },
    available: true
  },
  {
    id: '3',
    title: 'Luxury Penthouse Apartment',
    location: 'Hibberdene, KZN South Coast',
    price: 3200,
    images: [
      'https://images.unsplash.com/photo-1649972904349-6e44c42644a7?w=400&h=300&fit=crop'
    ],
    description: 'Spectacular penthouse with 360-degree views, modern kitchen, spacious balconies, and access to resort facilities including pool and spa.',
    agent: {
      name: 'Lisa van der Merwe',
      phone: '+27 84 333 9876',
      email: '<EMAIL>',
      photo: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face'
    },
    available: false,
    special: 'Book 7 nights, get 1 free'
  },
  {
    id: '4',
    title: 'Family Beach House',
    location: 'Port Shepstone, KZN South Coast',
    price: 1800,
    images: [
      'https://images.unsplash.com/photo-1721322800607-8c38375eef04?w=400&h=300&fit=crop'
    ],
    description: 'Spacious 3-bedroom house with large garden, braai area, and short walk to pristine beaches. Ideal for families with children.',
    agent: {
      name: 'David Naidoo',
      phone: '+27 82 777 1234',
      email: '<EMAIL>'
    },
    available: true
  }
];

export const FeaturedProperties = () => {
  const [properties, setProperties] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Transform API response to match component interface
  const transformProperty = (apiProperty) => {
    return {
      id: apiProperty.id,
      title: apiProperty.title,
      description: apiProperty.description,
      location: `${apiProperty.city || ''}, ${apiProperty.province || ''}`.replace(/^, |, $/, '') || 'South Africa',
      price: apiProperty.pricePerNight || apiProperty.price_per_night || 0,
      images: apiProperty.images || [], // Default to empty array
      available: true, // Default to available
      propertyType: apiProperty.propertyType || apiProperty.property_type,
      maxGuests: apiProperty.maxGuests || apiProperty.max_guests,
      bedrooms: apiProperty.bedrooms,
      bathrooms: apiProperty.bathrooms,
      amenities: apiProperty.amenities || [],
      averageRating: apiProperty.averageRating || apiProperty.average_rating || 0,
      reviewCount: apiProperty.reviewCount || apiProperty.review_count || 0,
      owner: apiProperty.owner || {},
      coordinates: apiProperty.coordinates,
      cleaningFee: apiProperty.cleaningFee || apiProperty.cleaning_fee
    };
  };

  useEffect(() => {
    const fetchFeaturedProperties = async () => {
      try {
        setLoading(true);
        const result = await propertiesService.getAllProperties(1, 8); // Get first 8 properties
        // Transform the properties to match the component interface
        const transformedProperties = result.properties.map(transformProperty);
        setProperties(transformedProperties);
      } catch (err) {
        setError('Failed to load featured properties');
        console.error('Featured properties error:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchFeaturedProperties();
  }, []);

  return (
    <section className="py-16 px-4">
      <div className="container mx-auto">
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 bg-gradient-to-r from-sea-green-50 to-ocean-blue-50 border border-sea-green-200 rounded-full px-4 py-2 mb-6">
            <Sparkles className="h-4 w-4 text-sea-green-600" />
            <span className="text-sm font-medium text-sea-green-700">Handpicked for You</span>
          </div>
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Featured Properties
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Discover exceptional accommodations across South Africa's most beautiful destinations
          </p>
        </div>

        {/* Loading State */}
        {loading && (
          <div className="text-center py-16">
            <div className="bg-white rounded-2xl shadow-lg p-8 max-w-md mx-auto">
              <Loader2 className="h-10 w-10 animate-spin mx-auto mb-4 text-sea-green-600" />
              <p className="text-gray-600 font-medium">Loading featured properties...</p>
            </div>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="text-center py-12">
            <p className="text-red-600 mb-4">{error}</p>
            <Button
              onClick={() => window.location.reload()}
              variant="outline"
            >
              Try Again
            </Button>
          </div>
        )}

        {/* Properties Grid */}
        {!loading && !error && properties.length > 0 && (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
              {properties.map((property) => (
                <ModernPropertyCard
                  key={property.id}
                  property={property}
                  onBookNow={(propertyId) => {
                    // Navigate to booking page
                    window.location.href = `/property/${propertyId}`;
                  }}
                  onFavoriteToggle={(propertyId) => {
                    // Handle favorite toggle
                    console.log('Toggle favorite for:', propertyId);
                  }}
                />
              ))}
            </div>

            <div className="text-center mt-16">
              <Button
                size="lg"
                className="bg-gradient-to-r from-sea-green-500 to-ocean-blue-500 hover:from-sea-green-600 hover:to-ocean-blue-600 text-white px-10 py-4 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
                onClick={() => window.location.href = '/search'}
              >
                View All Properties
                <ArrowRight className="h-5 w-5 ml-2" />
              </Button>
            </div>
          </>
        )}

        {/* No Properties */}
        {!loading && !error && properties.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-600">No featured properties available at the moment.</p>
          </div>
        )}
      </div>
    </section>
  );
};
