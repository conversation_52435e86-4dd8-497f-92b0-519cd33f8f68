import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Star, MessageSquare } from 'lucide-react';

interface Feedback {
  id: string;
  name: string;
  rating: number;
  comment: string;
  property: string;
  date: string;
}

export const FeedbackSection = () => {
  const [feedbacks] = useState<Feedback[]>([
    {
      id: '1',
      name: '<PERSON>',
      rating: 5,
      comment: 'Amazing beachfront property! The view was spectacular and the host was very responsive.',
      property: 'Ocean View Villa, Margate',
      date: '2024-06-15'
    },
    {
      id: '2',
      name: '<PERSON>',
      rating: 4,
      comment: 'Great location and clean facilities. Would definitely stay here again.',
      property: 'Cozy Beach Apartment, Scottburgh',
      date: '2024-06-10'
    },
    {
      id: '3',
      name: '<PERSON>',
      rating: 5,
      comment: 'Perfect family holiday spot. Kids loved the beach access and pool.',
      property: 'Family Beach House, Hibberdene',
      date: '2024-06-08'
    }
  ]);

  const [newFeedback, setNewFeedback] = useState({
    name: '',
    rating: 5,
    comment: '',
    property: ''
  });

  const handleSubmitFeedback = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('New feedback submitted:', newFeedback);
    // Here you would submit to your backend
    setNewFeedback({ name: '', rating: 5, comment: '', property: '' });
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${
          i < rating ? 'text-yellow-400 fill-yellow-400' : 'text-gray-300'
        }`}
      />
    ));
  };

  return (
    <section className="py-16 bg-gradient-to-br from-sea-green-50 to-ocean-blue-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-black mb-4">
            Guest <span className="text-transparent bg-clip-text bg-gradient-to-r from-sea-green-600 to-ocean-blue-600">Feedback</span>
          </h2>
          <p className="text-xl text-black max-w-2xl mx-auto">
            See what our guests have to say about their KZN South Coast experience
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-8">
          {/* Feedback Display */}
          <div className="space-y-6">
            <h3 className="text-2xl font-semibold text-black mb-6">Recent Reviews</h3>
            {feedbacks.map((feedback) => (
              <Card key={feedback.id} className="hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div>
                      <h4 className="font-semibold text-black">{feedback.name}</h4>
                      <p className="text-sm text-sea-green-600">{feedback.property}</p>
                    </div>
                    <Badge variant="outline" className="text-xs">
                      {new Date(feedback.date).toLocaleDateString()}
                    </Badge>
                  </div>
                  
                  <div className="flex items-center mb-3">
                    <div className="flex space-x-1 mr-2">
                      {renderStars(feedback.rating)}
                    </div>
                    <span className="text-sm text-black">({feedback.rating}/5)</span>
                  </div>
                  
                  <p className="text-black">{feedback.comment}</p>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Feedback Form */}
          <div>
            <Card className="sticky top-8">
              <CardHeader>
                <CardTitle className="flex items-center text-black">
                  <MessageSquare className="h-5 w-5 mr-2 text-ocean-blue-600" />
                  Share Your Experience
                </CardTitle>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmitFeedback} className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium mb-2 text-black">Your Name</label>
                    <Input
                      value={newFeedback.name}
                      onChange={(e) => setNewFeedback({...newFeedback, name: e.target.value})}
                      placeholder="Enter your name"
                      className="text-black"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2 text-black">Property Stayed At</label>
                    <Input
                      value={newFeedback.property}
                      onChange={(e) => setNewFeedback({...newFeedback, property: e.target.value})}
                      placeholder="Which property did you stay at?"
                      className="text-black"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2 text-black">Rating</label>
                    <div className="flex space-x-1">
                      {Array.from({ length: 5 }, (_, i) => (
                        <button
                          key={i}
                          type="button"
                          onClick={() => setNewFeedback({...newFeedback, rating: i + 1})}
                          className="focus:outline-none"
                        >
                          <Star
                            className={`h-6 w-6 ${
                              i < newFeedback.rating 
                                ? 'text-yellow-400 fill-yellow-400' 
                                : 'text-gray-300 hover:text-yellow-400'
                            }`}
                          />
                        </button>
                      ))}
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2 text-black">Your Review</label>
                    <Textarea
                      value={newFeedback.comment}
                      onChange={(e) => setNewFeedback({...newFeedback, comment: e.target.value})}
                      placeholder="Tell us about your experience..."
                      className="text-black"
                      rows={4}
                      required
                    />
                  </div>

                  <Button 
                    type="submit"
                    className="w-full bg-gradient-to-r from-sea-green-500 to-ocean-blue-500 hover:from-sea-green-600 hover:to-ocean-blue-600 text-black font-bold"
                  >
                    Submit Review
                  </Button>
                </form>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </section>
  );
};
