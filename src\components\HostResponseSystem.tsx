import React, { useState, useEffect } from 'react';
import { MessageCircle, Clock, Send, Star, Zap, Edit, Trash2, Plus, Search, Filter } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { toast } from '@/components/ui/use-toast';
import { cn } from '@/lib/utils';

interface QuickResponse {
  id: string;
  title: string;
  content: string;
  category: 'booking' | 'checkin' | 'amenities' | 'location' | 'policies' | 'general';
  tags: string[];
  usageCount: number;
  lastUsed?: Date;
  isActive: boolean;
}

interface HostResponseSystemProps {
  onResponseSelect: (response: QuickResponse) => void;
  onResponseSave: (response: Omit<QuickResponse, 'id' | 'usageCount' | 'lastUsed'>) => void;
  onResponseUpdate: (id: string, response: Partial<QuickResponse>) => void;
  onResponseDelete: (id: string) => void;
  className?: string;
}

export const HostResponseSystem: React.FC<HostResponseSystemProps> = ({
  onResponseSelect,
  onResponseSave,
  onResponseUpdate,
  onResponseDelete,
  className
}) => {
  const [responses, setResponses] = useState<QuickResponse[]>([
    {
      id: '1',
      title: 'Welcome Message',
      content: 'Welcome to our beautiful property! We\'re excited to host you. Check-in is from 3 PM onwards. Please let us know your estimated arrival time so we can prepare everything for you.',
      category: 'checkin',
      tags: ['welcome', 'checkin', 'arrival'],
      usageCount: 45,
      lastUsed: new Date(Date.now() - 86400000),
      isActive: true
    },
    {
      id: '2',
      title: 'WiFi Information',
      content: 'The WiFi network name is "StayFinder_Guest" and the password is "Welcome2024!". You\'ll find this information also posted in the living room. The connection is high-speed fiber, perfect for work or streaming.',
      category: 'amenities',
      tags: ['wifi', 'internet', 'password'],
      usageCount: 32,
      lastUsed: new Date(Date.now() - 172800000),
      isActive: true
    },
    {
      id: '3',
      title: 'Parking Instructions',
      content: 'Free parking is available right in front of the building. No permit required. If the front spots are full, there\'s additional parking around the corner on Oak Street.',
      category: 'location',
      tags: ['parking', 'car', 'location'],
      usageCount: 28,
      lastUsed: new Date(Date.now() - 259200000),
      isActive: true
    },
    {
      id: '4',
      title: 'Local Recommendations',
      content: 'For great local dining, I highly recommend The Waterfront Restaurant (5 min walk) for seafood, and Café Ubuntu (3 min walk) for coffee and light meals. The V&A Waterfront is just 10 minutes away with shopping and entertainment.',
      category: 'location',
      tags: ['restaurants', 'recommendations', 'local'],
      usageCount: 19,
      lastUsed: new Date(Date.now() - 345600000),
      isActive: true
    },
    {
      id: '5',
      title: 'Checkout Instructions',
      content: 'Checkout is by 11 AM. Please leave the keys on the kitchen counter and ensure all windows and doors are locked. No need to strip the beds or wash dishes - our cleaning team will handle everything. Thank you for staying with us!',
      category: 'policies',
      tags: ['checkout', 'keys', 'departure'],
      usageCount: 41,
      lastUsed: new Date(Date.now() - 432000000),
      isActive: true
    }
  ]);

  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editingResponse, setEditingResponse] = useState<QuickResponse | null>(null);
  const [newResponse, setNewResponse] = useState({
    title: '',
    content: '',
    category: 'general' as QuickResponse['category'],
    tags: [] as string[],
    isActive: true
  });

  const categories = {
    booking: 'Booking',
    checkin: 'Check-in',
    amenities: 'Amenities',
    location: 'Location',
    policies: 'Policies',
    general: 'General'
  };

  const categoryColors = {
    booking: 'bg-blue-100 text-blue-800',
    checkin: 'bg-green-100 text-green-800',
    amenities: 'bg-purple-100 text-purple-800',
    location: 'bg-orange-100 text-orange-800',
    policies: 'bg-red-100 text-red-800',
    general: 'bg-gray-100 text-gray-800'
  };

  // Filter responses based on search and category
  const filteredResponses = responses.filter(response => {
    const matchesSearch = searchQuery === '' || 
      response.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      response.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
      response.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    
    const matchesCategory = selectedCategory === 'all' || response.category === selectedCategory;
    
    return matchesSearch && matchesCategory && response.isActive;
  });

  // Sort by usage count and last used
  const sortedResponses = filteredResponses.sort((a, b) => {
    if (a.usageCount !== b.usageCount) {
      return b.usageCount - a.usageCount;
    }
    if (a.lastUsed && b.lastUsed) {
      return b.lastUsed.getTime() - a.lastUsed.getTime();
    }
    return 0;
  });

  const handleResponseSelect = (response: QuickResponse) => {
    // Update usage statistics
    const updatedResponse = {
      ...response,
      usageCount: response.usageCount + 1,
      lastUsed: new Date()
    };
    
    setResponses(prev => prev.map(r => r.id === response.id ? updatedResponse : r));
    onResponseSelect(updatedResponse);
    
    toast({
      title: "Response inserted",
      description: `"${response.title}" has been added to your message`,
    });
  };

  const handleSaveResponse = () => {
    if (!newResponse.title.trim() || !newResponse.content.trim()) {
      toast({
        title: "Missing information",
        description: "Please provide both title and content",
        variant: "destructive"
      });
      return;
    }

    const response: QuickResponse = {
      id: `response_${Date.now()}`,
      ...newResponse,
      usageCount: 0
    };

    setResponses(prev => [...prev, response]);
    onResponseSave(newResponse);
    
    setNewResponse({
      title: '',
      content: '',
      category: 'general',
      tags: [],
      isActive: true
    });
    setShowCreateForm(false);

    toast({
      title: "Response saved",
      description: "Your quick response has been created",
    });
  };

  const handleUpdateResponse = () => {
    if (!editingResponse) return;

    setResponses(prev => prev.map(r => r.id === editingResponse.id ? editingResponse : r));
    onResponseUpdate(editingResponse.id, editingResponse);
    setEditingResponse(null);

    toast({
      title: "Response updated",
      description: "Your changes have been saved",
    });
  };

  const handleDeleteResponse = (id: string) => {
    setResponses(prev => prev.filter(r => r.id !== id));
    onResponseDelete(id);

    toast({
      title: "Response deleted",
      description: "The quick response has been removed",
    });
  };

  const formatLastUsed = (date: Date) => {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) return 'Today';
    if (diffDays === 1) return 'Yesterday';
    if (diffDays < 7) return `${diffDays} days ago`;
    return date.toLocaleDateString();
  };

  const parseTagsInput = (input: string): string[] => {
    return input.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0);
  };

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Zap className="h-5 w-5" />
              <span>Quick Responses</span>
              <Badge variant="secondary">{responses.filter(r => r.isActive).length}</Badge>
            </div>
            <Button onClick={() => setShowCreateForm(true)}>
              <Plus className="h-4 w-4 mr-2" />
              New Response
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-600 mb-4">
            Save time with pre-written responses to common guest questions. Click any response to insert it into your message.
          </p>
          
          {/* Search and Filter */}
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search responses..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-9"
              />
            </div>
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All categories</SelectItem>
                {Object.entries(categories).map(([value, label]) => (
                  <SelectItem key={value} value={value}>{label}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Quick Responses List */}
      <div className="grid gap-4">
        {sortedResponses.map((response) => (
          <Card key={response.id} className="hover:shadow-md transition-shadow cursor-pointer group">
            <CardContent className="p-4">
              <div className="flex items-start justify-between">
                <div className="flex-1" onClick={() => handleResponseSelect(response)}>
                  <div className="flex items-center gap-2 mb-2">
                    <h3 className="font-semibold">{response.title}</h3>
                    <Badge 
                      variant="secondary" 
                      className={categoryColors[response.category]}
                    >
                      {categories[response.category]}
                    </Badge>
                    <div className="flex items-center gap-1 text-sm text-gray-500">
                      <Star className="h-3 w-3" />
                      <span>{response.usageCount}</span>
                    </div>
                  </div>
                  
                  <p className="text-gray-700 text-sm mb-3 line-clamp-2">
                    {response.content}
                  </p>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex flex-wrap gap-1">
                      {response.tags.map((tag, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                    
                    {response.lastUsed && (
                      <div className="flex items-center gap-1 text-xs text-gray-500">
                        <Clock className="h-3 w-3" />
                        <span>{formatLastUsed(response.lastUsed)}</span>
                      </div>
                    )}
                  </div>
                </div>
                
                <div className="flex items-center gap-1 ml-4 opacity-0 group-hover:opacity-100 transition-opacity">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      setEditingResponse(response);
                    }}
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDeleteResponse(response.id);
                    }}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {sortedResponses.length === 0 && (
        <Card>
          <CardContent className="p-8 text-center">
            <MessageCircle className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <h3 className="text-lg font-medium mb-2">No responses found</h3>
            <p className="text-gray-600 mb-4">
              {searchQuery || selectedCategory !== 'all' 
                ? 'Try adjusting your search or filters' 
                : 'Create your first quick response to get started'}
            </p>
            <Button onClick={() => setShowCreateForm(true)}>
              Create Quick Response
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Create/Edit Response Modal */}
      {(showCreateForm || editingResponse) && (
        <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
          <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                {editingResponse ? 'Edit Response' : 'Create Quick Response'}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setShowCreateForm(false);
                    setEditingResponse(null);
                  }}
                >
                  ×
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="title">Title</Label>
                <Input
                  id="title"
                  value={editingResponse ? editingResponse.title : newResponse.title}
                  onChange={(e) => {
                    if (editingResponse) {
                      setEditingResponse({ ...editingResponse, title: e.target.value });
                    } else {
                      setNewResponse({ ...newResponse, title: e.target.value });
                    }
                  }}
                  placeholder="e.g., WiFi Information"
                />
              </div>
              
              <div>
                <Label htmlFor="category">Category</Label>
                <Select 
                  value={editingResponse ? editingResponse.category : newResponse.category}
                  onValueChange={(value) => {
                    if (editingResponse) {
                      setEditingResponse({ ...editingResponse, category: value as QuickResponse['category'] });
                    } else {
                      setNewResponse({ ...newResponse, category: value as QuickResponse['category'] });
                    }
                  }}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.entries(categories).map(([value, label]) => (
                      <SelectItem key={value} value={value}>{label}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label htmlFor="content">Response Content</Label>
                <Textarea
                  id="content"
                  value={editingResponse ? editingResponse.content : newResponse.content}
                  onChange={(e) => {
                    if (editingResponse) {
                      setEditingResponse({ ...editingResponse, content: e.target.value });
                    } else {
                      setNewResponse({ ...newResponse, content: e.target.value });
                    }
                  }}
                  placeholder="Write your response here..."
                  rows={4}
                />
              </div>
              
              <div>
                <Label htmlFor="tags">Tags (comma-separated)</Label>
                <Input
                  id="tags"
                  value={editingResponse ? editingResponse.tags.join(', ') : newResponse.tags.join(', ')}
                  onChange={(e) => {
                    const tags = parseTagsInput(e.target.value);
                    if (editingResponse) {
                      setEditingResponse({ ...editingResponse, tags });
                    } else {
                      setNewResponse({ ...newResponse, tags });
                    }
                  }}
                  placeholder="e.g., wifi, internet, password"
                />
              </div>
              
              <div className="flex gap-3">
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowCreateForm(false);
                    setEditingResponse(null);
                  }}
                  className="flex-1"
                >
                  Cancel
                </Button>
                <Button
                  onClick={editingResponse ? handleUpdateResponse : handleSaveResponse}
                  className="flex-1"
                >
                  <Send className="h-4 w-4 mr-2" />
                  {editingResponse ? 'Update' : 'Save'} Response
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};
