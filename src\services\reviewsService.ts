interface Review {
  id: string;
  property_id: string;
  reviewer_id: string;
  booking_id: string;
  rating: number;
  comment: string;
  response?: string;
  response_date?: string;
  created_at: string;
  updated_at: string;
  reviewer_name: string;
  reviewer_profile_picture?: string;
  host_name: string;
  stay_date: string;
  has_response: boolean;
  property_title: string;
}

interface ReviewStats {
  total_reviews: number;
  average_rating: number;
  five_star: number;
  four_star: number;
  three_star: number;
  two_star: number;
  one_star: number;
  five_star_percent: number;
  four_star_percent: number;
  three_star_percent: number;
  two_star_percent: number;
  one_star_percent: number;
}

interface ReviewsData {
  reviews: Review[];
  stats: ReviewStats;
  pagination: {
    limit: number | null;
    offset: number;
    total: number;
  };
}

interface CreateReviewData {
  property_id: string;
  booking_id: string;
  rating: number;
  comment: string;
}

interface UpdateReviewData {
  rating?: number;
  comment?: string;
}

interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

class ReviewsService {
  private baseUrl = 'http://localhost:3001/api/reviews';

  private async makeRequest<T>(url: string, options: RequestInit = {}): Promise<ApiResponse<T>> {
    try {
      const token = localStorage.getItem('auth_token');
      const headers: HeadersInit = {
        'Content-Type': 'application/json',
        ...options.headers,
      };

      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const response = await fetch(url, {
        ...options,
        headers,
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Reviews API error:', error);
      throw error;
    }
  }

  async getPropertyReviews(
    propertyId: string,
    options: {
      limit?: number;
      offset?: number;
      sortBy?: 'created_at' | 'rating';
      sortOrder?: 'ASC' | 'DESC';
    } = {}
  ): Promise<ReviewsData> {
    try {
      const params = new URLSearchParams({
        property_id: propertyId,
        ...(options.limit && { limit: options.limit.toString() }),
        ...(options.offset && { offset: options.offset.toString() }),
        ...(options.sortBy && { sort_by: options.sortBy }),
        ...(options.sortOrder && { sort_order: options.sortOrder }),
      });

      const response = await this.makeRequest<ReviewsData>(`${this.baseUrl}?${params}`);
      
      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to fetch reviews');
      }
    } catch (error) {
      console.error('Error fetching property reviews:', error);
      // Return mock data for testing
      return this.getMockReviewsData(propertyId);
    }
  }

  async getPropertyRatingStats(propertyId: string): Promise<ReviewStats> {
    try {
      const params = new URLSearchParams({
        property_id: propertyId,
        stats_only: 'true',
      });

      const response = await this.makeRequest<ReviewStats>(`${this.baseUrl}?${params}`);
      
      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to fetch rating stats');
      }
    } catch (error) {
      console.error('Error fetching rating stats:', error);
      // Return mock stats for testing
      return this.getMockStats();
    }
  }

  async createReview(reviewData: CreateReviewData): Promise<string> {
    try {
      const response = await this.makeRequest<{ review_id: string }>(this.baseUrl, {
        method: 'POST',
        body: JSON.stringify(reviewData),
      });
      
      if (response.success && response.data) {
        return response.data.review_id;
      } else {
        throw new Error(response.error || 'Failed to create review');
      }
    } catch (error) {
      console.error('Error creating review:', error);
      throw error;
    }
  }

  async updateReview(reviewId: string, updateData: UpdateReviewData): Promise<boolean> {
    try {
      const params = new URLSearchParams({ review_id: reviewId });
      
      const response = await this.makeRequest<void>(`${this.baseUrl}?${params}`, {
        method: 'PUT',
        body: JSON.stringify(updateData),
      });
      
      return response.success;
    } catch (error) {
      console.error('Error updating review:', error);
      throw error;
    }
  }

  async addHostResponse(reviewId: string, response: string): Promise<boolean> {
    try {
      const params = new URLSearchParams({ 
        action: 'respond',
        review_id: reviewId 
      });
      
      const apiResponse = await this.makeRequest<void>(`${this.baseUrl}?${params}`, {
        method: 'POST',
        body: JSON.stringify({ response }),
      });
      
      return apiResponse.success;
    } catch (error) {
      console.error('Error adding host response:', error);
      throw error;
    }
  }

  // Helper methods
  formatRating(rating: number): string {
    return rating.toFixed(1);
  }

  formatReviewDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }

  getTimeAgo(dateString: string): string {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMs = now.getTime() - date.getTime();
    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));
    
    if (diffInDays === 0) return 'Today';
    if (diffInDays === 1) return 'Yesterday';
    if (diffInDays < 7) return `${diffInDays} days ago`;
    if (diffInDays < 30) return `${Math.floor(diffInDays / 7)} weeks ago`;
    if (diffInDays < 365) return `${Math.floor(diffInDays / 30)} months ago`;
    return `${Math.floor(diffInDays / 365)} years ago`;
  }

  canEditReview(createdAt: string): boolean {
    const createdDate = new Date(createdAt);
    const now = new Date();
    const hoursSinceCreated = (now.getTime() - createdDate.getTime()) / (1000 * 60 * 60);
    return hoursSinceCreated <= 24;
  }

  validateReviewData(data: CreateReviewData | UpdateReviewData): { isValid: boolean; errors: Record<string, string> } {
    const errors: Record<string, string> = {};

    if ('rating' in data && data.rating !== undefined) {
      if (data.rating < 1 || data.rating > 5) {
        errors.rating = 'Rating must be between 1 and 5';
      }
    }

    if ('comment' in data && data.comment !== undefined) {
      if (data.comment.trim().length < 10) {
        errors.comment = 'Comment must be at least 10 characters long';
      }
      if (data.comment.length > 1000) {
        errors.comment = 'Comment must be less than 1000 characters';
      }
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  }

  // Mock data for testing
  getMockReviewsData(propertyId: string): ReviewsData {
    const mockReviews: Review[] = [
      {
        id: 'review-1',
        property_id: propertyId,
        reviewer_id: 'user-1',
        booking_id: 'booking-1',
        rating: 5,
        comment: 'Absolutely amazing stay! The villa was exactly as described and the host was incredibly helpful. The ocean views were breathtaking and we loved the private pool. Would definitely stay here again!',
        response: 'Thank you so much for the wonderful review! We\'re thrilled you enjoyed your stay and hope to welcome you back soon.',
        response_date: '2024-05-21T10:30:00Z',
        created_at: '2024-05-20T10:30:00Z',
        updated_at: '2024-05-20T10:30:00Z',
        reviewer_name: 'Sarah Johnson',
        reviewer_profile_picture: '/placeholder.svg',
        host_name: 'Jane Smith',
        stay_date: '2024-05-15',
        has_response: true,
        property_title: 'Luxury Villa in Margate'
      },
      {
        id: 'review-2',
        property_id: propertyId,
        reviewer_id: 'user-2',
        booking_id: 'booking-2',
        rating: 4,
        comment: 'Great location right on the beach. The cottage was clean and comfortable. Only minor issue was the WiFi was a bit slow, but overall a fantastic experience.',
        created_at: '2024-04-25T14:15:00Z',
        updated_at: '2024-04-25T14:15:00Z',
        reviewer_name: 'Mike Wilson',
        host_name: 'Jane Smith',
        stay_date: '2024-04-20',
        has_response: false,
        property_title: 'Luxury Villa in Margate'
      },
      {
        id: 'review-3',
        property_id: propertyId,
        reviewer_id: 'user-3',
        booking_id: 'booking-3',
        rating: 5,
        comment: 'Perfect getaway! The property exceeded our expectations. Beautiful views, excellent amenities, and the host was very responsive to our questions.',
        response: 'We\'re so happy you had a perfect getaway! Thank you for taking care of our property.',
        response_date: '2024-03-12T09:00:00Z',
        created_at: '2024-03-10T16:45:00Z',
        updated_at: '2024-03-10T16:45:00Z',
        reviewer_name: 'Emily Davis',
        host_name: 'Jane Smith',
        stay_date: '2024-03-05',
        has_response: true,
        property_title: 'Luxury Villa in Margate'
      }
    ];

    const mockStats: ReviewStats = {
      total_reviews: 3,
      average_rating: 4.7,
      five_star: 2,
      four_star: 1,
      three_star: 0,
      two_star: 0,
      one_star: 0,
      five_star_percent: 66.7,
      four_star_percent: 33.3,
      three_star_percent: 0,
      two_star_percent: 0,
      one_star_percent: 0
    };

    return {
      reviews: mockReviews,
      stats: mockStats,
      pagination: {
        limit: null,
        offset: 0,
        total: 3
      }
    };
  }

  getMockStats(): ReviewStats {
    return {
      total_reviews: 3,
      average_rating: 4.7,
      five_star: 2,
      four_star: 1,
      three_star: 0,
      two_star: 0,
      one_star: 0,
      five_star_percent: 66.7,
      four_star_percent: 33.3,
      three_star_percent: 0,
      two_star_percent: 0,
      one_star_percent: 0
    };
  }
}

export const reviewsService = new ReviewsService();
export type { Review, ReviewStats, ReviewsData, CreateReviewData, UpdateReviewData, ApiResponse };
