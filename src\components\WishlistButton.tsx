import React, { useState } from 'react';
import { Heart, MessageSquare, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { useWishlist } from '@/contexts/WishlistContext';
import { Property } from '@/contexts/ComparisonContext';
import { cn } from '@/lib/utils';
import { toast } from '@/components/ui/use-toast';

interface WishlistButtonProps {
  property: Property;
  variant?: 'default' | 'icon' | 'compact';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  showAnimation?: boolean;
}

export const WishlistButton: React.FC<WishlistButtonProps> = ({
  property,
  variant = 'icon',
  size = 'md',
  className,
  showAnimation = true
}) => {
  const { addToWishlist, removeFromWishlist, isInWishlist, getWishlistItem } = useWishlist();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [notes, setNotes] = useState('');
  const [isAnimating, setIsAnimating] = useState(false);

  const isWishlisted = isInWishlist(property.id);
  const wishlistItem = getWishlistItem(property.id);

  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (isWishlisted) {
      removeFromWishlist(property.id);
      toast({
        title: "Removed from wishlist",
        description: `${property.title} has been removed from your wishlist.`,
      });
    } else {
      if (variant === 'default') {
        // Open dialog for notes
        setIsDialogOpen(true);
      } else {
        // Add directly without notes
        addToWishlist(property);
        if (showAnimation) {
          setIsAnimating(true);
          setTimeout(() => setIsAnimating(false), 600);
        }
        toast({
          title: "Added to wishlist",
          description: `${property.title} has been saved to your wishlist.`,
        });
      }
    }
  };

  const handleAddWithNotes = () => {
    addToWishlist(property, notes);
    setIsDialogOpen(false);
    setNotes('');
    if (showAnimation) {
      setIsAnimating(true);
      setTimeout(() => setIsAnimating(false), 600);
    }
    toast({
      title: "Added to wishlist",
      description: `${property.title} has been saved to your wishlist.`,
    });
  };

  const getButtonSize = () => {
    switch (size) {
      case 'sm':
        return 'w-8 h-8';
      case 'lg':
        return 'w-12 h-12';
      default:
        return 'w-10 h-10';
    }
  };

  const getIconSize = () => {
    switch (size) {
      case 'sm':
        return 'h-4 w-4';
      case 'lg':
        return 'h-6 w-6';
      default:
        return 'h-5 w-5';
    }
  };

  if (variant === 'compact') {
    return (
      <Button
        variant={isWishlisted ? "default" : "outline"}
        size="sm"
        onClick={handleClick}
        className={cn(
          "text-xs transition-all duration-200",
          isWishlisted 
            ? "bg-red-500 hover:bg-red-600 text-white border-red-500" 
            : "border-gray-300 hover:border-red-500 hover:text-red-600",
          className
        )}
      >
        <Heart 
          className={cn(
            "h-3 w-3 mr-1 transition-all duration-200",
            isWishlisted && "fill-current"
          )} 
        />
        {isWishlisted ? 'Saved' : 'Save'}
      </Button>
    );
  }

  if (variant === 'default') {
    return (
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogTrigger asChild>
          <Button
            variant={isWishlisted ? "default" : "outline"}
            onClick={handleClick}
            className={cn(
              "transition-all duration-200",
              isWishlisted 
                ? "bg-red-500 hover:bg-red-600 text-white border-red-500" 
                : "border-gray-300 hover:border-red-500 hover:text-red-600",
              className
            )}
          >
            <Heart 
              className={cn(
                "h-4 w-4 mr-2 transition-all duration-200",
                isWishlisted && "fill-current"
              )} 
            />
            {isWishlisted ? 'Saved to Wishlist' : 'Save to Wishlist'}
          </Button>
        </DialogTrigger>
        
        {!isWishlisted && (
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>Save to Wishlist</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                <img
                  src={property.images[0] || '/placeholder-property.jpg'}
                  alt={property.title}
                  className="w-12 h-12 rounded-lg object-cover"
                />
                <div>
                  <p className="font-medium text-sm">{property.title}</p>
                  <p className="text-xs text-gray-600">{property.location}</p>
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="notes">Notes (optional)</Label>
                <Textarea
                  id="notes"
                  placeholder="Add a note about why you saved this property..."
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  className="resize-none"
                  rows={3}
                />
              </div>
              
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  onClick={() => setIsDialogOpen(false)}
                  className="flex-1"
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleAddWithNotes}
                  className="flex-1 bg-red-500 hover:bg-red-600 text-white"
                >
                  <Heart className="h-4 w-4 mr-2" />
                  Save
                </Button>
              </div>
            </div>
          </DialogContent>
        )}
      </Dialog>
    );
  }

  // Icon variant (default)
  return (
    <Button
      variant="ghost"
      onClick={handleClick}
      className={cn(
        getButtonSize(),
        "p-0 rounded-full transition-all duration-200 relative",
        "bg-white/80 backdrop-blur-sm hover:bg-white/90",
        "border border-gray-200 hover:border-red-300",
        "shadow-sm hover:shadow-md",
        isAnimating && showAnimation && "animate-pulse",
        className
      )}
      title={isWishlisted ? "Remove from wishlist" : "Add to wishlist"}
    >
      <Heart 
        className={cn(
          getIconSize(),
          "transition-all duration-300",
          isWishlisted 
            ? "fill-red-500 text-red-500 scale-110" 
            : "text-gray-600 hover:text-red-500 hover:scale-110"
        )} 
      />
      
      {/* Animation effect */}
      {isAnimating && showAnimation && (
        <div className="absolute inset-0 rounded-full">
          <div className="absolute inset-0 rounded-full bg-red-500/20 animate-ping" />
          <div className="absolute inset-0 rounded-full bg-red-500/10 animate-ping animation-delay-150" />
        </div>
      )}
    </Button>
  );
};

// Wishlist counter badge
export const WishlistCounter: React.FC<{ className?: string }> = ({ className }) => {
  const { wishlistCount } = useWishlist();

  if (wishlistCount === 0) {
    return null;
  }

  return (
    <div className={cn(
      "absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-medium",
      className
    )}>
      {wishlistCount > 99 ? '99+' : wishlistCount}
    </div>
  );
};

// Wishlist item with notes display
export const WishlistItemCard: React.FC<{ 
  item: any; 
  onRemove: (id: string) => void;
  onUpdateNotes: (id: string, notes: string) => void;
}> = ({ item, onRemove, onUpdateNotes }) => {
  const [isEditingNotes, setIsEditingNotes] = useState(false);
  const [notes, setNotes] = useState(item.notes || '');

  const handleSaveNotes = () => {
    onUpdateNotes(item.id, notes);
    setIsEditingNotes(false);
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
      <div className="aspect-video relative">
        <img
          src={item.images[0] || '/placeholder-property.jpg'}
          alt={item.title}
          className="w-full h-full object-cover"
        />
        <Button
          variant="ghost"
          size="sm"
          onClick={() => onRemove(item.id)}
          className="absolute top-2 right-2 w-8 h-8 p-0 rounded-full bg-white/80 hover:bg-white/90"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>
      
      <div className="p-4 space-y-3">
        <div>
          <h3 className="font-semibold line-clamp-1">{item.title}</h3>
          <p className="text-sm text-gray-600">{item.location}</p>
          <p className="text-lg font-bold text-sea-green-600">
            R{item.price.toLocaleString()}/night
          </p>
        </div>
        
        <div className="text-xs text-gray-500">
          Saved {item.addedAt.toLocaleDateString()}
        </div>
        
        {isEditingNotes ? (
          <div className="space-y-2">
            <Textarea
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder="Add a note..."
              className="text-sm resize-none"
              rows={2}
            />
            <div className="flex gap-2">
              <Button size="sm" onClick={handleSaveNotes} className="flex-1">
                Save
              </Button>
              <Button 
                size="sm" 
                variant="outline" 
                onClick={() => setIsEditingNotes(false)}
                className="flex-1"
              >
                Cancel
              </Button>
            </div>
          </div>
        ) : (
          <div className="space-y-2">
            {item.notes && (
              <p className="text-sm text-gray-700 bg-gray-50 p-2 rounded">
                {item.notes}
              </p>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsEditingNotes(true)}
              className="w-full text-xs"
            >
              <MessageSquare className="h-3 w-3 mr-1" />
              {item.notes ? 'Edit Note' : 'Add Note'}
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};
