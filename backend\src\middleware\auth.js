const jwt = require('jsonwebtoken');
const { findOne } = require('../utils/database');

// Middleware to verify JWT token
const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      return res.status(401).json({
        error: 'Access token required'
      });
    }

    // Verify token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // Get user from database to ensure they still exist
    const user = await findOne(
      'SELECT id, email, role, email_verified FROM users WHERE id = ?',
      [decoded.userId]
    );

    if (!user) {
      return res.status(401).json({
        error: 'Invalid token - user not found'
      });
    }

    // Add user info to request object
    req.user = {
      id: user.id,
      email: user.email,
      role: user.role,
      emailVerified: user.email_verified
    };

    next();
  } catch (error) {
    console.error('Authentication error:', error);
    
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        error: 'Invalid token'
      });
    }
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        error: 'Token expired'
      });
    }

    return res.status(500).json({
      error: 'Authentication failed'
    });
  }
};

// Middleware to check if user has specific role
const requireRole = (roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        error: 'Authentication required'
      });
    }

    const userRole = req.user.role;
    const allowedRoles = Array.isArray(roles) ? roles : [roles];

    if (!allowedRoles.includes(userRole)) {
      return res.status(403).json({
        error: 'Insufficient permissions',
        required: allowedRoles,
        current: userRole
      });
    }

    next();
  };
};

// Middleware to check if user is property owner or admin
const requireOwnershipOrAdmin = async (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        error: 'Authentication required'
      });
    }

    const { id: propertyId } = req.params;
    const userId = req.user.id;
    const userRole = req.user.role;

    // Admin can access everything
    if (userRole === 'admin') {
      return next();
    }

    // Check if user owns the property
    const property = await findOne(
      'SELECT owner_id FROM properties WHERE id = ?',
      [propertyId]
    );

    if (!property) {
      return res.status(404).json({
        error: 'Property not found'
      });
    }

    if (property.owner_id !== userId) {
      return res.status(403).json({
        error: 'Access denied - not property owner'
      });
    }

    next();
  } catch (error) {
    console.error('Ownership check error:', error);
    res.status(500).json({
      error: 'Authorization check failed'
    });
  }
};

// Middleware to check if user is booking owner or admin
const requireBookingOwnershipOrAdmin = async (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        error: 'Authentication required'
      });
    }

    const { id: bookingId } = req.params;
    const userId = req.user.id;
    const userRole = req.user.role;

    // Admin can access everything
    if (userRole === 'admin') {
      return next();
    }

    // Check if user owns the booking or the property
    const booking = await findOne(`
      SELECT b.guest_id, p.owner_id 
      FROM bookings b
      LEFT JOIN properties p ON b.property_id = p.id
      WHERE b.id = ?
    `, [bookingId]);

    if (!booking) {
      return res.status(404).json({
        error: 'Booking not found'
      });
    }

    // User can access if they are the guest or the property owner
    if (booking.guest_id !== userId && booking.owner_id !== userId) {
      return res.status(403).json({
        error: 'Access denied - not booking participant'
      });
    }

    next();
  } catch (error) {
    console.error('Booking ownership check error:', error);
    res.status(500).json({
      error: 'Authorization check failed'
    });
  }
};

// Optional authentication - doesn't fail if no token provided
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
      req.user = null;
      return next();
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    const user = await findOne(
      'SELECT id, email, role, email_verified FROM users WHERE id = ?',
      [decoded.userId]
    );

    req.user = user ? {
      id: user.id,
      email: user.email,
      role: user.role,
      emailVerified: user.email_verified
    } : null;

    next();
  } catch (error) {
    // If token is invalid, just continue without user
    req.user = null;
    next();
  }
};

module.exports = {
  authenticateToken,
  requireRole,
  requireOwnershipOrAdmin,
  requireBookingOwnershipOrAdmin,
  optionalAuth
};
