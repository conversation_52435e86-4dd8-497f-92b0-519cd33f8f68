import React from 'react';
import { FileText, Calendar, Shield, AlertCircle } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';

export const TermsOfService: React.FC = () => {
  const lastUpdated = "December 27, 2024";

  const sections = [
    {
      title: "1. Acceptance of Terms",
      content: `By accessing and using StayFinder ("the Platform"), you accept and agree to be bound by the terms and provision of this agreement. If you do not agree to abide by the above, please do not use this service.`
    },
    {
      title: "2. Use License",
      content: `Permission is granted to temporarily download one copy of the materials on StayFinder for personal, non-commercial transitory viewing only. This is the grant of a license, not a transfer of title, and under this license you may not:
      
      • Modify or copy the materials
      • Use the materials for any commercial purpose or for any public display
      • Attempt to reverse engineer any software contained on the Platform
      • Remove any copyright or other proprietary notations from the materials`
    },
    {
      title: "3. User Accounts",
      content: `To access certain features of the Platform, you must register for an account. You agree to:
      
      • Provide accurate, current, and complete information during registration
      • Maintain and promptly update your account information
      • Maintain the security of your password and accept all risks of unauthorized access
      • Immediately notify us of any unauthorized use of your account`
    },
    {
      title: "4. Booking and Payments",
      content: `When you make a booking through StayFinder:
      
      • You enter into a direct contractual relationship with the property owner/host
      • StayFinder acts as an intermediary platform
      • Payment processing is handled securely through our payment partners
      • Cancellation policies are set by individual property owners
      • Refunds are subject to the applicable cancellation policy`
    },
    {
      title: "5. User Conduct",
      content: `You agree not to use the Platform to:
      
      • Violate any applicable laws or regulations
      • Transmit any harmful, threatening, abusive, or defamatory content
      • Impersonate any person or entity
      • Interfere with or disrupt the Platform's operation
      • Collect or harvest any personally identifiable information
      • Use automated systems to access the Platform without permission`
    },
    {
      title: "6. Property Listings",
      content: `Property owners and hosts agree that:
      
      • All listing information must be accurate and up-to-date
      • Properties must comply with local laws and regulations
      • You have the right to rent or sublease the property
      • You will honor confirmed bookings according to your stated policies
      • You will maintain appropriate insurance coverage`
    },
    {
      title: "7. Reviews and Content",
      content: `Users may submit reviews and other content. By submitting content, you:
      
      • Grant StayFinder a non-exclusive, royalty-free license to use the content
      • Represent that the content is accurate and does not violate any rights
      • Agree that reviews must be based on genuine experiences
      • Understand that StayFinder may moderate or remove inappropriate content`
    },
    {
      title: "8. Privacy and Data Protection",
      content: `Your privacy is important to us. Our Privacy Policy explains how we collect, use, and protect your information. By using the Platform, you consent to our data practices as described in our Privacy Policy.`
    },
    {
      title: "9. Disclaimers",
      content: `The materials on StayFinder are provided on an 'as is' basis. StayFinder makes no warranties, expressed or implied, and hereby disclaims and negates all other warranties including without limitation, implied warranties or conditions of merchantability, fitness for a particular purpose, or non-infringement of intellectual property or other violation of rights.`
    },
    {
      title: "10. Limitations of Liability",
      content: `In no event shall StayFinder or its suppliers be liable for any damages (including, without limitation, damages for loss of data or profit, or due to business interruption) arising out of the use or inability to use the materials on StayFinder, even if StayFinder or its authorized representative has been notified orally or in writing of the possibility of such damage.`
    },
    {
      title: "11. Indemnification",
      content: `You agree to indemnify and hold harmless StayFinder, its officers, directors, employees, and agents from any claims, damages, losses, or expenses arising from your use of the Platform or violation of these Terms.`
    },
    {
      title: "12. Termination",
      content: `We may terminate or suspend your account and access to the Platform immediately, without prior notice or liability, for any reason whatsoever, including without limitation if you breach the Terms of Service.`
    },
    {
      title: "13. Governing Law",
      content: `These Terms shall be governed and construed in accordance with the laws of South Africa, without regard to its conflict of law provisions. Any disputes arising from these Terms will be subject to the exclusive jurisdiction of the courts of South Africa.`
    },
    {
      title: "14. Changes to Terms",
      content: `StayFinder reserves the right, at our sole discretion, to modify or replace these Terms at any time. If a revision is material, we will try to provide at least 30 days notice prior to any new terms taking effect.`
    },
    {
      title: "15. Contact Information",
      content: `If you have any questions about these Terms of Service, please contact us at:
      
      Email: <EMAIL>
      Phone: +27 21 123 4567
      Address: 123 Long Street, Cape Town, 8001, South Africa`
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-sea-green-600 to-ocean-blue-600 text-white py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <FileText className="h-16 w-16 mx-auto mb-6 opacity-90" />
            <h1 className="text-4xl font-bold mb-4">Terms of Service</h1>
            <p className="text-xl opacity-90 mb-6">
              Please read these terms carefully before using StayFinder
            </p>
            <div className="flex items-center justify-center gap-4 text-sm">
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                <span>Last updated: {lastUpdated}</span>
              </div>
              <Badge variant="secondary" className="bg-white/20 text-white">
                Version 1.0
              </Badge>
            </div>
          </div>
        </div>
      </section>

      <div className="container mx-auto px-4 py-12">
        <div className="max-w-4xl mx-auto">
          {/* Important Notice */}
          <Card className="mb-8 border-orange-200 bg-orange-50">
            <CardContent className="p-6">
              <div className="flex items-start gap-3">
                <AlertCircle className="h-6 w-6 text-orange-600 flex-shrink-0 mt-0.5" />
                <div>
                  <h3 className="font-semibold text-orange-800 mb-2">Important Notice</h3>
                  <p className="text-orange-700 text-sm leading-relaxed">
                    These Terms of Service constitute a legally binding agreement between you and StayFinder. 
                    By using our platform, you acknowledge that you have read, understood, and agree to be bound by these terms. 
                    If you do not agree with any part of these terms, you must not use our services.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Table of Contents */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Table of Contents
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-2 gap-2">
                {sections.map((section, index) => (
                  <a
                    key={index}
                    href={`#section-${index + 1}`}
                    className="text-sm text-sea-green-600 hover:text-sea-green-700 hover:underline py-1 block"
                  >
                    {section.title}
                  </a>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Terms Sections */}
          <div className="space-y-6">
            {sections.map((section, index) => (
              <Card key={index} id={`section-${index + 1}`}>
                <CardHeader>
                  <CardTitle className="text-xl text-gray-900">
                    {section.title}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="prose prose-gray max-w-none">
                    {section.content.split('\n').map((paragraph, pIndex) => {
                      if (paragraph.trim().startsWith('•')) {
                        return (
                          <ul key={pIndex} className="list-disc list-inside ml-4 mb-4">
                            <li className="text-gray-700 leading-relaxed">
                              {paragraph.trim().substring(1).trim()}
                            </li>
                          </ul>
                        );
                      } else if (paragraph.trim()) {
                        return (
                          <p key={pIndex} className="text-gray-700 leading-relaxed mb-4">
                            {paragraph.trim()}
                          </p>
                        );
                      }
                      return null;
                    })}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Footer */}
          <Card className="mt-12 bg-gradient-to-r from-sea-green-50 to-ocean-blue-50 border-sea-green-200">
            <CardContent className="p-8 text-center">
              <Shield className="h-12 w-12 mx-auto mb-4 text-sea-green-600" />
              <h3 className="text-xl font-semibold mb-4">Questions about our Terms?</h3>
              <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
                If you have any questions about these Terms of Service, please don't hesitate to contact our legal team. 
                We're here to help clarify any concerns you may have.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <a
                  href="mailto:<EMAIL>"
                  className="inline-flex items-center justify-center px-6 py-3 bg-sea-green-600 text-white rounded-lg hover:bg-sea-green-700 transition-colors"
                >
                  Contact Legal Team
                </a>
                <a
                  href="/privacy-policy"
                  className="inline-flex items-center justify-center px-6 py-3 border border-sea-green-600 text-sea-green-600 rounded-lg hover:bg-sea-green-50 transition-colors"
                >
                  View Privacy Policy
                </a>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};
