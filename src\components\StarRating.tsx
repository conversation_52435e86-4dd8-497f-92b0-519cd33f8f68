import React, { useState } from 'react';
import { Star } from 'lucide-react';
import { cn } from '@/lib/utils';

interface StarRatingProps {
  rating: number;
  onRatingChange?: (rating: number) => void;
  readonly?: boolean;
  size?: 'sm' | 'md' | 'lg';
  showValue?: boolean;
  className?: string;
}

export const StarRating: React.FC<StarRatingProps> = ({
  rating,
  onRatingChange,
  readonly = false,
  size = 'md',
  showValue = false,
  className
}) => {
  const [hoverRating, setHoverRating] = useState(0);

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'h-3 w-3';
      case 'lg':
        return 'h-6 w-6';
      default:
        return 'h-4 w-4';
    }
  };

  const getTextSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'text-xs';
      case 'lg':
        return 'text-lg';
      default:
        return 'text-sm';
    }
  };

  const handleStarClick = (starRating: number) => {
    if (!readonly && onRatingChange) {
      onRatingChange(starRating);
    }
  };

  const handleStarHover = (starRating: number) => {
    if (!readonly) {
      setHoverRating(starRating);
    }
  };

  const handleMouseLeave = () => {
    if (!readonly) {
      setHoverRating(0);
    }
  };

  const displayRating = hoverRating || rating;

  return (
    <div className={cn('flex items-center gap-1', className)}>
      <div 
        className="flex items-center"
        onMouseLeave={handleMouseLeave}
      >
        {[1, 2, 3, 4, 5].map((star) => {
          const isFilled = star <= displayRating;
          const isPartiallyFilled = star - 0.5 <= displayRating && displayRating < star;
          
          return (
            <button
              key={star}
              type="button"
              className={cn(
                'relative transition-colors duration-150',
                !readonly && 'hover:scale-110 cursor-pointer',
                readonly && 'cursor-default'
              )}
              onClick={() => handleStarClick(star)}
              onMouseEnter={() => handleStarHover(star)}
              disabled={readonly}
            >
              <Star
                className={cn(
                  getSizeClasses(),
                  'transition-colors duration-150',
                  isFilled
                    ? 'fill-yellow-400 text-yellow-400'
                    : isPartiallyFilled
                    ? 'fill-yellow-200 text-yellow-400'
                    : readonly
                    ? 'fill-gray-200 text-gray-300'
                    : 'fill-gray-100 text-gray-300 hover:fill-yellow-200 hover:text-yellow-400'
                )}
              />
              
              {/* Partial fill for half stars */}
              {isPartiallyFilled && (
                <Star
                  className={cn(
                    getSizeClasses(),
                    'absolute top-0 left-0 fill-yellow-400 text-yellow-400',
                    'clip-path-half'
                  )}
                  style={{
                    clipPath: 'polygon(0 0, 50% 0, 50% 100%, 0 100%)'
                  }}
                />
              )}
            </button>
          );
        })}
      </div>
      
      {showValue && (
        <span className={cn(
          'font-medium text-gray-700 ml-1',
          getTextSizeClasses()
        )}>
          {rating.toFixed(1)}
        </span>
      )}
      
      {!readonly && hoverRating > 0 && (
        <span className={cn(
          'text-gray-500 ml-2',
          getTextSizeClasses()
        )}>
          {hoverRating === 1 ? 'Poor' :
           hoverRating === 2 ? 'Fair' :
           hoverRating === 3 ? 'Good' :
           hoverRating === 4 ? 'Very Good' :
           'Excellent'}
        </span>
      )}
    </div>
  );
};

// Rating display component for showing average ratings
interface RatingDisplayProps {
  rating: number;
  reviewCount?: number;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export const RatingDisplay: React.FC<RatingDisplayProps> = ({
  rating,
  reviewCount,
  size = 'md',
  className
}) => {
  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'h-3 w-3';
      case 'lg':
        return 'h-5 w-5';
      default:
        return 'h-4 w-4';
    }
  };

  const getTextSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'text-xs';
      case 'lg':
        return 'text-base';
      default:
        return 'text-sm';
    }
  };

  if (rating === 0 && (!reviewCount || reviewCount === 0)) {
    return (
      <div className={cn('flex items-center gap-1 text-gray-500', className)}>
        <Star className={cn(getSizeClasses(), 'fill-gray-200 text-gray-300')} />
        <span className={getTextSizeClasses()}>No reviews yet</span>
      </div>
    );
  }

  return (
    <div className={cn('flex items-center gap-1', className)}>
      <Star className={cn(getSizeClasses(), 'fill-yellow-400 text-yellow-400')} />
      <span className={cn('font-medium text-gray-900', getTextSizeClasses())}>
        {rating.toFixed(1)}
      </span>
      {reviewCount !== undefined && (
        <span className={cn('text-gray-600', getTextSizeClasses())}>
          ({reviewCount} {reviewCount === 1 ? 'review' : 'reviews'})
        </span>
      )}
    </div>
  );
};

// Rating breakdown component for detailed statistics
interface RatingBreakdownProps {
  ratings: {
    5: number;
    4: number;
    3: number;
    2: number;
    1: number;
  };
  totalReviews: number;
  className?: string;
}

export const RatingBreakdown: React.FC<RatingBreakdownProps> = ({
  ratings,
  totalReviews,
  className
}) => {
  const getPercentage = (count: number) => {
    return totalReviews > 0 ? (count / totalReviews) * 100 : 0;
  };

  return (
    <div className={cn('space-y-2', className)}>
      {[5, 4, 3, 2, 1].map((stars) => {
        const count = ratings[stars as keyof typeof ratings];
        const percentage = getPercentage(count);
        
        return (
          <div key={stars} className="flex items-center gap-2 text-sm">
            <div className="flex items-center gap-1 w-12">
              <span className="text-gray-700">{stars}</span>
              <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
            </div>
            <div className="flex-1 bg-gray-200 rounded-full h-2">
              <div
                className="bg-yellow-400 h-2 rounded-full transition-all duration-300"
                style={{ width: `${percentage}%` }}
              />
            </div>
            <span className="text-gray-600 w-8 text-right">{count}</span>
          </div>
        );
      })}
    </div>
  );
};
