import React, { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';

interface ResponsiveContainerProps {
  children: React.ReactNode;
  className?: string;
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full';
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  centerContent?: boolean;
  mobileFirst?: boolean;
}

interface ViewportInfo {
  width: number;
  height: number;
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  orientation: 'portrait' | 'landscape';
  touchDevice: boolean;
}

// Hook for responsive design
export const useResponsive = () => {
  const [viewport, setViewport] = useState<ViewportInfo>({
    width: typeof window !== 'undefined' ? window.innerWidth : 1024,
    height: typeof window !== 'undefined' ? window.innerHeight : 768,
    isMobile: false,
    isTablet: false,
    isDesktop: true,
    orientation: 'landscape',
    touchDevice: false
  });

  useEffect(() => {
    const updateViewport = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;
      const isMobile = width < 768;
      const isTablet = width >= 768 && width < 1024;
      const isDesktop = width >= 1024;
      const orientation = height > width ? 'portrait' : 'landscape';
      const touchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;

      setViewport({
        width,
        height,
        isMobile,
        isTablet,
        isDesktop,
        orientation,
        touchDevice
      });
    };

    updateViewport();
    window.addEventListener('resize', updateViewport);
    window.addEventListener('orientationchange', updateViewport);

    return () => {
      window.removeEventListener('resize', updateViewport);
      window.removeEventListener('orientationchange', updateViewport);
    };
  }, []);

  return viewport;
};

// Responsive container component
export const ResponsiveContainer: React.FC<ResponsiveContainerProps> = ({
  children,
  className,
  maxWidth = 'xl',
  padding = 'md',
  centerContent = true,
  mobileFirst = true
}) => {
  const viewport = useResponsive();

  const maxWidthClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-7xl',
    '2xl': 'max-w-none',
    full: 'max-w-full'
  };

  const paddingClasses = {
    none: '',
    sm: 'px-2 py-1',
    md: 'px-4 py-2',
    lg: 'px-6 py-4',
    xl: 'px-8 py-6'
  };

  return (
    <div
      className={cn(
        'w-full',
        maxWidthClasses[maxWidth],
        paddingClasses[padding],
        centerContent && 'mx-auto',
        mobileFirst && 'mobile-first',
        viewport.isMobile && 'is-mobile',
        viewport.isTablet && 'is-tablet',
        viewport.isDesktop && 'is-desktop',
        viewport.touchDevice && 'is-touch',
        className
      )}
    >
      {children}
    </div>
  );
};

// Touch-friendly component wrapper
export const TouchFriendly: React.FC<{
  children: React.ReactNode;
  className?: string;
  minTouchTarget?: boolean;
}> = ({ children, className, minTouchTarget = true }) => {
  const viewport = useResponsive();

  return (
    <div
      className={cn(
        viewport.touchDevice && minTouchTarget && 'min-h-[44px] min-w-[44px]',
        viewport.touchDevice && 'touch-manipulation',
        className
      )}
    >
      {children}
    </div>
  );
};

// Responsive grid component
export const ResponsiveGrid: React.FC<{
  children: React.ReactNode;
  columns?: {
    mobile?: number;
    tablet?: number;
    desktop?: number;
  };
  gap?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
}> = ({ 
  children, 
  columns = { mobile: 1, tablet: 2, desktop: 3 },
  gap = 'md',
  className 
}) => {
  const gapClasses = {
    sm: 'gap-2',
    md: 'gap-4',
    lg: 'gap-6',
    xl: 'gap-8'
  };

  return (
    <div
      className={cn(
        'grid',
        `grid-cols-${columns.mobile}`,
        `md:grid-cols-${columns.tablet}`,
        `lg:grid-cols-${columns.desktop}`,
        gapClasses[gap],
        className
      )}
    >
      {children}
    </div>
  );
};

// Responsive text component
export const ResponsiveText: React.FC<{
  children: React.ReactNode;
  size?: {
    mobile?: string;
    tablet?: string;
    desktop?: string;
  };
  weight?: {
    mobile?: string;
    tablet?: string;
    desktop?: string;
  };
  className?: string;
}> = ({ 
  children, 
  size = { mobile: 'text-sm', tablet: 'text-base', desktop: 'text-lg' },
  weight = { mobile: 'font-normal', tablet: 'font-normal', desktop: 'font-normal' },
  className 
}) => {
  return (
    <span
      className={cn(
        size.mobile,
        `md:${size.tablet}`,
        `lg:${size.desktop}`,
        weight.mobile,
        `md:${weight.tablet}`,
        `lg:${weight.desktop}`,
        className
      )}
    >
      {children}
    </span>
  );
};

// Responsive image component
export const ResponsiveImage: React.FC<{
  src: string;
  alt: string;
  sizes?: {
    mobile?: string;
    tablet?: string;
    desktop?: string;
  };
  className?: string;
  loading?: 'lazy' | 'eager';
}> = ({ 
  src, 
  alt, 
  sizes = { mobile: '100vw', tablet: '50vw', desktop: '33vw' },
  className,
  loading = 'lazy'
}) => {
  const viewport = useResponsive();

  const getCurrentSize = () => {
    if (viewport.isMobile) return sizes.mobile;
    if (viewport.isTablet) return sizes.tablet;
    return sizes.desktop;
  };

  return (
    <img
      src={src}
      alt={alt}
      loading={loading}
      sizes={getCurrentSize()}
      className={cn(
        'w-full h-auto object-cover',
        className
      )}
    />
  );
};

// Responsive navigation component
export const ResponsiveNav: React.FC<{
  children: React.ReactNode;
  mobileBreakpoint?: number;
  className?: string;
}> = ({ children, mobileBreakpoint = 768, className }) => {
  const viewport = useResponsive();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const isMobile = viewport.width < mobileBreakpoint;

  if (isMobile) {
    return (
      <div className={cn('relative', className)}>
        <button
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          className="p-2 rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-sea-green-500"
          aria-label="Toggle mobile menu"
        >
          <div className="w-6 h-6 flex flex-col justify-center items-center">
            <span className={cn(
              "block h-0.5 w-6 bg-current transition-all duration-300",
              isMobileMenuOpen ? "rotate-45 translate-y-1" : "-translate-y-1"
            )} />
            <span className={cn(
              "block h-0.5 w-6 bg-current transition-all duration-300",
              isMobileMenuOpen ? "opacity-0" : "opacity-100"
            )} />
            <span className={cn(
              "block h-0.5 w-6 bg-current transition-all duration-300",
              isMobileMenuOpen ? "-rotate-45 -translate-y-1" : "translate-y-1"
            )} />
          </div>
        </button>
        
        {isMobileMenuOpen && (
          <div className="absolute top-full left-0 right-0 bg-white shadow-lg border rounded-md mt-2 py-2 z-50">
            {children}
          </div>
        )}
      </div>
    );
  }

  return (
    <nav className={cn('flex items-center space-x-4', className)}>
      {children}
    </nav>
  );
};

// Responsive modal component
export const ResponsiveModal: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
  title?: string;
  fullScreenOnMobile?: boolean;
  className?: string;
}> = ({ isOpen, onClose, children, title, fullScreenOnMobile = true, className }) => {
  const viewport = useResponsive();

  if (!isOpen) return null;

  const isFullScreen = fullScreenOnMobile && viewport.isMobile;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      <div 
        className="absolute inset-0 bg-black/50" 
        onClick={onClose}
      />
      <div
        className={cn(
          'relative bg-white rounded-lg shadow-xl max-h-[90vh] overflow-y-auto',
          isFullScreen ? 'w-full h-full rounded-none' : 'w-full max-w-md',
          className
        )}
      >
        {title && (
          <div className="flex items-center justify-between p-4 border-b">
            <h2 className="text-lg font-semibold">{title}</h2>
            <button
              onClick={onClose}
              className="p-1 hover:bg-gray-100 rounded"
              aria-label="Close modal"
            >
              ×
            </button>
          </div>
        )}
        <div className="p-4">
          {children}
        </div>
      </div>
    </div>
  );
};

// Responsive breakpoint utilities
export const breakpoints = {
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px'
};

// Media query hook
export const useMediaQuery = (query: string) => {
  const [matches, setMatches] = useState(false);

  useEffect(() => {
    const media = window.matchMedia(query);
    if (media.matches !== matches) {
      setMatches(media.matches);
    }
    
    const listener = () => setMatches(media.matches);
    media.addEventListener('change', listener);
    
    return () => media.removeEventListener('change', listener);
  }, [matches, query]);

  return matches;
};

// Push Notifications Service
export const pushNotifications = {
  isSupported: () => 'serviceWorker' in navigator && 'PushManager' in window,

  requestPermission: async () => {
    if (!pushNotifications.isSupported()) return false;

    const permission = await Notification.requestPermission();
    return permission === 'granted';
  },

  sendNotification: (title: string, options?: NotificationOptions) => {
    if (Notification.permission === 'granted') {
      return new Notification(title, {
        icon: '/favicon.svg',
        badge: '/favicon.svg',
        ...options
      });
    }
  }
};

// Camera Integration Service
export const cameraService = {
  isSupported: () => 'mediaDevices' in navigator && 'getUserMedia' in navigator.mediaDevices,

  capturePhoto: async (): Promise<string | null> => {
    if (!cameraService.isSupported()) return null;

    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: { facingMode: 'environment' }
      });

      const video = document.createElement('video');
      video.srcObject = stream;
      video.play();

      return new Promise((resolve) => {
        video.addEventListener('loadedmetadata', () => {
          const canvas = document.createElement('canvas');
          canvas.width = video.videoWidth;
          canvas.height = video.videoHeight;

          const ctx = canvas.getContext('2d');
          ctx?.drawImage(video, 0, 0);

          stream.getTracks().forEach(track => track.stop());
          resolve(canvas.toDataURL('image/jpeg', 0.8));
        });
      });
    } catch (error) {
      console.error('Camera capture error:', error);
      return null;
    }
  }
};

// GPS Integration Service
export const gpsService = {
  isSupported: () => 'geolocation' in navigator,

  getCurrentPosition: (): Promise<GeolocationPosition | null> => {
    if (!gpsService.isSupported()) return Promise.resolve(null);

    return new Promise((resolve, reject) => {
      navigator.geolocation.getCurrentPosition(
        (position) => resolve(position),
        (error) => {
          console.error('GPS error:', error);
          resolve(null);
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 300000
        }
      );
    });
  },

  watchPosition: (callback: (position: GeolocationPosition) => void) => {
    if (!gpsService.isSupported()) return null;

    return navigator.geolocation.watchPosition(
      callback,
      (error) => console.error('GPS watch error:', error),
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 300000
      }
    );
  }
};

// Offline Mode Service
export const offlineService = {
  isOnline: () => navigator.onLine,

  cacheProperty: (property: any) => {
    if ('localStorage' in window) {
      const cached = JSON.parse(localStorage.getItem('cachedProperties') || '[]');
      cached.push(property);
      localStorage.setItem('cachedProperties', JSON.stringify(cached.slice(-50))); // Keep last 50
    }
  },

  getCachedProperties: () => {
    if ('localStorage' in window) {
      return JSON.parse(localStorage.getItem('cachedProperties') || '[]');
    }
    return [];
  },

  clearCache: () => {
    if ('localStorage' in window) {
      localStorage.removeItem('cachedProperties');
    }
  }
};

// Responsive utilities
export const responsive = {
  isMobile: () => window.innerWidth < 768,
  isTablet: () => window.innerWidth >= 768 && window.innerWidth < 1024,
  isDesktop: () => window.innerWidth >= 1024,
  isTouchDevice: () => 'ontouchstart' in window || navigator.maxTouchPoints > 0,
  getOrientation: () => window.innerHeight > window.innerWidth ? 'portrait' : 'landscape',

  // CSS classes for responsive design
  classes: {
    container: 'w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8',
    grid: {
      responsive: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4',
      auto: 'grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4'
    },
    text: {
      responsive: 'text-sm md:text-base lg:text-lg',
      heading: 'text-lg md:text-xl lg:text-2xl'
    },
    spacing: {
      responsive: 'p-4 md:p-6 lg:p-8',
      margin: 'm-2 md:m-4 lg:m-6'
    }
  }
};
