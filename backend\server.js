const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3001;

// Security middleware
app.use(helmet());

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.'
});
app.use('/api/', limiter);

// CORS configuration
app.use(cors({
  origin: process.env.CORS_ORIGIN || 'http://localhost:5173',
  credentials: true
}));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Logging middleware
app.use(morgan('combined'));

// Static files for uploads
app.use('/uploads', express.static('uploads'));

// Import routes
const authRoutes = require('./src/routes/auth');
const propertyRoutes = require('./src/routes/properties');
const bookingRoutes = require('./src/routes/bookings');
const reviewRoutes = require('./src/routes/reviews');

// Simple recommendations endpoint
app.get('/api/recommendations', async (req, res) => {
  try {
    const { getClient } = require('./src/utils/database');
    const supabase = getClient();

    const { type = 'trending', limit = 6 } = req.query;

    // Get random properties as recommendations
    const { data, error } = await supabase
      .from('properties')
      .select('*')
      .limit(parseInt(limit));

    if (error) {
      return res.status(500).json({
        success: false,
        error: error.message
      });
    }

    res.json({
      success: true,
      type,
      recommendations: data || []
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// API routes
app.use('/api/auth', authRoutes);
app.use('/api/properties', propertyRoutes);
app.use('/api/bookings', bookingRoutes);
app.use('/api/reviews', reviewRoutes);

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV
  });
});

// Database test endpoint
app.get('/api/db-test', async (req, res) => {
  try {
    const { getClient } = require('./src/utils/database');
    const supabase = getClient();

    // Test basic connection
    const { data, error, count } = await supabase.from('properties').select('*', { count: 'exact' });

    if (error) {
      return res.json({
        success: false,
        error: error.message,
        details: error,
        message: 'Failed to connect to properties table'
      });
    }

    res.json({
      success: true,
      message: 'Database connection working',
      propertiesCount: count || 0,
      sampleData: data ? data.slice(0, 2) : []
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
});

// Check database schema
app.get('/api/schema-info', async (req, res) => {
  try {
    const { getClient } = require('./src/utils/database');
    const supabase = getClient();

    // Try to insert a minimal record to see what columns are required/available
    const testRecord = {
      title: 'Test Property'
    };

    const { data, error } = await supabase.from('properties').insert(testRecord).select();

    if (error) {
      return res.json({
        success: false,
        error: error.message,
        details: error,
        message: 'Schema inspection failed - this tells us about the table structure'
      });
    }

    // If successful, delete the test record
    if (data && data.length > 0) {
      await supabase.from('properties').delete().eq('id', data[0].id);
    }

    res.json({
      success: true,
      message: 'Schema inspection successful',
      insertedRecord: data ? data[0] : null,
      availableColumns: data && data.length > 0 ? Object.keys(data[0]) : []
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
});

// Check all tables and their data
app.get('/api/tables-info', async (req, res) => {
  try {
    const { getClient } = require('./src/utils/database');
    const supabase = getClient();

    const tables = ['users', 'properties', 'bookings', 'reviews', 'messages'];
    const tableInfo = {};

    for (const table of tables) {
      try {
        const { data, error, count } = await supabase.from(table).select('*', { count: 'exact' }).limit(1);

        if (error) {
          tableInfo[table] = { error: error.message, exists: false };
        } else {
          tableInfo[table] = {
            exists: true,
            count: count || 0,
            sampleColumns: data && data.length > 0 ? Object.keys(data[0]) : [],
            sampleData: data && data.length > 0 ? data[0] : null
          };
        }
      } catch (err) {
        tableInfo[table] = { error: err.message, exists: false };
      }
    }

    res.json({
      success: true,
      message: 'Database tables information',
      tables: tableInfo
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
});

// Seed database with sample data (with proper structure)
app.post('/api/seed-data', async (req, res) => {
  try {
    const { getClient } = require('./src/utils/database');
    const supabase = getClient();

    // First, create a sample user to be the host
    const sampleUser = {
      email: '<EMAIL>',
      full_name: 'Sample Host',
      phone: '+27123456789',
      role: 'host'
    };

    const { data: userData, error: userError } = await supabase
      .from('users')
      .insert(sampleUser)
      .select()
      .single();

    if (userError && userError.code !== '23505') { // Ignore duplicate key error
      return res.status(500).json({
        success: false,
        error: 'Failed to create user: ' + userError.message,
        details: userError
      });
    }

    // Get the user ID (either newly created or existing)
    let hostId;
    if (userData) {
      hostId = userData.id;
    } else {
      // User already exists, get their ID
      const { data: existingUser } = await supabase
        .from('users')
        .select('id')
        .eq('email', '<EMAIL>')
        .single();
      hostId = existingUser?.id;
    }

    if (!hostId) {
      return res.status(500).json({
        success: false,
        error: 'Could not get or create host user'
      });
    }

    // Now create properties with the correct structure
    const sampleProperties = [
      {
        host_id: hostId,
        title: 'Luxury Cape Town Villa',
        description: 'Beautiful villa with stunning views of Table Mountain',
        address: 'Cape Town, Western Cape',
        city: 'Cape Town',
        province: 'Western Cape',
        country: 'South Africa',
        price_per_night: 2500,
        max_guests: 8,
        bedrooms: 4,
        bathrooms: 3,
        property_type: 'villa'
      },
      {
        host_id: hostId,
        title: 'Johannesburg City Apartment',
        description: 'Modern apartment in the heart of Johannesburg',
        address: 'Johannesburg, Gauteng',
        city: 'Johannesburg',
        province: 'Gauteng',
        country: 'South Africa',
        price_per_night: 800,
        max_guests: 4,
        bedrooms: 2,
        bathrooms: 2,
        property_type: 'apartment'
      }
    ];

    const { data, error } = await supabase.from('properties').insert(sampleProperties).select();

    if (error) {
      return res.status(500).json({
        success: false,
        error: error.message,
        details: error
      });
    }

    res.json({
      success: true,
      message: 'Sample data inserted successfully',
      insertedCount: sampleProperties.length,
      hostId: hostId,
      properties: data
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ 
    error: 'Route not found',
    path: req.originalUrl 
  });
});

// Global error handler
app.use((err, req, res, next) => {
  console.error('Error:', err);
  
  if (err.type === 'entity.parse.failed') {
    return res.status(400).json({ error: 'Invalid JSON in request body' });
  }
  
  res.status(err.status || 500).json({
    error: process.env.NODE_ENV === 'production' 
      ? 'Internal server error' 
      : err.message,
    ...(process.env.NODE_ENV !== 'production' && { stack: err.stack })
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 StayFinder API Server running on port ${PORT}`);
  console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`🌐 CORS Origin: ${process.env.CORS_ORIGIN || 'http://localhost:5173'}`);
  console.log(`🗄️  Database: Supabase (${process.env.SUPABASE_URL ? 'Connected' : 'Not Configured'})`);
});

module.exports = app;
