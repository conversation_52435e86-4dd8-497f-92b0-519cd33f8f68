import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { 
  Home, 
  MapPin, 
  Users, 
  Bed, 
  Bath,
  Building,
  FileText
} from 'lucide-react';

interface PropertyBasicInfoProps {
  data: {
    title: string;
    description: string;
    propertyType: string;
    location: string;
    address: string;
    maxGuests: number;
    bedrooms: number;
    bathrooms: number;
  };
  errors: Record<string, string>;
  onChange: (updates: any) => void;
}

const PROPERTY_TYPES = [
  { value: 'house', label: 'House', icon: Home },
  { value: 'apartment', label: 'Apartment', icon: Building },
  { value: 'villa', label: 'Villa', icon: Home },
  { value: 'cottage', label: 'Cottage', icon: Home },
  { value: 'townhouse', label: 'Townhouse', icon: Building },
  { value: 'guesthouse', label: 'Guest House', icon: Home },
  { value: 'bnb', label: 'Bed & Breakfast', icon: Bed },
  { value: 'other', label: 'Other', icon: Building }
];

export const PropertyBasicInfo: React.FC<PropertyBasicInfoProps> = ({
  data,
  errors,
  onChange
}) => {
  return (
    <div className="space-y-6">
      {/* Property Title */}
      <div className="space-y-2">
        <Label htmlFor="title" className="flex items-center gap-2">
          <FileText className="h-4 w-4" />
          Property Title *
        </Label>
        <Input
          id="title"
          placeholder="e.g., Beautiful Beachfront Villa in Margate"
          value={data.title}
          onChange={(e) => onChange({ title: e.target.value })}
          className={errors.title ? 'border-red-300' : ''}
        />
        {errors.title && (
          <p className="text-red-600 text-sm">{errors.title}</p>
        )}
        <p className="text-gray-500 text-sm">
          Choose a catchy title that highlights your property's best features
        </p>
      </div>

      {/* Property Description */}
      <div className="space-y-2">
        <Label htmlFor="description" className="flex items-center gap-2">
          <FileText className="h-4 w-4" />
          Property Description *
        </Label>
        <Textarea
          id="description"
          placeholder="Describe your property, its features, and what makes it special..."
          value={data.description}
          onChange={(e) => onChange({ description: e.target.value })}
          rows={4}
          className={errors.description ? 'border-red-300' : ''}
        />
        {errors.description && (
          <p className="text-red-600 text-sm">{errors.description}</p>
        )}
        <p className="text-gray-500 text-sm">
          {data.description.length}/500 characters. Describe the space, amenities, and neighborhood.
        </p>
      </div>

      {/* Property Type */}
      <div className="space-y-2">
        <Label className="flex items-center gap-2">
          <Building className="h-4 w-4" />
          Property Type *
        </Label>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          {PROPERTY_TYPES.map((type) => {
            const IconComponent = type.icon;
            return (
              <Card
                key={type.value}
                className={`cursor-pointer transition-all hover:shadow-md ${
                  data.propertyType === type.value
                    ? 'border-sea-green-500 bg-sea-green-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => onChange({ propertyType: type.value })}
              >
                <CardContent className="p-4 text-center">
                  <IconComponent className={`h-6 w-6 mx-auto mb-2 ${
                    data.propertyType === type.value ? 'text-sea-green-600' : 'text-gray-600'
                  }`} />
                  <p className={`text-sm font-medium ${
                    data.propertyType === type.value ? 'text-sea-green-900' : 'text-gray-900'
                  }`}>
                    {type.label}
                  </p>
                </CardContent>
              </Card>
            );
          })}
        </div>
        {errors.propertyType && (
          <p className="text-red-600 text-sm">{errors.propertyType}</p>
        )}
      </div>

      {/* Location */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="location" className="flex items-center gap-2">
            <MapPin className="h-4 w-4" />
            City/Area *
          </Label>
          <Input
            id="location"
            placeholder="e.g., Margate, KZN"
            value={data.location}
            onChange={(e) => onChange({ location: e.target.value })}
            className={errors.location ? 'border-red-300' : ''}
          />
          {errors.location && (
            <p className="text-red-600 text-sm">{errors.location}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="address" className="flex items-center gap-2">
            <MapPin className="h-4 w-4" />
            Full Address *
          </Label>
          <Input
            id="address"
            placeholder="e.g., 123 Beach Road, Margate, 4275"
            value={data.address}
            onChange={(e) => onChange({ address: e.target.value })}
            className={errors.address ? 'border-red-300' : ''}
          />
          {errors.address && (
            <p className="text-red-600 text-sm">{errors.address}</p>
          )}
        </div>
      </div>

      {/* Property Details */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900">Property Details</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Max Guests */}
          <div className="space-y-2">
            <Label htmlFor="maxGuests" className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              Max Guests *
            </Label>
            <div className="flex items-center gap-2">
              <button
                type="button"
                onClick={() => onChange({ maxGuests: Math.max(1, data.maxGuests - 1) })}
                className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50"
              >
                -
              </button>
              <Input
                id="maxGuests"
                type="number"
                min="1"
                max="20"
                value={data.maxGuests}
                onChange={(e) => onChange({ maxGuests: parseInt(e.target.value) || 1 })}
                className={`text-center ${errors.maxGuests ? 'border-red-300' : ''}`}
              />
              <button
                type="button"
                onClick={() => onChange({ maxGuests: Math.min(20, data.maxGuests + 1) })}
                className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50"
              >
                +
              </button>
            </div>
            {errors.maxGuests && (
              <p className="text-red-600 text-sm">{errors.maxGuests}</p>
            )}
          </div>

          {/* Bedrooms */}
          <div className="space-y-2">
            <Label htmlFor="bedrooms" className="flex items-center gap-2">
              <Bed className="h-4 w-4" />
              Bedrooms *
            </Label>
            <div className="flex items-center gap-2">
              <button
                type="button"
                onClick={() => onChange({ bedrooms: Math.max(1, data.bedrooms - 1) })}
                className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50"
              >
                -
              </button>
              <Input
                id="bedrooms"
                type="number"
                min="1"
                max="10"
                value={data.bedrooms}
                onChange={(e) => onChange({ bedrooms: parseInt(e.target.value) || 1 })}
                className={`text-center ${errors.bedrooms ? 'border-red-300' : ''}`}
              />
              <button
                type="button"
                onClick={() => onChange({ bedrooms: Math.min(10, data.bedrooms + 1) })}
                className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50"
              >
                +
              </button>
            </div>
            {errors.bedrooms && (
              <p className="text-red-600 text-sm">{errors.bedrooms}</p>
            )}
          </div>

          {/* Bathrooms */}
          <div className="space-y-2">
            <Label htmlFor="bathrooms" className="flex items-center gap-2">
              <Bath className="h-4 w-4" />
              Bathrooms *
            </Label>
            <div className="flex items-center gap-2">
              <button
                type="button"
                onClick={() => onChange({ bathrooms: Math.max(1, data.bathrooms - 1) })}
                className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50"
              >
                -
              </button>
              <Input
                id="bathrooms"
                type="number"
                min="1"
                max="10"
                value={data.bathrooms}
                onChange={(e) => onChange({ bathrooms: parseInt(e.target.value) || 1 })}
                className={`text-center ${errors.bathrooms ? 'border-red-300' : ''}`}
              />
              <button
                type="button"
                onClick={() => onChange({ bathrooms: Math.min(10, data.bathrooms + 1) })}
                className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50"
              >
                +
              </button>
            </div>
            {errors.bathrooms && (
              <p className="text-red-600 text-sm">{errors.bathrooms}</p>
            )}
          </div>
        </div>
      </div>

      {/* Tips */}
      <Card className="bg-blue-50 border-blue-200">
        <CardContent className="p-4">
          <h4 className="font-medium text-blue-900 mb-2">💡 Tips for a great listing</h4>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• Use a descriptive title that highlights your property's unique features</li>
            <li>• Include details about the neighborhood and nearby attractions</li>
            <li>• Be accurate with guest capacity and room counts</li>
            <li>• Mention any special features like ocean views, pool, or garden</li>
          </ul>
        </CardContent>
      </Card>
    </div>
  );
};
