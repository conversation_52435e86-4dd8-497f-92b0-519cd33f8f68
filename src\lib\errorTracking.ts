// Error tracking and monitoring system for StayFinder

interface ErrorInfo {
  message: string;
  stack?: string;
  componentStack?: string;
  errorBoundary?: string;
  errorInfo?: any;
  url?: string;
  userAgent?: string;
  timestamp: Date;
  userId?: string;
  sessionId: string;
  buildVersion?: string;
  environment: 'development' | 'staging' | 'production';
  severity: 'low' | 'medium' | 'high' | 'critical';
  category: 'javascript' | 'network' | 'ui' | 'performance' | 'security' | 'business';
  tags?: string[];
  context?: Record<string, any>;
}

interface NetworkError {
  url: string;
  method: string;
  status: number;
  statusText: string;
  responseText?: string;
  requestData?: any;
  timestamp: Date;
  duration?: number;
}

interface PerformanceIssue {
  type: 'slow_page_load' | 'slow_api_call' | 'memory_leak' | 'large_bundle';
  metric: string;
  value: number;
  threshold: number;
  url: string;
  timestamp: Date;
}

class ErrorTracker {
  private sessionId: string;
  private userId?: string;
  private environment: 'development' | 'staging' | 'production';
  private buildVersion: string;
  private errorQueue: ErrorInfo[] = [];
  private isOnline: boolean = navigator.onLine;
  private maxQueueSize: number = 100;

  constructor() {
    this.sessionId = this.generateSessionId();
    this.environment = this.detectEnvironment();
    this.buildVersion = import.meta.env.VITE_BUILD_VERSION || '1.0.0';
    
    this.setupGlobalErrorHandlers();
    this.setupNetworkMonitoring();
    this.setupPerformanceMonitoring();
    this.setupOnlineStatusMonitoring();
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private detectEnvironment(): 'development' | 'staging' | 'production' {
    if (import.meta.env.DEV) return 'development';
    if (window.location.hostname.includes('staging')) return 'staging';
    return 'production';
  }

  private setupGlobalErrorHandlers(): void {
    // Handle uncaught JavaScript errors
    window.addEventListener('error', (event) => {
      this.captureError({
        message: event.message,
        stack: event.error?.stack,
        url: event.filename,
        severity: 'high',
        category: 'javascript',
        context: {
          lineno: event.lineno,
          colno: event.colno,
          filename: event.filename
        }
      });
    });

    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      this.captureError({
        message: `Unhandled Promise Rejection: ${event.reason}`,
        stack: event.reason?.stack,
        severity: 'high',
        category: 'javascript',
        context: {
          reason: event.reason,
          promise: event.promise
        }
      });
    });

    // Handle console errors (in development)
    if (this.environment === 'development') {
      const originalConsoleError = console.error;
      console.error = (...args) => {
        this.captureError({
          message: args.join(' '),
          severity: 'medium',
          category: 'javascript',
          context: { consoleArgs: args }
        });
        originalConsoleError.apply(console, args);
      };
    }
  }

  private setupNetworkMonitoring(): void {
    // Monitor fetch requests
    const originalFetch = window.fetch;
    window.fetch = async (...args) => {
      const startTime = performance.now();
      const url = typeof args[0] === 'string' ? args[0] : args[0].url;
      const method = args[1]?.method || 'GET';

      try {
        const response = await originalFetch(...args);
        const duration = performance.now() - startTime;

        // Log slow API calls
        if (duration > 5000) {
          this.capturePerformanceIssue({
            type: 'slow_api_call',
            metric: 'response_time',
            value: duration,
            threshold: 5000,
            url,
            timestamp: new Date()
          });
        }

        // Log HTTP errors
        if (!response.ok) {
          this.captureNetworkError({
            url,
            method,
            status: response.status,
            statusText: response.statusText,
            timestamp: new Date(),
            duration
          });
        }

        return response;
      } catch (error) {
        this.captureNetworkError({
          url,
          method,
          status: 0,
          statusText: 'Network Error',
          timestamp: new Date(),
          duration: performance.now() - startTime
        });
        throw error;
      }
    };
  }

  private setupPerformanceMonitoring(): void {
    // Monitor page load performance
    window.addEventListener('load', () => {
      setTimeout(() => {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        if (navigation) {
          const loadTime = navigation.loadEventEnd - navigation.fetchStart;
          
          if (loadTime > 3000) {
            this.capturePerformanceIssue({
              type: 'slow_page_load',
              metric: 'load_time',
              value: loadTime,
              threshold: 3000,
              url: window.location.href,
              timestamp: new Date()
            });
          }
        }
      }, 0);
    });

    // Monitor memory usage (if available)
    if ('memory' in performance) {
      setInterval(() => {
        const memory = (performance as any).memory;
        const usedMemory = memory.usedJSHeapSize;
        const totalMemory = memory.totalJSHeapSize;
        const memoryUsagePercent = (usedMemory / totalMemory) * 100;

        if (memoryUsagePercent > 90) {
          this.capturePerformanceIssue({
            type: 'memory_leak',
            metric: 'memory_usage_percent',
            value: memoryUsagePercent,
            threshold: 90,
            url: window.location.href,
            timestamp: new Date()
          });
        }
      }, 30000); // Check every 30 seconds
    }
  }

  private setupOnlineStatusMonitoring(): void {
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.flushErrorQueue();
    });

    window.addEventListener('offline', () => {
      this.isOnline = false;
    });
  }

  public setUserId(userId: string): void {
    this.userId = userId;
  }

  public captureError(errorData: Partial<ErrorInfo>): void {
    const error: ErrorInfo = {
      message: errorData.message || 'Unknown error',
      stack: errorData.stack,
      componentStack: errorData.componentStack,
      errorBoundary: errorData.errorBoundary,
      errorInfo: errorData.errorInfo,
      url: errorData.url || window.location.href,
      userAgent: navigator.userAgent,
      timestamp: new Date(),
      userId: this.userId,
      sessionId: this.sessionId,
      buildVersion: this.buildVersion,
      environment: this.environment,
      severity: errorData.severity || 'medium',
      category: errorData.category || 'javascript',
      tags: errorData.tags || [],
      context: errorData.context || {}
    };

    this.addToQueue(error);
    this.logToConsole(error);
    
    if (this.isOnline) {
      this.sendError(error);
    }
  }

  public captureNetworkError(networkError: NetworkError): void {
    this.captureError({
      message: `Network Error: ${networkError.method} ${networkError.url} - ${networkError.status} ${networkError.statusText}`,
      severity: networkError.status >= 500 ? 'high' : 'medium',
      category: 'network',
      context: {
        networkError,
        url: networkError.url,
        method: networkError.method,
        status: networkError.status,
        duration: networkError.duration
      }
    });
  }

  public capturePerformanceIssue(performanceIssue: PerformanceIssue): void {
    this.captureError({
      message: `Performance Issue: ${performanceIssue.type} - ${performanceIssue.metric} (${performanceIssue.value}ms > ${performanceIssue.threshold}ms)`,
      severity: 'medium',
      category: 'performance',
      context: {
        performanceIssue,
        metric: performanceIssue.metric,
        value: performanceIssue.value,
        threshold: performanceIssue.threshold
      }
    });
  }

  public captureUserAction(action: string, context?: Record<string, any>): void {
    // Log user actions for debugging context
    if (this.environment === 'development') {
      console.log('User Action:', action, context);
    }
    
    // Store recent user actions for error context
    const userActions = this.getUserActions();
    userActions.push({
      action,
      timestamp: new Date(),
      context
    });
    
    // Keep only last 10 actions
    if (userActions.length > 10) {
      userActions.shift();
    }
    
    sessionStorage.setItem('user_actions', JSON.stringify(userActions));
  }

  private getUserActions(): any[] {
    try {
      return JSON.parse(sessionStorage.getItem('user_actions') || '[]');
    } catch {
      return [];
    }
  }

  private addToQueue(error: ErrorInfo): void {
    this.errorQueue.push(error);
    
    // Limit queue size
    if (this.errorQueue.length > this.maxQueueSize) {
      this.errorQueue.shift();
    }
    
    // Store in localStorage for persistence
    try {
      localStorage.setItem('error_queue', JSON.stringify(this.errorQueue));
    } catch {
      // Ignore localStorage errors
    }
  }

  private async flushErrorQueue(): Promise<void> {
    if (this.errorQueue.length === 0) return;

    const errors = [...this.errorQueue];
    this.errorQueue = [];
    
    try {
      await this.sendErrors(errors);
      localStorage.removeItem('error_queue');
    } catch (error) {
      // Re-add errors to queue if sending fails
      this.errorQueue.unshift(...errors);
    }
  }

  private logToConsole(error: ErrorInfo): void {
    if (this.environment === 'development') {
      console.group(`🐛 Error Tracked: ${error.severity.toUpperCase()}`);
      console.error('Message:', error.message);
      console.error('Stack:', error.stack);
      console.error('Context:', error.context);
      console.error('Full Error:', error);
      console.groupEnd();
    }
  }

  private async sendError(error: ErrorInfo): Promise<void> {
    try {
      // In a real implementation, you would send to your error tracking service
      // For now, we'll just log to console in production
      if (this.environment === 'production') {
        // Example: Send to Sentry, LogRocket, or custom endpoint
        // await fetch('/api/errors', {
        //   method: 'POST',
        //   headers: { 'Content-Type': 'application/json' },
        //   body: JSON.stringify(error)
        // });
        console.warn('Error tracked:', error.message);
      }
    } catch (sendError) {
      console.error('Failed to send error:', sendError);
    }
  }

  private async sendErrors(errors: ErrorInfo[]): Promise<void> {
    try {
      // Batch send errors
      if (this.environment === 'production') {
        // await fetch('/api/errors/batch', {
        //   method: 'POST',
        //   headers: { 'Content-Type': 'application/json' },
        //   body: JSON.stringify({ errors })
        // });
        console.warn('Batch errors tracked:', errors.length);
      }
    } catch (sendError) {
      console.error('Failed to send batch errors:', sendError);
      throw sendError;
    }
  }

  public getErrorStats(): {
    totalErrors: number;
    errorsByCategory: Record<string, number>;
    errorsBySeverity: Record<string, number>;
    recentErrors: ErrorInfo[];
  } {
    const allErrors = [...this.errorQueue];
    
    return {
      totalErrors: allErrors.length,
      errorsByCategory: allErrors.reduce((acc, error) => {
        acc[error.category] = (acc[error.category] || 0) + 1;
        return acc;
      }, {} as Record<string, number>),
      errorsBySeverity: allErrors.reduce((acc, error) => {
        acc[error.severity] = (acc[error.severity] || 0) + 1;
        return acc;
      }, {} as Record<string, number>),
      recentErrors: allErrors.slice(-10)
    };
  }
}

// Create global error tracker instance
export const errorTracker = new ErrorTracker();

// Export utility functions
export const captureError = (error: Partial<ErrorInfo>) => errorTracker.captureError(error);
export const captureUserAction = (action: string, context?: Record<string, any>) => 
  errorTracker.captureUserAction(action, context);
export const setUserId = (userId: string) => errorTracker.setUserId(userId);
export const getErrorStats = () => errorTracker.getErrorStats();
