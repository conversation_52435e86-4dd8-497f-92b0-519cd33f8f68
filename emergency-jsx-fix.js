#!/usr/bin/env node

/**
 * Emergency JSX Fix - Direct line-by-line fixes
 */

import fs from 'fs';

const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  red: '\x1b[31m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

// Create a minimal working version of Hero.tsx
function createMinimalHero() {
  const content = `import React, { useState, useEffect } from 'react';
import { Search, MapPin, Calendar, Users, Star, TrendingUp, Award, Shield } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';
import { SlideIn } from '@/components/ui/slide-in';
import { HoverAnimation } from '@/components/ui/hover-animation';
import { StaggeredAnimation } from '@/components/ui/staggered-animation';

interface HeroProps {
  onSearch?: (query: string) => void;
  className?: string;
}

export const Hero: React.FC<HeroProps> = ({ onSearch, className = '' }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [checkIn, setCheckIn] = useState('');
  const [checkOut, setCheckOut] = useState('');
  const [guests, setGuests] = useState('2');

  const handleSearch = () => {
    if (onSearch) {
      onSearch(searchQuery);
    }
  };

  return (
    <div className={\`relative min-h-screen bg-gradient-to-br from-sea-green-50 via-white to-ocean-blue-50 overflow-hidden \${className}\`}>
      {/* Background Elements */}
      <div className="absolute inset-0 bg-[url('/hero-pattern.svg')] opacity-5"></div>
      
      <div className="relative z-10 container mx-auto px-4 pt-20 pb-16">
        <div className="max-w-6xl mx-auto text-center">
          {/* Main Hero Content */}
          <SlideIn direction="up" delay={200}>
            <div className="mb-12">
              <h1 className="text-5xl md:text-7xl font-bold text-gray-900 mb-6 leading-tight">
                Discover Your Perfect
                <span className="block bg-gradient-to-r from-sea-green-600 to-ocean-blue-600 bg-clip-text text-transparent">
                  South African Getaway
                </span>
              </h1>
              <p className="text-xl md:text-2xl text-gray-600 mb-8 max-w-3xl mx-auto leading-relaxed">
                From Cape Town's stunning coastline to Johannesburg's vibrant culture, 
                find unique accommodations across all 9 provinces of South Africa.
              </p>
            </div>
          </SlideIn>

          {/* Search Section */}
          <SlideIn direction="up" delay={400}>
            <div className="bg-white rounded-2xl shadow-2xl p-8 mb-12 max-w-4xl mx-auto border border-gray-100">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div className="relative">
                  <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                  <Input
                    placeholder="Where to?"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10 h-12 text-lg border-gray-200 focus:border-sea-green-500"
                  />
                </div>
                <div className="relative">
                  <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                  <Input
                    type="date"
                    value={checkIn}
                    onChange={(e) => setCheckIn(e.target.value)}
                    className="pl-10 h-12 text-lg border-gray-200 focus:border-sea-green-500"
                  />
                </div>
                <div className="relative">
                  <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                  <Input
                    type="date"
                    value={checkOut}
                    onChange={(e) => setCheckOut(e.target.value)}
                    className="pl-10 h-12 text-lg border-gray-200 focus:border-sea-green-500"
                  />
                </div>
                <div className="relative">
                  <Users className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                  <select
                    value={guests}
                    onChange={(e) => setGuests(e.target.value)}
                    className="w-full pl-10 h-12 text-lg border border-gray-200 rounded-md focus:border-sea-green-500 focus:outline-none"
                  >
                    <option value="1">1 Guest</option>
                    <option value="2">2 Guests</option>
                    <option value="3">3 Guests</option>
                    <option value="4">4 Guests</option>
                    <option value="5">5+ Guests</option>
                  </select>
                </div>
              </div>
              
              <Button 
                onClick={handleSearch}
                size="lg" 
                className="w-full md:w-auto px-12 py-4 text-lg bg-gradient-to-r from-sea-green-600 to-ocean-blue-600 hover:from-sea-green-700 hover:to-ocean-blue-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300"
              >
                <Search className="mr-2 h-5 w-5" />
                Search Properties
              </Button>
            </div>
          </SlideIn>

          {/* Popular Destinations */}
          <SlideIn direction="up" delay={600}>
            <div className="mb-16">
              <h3 className="text-2xl font-semibold text-gray-800 mb-6">Popular Destinations</h3>
              <div className="flex flex-wrap justify-center gap-3">
                <StaggeredAnimation delay={100}>
                  {['Cape Town', 'Johannesburg', 'Durban', 'Stellenbosch', 'Port Elizabeth', 'Knysna'].map((location) => (
                    <HoverAnimation key={location} type="scale">
                      <button
                        onClick={() => setSearchQuery(location)}
                        className="px-5 py-3 text-sm bg-gradient-to-r from-white to-gray-50 hover:from-sea-green-50 hover:to-ocean-blue-50 text-gray-700 hover:text-sea-green-700 rounded-full transition-all duration-300 border border-gray-200 hover:border-sea-green-300 hover:shadow-lg font-medium transform hover:-translate-y-0.5"
                      >
                        {location}
                      </button>
                    </HoverAnimation>
                  ))}
                </StaggeredAnimation>
              </div>
            </div>
          </SlideIn>

          {/* Stats Section */}
          <SlideIn direction="up" delay={700}>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto mb-16">
              <StaggeredAnimation delay={150}>
                <HoverAnimation type="lift">
                  <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-gray-50 hover:shadow-xl transition-all duration-300">
                    <CardContent className="p-6 text-center">
                      <div className="text-3xl font-bold text-sea-green-600 mb-2">1000+</div>
                      <div className="text-gray-600 font-medium">Properties</div>
                    </CardContent>
                  </Card>
                </HoverAnimation>
                <HoverAnimation type="lift">
                  <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-gray-50 hover:shadow-xl transition-all duration-300">
                    <CardContent className="p-6 text-center">
                      <div className="text-3xl font-bold text-ocean-blue-600 mb-2">9</div>
                      <div className="text-gray-600 font-medium">Provinces</div>
                    </CardContent>
                  </Card>
                </HoverAnimation>
                <HoverAnimation type="lift">
                  <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-gray-50 hover:shadow-xl transition-all duration-300">
                    <CardContent className="p-6 text-center">
                      <div className="text-3xl font-bold text-sea-green-600 mb-2">50K+</div>
                      <div className="text-gray-600 font-medium">Happy Guests</div>
                    </CardContent>
                  </Card>
                </HoverAnimation>
                <HoverAnimation type="lift">
                  <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-gray-50 hover:shadow-xl transition-all duration-300">
                    <CardContent className="p-6 text-center">
                      <div className="text-3xl font-bold text-ocean-blue-600 mb-2">4.8</div>
                      <div className="text-gray-600 font-medium">Average Rating</div>
                    </CardContent>
                  </Card>
                </HoverAnimation>
              </StaggeredAnimation>
            </div>
          </SlideIn>
        </div>
      </div>
    </div>
  );
};`;

  fs.writeFileSync('src/components/Hero.tsx', content, 'utf8');
  log('✅ Created minimal Hero.tsx', colors.green);
}

// Create minimal MapSearch.tsx
function createMinimalMapSearch() {
  const content = `import React from 'react';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Navigation, Search } from 'lucide-react';
import { SlideIn } from '@/components/ui/slide-in';

interface MapSearchProps {
  className?: string;
}

export const MapSearch: React.FC<MapSearchProps> = ({ className = '' }) => {
  const handleSearchArea = () => {
    console.log('Search area clicked');
  };

  const getViewModeIcon = (mode: string) => {
    return <Navigation className="h-4 w-4" />;
  };

  return (
    <SlideIn direction="up" delay={100}>
      <div className={\`space-y-6 \${className}\`}>
        <Card className="border-0 shadow-lg">
          <CardHeader className="pb-4">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
              <CardTitle className="flex items-center gap-2">
                <Navigation className="h-5 w-5 text-sea-green-600" />
                Interactive Map Search
              </CardTitle>
              
              <div className="flex items-center gap-2">
                {['map', 'list', 'split'].map((mode) => (
                  <Button
                    key={mode}
                    variant="outline"
                    size="sm"
                    className="flex items-center gap-1"
                  >
                    {getViewModeIcon(mode)}
                  </Button>
                ))}
              </div>

              <Button
                variant="outline"
                size="sm"
                onClick={handleSearchArea}
                className="flex items-center gap-2"
              >
                <Search className="h-4 w-4" />
                Search This Area
              </Button>
            </div>
          </CardHeader>
        </Card>

        <Card className="border-0 shadow-lg">
          <CardHeader>
            <CardTitle>Map Instructions</CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2 text-sm text-gray-600">
              <li>• Use the search controls above to filter properties</li>
              <li>• Switch between Map, List, and Split views using the toggle buttons</li>
              <li>• Zoom in/out to explore different areas along the coast</li>
            </ul>
          </CardContent>
        </Card>
      </div>
    </SlideIn>
  );
};`;

  fs.writeFileSync('src/components/MapSearch.tsx', content, 'utf8');
  log('✅ Created minimal MapSearch.tsx', colors.green);
}

// Create minimal PropertyAnalytics.tsx
function createMinimalPropertyAnalytics() {
  const content = `import React from 'react';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { SlideIn } from '@/components/ui/slide-in';
import { TrendingUp } from 'lucide-react';

interface PropertyAnalyticsProps {
  className?: string;
  analytics?: any;
}

export const PropertyAnalytics: React.FC<PropertyAnalyticsProps> = ({ 
  className = '', 
  analytics 
}) => {
  if (!analytics) {
    return null;
  }

  return (
    <SlideIn direction="up" delay={100}>
      <div className={\`space-y-6 \${className}\`}>
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Property Analytics</h2>
            <p className="text-gray-600">Track your property performance</p>
          </div>
        </div>

        <Card className="border-0 shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-sea-green-600" />
              Analytics Overview
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600">Analytics data will be displayed here.</p>
          </CardContent>
        </Card>
      </div>
    </SlideIn>
  );
};`;

  fs.writeFileSync('src/components/PropertyAnalytics.tsx', content, 'utf8');
  log('✅ Created minimal PropertyAnalytics.tsx', colors.green);
}

// Create minimal UserDashboard.tsx
function createMinimalUserDashboard() {
  const content = `import React, { useState } from 'react';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { SlideIn } from '@/components/ui/slide-in';
import { PageTransition } from '@/components/ui/page-transition';
import { Header } from '@/components/Header';
import { User, Calendar, Heart, Star, Settings, Bell } from 'lucide-react';

export const UserDashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');

  const tabs = [
    { id: 'overview', label: 'Overview', icon: User },
    { id: 'bookings', label: 'Bookings', icon: Calendar },
    { id: 'wishlist', label: 'Wishlist', icon: Heart },
    { id: 'reviews', label: 'Reviews', icon: Star },
    { id: 'profile', label: 'Profile', icon: User },
    { id: 'settings', label: 'Settings', icon: Settings }
  ];

  return (
    <PageTransition>
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-sea-green-50">
        <Header />

        <div className="container mx-auto px-4 py-6">
          <SlideIn direction="up" delay={100}>
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-gray-900 mb-2">Welcome back!</h1>
              <p className="text-gray-600">Manage your bookings and preferences</p>
            </div>
          </SlideIn>

          <SlideIn direction="up" delay={200}>
            <Card className="mb-8">
              <CardContent className="p-0">
                <div className="flex overflow-x-auto border-b border-gray-200">
                  {tabs.map((tab) => {
                    const Icon = tab.icon;
                    return (
                      <button
                        key={tab.id}
                        onClick={() => setActiveTab(tab.id)}
                        className={\`flex items-center space-x-2 px-6 py-4 font-medium text-sm transition-all duration-200 border-b-2 whitespace-nowrap \${
                          activeTab === tab.id
                            ? 'border-sea-green-500 text-sea-green-600 bg-sea-green-50'
                            : 'border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                        }\`}
                      >
                        <Icon className="h-4 w-4" />
                        <span>{tab.label}</span>
                      </button>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </SlideIn>

          {activeTab === 'overview' && (
            <div className="space-y-8">
              <SlideIn direction="up" delay={300}>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  <Card className="overflow-hidden border-0 shadow-lg bg-gradient-to-br from-sea-green-500 to-sea-green-600 text-white">
                    <CardContent className="p-6 text-center relative">
                      <Calendar className="h-10 w-10 mx-auto mb-3 text-white/90" />
                      <div className="text-3xl font-bold mb-1">3</div>
                      <div className="text-white/90 font-medium">Upcoming Trips</div>
                    </CardContent>
                  </Card>
                </div>
              </SlideIn>
            </div>
          )}

          {activeTab === 'bookings' && (
            <SlideIn direction="up" delay={100}>
              <Card>
                <CardHeader>
                  <CardTitle>Your Bookings</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600">Your booking history will appear here.</p>
                </CardContent>
              </Card>
            </SlideIn>
          )}

          {activeTab === 'wishlist' && (
            <SlideIn direction="up" delay={100}>
              <Card>
                <CardHeader>
                  <CardTitle>Your Wishlist</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600">Your saved properties will appear here.</p>
                </CardContent>
              </Card>
            </SlideIn>
          )}

          {activeTab === 'reviews' && (
            <SlideIn direction="up" delay={100}>
              <Card>
                <CardHeader>
                  <CardTitle>Your Reviews</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600">Your reviews will appear here.</p>
                </CardContent>
              </Card>
            </SlideIn>
          )}

          {activeTab === 'profile' && (
            <SlideIn direction="up" delay={100}>
              <Card>
                <CardHeader>
                  <CardTitle>Profile Settings</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600">Profile settings will appear here.</p>
                </CardContent>
              </Card>
            </SlideIn>
          )}

          {activeTab === 'settings' && (
            <SlideIn direction="up" delay={100}>
              <Card>
                <CardHeader>
                  <CardTitle>Account Settings</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600">Account settings will appear here.</p>
                </CardContent>
              </Card>
            </SlideIn>
          )}
        </div>
      </div>
    </PageTransition>
  );
};`;

  fs.writeFileSync('src/pages/UserDashboard.tsx', content, 'utf8');
  log('✅ Created minimal UserDashboard.tsx', colors.green);
}

// Main function
async function emergencyFix() {
  log('🚨 EMERGENCY JSX FIX - CREATING MINIMAL COMPONENTS', colors.cyan);
  log('='.repeat(60), colors.cyan);
  
  createMinimalHero();
  createMinimalMapSearch();
  createMinimalPropertyAnalytics();
  createMinimalUserDashboard();
  
  log('\n✅ All problematic components replaced with minimal versions', colors.green);
  log('🎯 Try starting the dev server now!', colors.blue);
}

emergencyFix();
