
import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { Mail, Phone, User, Facebook, Twitter, Instagram, Send, Globe, Award, Users, Home, TrendingUp, ChevronUp, MapPin } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { toast } from '@/components/ui/use-toast';

export const Footer = () => {
  const [email, setEmail] = useState('');
  const [isSubscribed, setIsSubscribed] = useState(false);

  const handleNewsletterSignup = (e: React.FormEvent) => {
    e.preventDefault();
    if (email) {
      setIsSubscribed(true);
      toast({
        title: "Successfully subscribed!",
        description: "Thank you for subscribing to our newsletter.",
      });
      setEmail('');
    }
  };

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const stats = [
    { icon: <Home className="h-5 w-5" />, value: '10,000+', label: 'Properties' },
    { icon: <Users className="h-5 w-5" />, value: '50,000+', label: 'Happy Guests' },
    { icon: <Globe className="h-5 w-5" />, value: '200+', label: 'Cities' },
    { icon: <Award className="h-5 w-5" />, value: '4.8/5', label: 'Average Rating' }
  ];

  return (
    <footer className="bg-gray-900 text-white relative">
      {/* Stats Section */}
      <div className="bg-sea-green-600 py-8">
        <div className="max-w-7xl mx-auto px-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="flex items-center justify-center mb-2">
                  {stat.icon}
                </div>
                <div className="text-2xl font-bold mb-1">{stat.value}</div>
                <div className="text-sm opacity-90">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">
          {/* Company Info */}
          <div className="lg:col-span-2">
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-10 h-10 bg-gradient-to-br from-sea-green-400 to-ocean-blue-400 rounded-full flex items-center justify-center">
                <span className="text-white font-bold text-lg">S</span>
              </div>
              <div>
                <h3 className="text-2xl font-bold text-sea-green-400">StayFinder</h3>
                <p className="text-sea-green-200 text-sm">Your perfect stay awaits</p>
              </div>
            </div>
            <p className="text-gray-300 mb-6 leading-relaxed">
              Discover unique accommodations across South Africa. From cozy apartments in Cape Town
              to luxury safari lodges in Kruger. Your perfect stay is just a click away.
            </p>

            {/* Newsletter Signup */}
            <div className="mb-6">
              <h4 className="text-lg font-semibold mb-3">Stay Updated</h4>
              {!isSubscribed ? (
                <form onSubmit={handleNewsletterSignup} className="flex gap-2">
                  <Input
                    type="email"
                    placeholder="Enter your email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="bg-gray-800 border-gray-700 text-white placeholder-gray-400"
                    required
                  />
                  <Button type="submit" className="bg-sea-green-600 hover:bg-sea-green-700">
                    <Send className="h-4 w-4" />
                  </Button>
                </form>
              ) : (
                <div className="flex items-center gap-2 text-sea-green-400">
                  <Badge className="bg-sea-green-600">✓ Subscribed</Badge>
                  <span className="text-sm">Thank you for subscribing!</span>
                </div>
              )}
              <p className="text-xs text-gray-400 mt-2">
                Get travel tips, exclusive deals, and destination guides.
              </p>
            </div>

            {/* Social Media */}
            <div>
              <h4 className="text-lg font-semibold mb-3">Follow Us</h4>
              <div className="flex space-x-4">
                <a href="#" className="p-2 bg-gray-800 rounded-lg hover:bg-blue-600 transition-colors">
                  <Facebook className="h-5 w-5" />
                </a>
                <a href="#" className="p-2 bg-gray-800 rounded-lg hover:bg-blue-400 transition-colors">
                  <Twitter className="h-5 w-5" />
                </a>
                <a href="#" className="p-2 bg-gray-800 rounded-lg hover:bg-pink-600 transition-colors">
                  <Instagram className="h-5 w-5" />
                </a>
              </div>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Explore</h4>
            <ul className="space-y-3">
              <li><Link to="/search" className="text-gray-300 hover:text-sea-green-400 transition-colors">Search Properties</Link></li>
              <li><Link to="/destinations" className="text-gray-300 hover:text-sea-green-400 transition-colors">Popular Destinations</Link></li>
              <li><Link to="/blog" className="text-gray-300 hover:text-sea-green-400 transition-colors">Travel Blog</Link></li>
              <li><Link to="/about" className="text-gray-300 hover:text-sea-green-400 transition-colors">About Us</Link></li>
              <li><Link to="/contact" className="text-gray-300 hover:text-sea-green-400 transition-colors">Contact</Link></li>
            </ul>
          </div>

          {/* For Hosts */}
          <div>
            <h4 className="text-lg font-semibold mb-4">For Hosts</h4>
            <ul className="space-y-3">
              <li><Link to="/host" className="text-gray-300 hover:text-sea-green-400 transition-colors">Become a Host</Link></li>
              <li><Link to="/host/resources" className="text-gray-300 hover:text-sea-green-400 transition-colors">Host Resources</Link></li>
              <li><Link to="/host/community" className="text-gray-300 hover:text-sea-green-400 transition-colors">Host Community</Link></li>
              <li><Link to="/host/insurance" className="text-gray-300 hover:text-sea-green-400 transition-colors">Host Protection</Link></li>
              <li><Link to="/host/pricing" className="text-gray-300 hover:text-sea-green-400 transition-colors">Smart Pricing</Link></li>
            </ul>
          </div>

          {/* Support */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Support</h4>
            <ul className="space-y-3">
              <li><Link to="/help" className="text-gray-300 hover:text-sea-green-400 transition-colors">Help Center</Link></li>
              <li><Link to="/faq" className="text-gray-300 hover:text-sea-green-400 transition-colors">FAQ</Link></li>
              <li><Link to="/safety" className="text-gray-300 hover:text-sea-green-400 transition-colors">Safety Center</Link></li>
              <li><Link to="/accessibility" className="text-gray-300 hover:text-sea-green-400 transition-colors">Accessibility</Link></li>
              <li><Link to="/cancellation" className="text-gray-300 hover:text-sea-green-400 transition-colors">Cancellation Options</Link></li>
            </ul>

            {/* Contact Info */}
            <div className="mt-6">
              <h5 className="font-semibold mb-3">Contact Us</h5>
              <div className="space-y-2 text-sm">
                <div className="flex items-center text-gray-300">
                  <Mail className="h-4 w-4 mr-2 flex-shrink-0" />
                  <span><EMAIL></span>
                </div>
                <div className="flex items-center text-gray-300">
                  <Phone className="h-4 w-4 mr-2 flex-shrink-0" />
                  <span>+27 21 123 4567</span>
                </div>
                <div className="flex items-center text-gray-300">
                  <MapPin className="h-4 w-4 mr-2 flex-shrink-0" />
                  <span>Cape Town, South Africa</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-gray-700 mt-12 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="text-gray-300 text-sm mb-4 md:mb-0">
              © 2024 StayFinder. All rights reserved. Made with ❤️ in South Africa.
            </div>

            <div className="flex flex-wrap items-center gap-4 text-sm">
              <Link to="/privacy" className="text-gray-300 hover:text-sea-green-400 transition-colors">Privacy Policy</Link>
              <span className="text-gray-600">•</span>
              <Link to="/terms" className="text-gray-300 hover:text-sea-green-400 transition-colors">Terms of Service</Link>
              <span className="text-gray-600">•</span>
              <Link to="/cookies" className="text-gray-300 hover:text-sea-green-400 transition-colors">Cookie Policy</Link>
              <span className="text-gray-600">•</span>
              <Link to="/sitemap" className="text-gray-300 hover:text-sea-green-400 transition-colors">Sitemap</Link>
            </div>
          </div>
        </div>
      </div>

      {/* Back to Top Button */}
      <button
        onClick={scrollToTop}
        className="absolute bottom-6 right-6 bg-sea-green-600 hover:bg-sea-green-700 text-white p-3 rounded-full shadow-lg transition-colors"
        aria-label="Back to top"
      >
        <ChevronUp className="h-5 w-5" />
      </button>
    </footer>
  );
};
