
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. */

@layer base {
  :root {
    --background: 255 255 255;
    --foreground: 15 23 42;

    --card: 255 255 255;
    --card-foreground: 15 23 42;

    --popover: 255 255 255;
    --popover-foreground: 15 23 42;

    --primary: 20 184 166;
    --primary-foreground: 255 255 255;

    --secondary: 241 245 249;
    --secondary-foreground: 15 23 42;

    --muted: 241 245 249;
    --muted-foreground: 100 116 139;

    --accent: 241 245 249;
    --accent-foreground: 15 23 42;

    --destructive: 239 68 68;
    --destructive-foreground: 255 255 255;

    --border: 226 232 240;
    --input: 226 232 240;
    --ring: 20 184 166;

    --radius: 0.5rem;

    --sidebar-background: 255 255 255;
    --sidebar-foreground: 15 23 42;
    --sidebar-primary: 20 184 166;
    --sidebar-primary-foreground: 255 255 255;
    --sidebar-accent: 241 245 249;
    --sidebar-accent-foreground: 15 23 42;
    --sidebar-border: 226 232 240;
    --sidebar-ring: 20 184 166;
  }

  .dark {
    --background: 15 23 42;
    --foreground: 248 250 252;

    --card: 15 23 42;
    --card-foreground: 248 250 252;

    --popover: 15 23 42;
    --popover-foreground: 248 250 252;

    --primary: 45 212 191;
    --primary-foreground: 15 23 42;

    --secondary: 30 41 59;
    --secondary-foreground: 248 250 252;

    --muted: 30 41 59;
    --muted-foreground: 148 163 184;

    --accent: 30 41 59;
    --accent-foreground: 248 250 252;

    --destructive: 220 38 38;
    --destructive-foreground: 248 250 252;

    --border: 30 41 59;
    --input: 30 41 59;
    --ring: 45 212 191;

    --sidebar-background: 15 23 42;
    --sidebar-foreground: 248 250 252;
    --sidebar-primary: 45 212 191;
    --sidebar-primary-foreground: 15 23 42;
    --sidebar-accent: 30 41 59;
    --sidebar-accent-foreground: 248 250 252;
    --sidebar-border: 30 41 59;
    --sidebar-ring: 45 212 191;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-gradient-to-br from-sea-green-50 via-white to-ocean-blue-50 text-foreground;
    min-height: 100vh;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100;
}

::-webkit-scrollbar-thumb {
  @apply bg-sea-green-400 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-sea-green-500;
}

/* Smooth animations */
* {
  transition: all 0.2s ease-in-out;
}
