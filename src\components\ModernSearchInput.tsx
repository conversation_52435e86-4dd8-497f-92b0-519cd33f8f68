import React from 'react';
import { EnhancedSearchAutocomplete } from './EnhancedSearchAutocomplete';
import { SearchSuggestion } from '@/hooks/useSearchSuggestions';
import { cn } from '@/lib/utils';

interface ModernSearchInputProps {
  value: string;
  onChange: (value: string) => void;
  onSearch?: (value: string) => void;
  onSuggestionSelect?: (suggestion: SearchSuggestion) => void;
  placeholder?: string;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  showIcon?: boolean;
  autoFocus?: boolean;
}

export const ModernSearchInput: React.FC<ModernSearchInputProps> = ({
  value,
  onChange,
  onSearch,
  onSuggestionSelect,
  placeholder = "Search destinations, properties...",
  className,
  size = 'md',
  showIcon = true,
  autoFocus = false
}) => {
  // Size-based styling
  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'text-sm py-2';
      case 'lg':
        return 'text-lg py-4';
      default:
        return 'text-base py-3';
    }
  };

  return (
    <EnhancedSearchAutocomplete
      value={value}
      onChange={onChange}
      onSelect={onSuggestionSelect}
      placeholder={placeholder}
      autoFocus={autoFocus}
      className={cn(getSizeClasses(), className)}
      showRecentSearches={true}
      showPopularDestinations={true}
      maxSuggestions={8}
    />
  );
};
