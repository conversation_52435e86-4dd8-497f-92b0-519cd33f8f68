const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Supabase configuration
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration. Please check your .env file.');
  process.exit(1);
}

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Test Supabase connection
async function testConnection() {
  try {
    const { data, error } = await supabase
      .from('users')
      .select('count', { count: 'exact', head: true });

    if (error) {
      console.error('❌ Supabase connection failed:', error.message);
      return false;
    }

    console.log('✅ Supabase connected successfully');
    return true;
  } catch (error) {
    console.error('❌ Supabase connection failed:', error.message);
    return false;
  }
}

// Execute Supabase query with error handling
async function executeQuery(table, operation, options = {}) {
  try {
    let query = supabase.from(table);

    switch (operation) {
      case 'select':
        query = query.select(options.select || '*');
        if (options.eq) query = query.eq(options.eq.column, options.eq.value);
        if (options.filter) query = query.filter(options.filter.column, options.filter.operator, options.filter.value);
        if (options.order) query = query.order(options.order.column, { ascending: options.order.ascending });
        if (options.limit) query = query.limit(options.limit);
        if (options.range) query = query.range(options.range.from, options.range.to);
        break;
      case 'insert':
        query = query.insert(options.data);
        break;
      case 'update':
        query = query.update(options.data);
        if (options.eq) query = query.eq(options.eq.column, options.eq.value);
        break;
      case 'delete':
        if (options.eq) query = query.delete().eq(options.eq.column, options.eq.value);
        break;
    }

    const { data, error } = await query;

    if (error) {
      console.error('Supabase query error:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Database query error:', error);
    throw error;
  }
}

// Get single record
async function findOne(table, options = {}) {
  try {
    const results = await executeQuery(table, 'select', { ...options, limit: 1 });
    return results && results.length > 0 ? results[0] : null;
  } catch (error) {
    throw error;
  }
}

// Get multiple records
async function findMany(table, options = {}) {
  try {
    const results = await executeQuery(table, 'select', options);
    return results || [];
  } catch (error) {
    throw error;
  }
}

// Insert record
async function insertRecord(table, data) {
  try {
    const result = await executeQuery(table, 'insert', { data });

    return {
      data: result,
      success: true
    };
  } catch (error) {
    throw error;
  }
}

// Update record
async function updateRecord(table, data, whereColumn, whereValue) {
  try {
    const result = await executeQuery(table, 'update', {
      data,
      eq: { column: whereColumn, value: whereValue }
    });

    return {
      data: result,
      success: true
    };
  } catch (error) {
    throw error;
  }
}

// Delete record
async function deleteRecord(table, whereColumn, whereValue) {
  try {
    const result = await executeQuery(table, 'delete', {
      eq: { column: whereColumn, value: whereValue }
    });

    return {
      success: true
    };
  } catch (error) {
    throw error;
  }
}

// Transaction wrapper (Supabase handles transactions internally)
async function transaction(callback) {
  try {
    // Supabase handles transactions automatically for single operations
    // For complex transactions, we'll need to handle them differently
    const result = await callback(supabase);
    return result;
  } catch (error) {
    throw error;
  }
}

// Get Supabase client for direct access
function getClient() {
  return supabase;
}

// Initialize database connection on startup
testConnection();

module.exports = {
  supabase,
  getClient,
  testConnection,
  executeQuery,
  findOne,
  findMany,
  insertRecord,
  updateRecord,
  deleteRecord,
  transaction
};
