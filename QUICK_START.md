# 🚀 Quick Start Guide
## Get StayFinder Running in 10 Minutes

### ✅ Prerequisites Check

Since you have XAMPP running, let's verify everything is ready:

1. **XAMPP Status**: ✅ Apache and MySQL running
2. **Node.js**: Check with `node --version` (need v16+)
3. **npm**: Check with `npm --version`

### 📋 Step-by-Step Setup

#### 1. Database Setup (2 minutes)

1. Open **phpMyAdmin**: http://localhost/phpmyadmin
2. Click **Import** tab
3. Choose file: `database_init.sql` (from project root)
4. Click **Go** to execute

**Verify**: You should see `stayfinder_dev` database with 5 tables and sample data.

#### 2. Backend Setup (3 minutes)

```bash
# Navigate to backend folder
cd backend

# Install dependencies
npm install

# Start the backend server
npm run dev
```

**Expected Output**:
```
🚀 StayFinder API Server running on port 3001
📊 Environment: development
🌐 CORS Origin: http://localhost:5173
🗄️  Database: localhost:3306/stayfinder_dev
✅ Database connected successfully
```

#### 3. Frontend Setup (2 minutes)

Open a **new terminal** in project root:

```bash
# Install frontend dependencies (if not done already)
npm install

# Start the frontend development server
npm run dev
```

**Expected Output**:
```
  VITE v5.4.1  ready in 500 ms

  ➜  Local:   http://localhost:5173/
  ➜  Network: use --host to expose
```

#### 4. Test the Integration (3 minutes)

1. **Open Browser**: http://localhost:5173
2. **Test Search**: Try searching for "Margate" or "Villa"
3. **Test Login**: Use test credentials:
   - **Email**: `<EMAIL>`
   - **Password**: `password123`

### 🧪 API Testing

Test the backend API directly:

#### Health Check
```bash
curl http://localhost:3001/api/health
```

#### Get Properties
```bash
curl http://localhost:3001/api/properties
```

#### Login Test
```bash
curl -X POST http://localhost:3001/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'
```

### 🔧 Development Workflow

#### Frontend Development
- **URL**: http://localhost:5173
- **Hot Reload**: Automatic on file changes
- **Components**: Located in `src/components/`

#### Backend Development
- **URL**: http://localhost:3001
- **Auto Restart**: Using nodemon
- **Routes**: Located in `backend/src/routes/`

#### Database Management
- **phpMyAdmin**: http://localhost/phpmyadmin
- **Database**: `stayfinder_dev`
- **User**: `root` (no password)

### 📊 Sample Data Available

#### Test Users
| Email | Password | Role |
|-------|----------|------|
| `<EMAIL>` | `password123` | Guest |
| `<EMAIL>` | `password123` | Host |
| `<EMAIL>` | `password123` | Admin |

#### Test Properties
- **Luxury Villa** in Margate (R2,500/night)
- **Beach Cottage** in Scottburgh (R800/night)
- **Modern Apartment** in Hibberdene (R1,200/night)
- **Family Guesthouse** in Port Shepstone (R1,800/night)
- **Beachfront Apartment** in Amanzimtoti (R1,000/night)

### 🔍 Troubleshooting

#### Backend Won't Start
```bash
# Check if port 3001 is in use
netstat -an | findstr :3001

# Kill process if needed (Windows)
taskkill /F /PID <process_id>
```

#### Database Connection Error
1. Verify MySQL is running in XAMPP
2. Check database exists: `stayfinder_dev`
3. Verify `.env` file in backend folder

#### Frontend API Calls Failing
1. Check backend is running on port 3001
2. Verify CORS settings in `backend/server.js`
3. Check browser console for errors

### 🛠️ Development Tools

#### Recommended VS Code Extensions
- **ES7+ React/Redux/React-Native snippets**
- **Prettier - Code formatter**
- **ESLint**
- **Thunder Client** (for API testing)
- **MySQL** (for database management)

#### Browser Dev Tools
- **React Developer Tools**
- **Network tab** for API debugging
- **Console** for error checking

### 📝 Next Development Steps

#### Immediate Tasks
1. **Connect Frontend to Backend**:
   - Update `src/components/Hero.tsx` to use real API
   - Replace mock data in components
   - Add authentication state management

2. **Implement Real Search**:
   - Connect search form to `/api/properties` endpoint
   - Add filtering and pagination
   - Implement location-based search

3. **Add Authentication Flow**:
   - Update login/register forms
   - Add JWT token storage
   - Implement protected routes

#### Medium-term Features
1. **Property Management**:
   - Host dashboard for property owners
   - Property creation/editing forms
   - Image upload functionality

2. **Booking System**:
   - Real booking creation
   - Payment integration (PayFast)
   - Booking management dashboard

3. **Review System**:
   - Connect review forms to API
   - Display real reviews
   - Host response functionality

### 🎯 Success Indicators

You'll know everything is working when:

✅ **Frontend loads** at http://localhost:5173  
✅ **Backend responds** at http://localhost:3001/api/health  
✅ **Database connected** (check backend console)  
✅ **Search returns** sample properties  
✅ **Login works** with test credentials  
✅ **No console errors** in browser dev tools  

### 📞 Getting Help

If you encounter issues:

1. **Check the logs** in both terminal windows
2. **Verify all services** are running (XAMPP, Backend, Frontend)
3. **Test API endpoints** directly with curl or Postman
4. **Check database** has sample data in phpMyAdmin

### 🚀 Ready to Code!

Once everything is running, you have a full-stack development environment with:

- **React Frontend** with modern UI components
- **Node.js Backend** with RESTful API
- **MySQL Database** with sample data
- **Authentication System** ready to use
- **Hot reload** for rapid development

Start by exploring the existing components and API endpoints, then begin implementing the features outlined in the DDD.md document!

---

**Happy Coding! 🎉**
