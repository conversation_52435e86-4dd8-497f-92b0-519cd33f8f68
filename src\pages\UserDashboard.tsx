import React, { useState, useEffect } from 'react';
import { Header } from '../components/Header';
import { RecommendationsDashboard } from '../components/RecommendationsDashboard';
import { UserProfile } from '../components/UserProfile';
import { UserSettings } from '../components/UserSettings';
import { BookingHistory } from '../components/BookingHistory';
import { Wishlist } from '../components/Wishlist';
import { ReviewManagement } from '../components/ReviewManagement';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useAuth } from '../contexts/AuthContext';
import {
  User,
  Calendar,
  Heart,
  Star,
  MapPin,
  Settings,
  Bell,
  CreditCard,
  TrendingUp,
  Award,
  Clock,
  Plus,
  Search,
  Filter
} from 'lucide-react';
import {
  PageTransition,
  SlideIn,
  StaggeredAnimation,
  HoverAnimation
} from '@/components/ui/page-transitions';
import {
  DashboardSkeleton,
  PropertyCardSkeleton
} from '@/components/ui/loading-skeletons';
import { StayFinderLoader } from '@/components/ui/loading-spinner';

export const UserDashboard: React.FC = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState<'overview' | 'bookings' | 'wishlist' | 'reviews' | 'profile' | 'settings'>('overview');
  const [isLoading, setIsLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState({
    upcomingTrips: 3,
    savedProperties: 12,
    totalReviews: 8,
    memberSince: '2023'
  });

  // Simulate loading dashboard data
  useEffect(() => {
    const loadDashboardData = async () => {
      setIsLoading(true);
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      setIsLoading(false);
    };

    loadDashboardData();
  }, []);

  const getUserInitials = () => {
    if (!user?.firstName || !user?.lastName) return 'U';
    return `${user.firstName.charAt(0)}${user.lastName.charAt(0)}`.toUpperCase();
  };

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="container mx-auto px-4 py-12">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              Please log in to view your dashboard
            </h2>
            <Button onClick={() => window.location.href = '/login'}>
              Log In
            </Button>
          </div>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="container mx-auto px-4 py-6">
          <DashboardSkeleton />
        </div>
      </div>
    );
  }

  return (
    <PageTransition>
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-sea-green-50">
        <Header />

        <div className="container mx-auto px-4 py-6">
          {/* Enhanced Welcome Section */}
          <SlideIn direction="up" delay={100}>
            <div className="mb-8 relative overflow-hidden">
              <div className="bg-gradient-to-r from-sea-green-500 via-ocean-blue-500 to-sea-green-600 rounded-2xl p-8 text-white shadow-xl">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-6">
                    <Avatar className="h-20 w-20 border-4 border-white/20 shadow-lg">
                      <AvatarImage src="" alt={user.firstName} />
                      <AvatarFallback className="bg-white/20 text-white text-2xl font-bold">
                        {getUserInitials()}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <h1 className="text-4xl font-bold mb-2">
                        Welcome back, {user.firstName}!
                      </h1>
                      <p className="text-white/90 text-lg">
                        Manage your profile, bookings, and discover your next perfect getaway
                      </p>
                      <div className="flex items-center mt-3 space-x-4">
                        <Badge variant="secondary" className="bg-white/20 text-white border-white/30">
                          <Award className="h-4 w-4 mr-1" />
                          Member since {dashboardData.memberSince}
                        </Badge>
                        <Badge variant="secondary" className="bg-white/20 text-white border-white/30">
                          <Star className="h-4 w-4 mr-1" />
                          Verified User
                        </Badge>
                      </div>
                    </div>
                  </div>
                  <div className="hidden md:block">
                    <Button
                      variant="secondary"
                      className="bg-white/20 hover:bg-white/30 text-white border-white/30"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Quick Book
                    </Button>
                  </div>
                </div>

                {/* Decorative elements */}
                <div className="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -translate-y-16 translate-x-16"></div>
                <div className="absolute bottom-0 left-0 w-24 h-24 bg-white/10 rounded-full translate-y-12 -translate-x-12"></div>
              </div>
            </div>
          </SlideIn>

          {/* Enhanced Navigation Tabs */}
          <SlideIn direction="up" delay={200}>
            <div className="mb-8">
              <Card className="overflow-hidden shadow-lg border-0">
                <CardContent className="p-0">
                  <nav className="flex overflow-x-auto">
                    {[
                      { id: 'overview', label: 'Overview', icon: TrendingUp },
                      { id: 'bookings', label: 'Bookings', icon: Calendar },
                      { id: 'wishlist', label: 'Wishlist', icon: Heart },
                      { id: 'reviews', label: 'Reviews', icon: Star },
                      { id: 'profile', label: 'Profile', icon: User },
                      { id: 'settings', label: 'Settings', icon: Settings }
                    ].map((tab) => {
                      const Icon = tab.icon;
                      return (
                        <button
                          key={tab.id}
                          onClick={() => setActiveTab(tab.id as any)}
                          className={`flex items-center space-x-2 px-6 py-4 font-medium text-sm transition-all duration-200 border-b-2 whitespace-nowrap ${
                            activeTab === tab.id
                              ? 'border-sea-green-500 text-sea-green-600 bg-sea-green-50'
                              : 'border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                          }`}
                        >
                          <Icon className="h-4 w-4" />
                          <span>{tab.label}</span>
                          {tab.id === 'bookings' && (
                            <Badge variant="secondary" className="ml-2 bg-sea-green-100 text-sea-green-700">
                              {dashboardData.upcomingTrips}
                            </Badge>
                          )}
                          {tab.id === 'wishlist' && (
                            <Badge variant="secondary" className="ml-2 bg-red-100 text-red-700">
                              {dashboardData.savedProperties}
                            </Badge>
                          )}
                          {tab.id === 'reviews' && (
                            <Badge variant="secondary" className="ml-2 bg-yellow-100 text-yellow-700">
                              {dashboardData.totalReviews}
                            </Badge>
                          )}
                        </button>
                      );
                    })}
                  </nav>
                </CardContent>
              </Card>
            </div>
          </SlideIn>

          {/* Tab Content */}
          {activeTab === 'overview' && (
            <div className="space-y-8">
              {/* Enhanced Quick Stats */}
              <SlideIn direction="up" delay={300}>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  <HoverAnimation type="lift">
                    <Card className="overflow-hidden border-0 shadow-lg bg-gradient-to-br from-sea-green-500 to-sea-green-600 text-white">
                      <CardContent className="p-6 text-center relative">
                        <Calendar className="h-10 w-10 mx-auto mb-3 text-white/90" />
                        <div className="text-3xl font-bold mb-1">{dashboardData.upcomingTrips}</div>
                        <div className="text-white/90 font-medium">Upcoming Trips</div>
                        <div className="absolute top-2 right-2 w-8 h-8 bg-white/20 rounded-full"></div>
                      </CardContent>
                    </Card>
                  </HoverAnimation>

                  <HoverAnimation type="lift">
                    <Card className="overflow-hidden border-0 shadow-lg bg-gradient-to-br from-red-500 to-pink-600 text-white">
                      <CardContent className="p-6 text-center relative">
                        <Heart className="h-10 w-10 mx-auto mb-3 text-white/90" />
                        <div className="text-3xl font-bold mb-1">{dashboardData.savedProperties}</div>
                        <div className="text-white/90 font-medium">Saved Properties</div>
                        <div className="absolute top-2 right-2 w-8 h-8 bg-white/20 rounded-full"></div>
                      </CardContent>
                    </Card>
                  </HoverAnimation>

                  <HoverAnimation type="lift">
                    <Card className="overflow-hidden border-0 shadow-lg bg-gradient-to-br from-yellow-500 to-orange-600 text-white">
                      <CardContent className="p-6 text-center relative">
                        <Star className="h-10 w-10 mx-auto mb-3 text-white/90" />
                        <div className="text-3xl font-bold mb-1">{dashboardData.totalReviews}</div>
                        <div className="text-white/90 font-medium">Reviews Written</div>
                        <div className="absolute top-2 right-2 w-8 h-8 bg-white/20 rounded-full"></div>
                      </CardContent>
                    </Card>
                  </HoverAnimation>

                  <HoverAnimation type="lift">
                    <Card className="overflow-hidden border-0 shadow-lg bg-gradient-to-br from-ocean-blue-500 to-blue-600 text-white">
                      <CardContent className="p-6 text-center relative">
                        <MapPin className="h-10 w-10 mx-auto mb-3 text-white/90" />
                        <div className="text-3xl font-bold mb-1">5</div>
                        <div className="text-white/90 font-medium">Cities Visited</div>
                        <div className="absolute top-2 right-2 w-8 h-8 bg-white/20 rounded-full"></div>
                      </CardContent>
                    </Card>
                  </HoverAnimation>
                </div>
              </SlideIn>

              {/* Enhanced Quick Actions */}
              <SlideIn direction="up" delay={400}>
                <Card className="mb-8 border-0 shadow-lg">
                  <CardHeader className="bg-gradient-to-r from-gray-50 to-white">
                    <CardTitle className="flex items-center">
                      <TrendingUp className="h-5 w-5 mr-2 text-sea-green-600" />
                      Quick Actions
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-6">
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <HoverAnimation type="scale">
                        <Button
                          variant="outline"
                          className="h-24 flex-col border-2 hover:border-sea-green-300 hover:bg-sea-green-50 transition-all duration-200"
                          onClick={() => setActiveTab('bookings')}
                        >
                          <Calendar className="h-8 w-8 mb-2 text-sea-green-600" />
                          <span className="text-sm font-medium">My Bookings</span>
                          <Badge variant="secondary" className="mt-1 bg-sea-green-100 text-sea-green-700">
                            {dashboardData.upcomingTrips}
                          </Badge>
                        </Button>
                      </HoverAnimation>

                      <HoverAnimation type="scale">
                        <Button
                          variant="outline"
                          className="h-24 flex-col border-2 hover:border-red-300 hover:bg-red-50 transition-all duration-200"
                          onClick={() => setActiveTab('wishlist')}
                        >
                          <Heart className="h-8 w-8 mb-2 text-red-600" />
                          <span className="text-sm font-medium">Saved Properties</span>
                          <Badge variant="secondary" className="mt-1 bg-red-100 text-red-700">
                            {dashboardData.savedProperties}
                          </Badge>
                        </Button>
                      </HoverAnimation>

                      <HoverAnimation type="scale">
                        <Button
                          variant="outline"
                          className="h-24 flex-col border-2 hover:border-yellow-300 hover:bg-yellow-50 transition-all duration-200"
                          onClick={() => setActiveTab('reviews')}
                        >
                          <Star className="h-8 w-8 mb-2 text-yellow-600" />
                          <span className="text-sm font-medium">My Reviews</span>
                          <Badge variant="secondary" className="mt-1 bg-yellow-100 text-yellow-700">
                            {dashboardData.totalReviews}
                          </Badge>
                        </Button>
                      </HoverAnimation>

                      <HoverAnimation type="scale">
                        <Button
                          variant="outline"
                          className="h-24 flex-col border-2 hover:border-blue-300 hover:bg-blue-50 transition-all duration-200"
                          onClick={() => setActiveTab('settings')}
                        >
                          <Settings className="h-8 w-8 mb-2 text-blue-600" />
                          <span className="text-sm font-medium">Account Settings</span>
                        </Button>
                      </HoverAnimation>
                    </div>
                  </CardContent>
                </Card>
              </SlideIn>

              {/* Enhanced Recent Activity */}
              <SlideIn direction="up" delay={500}>
                <Card className="mb-8 border-0 shadow-lg">
                  <CardHeader className="bg-gradient-to-r from-gray-50 to-white">
                    <CardTitle className="flex items-center justify-between">
                      <div className="flex items-center">
                        <Clock className="h-5 w-5 mr-2 text-ocean-blue-600" />
                        Recent Activity
                      </div>
                      <Badge variant="secondary" className="bg-ocean-blue-100 text-ocean-blue-700">
                        5 new
                      </Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-6">
                    <StaggeredAnimation delay={100}>
                      {[
                        {
                          type: 'success',
                          message: 'Booking confirmed for Luxury Villa in Margate',
                          time: '2 hours ago',
                          color: 'green'
                        },
                        {
                          type: 'info',
                          message: 'New review received for your stay in Scottburgh',
                          time: '1 day ago',
                          color: 'blue'
                        },
                        {
                          type: 'warning',
                          message: 'Price drop alert: Beachfront Cottage in Hibberdene',
                          time: '2 days ago',
                          color: 'yellow'
                        }
                      ].map((activity, index) => (
                        <HoverAnimation key={index} type="lift">
                          <div className={`flex items-center gap-4 p-4 bg-${activity.color}-50 rounded-xl border border-${activity.color}-200 hover:shadow-md transition-all duration-200`}>
                            <div className={`w-3 h-3 bg-${activity.color}-500 rounded-full flex-shrink-0`}></div>
                            <div className="flex-1">
                              <p className="text-sm font-medium text-gray-900">
                                {activity.message}
                              </p>
                              <p className="text-xs text-gray-600 mt-1">{activity.time}</p>
                            </div>
                          </div>
                        </HoverAnimation>
                      ))}
                    </StaggeredAnimation>
                  </CardContent>
                </Card>
              </SlideIn>

              {/* Enhanced Upcoming Bookings Preview */}
              <SlideIn direction="up" delay={600}>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-xl font-bold text-gray-900 flex items-center">
                      <Calendar className="h-6 w-6 mr-2 text-sea-green-600" />
                      Upcoming Trips
                    </h3>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setActiveTab('bookings')}
                      className="hover:bg-sea-green-50 hover:border-sea-green-300"
                    >
                      View All
                      <TrendingUp className="h-4 w-4 ml-2" />
                    </Button>
                  </div>
                  <BookingHistory showUpcomingOnly={true} limit={3} />
                </div>
              </SlideIn>
            </div>
          )}
        </div>

        {/* Wishlist Preview */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900">Saved Properties</h3>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setActiveTab('wishlist')}
            >
              View All
            </Button>
          </div>
          <Wishlist limit={3} showHeader={false} />
        </div>

        {/* Recent Reviews Preview */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900">Recent Reviews</h3>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setActiveTab('reviews')}
            >
              View All
            </Button>
          </div>
          <ReviewManagement limit={2} showStats={false} />
        </div>

        {/* Smart Recommendations */}
        <RecommendationsDashboard
          showSimilar={false}
          showRecentlyViewed={true}
          showPersonalized={true}
          showTrending={true}
        />

        {/* Account Management */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Profile Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-700">Name</label>
                <p className="text-gray-900">{user.firstName} {user.lastName}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-700">Email</label>
                <p className="text-gray-900">{user.email}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-700">Member Since</label>
                <p className="text-gray-900">January 2024</p>
              </div>
              <Button variant="outline" className="w-full">
                Edit Profile
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Account Settings
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium">Email Notifications</p>
                  <p className="text-sm text-gray-600">Booking updates and offers</p>
                </div>
                <input type="checkbox" defaultChecked className="w-4 h-4" />
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium">SMS Notifications</p>
                  <p className="text-sm text-gray-600">Important booking alerts</p>
                </div>
                <input type="checkbox" defaultChecked className="w-4 h-4" />
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium">Marketing Communications</p>
                  <p className="text-sm text-gray-600">Special offers and promotions</p>
                </div>
                <input type="checkbox" className="w-4 h-4" />
              </div>
              <Button variant="outline" className="w-full">
                <Bell className="h-4 w-4 mr-2" />
                Notification Settings
              </Button>
            </CardContent>
          </Card>
        </div>
          </div>
        )}

        {/* Bookings Tab */}
        {activeTab === 'bookings' && (
          <SlideIn direction="up" delay={100}>
            <BookingHistory />
          </SlideIn>
        )}

        {/* Wishlist Tab */}
        {activeTab === 'wishlist' && (
          <SlideIn direction="up" delay={100}>
            <Wishlist />
          </SlideIn>
        )}

        {/* Reviews Tab */}
        {activeTab === 'reviews' && (
          <SlideIn direction="up" delay={100}>
            <ReviewManagement />
          </SlideIn>
        )}

        {/* Profile Tab */}
        {activeTab === 'profile' && (
          <SlideIn direction="up" delay={100}>
            <UserProfile onProfileUpdate={(profile) => {
              console.log('Profile updated:', profile);
            }} />
          </SlideIn>
        )}

        {/* Settings Tab */}
        {activeTab === 'settings' && (
          <SlideIn direction="up" delay={100}>
            <UserSettings onSettingsUpdate={(profile) => {
              console.log('Settings updated:', profile);
            }} />
          </SlideIn>
        )}
        </div>
      </div>
    </PageTransition>
  );
};
