import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>eader, CardTitle, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { SlideIn } from '@/components/ui/slide-in';
import { PageTransition } from '@/components/ui/page-transition';
import { Header } from '@/components/Header';
import { User, Calendar, Heart, Star, Settings, Bell } from 'lucide-react';

export const UserDashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');

  const tabs = [
    { id: 'overview', label: 'Overview', icon: User },
    { id: 'bookings', label: 'Bookings', icon: Calendar },
    { id: 'wishlist', label: 'Wishlist', icon: Heart },
    { id: 'reviews', label: 'Reviews', icon: Star },
    { id: 'profile', label: 'Profile', icon: User },
    { id: 'settings', label: 'Settings', icon: Settings }
  ];

  return (
    <PageTransition>
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-sea-green-50">
        <Header />

        <div className="container mx-auto px-4 py-6">
          <SlideIn direction="up" delay={100}>
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-gray-900 mb-2">Welcome back!</h1>
              <p className="text-gray-600">Manage your bookings and preferences</p>
            </div>
          </SlideIn>

          <SlideIn direction="up" delay={200}>
            <Card className="mb-8">
              <CardContent className="p-0">
                <div className="flex overflow-x-auto border-b border-gray-200">
                  {tabs.map((tab) => {
                    const Icon = tab.icon;
                    return (
                      <button
                        key={tab.id}
                        onClick={() => setActiveTab(tab.id)}
                        className={`flex items-center space-x-2 px-6 py-4 font-medium text-sm transition-all duration-200 border-b-2 whitespace-nowrap ${
                          activeTab === tab.id
                            ? 'border-sea-green-500 text-sea-green-600 bg-sea-green-50'
                            : 'border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                        }`}
                      >
                        <Icon className="h-4 w-4" />
                        <span>{tab.label}</span>
                      </button>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </SlideIn>

          {activeTab === 'overview' && (
            <div className="space-y-8">
              <SlideIn direction="up" delay={300}>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  <Card className="overflow-hidden border-0 shadow-lg bg-gradient-to-br from-sea-green-500 to-sea-green-600 text-white">
                    <CardContent className="p-6 text-center relative">
                      <Calendar className="h-10 w-10 mx-auto mb-3 text-white/90" />
                      <div className="text-3xl font-bold mb-1">3</div>
                      <div className="text-white/90 font-medium">Upcoming Trips</div>
                    </CardContent>
                  </Card>
                </div>
              </SlideIn>
            </div>
          )}

          {activeTab === 'bookings' && (
            <SlideIn direction="up" delay={100}>
              <Card>
                <CardHeader>
                  <CardTitle>Your Bookings</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600">Your booking history will appear here.</p>
                </CardContent>
              </Card>
            </SlideIn>
          )}

          {activeTab === 'wishlist' && (
            <SlideIn direction="up" delay={100}>
              <Card>
                <CardHeader>
                  <CardTitle>Your Wishlist</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600">Your saved properties will appear here.</p>
                </CardContent>
              </Card>
            </SlideIn>
          )}

          {activeTab === 'reviews' && (
            <SlideIn direction="up" delay={100}>
              <Card>
                <CardHeader>
                  <CardTitle>Your Reviews</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600">Your reviews will appear here.</p>
                </CardContent>
              </Card>
            </SlideIn>
          )}

          {activeTab === 'profile' && (
            <SlideIn direction="up" delay={100}>
              <Card>
                <CardHeader>
                  <CardTitle>Profile Settings</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600">Profile settings will appear here.</p>
                </CardContent>
              </Card>
            </SlideIn>
          )}

          {activeTab === 'settings' && (
            <SlideIn direction="up" delay={100}>
              <Card>
                <CardHeader>
                  <CardTitle>Account Settings</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600">Account settings will appear here.</p>
                </CardContent>
              </Card>
            </SlideIn>
          )}
        </div>
      </div>
    </PageTransition>
  );
};