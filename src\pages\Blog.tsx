import React, { useState } from 'react';
import { Calendar, Clock, User, Tag, Search, TrendingUp, MapPin, Camera, Heart, Share2, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { cn } from '@/lib/utils';

interface BlogPost {
  id: string;
  title: string;
  excerpt: string;
  content: string;
  author: {
    name: string;
    avatar: string;
    bio: string;
  };
  publishedAt: Date;
  readTime: number;
  category: string;
  tags: string[];
  featuredImage: string;
  views: number;
  likes: number;
  featured: boolean;
}

interface BlogCategory {
  id: string;
  name: string;
  description: string;
  postCount: number;
  color: string;
}

export const Blog: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedPost, setSelectedPost] = useState<BlogPost | null>(null);

  const categories: BlogCategory[] = [
    {
      id: 'travel-tips',
      name: 'Travel Tips',
      description: 'Expert advice for better travel experiences',
      postCount: 24,
      color: 'bg-blue-100 text-blue-800'
    },
    {
      id: 'destinations',
      name: 'Destinations',
      description: 'Discover amazing places in South Africa',
      postCount: 18,
      color: 'bg-green-100 text-green-800'
    },
    {
      id: 'local-guides',
      name: 'Local Guides',
      description: 'Insider tips from local experts',
      postCount: 15,
      color: 'bg-purple-100 text-purple-800'
    },
    {
      id: 'hosting-tips',
      name: 'Hosting Tips',
      description: 'Advice for property hosts',
      postCount: 12,
      color: 'bg-orange-100 text-orange-800'
    },
    {
      id: 'culture',
      name: 'Culture & Events',
      description: 'South African culture and events',
      postCount: 9,
      color: 'bg-red-100 text-red-800'
    }
  ];

  const blogPosts: BlogPost[] = [
    {
      id: '1',
      title: 'The Ultimate Guide to Cape Town: Hidden Gems and Must-See Attractions',
      excerpt: 'Discover the best of Cape Town beyond the tourist trails. From secret beaches to local markets, explore the Mother City like a local.',
      content: `# The Ultimate Guide to Cape Town: Hidden Gems and Must-See Attractions

Cape Town is one of the world's most beautiful cities, but there's so much more to discover beyond the famous Table Mountain and V&A Waterfront. Let's explore some hidden gems that will make your visit truly unforgettable.

## Hidden Beaches

### Llandudno Beach
Tucked away between Hout Bay and Camps Bay, Llandudno offers pristine white sand and crystal-clear waters without the crowds. Perfect for a peaceful sunset picnic.

### Beta Beach
A secluded spot in Bakoven, accessible via a short hike. Popular with locals and perfect for those Instagram-worthy shots.

## Local Markets and Food

### Neighbourgoods Market
Every Saturday at the Old Biscuit Mill in Woodstock. Sample artisanal foods, craft beer, and local delicacies while enjoying live music.

### Bay Harbour Market
A weekend market in Hout Bay with stunning mountain views. Try the famous fish and chips while browsing local crafts.

## Cultural Experiences

### Bo-Kaap Walking Tour
Explore the colorful houses and rich history of the Cape Malay community. Don't miss the spice route cooking class!

### Zeitz Museum of Contemporary African Art
World-class contemporary African art in a stunning converted grain silo. The architecture alone is worth the visit.

## Adventure Activities

### Lion's Head Sunrise Hike
Start early for a magical sunrise experience. The 360-degree views of Cape Town are absolutely breathtaking.

### Kirstenbosch Botanical Gardens
More than just a garden - it's a UNESCO World Heritage site with incredible biodiversity and the famous Tree Canopy Walkway.

## Local Tips

- Use Uber or Bolt for safe transportation
- Try local wines at Constantia wine farms
- Visit during shoulder seasons (April-May, September-October) for better weather and fewer crowds
- Always carry sunscreen - the African sun is strong!

Cape Town has something for everyone, from adventure seekers to culture enthusiasts. Take time to explore beyond the guidebooks and you'll discover why locals are so passionate about this incredible city.`,
      author: {
        name: 'Sarah Johnson',
        avatar: '/avatars/sarah.jpg',
        bio: 'Travel writer and Cape Town local with 10+ years of exploring South Africa'
      },
      publishedAt: new Date('2024-12-20'),
      readTime: 8,
      category: 'destinations',
      tags: ['Cape Town', 'South Africa', 'Travel Guide', 'Hidden Gems'],
      featuredImage: '/blog/cape-town-guide.jpg',
      views: 2450,
      likes: 189,
      featured: true
    },
    {
      id: '2',
      title: '10 Essential Tips for First-Time Visitors to South Africa',
      excerpt: 'Planning your first trip to South Africa? Here are the essential tips every traveler should know before visiting the Rainbow Nation.',
      content: `# 10 Essential Tips for First-Time Visitors to South Africa

South Africa is an incredible destination with diverse landscapes, rich culture, and amazing wildlife. Here are essential tips to make your first visit smooth and memorable.

## 1. Best Time to Visit

**Summer (December-February):** Hot and rainy, perfect for coastal areas
**Autumn (March-May):** Mild weather, great for wine regions
**Winter (June-August):** Dry season, ideal for safari
**Spring (September-November):** Wildflower season, excellent weather

## 2. Currency and Payments

- Currency: South African Rand (ZAR)
- Credit cards widely accepted
- Carry cash for tips and small vendors
- ATMs available in cities and towns

## 3. Safety Tips

- Stay aware of your surroundings
- Don't display expensive items
- Use reputable tour operators
- Stick to well-lit areas at night
- Keep copies of important documents

## 4. Transportation

- **Domestic Flights:** Quick for long distances
- **Car Rental:** Best for flexibility (drive on the left!)
- **Uber/Bolt:** Safe in major cities
- **Tour Operators:** Great for safari and wine tours

## 5. Health Considerations

- No special vaccinations required for most areas
- Malaria risk in some northern regions
- Bring sunscreen (SPF 30+)
- Stay hydrated
- Travel insurance recommended

## 6. Cultural Etiquette

- South Africa has 11 official languages
- English is widely spoken
- Tipping: 10-15% at restaurants
- Respect local customs and traditions
- Ask before photographing people

## 7. Must-Try Experiences

- Safari in Kruger National Park
- Wine tasting in Stellenbosch
- Penguin viewing at Boulders Beach
- Cage diving with great whites
- Cultural tours in Soweto

## 8. Food and Drink

- Try braai (BBQ) - it's a cultural experience
- Sample local wines
- Taste biltong (dried meat snack)
- Try bobotie (traditional dish)
- Enjoy fresh seafood on the coast

## 9. Packing Essentials

- Comfortable walking shoes
- Light layers for changing weather
- Hat and sunglasses
- Adapter for Type M plugs
- Binoculars for wildlife viewing

## 10. Communication

- Buy a local SIM card for data
- WiFi available in most accommodations
- Download offline maps
- Learn basic Afrikaans phrases
- Emergency number: 10111

South Africa offers incredible diversity in a single destination. From bustling cities to pristine wilderness, ancient cultures to modern amenities - you'll find experiences that will stay with you forever.

**Pro Tip:** Don't try to see everything in one trip. Focus on 2-3 regions to really experience what South Africa has to offer.`,
      author: {
        name: 'Michael Chen',
        avatar: '/avatars/michael.jpg',
        bio: 'Travel photographer and South Africa tourism specialist'
      },
      publishedAt: new Date('2024-12-18'),
      readTime: 6,
      category: 'travel-tips',
      tags: ['South Africa', 'First Time', 'Travel Tips', 'Planning'],
      featuredImage: '/blog/sa-tips.jpg',
      views: 1890,
      likes: 142,
      featured: true
    },
    {
      id: '3',
      title: 'How to Be a 5-Star Host: Secrets from Top-Rated Properties',
      excerpt: 'Learn from the best hosts on StayFinder. Discover the secrets that consistently earn 5-star reviews and happy guests.',
      content: `# How to Be a 5-Star Host: Secrets from Top-Rated Properties

Becoming a top-rated host isn't just about having a beautiful property. It's about creating exceptional experiences that guests remember long after they leave. Here are the secrets from our highest-rated hosts.

## The Welcome Experience

### Before Arrival
- Send a warm welcome message 24-48 hours before check-in
- Provide detailed directions and parking information
- Share local recommendations and your contact details
- Confirm arrival time and any special requests

### The First Impression
- Ensure the property is spotlessly clean
- Set the temperature to a comfortable level
- Leave lights on for evening arrivals
- Consider a small welcome gift (local treats, wine, flowers)

## Communication Excellence

### Response Time
- Aim to respond within 1 hour during business hours
- Set up auto-responses for common questions
- Use templates for efficiency while maintaining personal touch
- Be available during your guests' stay

### Clear Information
- Provide comprehensive house manual
- Include WiFi passwords, appliance instructions
- Share emergency contact information
- Explain any unique features or quirks

## Property Standards

### Cleanliness is King
- Deep clean between every guest
- Pay attention to details: baseboards, light switches, remote controls
- Use professional cleaning services if needed
- Provide fresh linens and towels

### Comfort and Amenities
- Invest in quality mattresses and pillows
- Provide extra blankets and pillows
- Stock basic toiletries and kitchen essentials
- Ensure reliable WiFi and entertainment options

## Going Above and Beyond

### Local Expertise
- Create a guidebook with your favorite local spots
- Provide maps and transportation information
- Offer to make restaurant reservations
- Share insider tips about hidden gems

### Thoughtful Touches
- Fresh flowers or plants
- Local coffee or tea selection
- Books about the area
- Board games for families
- Charging stations by the bed

## Problem Resolution

### When Things Go Wrong
- Acknowledge issues immediately
- Apologize sincerely and take responsibility
- Offer solutions, not excuses
- Follow up to ensure satisfaction
- Learn from every situation

### Preventive Measures
- Regular property maintenance
- Backup plans for common issues
- Emergency contact list for guests
- Clear house rules to prevent problems

## Building Your Reputation

### Encouraging Reviews
- Provide exceptional service that naturally leads to great reviews
- Send a follow-up message after checkout
- Politely ask satisfied guests to leave a review
- Respond to all reviews professionally

### Continuous Improvement
- Read all feedback carefully
- Implement suggestions when possible
- Stay updated on hospitality trends
- Invest in property improvements

## The Business Side

### Pricing Strategy
- Research your competition regularly
- Adjust for seasons and local events
- Offer discounts for longer stays
- Use dynamic pricing tools

### Professional Photography
- Invest in high-quality photos
- Show all rooms and amenities
- Update photos seasonally
- Ensure photos accurately represent your space

## Technology and Efficiency

### Smart Home Features
- Keyless entry systems
- Smart thermostats
- Automated lighting
- Security cameras (exterior only)

### Management Tools
- Use property management software
- Automate routine communications
- Track expenses and revenue
- Monitor performance metrics

## Building Relationships

### With Guests
- Remember repeat guests' preferences
- Send holiday greetings to past guests
- Offer returning guest discounts
- Create a sense of community

### With Neighbors
- Introduce yourself to neighbors
- Share your contact information
- Address any concerns promptly
- Be a good community member

## Seasonal Considerations

### Summer Hosting
- Ensure air conditioning works perfectly
- Provide fans and cooling options
- Stock sunscreen and beach gear
- Maintain outdoor spaces

### Winter Hosting
- Check heating systems regularly
- Provide extra blankets
- Ensure hot water availability
- Clear walkways of ice/snow

Remember, being a 5-star host is about consistency. Every guest should receive the same high level of service, regardless of the season or your personal schedule. The investment in excellence pays off through higher occupancy rates, better reviews, and increased revenue.

**Key Takeaway:** Great hosting is about anticipating needs and exceeding expectations. When guests feel truly cared for, they become your best marketing ambassadors.`,
      author: {
        name: 'Lisa Thompson',
        avatar: '/avatars/lisa.jpg',
        bio: 'Hospitality consultant and top-rated StayFinder host'
      },
      publishedAt: new Date('2024-12-15'),
      readTime: 10,
      category: 'hosting-tips',
      tags: ['Hosting', 'Customer Service', 'Reviews', 'Property Management'],
      featuredImage: '/blog/5-star-host.jpg',
      views: 1650,
      likes: 98,
      featured: false
    }
  ];

  const featuredPosts = blogPosts.filter(post => post.featured);
  const trendingPosts = [...blogPosts].sort((a, b) => b.views - a.views).slice(0, 3);

  const filteredPosts = blogPosts.filter(post => {
    const matchesSearch = searchQuery === '' || 
      post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      post.excerpt.toLowerCase().includes(searchQuery.toLowerCase()) ||
      post.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    
    const matchesCategory = selectedCategory === 'all' || post.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-ZA', { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    });
  };

  if (selectedPost) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 py-8">
          <Button 
            variant="ghost" 
            onClick={() => setSelectedPost(null)}
            className="mb-6"
          >
            ← Back to Blog
          </Button>
          
          <article>
            <img 
              src={selectedPost.featuredImage} 
              alt={selectedPost.title}
              className="w-full h-64 object-cover rounded-lg mb-6"
            />
            
            <div className="flex items-center gap-2 mb-4">
              <Badge className={categories.find(c => c.id === selectedPost.category)?.color}>
                {categories.find(c => c.id === selectedPost.category)?.name}
              </Badge>
              {selectedPost.tags.map(tag => (
                <Badge key={tag} variant="secondary">{tag}</Badge>
              ))}
            </div>
            
            <h1 className="text-4xl font-bold mb-4">{selectedPost.title}</h1>
            
            <div className="flex items-center gap-4 mb-8 text-gray-600">
              <div className="flex items-center gap-2">
                <Avatar className="h-8 w-8">
                  <AvatarImage src={selectedPost.author.avatar} />
                  <AvatarFallback>{selectedPost.author.name.charAt(0)}</AvatarFallback>
                </Avatar>
                <span>{selectedPost.author.name}</span>
              </div>
              <span className="flex items-center gap-1">
                <Calendar className="h-4 w-4" />
                {formatDate(selectedPost.publishedAt)}
              </span>
              <span className="flex items-center gap-1">
                <Clock className="h-4 w-4" />
                {selectedPost.readTime} min read
              </span>
            </div>
            
            <div className="prose max-w-none mb-8">
              <div dangerouslySetInnerHTML={{ 
                __html: selectedPost.content
                  .replace(/\n/g, '<br>')
                  .replace(/## /g, '<h2>')
                  .replace(/### /g, '<h3>')
                  .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
              }} />
            </div>
            
            <div className="flex items-center justify-between border-t pt-6">
              <div className="flex items-center gap-4">
                <Button variant="outline" size="sm">
                  <Heart className="h-4 w-4 mr-2" />
                  {selectedPost.likes} Likes
                </Button>
                <Button variant="outline" size="sm">
                  <Share2 className="h-4 w-4 mr-2" />
                  Share
                </Button>
              </div>
              
              <div className="text-sm text-gray-600">
                {selectedPost.views} views
              </div>
            </div>
          </article>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="max-w-6xl mx-auto px-4 py-12">
          <h1 className="text-4xl font-bold mb-4">StayFinder Travel Blog</h1>
          <p className="text-gray-600 mb-8 max-w-2xl">
            Discover South Africa through the eyes of locals and travel experts. Get insider tips, 
            hidden gems, and practical advice for your next adventure.
          </p>
          
          <div className="max-w-2xl relative">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            <Input
              placeholder="Search blog posts..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-12 py-3"
            />
          </div>
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-3">
            <Tabs value={selectedCategory} onValueChange={setSelectedCategory} className="space-y-6">
              <TabsList className="grid w-full grid-cols-6">
                <TabsTrigger value="all">All</TabsTrigger>
                {categories.map(category => (
                  <TabsTrigger key={category.id} value={category.id}>
                    {category.name}
                  </TabsTrigger>
                ))}
              </TabsList>

              <TabsContent value={selectedCategory} className="space-y-6">
                {/* Featured Posts */}
                {selectedCategory === 'all' && (
                  <div>
                    <h2 className="text-2xl font-bold mb-6">Featured Posts</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                      {featuredPosts.map(post => (
                        <Card 
                          key={post.id}
                          className="cursor-pointer hover:shadow-lg transition-shadow"
                          onClick={() => setSelectedPost(post)}
                        >
                          <img 
                            src={post.featuredImage} 
                            alt={post.title}
                            className="w-full h-48 object-cover rounded-t-lg"
                          />
                          <CardContent className="p-6">
                            <Badge className="mb-3">Featured</Badge>
                            <h3 className="font-bold mb-2 line-clamp-2">{post.title}</h3>
                            <p className="text-gray-600 text-sm mb-4 line-clamp-3">{post.excerpt}</p>
                            <div className="flex items-center justify-between text-sm text-gray-500">
                              <span className="flex items-center gap-1">
                                <Clock className="h-4 w-4" />
                                {post.readTime} min
                              </span>
                              <span>{formatDate(post.publishedAt)}</span>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </div>
                )}

                {/* All Posts */}
                <div>
                  <h2 className="text-2xl font-bold mb-6">
                    {selectedCategory === 'all' ? 'Latest Posts' : categories.find(c => c.id === selectedCategory)?.name}
                  </h2>
                  <div className="space-y-6">
                    {filteredPosts.map(post => (
                      <Card 
                        key={post.id}
                        className="cursor-pointer hover:shadow-md transition-shadow"
                        onClick={() => setSelectedPost(post)}
                      >
                        <CardContent className="p-6">
                          <div className="flex gap-6">
                            <img 
                              src={post.featuredImage} 
                              alt={post.title}
                              className="w-32 h-24 object-cover rounded-lg flex-shrink-0"
                            />
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-2">
                                <Badge className={categories.find(c => c.id === post.category)?.color}>
                                  {categories.find(c => c.id === post.category)?.name}
                                </Badge>
                                {post.featured && <Badge>Featured</Badge>}
                              </div>
                              <h3 className="font-bold mb-2">{post.title}</h3>
                              <p className="text-gray-600 text-sm mb-3 line-clamp-2">{post.excerpt}</p>
                              <div className="flex items-center justify-between text-sm text-gray-500">
                                <div className="flex items-center gap-4">
                                  <span className="flex items-center gap-1">
                                    <User className="h-4 w-4" />
                                    {post.author.name}
                                  </span>
                                  <span className="flex items-center gap-1">
                                    <Clock className="h-4 w-4" />
                                    {post.readTime} min
                                  </span>
                                </div>
                                <span>{formatDate(post.publishedAt)}</span>
                              </div>
                            </div>
                            <ChevronRight className="h-5 w-5 text-gray-400 flex-shrink-0" />
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Trending Posts */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  Trending Posts
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {trendingPosts.map((post, index) => (
                  <div 
                    key={post.id}
                    className="flex gap-3 cursor-pointer hover:bg-gray-50 p-2 rounded"
                    onClick={() => setSelectedPost(post)}
                  >
                    <div className="text-2xl font-bold text-gray-300 flex-shrink-0">
                      {index + 1}
                    </div>
                    <div>
                      <h4 className="font-medium text-sm line-clamp-2">{post.title}</h4>
                      <p className="text-xs text-gray-500">{post.views} views</p>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Categories */}
            <Card>
              <CardHeader>
                <CardTitle>Categories</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                {categories.map(category => (
                  <button
                    key={category.id}
                    onClick={() => setSelectedCategory(category.id)}
                    className="w-full text-left p-2 rounded hover:bg-gray-50 flex items-center justify-between"
                  >
                    <span className="text-sm">{category.name}</span>
                    <Badge variant="secondary">{category.postCount}</Badge>
                  </button>
                ))}
              </CardContent>
            </Card>

            {/* Newsletter */}
            <Card>
              <CardHeader>
                <CardTitle>Stay Updated</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600 mb-4">
                  Get the latest travel tips and destination guides delivered to your inbox.
                </p>
                <div className="space-y-2">
                  <Input placeholder="Your email address" />
                  <Button className="w-full">Subscribe</Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};
