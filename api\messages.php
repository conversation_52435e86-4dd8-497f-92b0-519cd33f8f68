<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: http://localhost:8081');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Credentials: true');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once 'config.php';
require_once 'auth.php';

function getConversations($userId, $limit = 20, $offset = 0) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            SELECT 
                c.id,
                c.property_id,
                c.host_id,
                c.guest_id,
                c.booking_id,
                c.status,
                c.last_message_at,
                c.created_at,
                p.title as property_title,
                p.images as property_images,
                CASE 
                    WHEN c.host_id = ? THEN CONCAT(guest.first_name, ' ', guest.last_name)
                    ELSE CONCAT(host.first_name, ' ', host.last_name)
                E<PERSON> as other_user_name,
                CASE 
                    WHEN c.host_id = ? THEN guest.profile_picture
                    ELSE host.profile_picture
                END as other_user_avatar,
                CASE 
                    WHEN c.host_id = ? THEN guest.id
                    ELSE host.id
                END as other_user_id,
                (SELECT COUNT(*) FROM messages m WHERE m.conversation_id = c.id AND m.sender_id != ? AND m.is_read = FALSE) as unread_count,
                (SELECT m.content FROM messages m WHERE m.conversation_id = c.id ORDER BY m.created_at DESC LIMIT 1) as last_message,
                (SELECT m.message_type FROM messages m WHERE m.conversation_id = c.id ORDER BY m.created_at DESC LIMIT 1) as last_message_type
            FROM conversations c
            JOIN properties p ON c.property_id = p.id
            JOIN users host ON c.host_id = host.id
            JOIN users guest ON c.guest_id = guest.id
            WHERE c.host_id = ? OR c.guest_id = ?
            ORDER BY c.last_message_at DESC
            LIMIT ? OFFSET ?
        ");
        
        $stmt->execute([$userId, $userId, $userId, $userId, $userId, $userId, $limit, $offset]);
        $conversations = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Format conversations
        foreach ($conversations as &$conversation) {
            $conversation['property_images'] = $conversation['property_images'] ? json_decode($conversation['property_images'], true) : [];
            $conversation['unread_count'] = (int)$conversation['unread_count'];
            $conversation['is_host'] = $conversation['host_id'] === $userId;
        }
        
        return $conversations;
        
    } catch (Exception $e) {
        error_log("Error getting conversations: " . $e->getMessage());
        return [];
    }
}

function getMessages($conversationId, $userId, $limit = 50, $offset = 0) {
    global $pdo;
    
    try {
        // Verify user has access to this conversation
        $stmt = $pdo->prepare("
            SELECT id FROM conversations 
            WHERE id = ? AND (host_id = ? OR guest_id = ?)
        ");
        $stmt->execute([$conversationId, $userId, $userId]);
        if (!$stmt->fetch()) {
            throw new Exception("Access denied to this conversation");
        }
        
        // Get messages
        $stmt = $pdo->prepare("
            SELECT 
                m.id,
                m.conversation_id,
                m.sender_id,
                m.message_type,
                m.content,
                m.attachment_url,
                m.attachment_name,
                m.attachment_size,
                m.is_read,
                m.read_at,
                m.created_at,
                u.first_name,
                u.last_name,
                u.profile_picture
            FROM messages m
            JOIN users u ON m.sender_id = u.id
            WHERE m.conversation_id = ?
            ORDER BY m.created_at ASC
            LIMIT ? OFFSET ?
        ");
        
        $stmt->execute([$conversationId, $limit, $offset]);
        $messages = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Format messages
        foreach ($messages as &$message) {
            $message['sender_name'] = $message['first_name'] . ' ' . $message['last_name'];
            $message['is_own_message'] = $message['sender_id'] === $userId;
            unset($message['first_name'], $message['last_name']);
        }
        
        return $messages;
        
    } catch (Exception $e) {
        error_log("Error getting messages: " . $e->getMessage());
        throw $e;
    }
}

function createMessage($data, $userId) {
    global $pdo;
    
    try {
        // Validate required fields
        $required = ['conversation_id', 'content'];
        foreach ($required as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                throw new Exception("Missing required field: $field");
            }
        }
        
        // Verify user has access to this conversation
        $stmt = $pdo->prepare("
            SELECT id, host_id, guest_id FROM conversations 
            WHERE id = ? AND (host_id = ? OR guest_id = ?)
        ");
        $stmt->execute([$data['conversation_id'], $userId, $userId]);
        $conversation = $stmt->fetch();
        if (!$conversation) {
            throw new Exception("Access denied to this conversation");
        }
        
        // Create message
        $messageId = 'msg-' . uniqid();
        $messageType = $data['message_type'] ?? 'text';
        
        $stmt = $pdo->prepare("
            INSERT INTO messages (
                id, conversation_id, sender_id, message_type, content,
                attachment_url, attachment_name, attachment_size, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
        ");
        
        $stmt->execute([
            $messageId,
            $data['conversation_id'],
            $userId,
            $messageType,
            $data['content'],
            $data['attachment_url'] ?? null,
            $data['attachment_name'] ?? null,
            $data['attachment_size'] ?? null
        ]);
        
        // Update conversation last_message_at
        $stmt = $pdo->prepare("
            UPDATE conversations 
            SET last_message_at = NOW(), updated_at = NOW()
            WHERE id = ?
        ");
        $stmt->execute([$data['conversation_id']]);
        
        return $messageId;
        
    } catch (Exception $e) {
        error_log("Error creating message: " . $e->getMessage());
        throw $e;
    }
}

function markMessagesAsRead($conversationId, $userId) {
    global $pdo;
    
    try {
        // Verify user has access to this conversation
        $stmt = $pdo->prepare("
            SELECT id FROM conversations 
            WHERE id = ? AND (host_id = ? OR guest_id = ?)
        ");
        $stmt->execute([$conversationId, $userId, $userId]);
        if (!$stmt->fetch()) {
            throw new Exception("Access denied to this conversation");
        }
        
        // Mark messages as read (only messages not sent by the user)
        $stmt = $pdo->prepare("
            UPDATE messages 
            SET is_read = TRUE, read_at = NOW(), updated_at = NOW()
            WHERE conversation_id = ? AND sender_id != ? AND is_read = FALSE
        ");
        $stmt->execute([$conversationId, $userId]);
        
        return $stmt->rowCount();
        
    } catch (Exception $e) {
        error_log("Error marking messages as read: " . $e->getMessage());
        throw $e;
    }
}

function createConversation($data, $userId) {
    global $pdo;
    
    try {
        // Validate required fields
        $required = ['property_id', 'host_id', 'guest_id'];
        foreach ($required as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                throw new Exception("Missing required field: $field");
            }
        }
        
        // Verify user is either host or guest
        if ($userId !== $data['host_id'] && $userId !== $data['guest_id']) {
            throw new Exception("User must be either host or guest");
        }
        
        // Check if conversation already exists
        $stmt = $pdo->prepare("
            SELECT id FROM conversations 
            WHERE property_id = ? AND host_id = ? AND guest_id = ?
            AND (booking_id = ? OR (booking_id IS NULL AND ? IS NULL))
        ");
        $bookingId = $data['booking_id'] ?? null;
        $stmt->execute([$data['property_id'], $data['host_id'], $data['guest_id'], $bookingId, $bookingId]);
        $existing = $stmt->fetch();
        
        if ($existing) {
            return $existing['id'];
        }
        
        // Create new conversation
        $conversationId = 'conv-' . uniqid();
        $stmt = $pdo->prepare("
            INSERT INTO conversations (
                id, property_id, host_id, guest_id, booking_id,
                status, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, 'active', NOW(), NOW())
        ");
        
        $stmt->execute([
            $conversationId,
            $data['property_id'],
            $data['host_id'],
            $data['guest_id'],
            $bookingId
        ]);
        
        return $conversationId;
        
    } catch (Exception $e) {
        error_log("Error creating conversation: " . $e->getMessage());
        throw $e;
    }
}

// Handle the request
$method = $_SERVER['REQUEST_METHOD'];
$path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
$pathParts = explode('/', trim($path, '/'));

try {
    $user = getCurrentUser();
    if (!$user) {
        http_response_code(401);
        echo json_encode(['error' => 'Authentication required']);
        exit;
    }
    
    switch ($method) {
        case 'GET':
            if (isset($_GET['action'])) {
                switch ($_GET['action']) {
                    case 'conversations':
                        $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 20;
                        $offset = isset($_GET['offset']) ? (int)$_GET['offset'] : 0;
                        
                        $conversations = getConversations($user['id'], $limit, $offset);
                        
                        echo json_encode([
                            'success' => true,
                            'data' => $conversations
                        ]);
                        break;
                        
                    case 'messages':
                        $conversationId = $_GET['conversation_id'] ?? '';
                        if (empty($conversationId)) {
                            http_response_code(400);
                            echo json_encode(['error' => 'Conversation ID is required']);
                            exit;
                        }
                        
                        $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 50;
                        $offset = isset($_GET['offset']) ? (int)$_GET['offset'] : 0;
                        
                        $messages = getMessages($conversationId, $user['id'], $limit, $offset);
                        
                        echo json_encode([
                            'success' => true,
                            'data' => $messages
                        ]);
                        break;
                        
                    default:
                        http_response_code(400);
                        echo json_encode(['error' => 'Invalid action']);
                        break;
                }
            } else {
                http_response_code(400);
                echo json_encode(['error' => 'Action parameter is required']);
            }
            break;
            
        case 'POST':
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (isset($_GET['action'])) {
                switch ($_GET['action']) {
                    case 'create_conversation':
                        $conversationId = createConversation($input, $user['id']);
                        
                        echo json_encode([
                            'success' => true,
                            'message' => 'Conversation created successfully',
                            'conversation_id' => $conversationId
                        ]);
                        break;
                        
                    case 'send_message':
                        $messageId = createMessage($input, $user['id']);
                        
                        echo json_encode([
                            'success' => true,
                            'message' => 'Message sent successfully',
                            'message_id' => $messageId
                        ]);
                        break;
                        
                    default:
                        http_response_code(400);
                        echo json_encode(['error' => 'Invalid action']);
                        break;
                }
            } else {
                http_response_code(400);
                echo json_encode(['error' => 'Action parameter is required']);
            }
            break;
            
        case 'PUT':
            if (isset($_GET['action']) && $_GET['action'] === 'mark_read') {
                $conversationId = $_GET['conversation_id'] ?? '';
                if (empty($conversationId)) {
                    http_response_code(400);
                    echo json_encode(['error' => 'Conversation ID is required']);
                    exit;
                }
                
                $updatedCount = markMessagesAsRead($conversationId, $user['id']);
                
                echo json_encode([
                    'success' => true,
                    'message' => 'Messages marked as read',
                    'updated_count' => $updatedCount
                ]);
            } else {
                http_response_code(400);
                echo json_encode(['error' => 'Invalid action']);
            }
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            break;
    }
    
} catch (Exception $e) {
    error_log("Messages API error: " . $e->getMessage());
    http_response_code(400);
    echo json_encode(['error' => $e->getMessage()]);
}
?>
