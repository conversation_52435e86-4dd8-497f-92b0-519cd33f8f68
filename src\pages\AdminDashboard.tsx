import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line, PieChart, Pie, Cell, AreaChart, Area } from 'recharts';
import { TrendingUp, TrendingDown, Users, Home, DollarSign, Calendar, MapPin, Star, AlertTriangle, CheckCircle, Clock, Eye, Bug } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { AdminDebugControls } from '@/components/DebugSystem/DebugSystemProvider';
import { cn } from '@/lib/utils';

interface DashboardMetrics {
  revenue: {
    total: number;
    growth: number;
    commission: number;
    trend: 'up' | 'down' | 'stable';
  };
  bookings: {
    total: number;
    growth: number;
    pending: number;
    confirmed: number;
    cancelled: number;
  };
  properties: {
    total: number;
    active: number;
    pending: number;
    suspended: number;
  };
  users: {
    total: number;
    hosts: number;
    guests: number;
    newThisMonth: number;
  };
}

interface RevenueData {
  month: string;
  revenue: number;
  commission: number;
  bookings: number;
}

interface LocationData {
  location: string;
  properties: number;
  revenue: number;
  averagePrice: number;
}

interface UserBehaviorData {
  metric: string;
  value: number;
  change: number;
}

export const AdminDashboard: React.FC = () => {
  const [metrics, setMetrics] = useState<DashboardMetrics | null>(null);
  const [revenueData, setRevenueData] = useState<RevenueData[]>([]);
  const [locationData, setLocationData] = useState<LocationData[]>([]);
  const [userBehavior, setUserBehavior] = useState<UserBehaviorData[]>([]);
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d' | '1y'>('30d');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadDashboardData();
  }, [timeRange]);

  const loadDashboardData = async () => {
    setLoading(true);
    
    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Mock dashboard data
      const mockMetrics: DashboardMetrics = {
        revenue: {
          total: 2450000,
          growth: 15.3,
          commission: 245000,
          trend: 'up'
        },
        bookings: {
          total: 1250,
          growth: 8.7,
          pending: 45,
          confirmed: 1180,
          cancelled: 25
        },
        properties: {
          total: 850,
          active: 780,
          pending: 35,
          suspended: 35
        },
        users: {
          total: 15600,
          hosts: 850,
          guests: 14750,
          newThisMonth: 320
        }
      };

      const mockRevenueData: RevenueData[] = [
        { month: 'Jan', revenue: 180000, commission: 18000, bookings: 95 },
        { month: 'Feb', revenue: 220000, commission: 22000, bookings: 110 },
        { month: 'Mar', revenue: 280000, commission: 28000, bookings: 140 },
        { month: 'Apr', revenue: 320000, commission: 32000, bookings: 160 },
        { month: 'May', revenue: 290000, commission: 29000, bookings: 145 },
        { month: 'Jun', revenue: 350000, commission: 35000, bookings: 175 },
        { month: 'Jul', revenue: 420000, commission: 42000, bookings: 210 },
        { month: 'Aug', revenue: 380000, commission: 38000, bookings: 190 },
        { month: 'Sep', revenue: 340000, commission: 34000, bookings: 170 },
        { month: 'Oct', revenue: 310000, commission: 31000, bookings: 155 },
        { month: 'Nov', revenue: 280000, commission: 28000, bookings: 140 },
        { month: 'Dec', revenue: 450000, commission: 45000, bookings: 225 }
      ];

      const mockLocationData: LocationData[] = [
        { location: 'Cape Town', properties: 320, revenue: 980000, averagePrice: 850 },
        { location: 'Johannesburg', properties: 180, revenue: 540000, averagePrice: 720 },
        { location: 'Durban', properties: 150, revenue: 450000, averagePrice: 680 },
        { location: 'Stellenbosch', properties: 80, revenue: 280000, averagePrice: 950 },
        { location: 'Hermanus', properties: 60, revenue: 220000, averagePrice: 1200 },
        { location: 'Knysna', properties: 60, revenue: 200000, averagePrice: 900 }
      ];

      const mockUserBehavior: UserBehaviorData[] = [
        { metric: 'Average Session Duration', value: 8.5, change: 12.3 },
        { metric: 'Bounce Rate', value: 32.1, change: -5.2 },
        { metric: 'Pages per Session', value: 4.2, change: 8.7 },
        { metric: 'Conversion Rate', value: 3.8, change: 15.6 },
        { metric: 'Return Visitor Rate', value: 45.2, change: 7.1 }
      ];

      setMetrics(mockMetrics);
      setRevenueData(mockRevenueData);
      setLocationData(mockLocationData);
      setUserBehavior(mockUserBehavior);
    } catch (error) {
      console.error('Dashboard data loading error:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('en-ZA').format(num);
  };

  const getGrowthColor = (growth: number) => {
    if (growth > 0) return 'text-green-600';
    if (growth < 0) return 'text-red-600';
    return 'text-gray-600';
  };

  const getGrowthIcon = (growth: number) => {
    if (growth > 0) return <TrendingUp className="h-4 w-4" />;
    if (growth < 0) return <TrendingDown className="h-4 w-4" />;
    return null;
  };

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-gray-200 rounded w-64"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="h-32 bg-gray-200 rounded-lg"></div>
              ))}
            </div>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="h-80 bg-gray-200 rounded-lg"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!metrics) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 mx-auto mb-4 text-gray-400" />
          <p className="text-gray-600">Unable to load dashboard data</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
            <p className="text-gray-600">StayFinder Business Intelligence</p>
          </div>
          
          <div className="flex items-center gap-4">
            <Select value={timeRange} onValueChange={(value: any) => setTimeRange(value)}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7d">Last 7 days</SelectItem>
                <SelectItem value="30d">Last 30 days</SelectItem>
                <SelectItem value="90d">Last 90 days</SelectItem>
                <SelectItem value="1y">Last year</SelectItem>
              </SelectContent>
            </Select>
            
            <Button onClick={loadDashboardData}>
              Refresh Data
            </Button>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Revenue</p>
                  <p className="text-2xl font-bold">{formatCurrency(metrics.revenue.total)}</p>
                  <div className={cn("flex items-center gap-1 text-sm", getGrowthColor(metrics.revenue.growth))}>
                    {getGrowthIcon(metrics.revenue.growth)}
                    <span>{metrics.revenue.growth > 0 ? '+' : ''}{metrics.revenue.growth}%</span>
                  </div>
                </div>
                <div className="p-3 bg-green-100 rounded-full">
                  <DollarSign className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Bookings</p>
                  <p className="text-2xl font-bold">{formatNumber(metrics.bookings.total)}</p>
                  <div className={cn("flex items-center gap-1 text-sm", getGrowthColor(metrics.bookings.growth))}>
                    {getGrowthIcon(metrics.bookings.growth)}
                    <span>{metrics.bookings.growth > 0 ? '+' : ''}{metrics.bookings.growth}%</span>
                  </div>
                </div>
                <div className="p-3 bg-blue-100 rounded-full">
                  <Calendar className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Active Properties</p>
                  <p className="text-2xl font-bold">{formatNumber(metrics.properties.active)}</p>
                  <p className="text-sm text-gray-500">of {formatNumber(metrics.properties.total)} total</p>
                </div>
                <div className="p-3 bg-purple-100 rounded-full">
                  <Home className="h-6 w-6 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Users</p>
                  <p className="text-2xl font-bold">{formatNumber(metrics.users.total)}</p>
                  <p className="text-sm text-green-600">+{formatNumber(metrics.users.newThisMonth)} this month</p>
                </div>
                <div className="p-3 bg-orange-100 rounded-full">
                  <Users className="h-6 w-6 text-orange-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Charts and Analytics */}
        <Tabs defaultValue="revenue" className="space-y-6">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="revenue">Revenue Analytics</TabsTrigger>
            <TabsTrigger value="locations">Location Analysis</TabsTrigger>
            <TabsTrigger value="users">User Behavior</TabsTrigger>
            <TabsTrigger value="operations">Operations</TabsTrigger>
            <TabsTrigger value="debug">Debug System</TabsTrigger>
          </TabsList>

          <TabsContent value="revenue" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Revenue Trend</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <AreaChart data={revenueData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis />
                      <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                      <Area type="monotone" dataKey="revenue" stroke="#8884d8" fill="#8884d8" fillOpacity={0.3} />
                    </AreaChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Commission vs Revenue</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={revenueData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis />
                      <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                      <Bar dataKey="revenue" fill="#8884d8" />
                      <Bar dataKey="commission" fill="#82ca9d" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="locations" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Revenue by Location</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <PieChart>
                      <Pie
                        data={locationData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ location, percent }) => `${location} ${(percent * 100).toFixed(0)}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="revenue"
                      >
                        {locationData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                    </PieChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Location Performance</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {locationData.map((location, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div>
                          <div className="font-medium">{location.location}</div>
                          <div className="text-sm text-gray-600">
                            {location.properties} properties • Avg: {formatCurrency(location.averagePrice)}
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="font-semibold">{formatCurrency(location.revenue)}</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="users" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>User Behavior Metrics</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {userBehavior.map((metric, index) => (
                      <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                        <div>
                          <div className="font-medium">{metric.metric}</div>
                          <div className="text-2xl font-bold">{metric.value}%</div>
                        </div>
                        <div className={cn("flex items-center gap-1", getGrowthColor(metric.change))}>
                          {getGrowthIcon(metric.change)}
                          <span>{metric.change > 0 ? '+' : ''}{metric.change}%</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>User Distribution</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                      <div className="flex items-center gap-2">
                        <Users className="h-5 w-5 text-blue-600" />
                        <span className="font-medium">Total Guests</span>
                      </div>
                      <span className="font-bold">{formatNumber(metrics.users.guests)}</span>
                    </div>
                    
                    <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                      <div className="flex items-center gap-2">
                        <Home className="h-5 w-5 text-green-600" />
                        <span className="font-medium">Total Hosts</span>
                      </div>
                      <span className="font-bold">{formatNumber(metrics.users.hosts)}</span>
                    </div>
                    
                    <div className="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
                      <div className="flex items-center gap-2">
                        <TrendingUp className="h-5 w-5 text-purple-600" />
                        <span className="font-medium">New This Month</span>
                      </div>
                      <span className="font-bold">{formatNumber(metrics.users.newThisMonth)}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="operations" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Booking Status</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span>Confirmed</span>
                      </div>
                      <Badge className="bg-green-100 text-green-800">
                        {metrics.bookings.confirmed}
                      </Badge>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4 text-yellow-500" />
                        <span>Pending</span>
                      </div>
                      <Badge className="bg-yellow-100 text-yellow-800">
                        {metrics.bookings.pending}
                      </Badge>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <AlertTriangle className="h-4 w-4 text-red-500" />
                        <span>Cancelled</span>
                      </div>
                      <Badge className="bg-red-100 text-red-800">
                        {metrics.bookings.cancelled}
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Property Status</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span>Active</span>
                      </div>
                      <Badge className="bg-green-100 text-green-800">
                        {metrics.properties.active}
                      </Badge>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4 text-yellow-500" />
                        <span>Pending</span>
                      </div>
                      <Badge className="bg-yellow-100 text-yellow-800">
                        {metrics.properties.pending}
                      </Badge>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <AlertTriangle className="h-4 w-4 text-red-500" />
                        <span>Suspended</span>
                      </div>
                      <Badge className="bg-red-100 text-red-800">
                        {metrics.properties.suspended}
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Quick Actions</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <Button className="w-full justify-start" variant="outline">
                      <Eye className="h-4 w-4 mr-2" />
                      View All Properties
                    </Button>
                    <Button className="w-full justify-start" variant="outline">
                      <Users className="h-4 w-4 mr-2" />
                      Manage Users
                    </Button>
                    <Button className="w-full justify-start" variant="outline">
                      <Calendar className="h-4 w-4 mr-2" />
                      Review Bookings
                    </Button>
                    <Button className="w-full justify-start" variant="outline">
                      <DollarSign className="h-4 w-4 mr-2" />
                      Financial Reports
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="debug" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Bug className="h-5 w-5" />
                  Debug System Configuration
                </CardTitle>
              </CardHeader>
              <CardContent>
                <AdminDebugControls />
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};
