import React, { useState } from 'react';
import { MessageCircle, Send, ThumbsUp, Clock, User, Search, Filter, ChevronDown, ChevronUp } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from '@/components/ui/use-toast';
import { cn } from '@/lib/utils';

interface QAItem {
  id: string;
  question: string;
  answer?: string;
  askedBy: {
    id: string;
    name: string;
    avatar?: string;
  };
  answeredBy?: {
    id: string;
    name: string;
    avatar?: string;
    isHost: boolean;
  };
  askedAt: Date;
  answeredAt?: Date;
  helpful: number;
  category: 'general' | 'amenities' | 'location' | 'policies' | 'pricing' | 'accessibility';
  isPublic: boolean;
}

interface PropertyQASectionProps {
  propertyId: string;
  questions: QAItem[];
  canAskQuestion?: boolean;
  isHost?: boolean;
  onAskQuestion: (question: string, category: string) => void;
  onAnswerQuestion: (questionId: string, answer: string) => void;
  onMarkHelpful: (questionId: string) => void;
  className?: string;
}

export const PropertyQASection: React.FC<PropertyQASectionProps> = ({
  propertyId,
  questions,
  canAskQuestion = true,
  isHost = false,
  onAskQuestion,
  onAnswerQuestion,
  onMarkHelpful,
  className
}) => {
  const [showAskForm, setShowAskForm] = useState(false);
  const [newQuestion, setNewQuestion] = useState('');
  const [questionCategory, setQuestionCategory] = useState<string>('general');
  const [searchQuery, setSearchQuery] = useState('');
  const [filterCategory, setFilterCategory] = useState<string>('all');
  const [sortBy, setSortBy] = useState<'newest' | 'oldest' | 'helpful' | 'unanswered'>('newest');
  const [expandedAnswers, setExpandedAnswers] = useState<Set<string>>(new Set());
  const [answerInputs, setAnswerInputs] = useState<Record<string, string>>({});

  const categories = {
    general: 'General',
    amenities: 'Amenities',
    location: 'Location',
    policies: 'Policies',
    pricing: 'Pricing',
    accessibility: 'Accessibility'
  };

  const categoryColors = {
    general: 'bg-blue-100 text-blue-800',
    amenities: 'bg-green-100 text-green-800',
    location: 'bg-purple-100 text-purple-800',
    policies: 'bg-orange-100 text-orange-800',
    pricing: 'bg-red-100 text-red-800',
    accessibility: 'bg-indigo-100 text-indigo-800'
  };

  // Filter and sort questions
  const filteredQuestions = questions
    .filter(qa => {
      if (filterCategory !== 'all' && qa.category !== filterCategory) return false;
      if (searchQuery && !qa.question.toLowerCase().includes(searchQuery.toLowerCase()) && 
          (!qa.answer || !qa.answer.toLowerCase().includes(searchQuery.toLowerCase()))) return false;
      return true;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'newest': return new Date(b.askedAt).getTime() - new Date(a.askedAt).getTime();
        case 'oldest': return new Date(a.askedAt).getTime() - new Date(b.askedAt).getTime();
        case 'helpful': return b.helpful - a.helpful;
        case 'unanswered': return (a.answer ? 1 : 0) - (b.answer ? 1 : 0);
        default: return 0;
      }
    });

  const unansweredCount = questions.filter(qa => !qa.answer).length;
  const totalQuestions = questions.length;

  const handleAskQuestion = () => {
    if (newQuestion.trim().length < 10) {
      toast({
        title: "Question too short",
        description: "Please write at least 10 characters",
        variant: "destructive"
      });
      return;
    }

    onAskQuestion(newQuestion.trim(), questionCategory);
    setNewQuestion('');
    setShowAskForm(false);
    
    toast({
      title: "Question submitted",
      description: "Your question has been sent to the host",
    });
  };

  const handleAnswerQuestion = (questionId: string) => {
    const answer = answerInputs[questionId]?.trim();
    if (!answer || answer.length < 5) {
      toast({
        title: "Answer too short",
        description: "Please provide a meaningful answer",
        variant: "destructive"
      });
      return;
    }

    onAnswerQuestion(questionId, answer);
    setAnswerInputs(prev => ({ ...prev, [questionId]: '' }));
    setExpandedAnswers(prev => {
      const newSet = new Set(prev);
      newSet.delete(questionId);
      return newSet;
    });

    toast({
      title: "Answer posted",
      description: "Your answer has been published",
    });
  };

  const toggleAnswerForm = (questionId: string) => {
    setExpandedAnswers(prev => {
      const newSet = new Set(prev);
      if (newSet.has(questionId)) {
        newSet.delete(questionId);
      } else {
        newSet.add(questionId);
      }
      return newSet;
    });
  };

  const formatTimeAgo = (date: Date) => {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMinutes = Math.floor(diffMs / (1000 * 60));

    if (diffDays > 0) return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
    if (diffHours > 0) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    if (diffMinutes > 0) return `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''} ago`;
    return 'Just now';
  };

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <MessageCircle className="h-5 w-5" />
              <span>Questions & Answers</span>
              <Badge variant="secondary">{totalQuestions}</Badge>
              {unansweredCount > 0 && (
                <Badge variant="destructive">{unansweredCount} unanswered</Badge>
              )}
            </div>
            {canAskQuestion && (
              <Button onClick={() => setShowAskForm(true)}>
                Ask a Question
              </Button>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-600">
            Get answers from the host and other guests about this property. 
            Your questions help future guests make informed decisions.
          </p>
        </CardContent>
      </Card>

      {/* Ask Question Form */}
      {showAskForm && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              Ask a Question
              <Button variant="ghost" size="sm" onClick={() => setShowAskForm(false)}>
                ×
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">Category</label>
              <Select value={questionCategory} onValueChange={setQuestionCategory}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(categories).map(([value, label]) => (
                    <SelectItem key={value} value={value}>{label}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-2">Your Question</label>
              <Textarea
                value={newQuestion}
                onChange={(e) => setNewQuestion(e.target.value)}
                placeholder="What would you like to know about this property?"
                rows={3}
              />
              <p className="text-sm text-gray-500 mt-1">
                {newQuestion.length}/500 characters
              </p>
            </div>
            
            <div className="flex gap-3">
              <Button variant="outline" onClick={() => setShowAskForm(false)} className="flex-1">
                Cancel
              </Button>
              <Button onClick={handleAskQuestion} className="flex-1">
                <Send className="h-4 w-4 mr-2" />
                Ask Question
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Filters and Search */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search questions and answers..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-9"
                />
              </div>
            </div>
            <Select value={filterCategory} onValueChange={setFilterCategory}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All categories</SelectItem>
                {Object.entries(categories).map(([value, label]) => (
                  <SelectItem key={value} value={value}>{label}</SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={sortBy} onValueChange={(value) => setSortBy(value as any)}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="newest">Newest first</SelectItem>
                <SelectItem value="oldest">Oldest first</SelectItem>
                <SelectItem value="helpful">Most helpful</SelectItem>
                <SelectItem value="unanswered">Unanswered first</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Questions List */}
      <div className="space-y-4">
        {filteredQuestions.map((qa) => (
          <Card key={qa.id} className={cn(!qa.answer && "border-orange-200 bg-orange-50/30")}>
            <CardContent className="p-6">
              {/* Question */}
              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={qa.askedBy.avatar} />
                    <AvatarFallback>{qa.askedBy.name.charAt(0)}</AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <span className="font-medium">{qa.askedBy.name}</span>
                      <Badge 
                        variant="secondary" 
                        className={categoryColors[qa.category]}
                      >
                        {categories[qa.category]}
                      </Badge>
                      <span className="text-sm text-gray-500">
                        {formatTimeAgo(qa.askedAt)}
                      </span>
                    </div>
                    <p className="text-gray-800">{qa.question}</p>
                  </div>
                </div>

                {/* Answer */}
                {qa.answer ? (
                  <div className="ml-11 pl-4 border-l-2 border-gray-200">
                    <div className="flex items-start gap-3">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={qa.answeredBy?.avatar} />
                        <AvatarFallback>{qa.answeredBy?.name.charAt(0)}</AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <span className="font-medium">{qa.answeredBy?.name}</span>
                          {qa.answeredBy?.isHost && (
                            <Badge variant="default" className="bg-sea-green-600">Host</Badge>
                          )}
                          <span className="text-sm text-gray-500">
                            {qa.answeredAt && formatTimeAgo(qa.answeredAt)}
                          </span>
                        </div>
                        <p className="text-gray-800">{qa.answer}</p>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="ml-11 pl-4 border-l-2 border-orange-200">
                    <p className="text-orange-600 text-sm font-medium">
                      Waiting for host response...
                    </p>
                  </div>
                )}

                {/* Actions */}
                <div className="flex items-center justify-between ml-11">
                  <div className="flex items-center gap-4">
                    <button
                      onClick={() => onMarkHelpful(qa.id)}
                      className="flex items-center gap-1 text-sm text-gray-600 hover:text-gray-800"
                    >
                      <ThumbsUp className="h-4 w-4" />
                      Helpful ({qa.helpful})
                    </button>
                  </div>
                  
                  {isHost && !qa.answer && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => toggleAnswerForm(qa.id)}
                    >
                      {expandedAnswers.has(qa.id) ? (
                        <>
                          <ChevronUp className="h-4 w-4 mr-1" />
                          Cancel
                        </>
                      ) : (
                        <>
                          <MessageCircle className="h-4 w-4 mr-1" />
                          Answer
                        </>
                      )}
                    </Button>
                  )}
                </div>

                {/* Answer Form */}
                {isHost && expandedAnswers.has(qa.id) && (
                  <div className="ml-11 mt-4 space-y-3">
                    <Textarea
                      value={answerInputs[qa.id] || ''}
                      onChange={(e) => setAnswerInputs(prev => ({ ...prev, [qa.id]: e.target.value }))}
                      placeholder="Write your answer..."
                      rows={3}
                    />
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => toggleAnswerForm(qa.id)}
                      >
                        Cancel
                      </Button>
                      <Button
                        size="sm"
                        onClick={() => handleAnswerQuestion(qa.id)}
                        disabled={!answerInputs[qa.id]?.trim()}
                      >
                        Post Answer
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredQuestions.length === 0 && (
        <Card>
          <CardContent className="p-8 text-center">
            <MessageCircle className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <h3 className="text-lg font-medium mb-2">No questions found</h3>
            <p className="text-gray-600 mb-4">
              {searchQuery || filterCategory !== 'all' 
                ? 'Try adjusting your search or filters' 
                : 'Be the first to ask a question about this property!'}
            </p>
            {canAskQuestion && !showAskForm && (
              <Button onClick={() => setShowAskForm(true)}>
                Ask the First Question
              </Button>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
};
