import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, userEvent, waitFor, setupCommonMocks } from '@/test/utils/test-utils';
import { SearchBar } from '../SearchBar';

describe('SearchBar Component', () => {
  beforeEach(() => {
    setupCommonMocks();
  });

  it('renders all search inputs', () => {
    render(<SearchBar />);
    
    expect(screen.getByPlaceholderText(/where are you going/i)).toBeInTheDocument();
    expect(screen.getByText(/check-in/i)).toBeInTheDocument();
    expect(screen.getByText(/check-out/i)).toBeInTheDocument();
    expect(screen.getByText(/guests/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /search/i })).toBeInTheDocument();
  });

  it('handles location input', async () => {
    const user = userEvent.setup();
    render(<SearchBar />);
    
    const locationInput = screen.getByPlaceholderText(/where are you going/i);
    await user.type(locationInput, 'Cape Town');
    
    expect(locationInput).toHaveValue('Cape Town');
  });

  it('shows location suggestions', async () => {
    const user = userEvent.setup();
    render(<SearchBar />);
    
    const locationInput = screen.getByPlaceholderText(/where are you going/i);
    await user.type(locationInput, 'Cape');
    
    await waitFor(() => {
      expect(screen.getByText(/cape town/i)).toBeInTheDocument();
    });
  });

  it('handles date selection', async () => {
    const user = userEvent.setup();
    render(<SearchBar />);
    
    const checkInButton = screen.getByText(/check-in/i);
    await user.click(checkInButton);
    
    // Should open date picker
    expect(screen.getByRole('dialog') || screen.getByTestId('date-picker')).toBeInTheDocument();
  });

  it('handles guest count selection', async () => {
    const user = userEvent.setup();
    render(<SearchBar />);
    
    const guestsButton = screen.getByText(/guests/i);
    await user.click(guestsButton);
    
    // Should open guest selector
    expect(screen.getByRole('dialog') || screen.getByTestId('guest-selector')).toBeInTheDocument();
  });

  it('increments and decrements guest count', async () => {
    const user = userEvent.setup();
    render(<SearchBar />);
    
    const guestsButton = screen.getByText(/guests/i);
    await user.click(guestsButton);
    
    const incrementButton = screen.getByRole('button', { name: /\+/i });
    const decrementButton = screen.getByRole('button', { name: /-/i });
    
    await user.click(incrementButton);
    expect(screen.getByText('2 guests')).toBeInTheDocument();
    
    await user.click(decrementButton);
    expect(screen.getByText('1 guest')).toBeInTheDocument();
  });

  it('prevents guest count from going below 1', async () => {
    const user = userEvent.setup();
    render(<SearchBar />);
    
    const guestsButton = screen.getByText(/guests/i);
    await user.click(guestsButton);
    
    const decrementButton = screen.getByRole('button', { name: /-/i });
    
    // Try to decrement below 1
    await user.click(decrementButton);
    expect(screen.getByText('1 guest')).toBeInTheDocument();
  });

  it('handles search submission', async () => {
    const onSearch = vi.fn();
    const user = userEvent.setup();
    
    render(<SearchBar onSearch={onSearch} />);
    
    const locationInput = screen.getByPlaceholderText(/where are you going/i);
    await user.type(locationInput, 'Cape Town');
    
    const searchButton = screen.getByRole('button', { name: /search/i });
    await user.click(searchButton);
    
    expect(onSearch).toHaveBeenCalledWith(
      expect.objectContaining({
        location: 'Cape Town'
      })
    );
  });

  it('validates required fields before search', async () => {
    const onSearch = vi.fn();
    const user = userEvent.setup();
    
    render(<SearchBar onSearch={onSearch} />);
    
    const searchButton = screen.getByRole('button', { name: /search/i });
    await user.click(searchButton);
    
    // Should show validation error or not call onSearch
    expect(screen.getByText(/please enter a location/i) || onSearch).toBeDefined();
  });

  it('clears search form', async () => {
    const user = userEvent.setup();
    render(<SearchBar />);
    
    const locationInput = screen.getByPlaceholderText(/where are you going/i);
    await user.type(locationInput, 'Cape Town');
    
    const clearButton = screen.getByRole('button', { name: /clear/i });
    await user.click(clearButton);
    
    expect(locationInput).toHaveValue('');
  });

  it('handles keyboard navigation', async () => {
    const user = userEvent.setup();
    render(<SearchBar />);
    
    const locationInput = screen.getByPlaceholderText(/where are you going/i);
    await user.type(locationInput, 'Cape');
    
    // Should be able to navigate suggestions with arrow keys
    await user.keyboard('{ArrowDown}');
    await user.keyboard('{Enter}');
    
    expect(locationInput).toHaveValue('Cape Town');
  });

  it('shows recent searches', async () => {
    const user = userEvent.setup();
    render(<SearchBar />);
    
    const locationInput = screen.getByPlaceholderText(/where are you going/i);
    await user.click(locationInput);
    
    // Should show recent searches
    expect(screen.getByText(/recent searches/i) || screen.getByTestId('recent-searches')).toBeInTheDocument();
  });

  it('handles mobile responsive layout', () => {
    // Mock mobile viewport
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 375,
    });
    
    render(<SearchBar />);
    
    // Should render mobile-friendly layout
    expect(screen.getByRole('button', { name: /search/i })).toBeInTheDocument();
  });

  it('displays loading state during search', async () => {
    const user = userEvent.setup();
    render(<SearchBar loading />);
    
    const searchButton = screen.getByRole('button', { name: /search/i });
    expect(searchButton).toBeDisabled();
    expect(screen.getByText(/searching/i) || screen.getByTestId('loading-spinner')).toBeInTheDocument();
  });

  it('handles date range validation', async () => {
    const user = userEvent.setup();
    render(<SearchBar />);
    
    // Select check-out date before check-in date
    const checkInButton = screen.getByText(/check-in/i);
    await user.click(checkInButton);
    
    // Mock date selection
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    const today = new Date();
    
    // Should validate that check-out is after check-in
    expect(screen.getByText(/check-out must be after check-in/i) || true).toBeDefined();
  });

  it('preserves search state on component updates', () => {
    const { rerender } = render(<SearchBar />);
    
    const locationInput = screen.getByPlaceholderText(/where are you going/i);
    
    // Simulate user input
    userEvent.type(locationInput, 'Cape Town');
    
    // Re-render component
    rerender(<SearchBar />);
    
    // Should preserve the input value
    expect(locationInput).toHaveValue('Cape Town');
  });

  it('handles accessibility requirements', () => {
    render(<SearchBar />);
    
    const locationInput = screen.getByPlaceholderText(/where are you going/i);
    expect(locationInput).toHaveAttribute('aria-label', expect.stringContaining('location'));
    
    const searchButton = screen.getByRole('button', { name: /search/i });
    expect(searchButton).toHaveAttribute('type', 'submit');
  });

  it('supports custom placeholder text', () => {
    render(<SearchBar locationPlaceholder="Enter destination" />);
    
    expect(screen.getByPlaceholderText('Enter destination')).toBeInTheDocument();
  });

  it('handles error states', () => {
    render(<SearchBar error="Search failed. Please try again." />);
    
    expect(screen.getByText('Search failed. Please try again.')).toBeInTheDocument();
  });
});
