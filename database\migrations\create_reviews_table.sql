-- Create reviews table for the reviews and ratings system
CREATE TABLE IF NOT EXISTS reviews (
    id VARCHAR(36) PRIMARY KEY,
    property_id VARCHAR(36) NOT NULL,
    reviewer_id VARCHAR(36) NOT NULL,
    booking_id VARCHAR(36) NOT NULL UNIQUE,
    rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
    comment TEXT NOT NULL,
    response TEXT NULL,
    response_date TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (property_id) REFERENCES properties(id) ON DELETE CASCADE,
    FOREIGN KEY (reviewer_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (booking_id) REFERENCES bookings(id) ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_reviews_property_id ON reviews(property_id);
CREATE INDEX IF NOT EXISTS idx_reviews_reviewer_id ON reviews(reviewer_id);
CREATE INDEX IF NOT EXISTS idx_reviews_booking_id ON reviews(booking_id);
CREATE INDEX IF NOT EXISTS idx_reviews_rating ON reviews(rating);
CREATE INDEX IF NOT EXISTS idx_reviews_created_at ON reviews(created_at);
CREATE INDEX IF NOT EXISTS idx_reviews_property_rating ON reviews(property_id, rating);

-- Insert sample review data for testing
INSERT IGNORE INTO reviews (
    id, property_id, reviewer_id, booking_id, rating, comment, 
    response, response_date, created_at, updated_at
) VALUES 
(
    'review-1',
    'prop-1',
    (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1),
    'booking-1',
    5,
    'Absolutely amazing stay! The villa was exactly as described and the host was incredibly helpful. The ocean views were breathtaking and we loved the private pool. Would definitely stay here again!',
    'Thank you so much for the wonderful review! We\'re thrilled you enjoyed your stay and hope to welcome you back soon.',
    '2024-05-21 10:30:00',
    '2024-05-20 10:30:00',
    '2024-05-20 10:30:00'
),
(
    'review-2',
    'prop-1',
    (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1),
    'booking-2',
    4,
    'Great location right on the beach. The cottage was clean and comfortable. Only minor issue was the WiFi was a bit slow, but overall a fantastic experience.',
    NULL,
    NULL,
    '2024-04-25 14:15:00',
    '2024-04-25 14:15:00'
),
(
    'review-3',
    'prop-2',
    (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1),
    'booking-3',
    5,
    'Perfect getaway! The property exceeded our expectations. Beautiful views, excellent amenities, and the host was very responsive to our questions.',
    'We\'re so happy you had a perfect getaway! Thank you for taking care of our property.',
    '2024-03-12 09:00:00',
    '2024-03-10 16:45:00',
    '2024-03-10 16:45:00'
),
(
    'review-4',
    'prop-2',
    (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1),
    'booking-4',
    4,
    'Lovely property with stunning mountain views. The cabin was well-equipped and perfect for our family retreat. Would recommend to anyone looking for a peaceful getaway.',
    'Thank you for choosing our mountain retreat! We\'re delighted you enjoyed the peaceful setting.',
    '2024-02-15 11:20:00',
    '2024-02-10 13:30:00',
    '2024-02-10 13:30:00'
),
(
    'review-5',
    'prop-3',
    (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1),
    'booking-5',
    5,
    'Exceptional stay! The apartment was modern, clean, and perfectly located. Easy access to the beach and local attractions. The host provided excellent recommendations.',
    NULL,
    NULL,
    '2024-01-20 16:45:00',
    '2024-01-20 16:45:00'
);

-- Create sample bookings if they don't exist (needed for foreign key constraints)
INSERT IGNORE INTO bookings (
    id, property_id, guest_id, check_in_date, check_out_date, 
    total_amount, booking_status, payment_status, guest_count, created_at
) VALUES 
(
    'booking-1',
    'prop-1',
    (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1),
    '2024-05-15',
    '2024-05-20',
    2500.00,
    'completed',
    'paid',
    4,
    '2024-05-10 10:00:00'
),
(
    'booking-2',
    'prop-1',
    (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1),
    '2024-04-20',
    '2024-04-25',
    1800.00,
    'completed',
    'paid',
    2,
    '2024-04-15 14:00:00'
),
(
    'booking-3',
    'prop-2',
    (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1),
    '2024-03-05',
    '2024-03-10',
    3200.00,
    'completed',
    'paid',
    6,
    '2024-03-01 16:00:00'
),
(
    'booking-4',
    'prop-2',
    (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1),
    '2024-02-05',
    '2024-02-10',
    2800.00,
    'completed',
    'paid',
    4,
    '2024-02-01 13:00:00'
),
(
    'booking-5',
    'prop-3',
    (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1),
    '2024-01-15',
    '2024-01-20',
    1400.00,
    'completed',
    'paid',
    2,
    '2024-01-10 16:00:00'
);

-- Update properties table with calculated review statistics
UPDATE properties p SET 
    average_rating = (
        SELECT COALESCE(AVG(r.rating), 0) 
        FROM reviews r 
        WHERE r.property_id = p.id
    ),
    review_count = (
        SELECT COUNT(*) 
        FROM reviews r 
        WHERE r.property_id = p.id
    )
WHERE EXISTS (
    SELECT 1 FROM reviews r WHERE r.property_id = p.id
);
