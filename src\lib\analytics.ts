// Google Analytics 4 Integration for StayFinder

declare global {
  interface Window {
    gtag: (...args: any[]) => void;
    dataLayer: any[];
  }
}

// Configuration
const GA_MEASUREMENT_ID = import.meta.env.VITE_GA_MEASUREMENT_ID || 'G-XXXXXXXXXX';
const DEBUG_MODE = import.meta.env.DEV;

// Initialize Google Analytics
export const initializeAnalytics = () => {
  if (typeof window === 'undefined') return;

  // Create gtag function
  window.dataLayer = window.dataLayer || [];
  window.gtag = function gtag() {
    window.dataLayer.push(arguments);
  };

  // Configure GA
  window.gtag('js', new Date());
  window.gtag('config', GA_MEASUREMENT_ID, {
    debug_mode: DEBUG_MODE,
    send_page_view: false, // We'll handle page views manually
    anonymize_ip: true,
    allow_google_signals: true,
    allow_ad_personalization_signals: false,
  });

  // Load GA script
  const script = document.createElement('script');
  script.async = true;
  script.src = `https://www.googletagmanager.com/gtag/js?id=${GA_MEASUREMENT_ID}`;
  document.head.appendChild(script);

  if (DEBUG_MODE) {
    console.log('Google Analytics initialized with ID:', GA_MEASUREMENT_ID);
  }
};

// Page view tracking
export const trackPageView = (path: string, title?: string) => {
  if (typeof window === 'undefined' || !window.gtag) return;

  window.gtag('config', GA_MEASUREMENT_ID, {
    page_path: path,
    page_title: title || document.title,
  });

  if (DEBUG_MODE) {
    console.log('Page view tracked:', { path, title });
  }
};

// Event tracking
export const trackEvent = (
  action: string,
  category: string,
  label?: string,
  value?: number,
  customParameters?: Record<string, any>
) => {
  if (typeof window === 'undefined' || !window.gtag) return;

  const eventData: any = {
    event_category: category,
    event_label: label,
    value: value,
    ...customParameters,
  };

  window.gtag('event', action, eventData);

  if (DEBUG_MODE) {
    console.log('Event tracked:', { action, category, label, value, customParameters });
  }
};

// Enhanced ecommerce tracking
export const trackPurchase = (transactionData: {
  transaction_id: string;
  value: number;
  currency: string;
  items: Array<{
    item_id: string;
    item_name: string;
    category: string;
    quantity: number;
    price: number;
  }>;
  coupon?: string;
  shipping?: number;
  tax?: number;
}) => {
  if (typeof window === 'undefined' || !window.gtag) return;

  window.gtag('event', 'purchase', {
    transaction_id: transactionData.transaction_id,
    value: transactionData.value,
    currency: transactionData.currency,
    items: transactionData.items,
    coupon: transactionData.coupon,
    shipping: transactionData.shipping,
    tax: transactionData.tax,
  });

  if (DEBUG_MODE) {
    console.log('Purchase tracked:', transactionData);
  }
};

// Property-specific tracking functions
export const PropertyAnalytics = {
  // Property search
  trackSearch: (searchParams: {
    location?: string;
    checkIn?: string;
    checkOut?: string;
    guests?: number;
    propertyType?: string;
    priceRange?: { min: number; max: number };
  }) => {
    trackEvent('search', 'property', 'property_search', undefined, {
      search_location: searchParams.location,
      search_check_in: searchParams.checkIn,
      search_check_out: searchParams.checkOut,
      search_guests: searchParams.guests,
      search_property_type: searchParams.propertyType,
      search_price_min: searchParams.priceRange?.min,
      search_price_max: searchParams.priceRange?.max,
    });
  },

  // Property view
  trackPropertyView: (propertyData: {
    property_id: string;
    property_name: string;
    property_type: string;
    location: string;
    price: number;
    rating?: number;
  }) => {
    trackEvent('view_item', 'property', propertyData.property_name, propertyData.price, {
      item_id: propertyData.property_id,
      item_name: propertyData.property_name,
      item_category: propertyData.property_type,
      item_location: propertyData.location,
      price: propertyData.price,
      rating: propertyData.rating,
    });
  },

  // Add to wishlist
  trackAddToWishlist: (propertyData: {
    property_id: string;
    property_name: string;
    property_type: string;
    price: number;
  }) => {
    trackEvent('add_to_wishlist', 'property', propertyData.property_name, propertyData.price, {
      item_id: propertyData.property_id,
      item_name: propertyData.property_name,
      item_category: propertyData.property_type,
      price: propertyData.price,
    });
  },

  // Remove from wishlist
  trackRemoveFromWishlist: (propertyData: {
    property_id: string;
    property_name: string;
  }) => {
    trackEvent('remove_from_wishlist', 'property', propertyData.property_name, undefined, {
      item_id: propertyData.property_id,
      item_name: propertyData.property_name,
    });
  },

  // Property comparison
  trackPropertyComparison: (propertyIds: string[]) => {
    trackEvent('compare_properties', 'property', 'property_comparison', propertyIds.length, {
      property_ids: propertyIds.join(','),
      comparison_count: propertyIds.length,
    });
  },
};

// Booking-specific tracking
export const BookingAnalytics = {
  // Begin checkout
  trackBeginCheckout: (bookingData: {
    property_id: string;
    property_name: string;
    check_in: string;
    check_out: string;
    guests: number;
    nights: number;
    total_price: number;
  }) => {
    trackEvent('begin_checkout', 'booking', bookingData.property_name, bookingData.total_price, {
      property_id: bookingData.property_id,
      property_name: bookingData.property_name,
      check_in: bookingData.check_in,
      check_out: bookingData.check_out,
      guests: bookingData.guests,
      nights: bookingData.nights,
      value: bookingData.total_price,
      currency: 'ZAR',
    });
  },

  // Complete booking
  trackBookingComplete: (bookingData: {
    booking_id: string;
    property_id: string;
    property_name: string;
    property_type: string;
    check_in: string;
    check_out: string;
    guests: number;
    nights: number;
    base_price: number;
    fees: number;
    total_price: number;
    payment_method?: string;
  }) => {
    trackPurchase({
      transaction_id: bookingData.booking_id,
      value: bookingData.total_price,
      currency: 'ZAR',
      items: [
        {
          item_id: bookingData.property_id,
          item_name: bookingData.property_name,
          category: bookingData.property_type,
          quantity: bookingData.nights,
          price: bookingData.base_price,
        },
      ],
      shipping: bookingData.fees,
    });

    // Additional booking completion event
    trackEvent('booking_complete', 'booking', bookingData.property_name, bookingData.total_price, {
      booking_id: bookingData.booking_id,
      property_id: bookingData.property_id,
      property_type: bookingData.property_type,
      check_in: bookingData.check_in,
      check_out: bookingData.check_out,
      guests: bookingData.guests,
      nights: bookingData.nights,
      payment_method: bookingData.payment_method,
    });
  },

  // Booking cancellation
  trackBookingCancellation: (bookingData: {
    booking_id: string;
    property_name: string;
    cancellation_reason?: string;
    refund_amount?: number;
  }) => {
    trackEvent('booking_cancellation', 'booking', bookingData.property_name, bookingData.refund_amount, {
      booking_id: bookingData.booking_id,
      cancellation_reason: bookingData.cancellation_reason,
      refund_amount: bookingData.refund_amount,
    });
  },
};

// User interaction tracking
export const UserAnalytics = {
  // User registration
  trackRegistration: (method: string = 'email') => {
    trackEvent('sign_up', 'user', method, undefined, {
      method: method,
    });
  },

  // User login
  trackLogin: (method: string = 'email') => {
    trackEvent('login', 'user', method, undefined, {
      method: method,
    });
  },

  // Profile completion
  trackProfileCompletion: (completionPercentage: number) => {
    trackEvent('profile_completion', 'user', 'profile_update', completionPercentage, {
      completion_percentage: completionPercentage,
    });
  },

  // Contact/Support
  trackContactSupport: (method: string, topic?: string) => {
    trackEvent('contact_support', 'user', method, undefined, {
      contact_method: method,
      support_topic: topic,
    });
  },
};

// Performance tracking
export const PerformanceAnalytics = {
  // Page load time
  trackPageLoadTime: (pageName: string, loadTime: number) => {
    trackEvent('page_load_time', 'performance', pageName, loadTime, {
      page_name: pageName,
      load_time_ms: loadTime,
    });
  },

  // Search performance
  trackSearchPerformance: (searchTime: number, resultCount: number) => {
    trackEvent('search_performance', 'performance', 'search_results', searchTime, {
      search_time_ms: searchTime,
      result_count: resultCount,
    });
  },

  // Error tracking
  trackError: (errorType: string, errorMessage: string, page?: string) => {
    trackEvent('exception', 'error', errorType, undefined, {
      description: errorMessage,
      fatal: false,
      page: page || window.location.pathname,
    });
  },
};

// Utility function to set user properties
export const setUserProperties = (properties: Record<string, any>) => {
  if (typeof window === 'undefined' || !window.gtag) return;

  window.gtag('config', GA_MEASUREMENT_ID, {
    custom_map: properties,
  });

  if (DEBUG_MODE) {
    console.log('User properties set:', properties);
  }
};

// Utility function to set user ID
export const setUserId = (userId: string) => {
  if (typeof window === 'undefined' || !window.gtag) return;

  window.gtag('config', GA_MEASUREMENT_ID, {
    user_id: userId,
  });

  if (DEBUG_MODE) {
    console.log('User ID set:', userId);
  }
};
