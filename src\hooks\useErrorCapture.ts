import { useState, useEffect, useCallback, useRef } from 'react';

export interface CapturedError {
  id: string;
  type: 'error' | 'warning' | 'info' | 'debug';
  message: string;
  stack?: string;
  timestamp: Date;
  source?: string;
  line?: number;
  column?: number;
  filename?: string;
  userAgent?: string;
  url?: string;
  componentStack?: string;
  dismissed?: boolean;
}

interface ErrorCaptureOptions {
  maxErrors?: number;
  captureConsole?: boolean;
  captureUnhandled?: boolean;
  captureReactErrors?: boolean;
}

export const useErrorCapture = (options: ErrorCaptureOptions = {}) => {
  const {
    maxErrors = 100,
    captureConsole = true,
    captureUnhandled = true,
    captureReactErrors = true
  } = options;

  const [errors, setErrors] = useState<CapturedError[]>([]);
  const [isCapturing, setIsCapturing] = useState(false);
  const originalConsole = useRef<{
    error: typeof console.error;
    warn: typeof console.warn;
    log: typeof console.log;
    info: typeof console.info;
    debug: typeof console.debug;
  }>();

  // Generate unique ID for errors
  const generateErrorId = useCallback(() => {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }, []);

  // Add error to the list
  const addError = useCallback((error: Omit<CapturedError, 'id'>) => {
    const newError: CapturedError = {
      ...error,
      id: generateErrorId(),
      timestamp: new Date(),
      userAgent: navigator.userAgent,
      url: window.location.href
    };

    setErrors(prev => {
      const updated = [newError, ...prev];
      return updated.slice(0, maxErrors);
    });
  }, [generateErrorId, maxErrors]);

  // Console method override
  const createConsoleOverride = useCallback((
    originalMethod: (...args: any[]) => void,
    type: CapturedError['type']
  ) => {
    return (...args: any[]) => {
      // Call original method first
      originalMethod.apply(console, args);

      if (isCapturing) {
        const message = args.map(arg => {
          if (typeof arg === 'object') {
            try {
              return JSON.stringify(arg, null, 2);
            } catch {
              return String(arg);
            }
          }
          return String(arg);
        }).join(' ');

        const stack = new Error().stack;
        
        addError({
          type,
          message,
          stack,
          source: 'console'
        });
      }
    };
  }, [isCapturing, addError]);

  // Global error handler
  const handleGlobalError = useCallback((event: ErrorEvent) => {
    if (!isCapturing) return;

    addError({
      type: 'error',
      message: event.message,
      stack: event.error?.stack,
      source: 'global',
      line: event.lineno,
      column: event.colno,
      filename: event.filename
    });
  }, [isCapturing, addError]);

  // Unhandled promise rejection handler
  const handleUnhandledRejection = useCallback((event: PromiseRejectionEvent) => {
    if (!isCapturing) return;

    const reason = event.reason;
    let message = 'Unhandled Promise Rejection';
    let stack: string | undefined;

    if (reason instanceof Error) {
      message = reason.message;
      stack = reason.stack;
    } else if (typeof reason === 'string') {
      message = reason;
    } else {
      try {
        message = JSON.stringify(reason);
      } catch {
        message = String(reason);
      }
    }

    addError({
      type: 'error',
      message,
      stack,
      source: 'promise'
    });
  }, [isCapturing, addError]);

  // React error boundary handler
  const handleReactError = useCallback((error: Error, errorInfo: { componentStack: string }) => {
    if (!isCapturing) return;

    addError({
      type: 'error',
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      source: 'react'
    });
  }, [isCapturing, addError]);

  // Start capturing errors
  const startCapturing = useCallback(() => {
    if (isCapturing) return;

    // Store original console methods
    originalConsole.current = {
      error: console.error,
      warn: console.warn,
      log: console.log,
      info: console.info,
      debug: console.debug
    };

    if (captureConsole) {
      // Override console methods
      console.error = createConsoleOverride(originalConsole.current.error, 'error');
      console.warn = createConsoleOverride(originalConsole.current.warn, 'warning');
      console.log = createConsoleOverride(originalConsole.current.log, 'info');
      console.info = createConsoleOverride(originalConsole.current.info, 'info');
      console.debug = createConsoleOverride(originalConsole.current.debug, 'debug');
    }

    if (captureUnhandled) {
      // Add global error listeners
      window.addEventListener('error', handleGlobalError);
      window.addEventListener('unhandledrejection', handleUnhandledRejection);
    }

    setIsCapturing(true);
  }, [
    isCapturing,
    captureConsole,
    captureUnhandled,
    createConsoleOverride,
    handleGlobalError,
    handleUnhandledRejection
  ]);

  // Stop capturing errors
  const stopCapturing = useCallback(() => {
    if (!isCapturing) return;

    // Restore original console methods
    if (originalConsole.current && captureConsole) {
      console.error = originalConsole.current.error;
      console.warn = originalConsole.current.warn;
      console.log = originalConsole.current.log;
      console.info = originalConsole.current.info;
      console.debug = originalConsole.current.debug;
    }

    if (captureUnhandled) {
      // Remove global error listeners
      window.removeEventListener('error', handleGlobalError);
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    }

    setIsCapturing(false);
  }, [isCapturing, captureConsole, captureUnhandled, handleGlobalError, handleUnhandledRejection]);

  // Clear all errors
  const clearErrors = useCallback(() => {
    setErrors([]);
  }, []);

  // Dismiss specific error
  const dismissError = useCallback((errorId: string) => {
    setErrors(prev => prev.filter(error => error.id !== errorId));
  }, []);

  // Get errors by type
  const getErrorsByType = useCallback((type: CapturedError['type']) => {
    return errors.filter(error => error.type === type);
  }, [errors]);

  // Get error counts
  const getErrorCounts = useCallback(() => {
    return {
      total: errors.length,
      error: errors.filter(e => e.type === 'error').length,
      warning: errors.filter(e => e.type === 'warning').length,
      info: errors.filter(e => e.type === 'info').length,
      debug: errors.filter(e => e.type === 'debug').length
    };
  }, [errors]);

  // Export errors as JSON
  const exportErrors = useCallback(() => {
    const exportData = {
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      totalErrors: errors.length,
      errors: errors.map(error => ({
        ...error,
        timestamp: error.timestamp.toISOString()
      }))
    };

    return JSON.stringify(exportData, null, 2);
  }, [errors]);

  // Copy error to clipboard
  const copyError = useCallback(async (error: CapturedError) => {
    const errorText = `
Error ID: ${error.id}
Type: ${error.type.toUpperCase()}
Message: ${error.message}
Timestamp: ${error.timestamp.toISOString()}
Source: ${error.source || 'unknown'}
${error.filename ? `File: ${error.filename}:${error.line}:${error.column}` : ''}
${error.stack ? `Stack Trace:\n${error.stack}` : ''}
${error.componentStack ? `Component Stack:\n${error.componentStack}` : ''}
    `.trim();

    try {
      await navigator.clipboard.writeText(errorText);
      return true;
    } catch {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = errorText;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      return true;
    }
  }, []);

  // Copy all errors to clipboard
  const copyAllErrors = useCallback(async () => {
    const allErrorsText = errors.map(error => {
      return `
[${error.timestamp.toISOString()}] ${error.type.toUpperCase()}: ${error.message}
${error.stack ? `Stack: ${error.stack}` : ''}
${error.componentStack ? `Component: ${error.componentStack}` : ''}
---
      `.trim();
    }).join('\n\n');

    try {
      await navigator.clipboard.writeText(allErrorsText);
      return true;
    } catch {
      return false;
    }
  }, [errors]);

  // Auto-start capturing on mount
  useEffect(() => {
    startCapturing();
    return () => {
      stopCapturing();
    };
  }, [startCapturing, stopCapturing]);

  return {
    errors,
    isCapturing,
    startCapturing,
    stopCapturing,
    clearErrors,
    dismissError,
    getErrorsByType,
    getErrorCounts,
    exportErrors,
    copyError,
    copyAllErrors,
    addError: handleReactError // For React Error Boundary
  };
};
