import React, { createContext, useContext, useState, useCallback } from 'react';
import reviewsService from '../services/reviews';
import { useAuth } from './AuthContext';

interface Review {
  id: string;
  propertyId: string;
  bookingId: string;
  guestId: string;
  rating: number;
  comment: string;
  createdAt: string;
  updatedAt: string;
  guest?: {
    firstName: string;
    lastName: string;
    email: string;
  };
  property?: {
    title: string;
    location: string;
  };
  booking?: {
    checkInDate: string;
    checkOutDate: string;
  };
}

interface ReviewsContextType {
  // Reviews state
  reviews: Review[];
  propertyReviews: Review[];
  userReviews: Review[];
  
  // Loading states
  loading: boolean;
  submitting: boolean;
  
  // Error state
  error: string | null;
  
  // Review actions
  createReview: (reviewData: {
    propertyId: string;
    bookingId: string;
    rating: number;
    comment: string;
  }) => Promise<Review>;
  
  updateReview: (reviewId: string, reviewData: {
    rating: number;
    comment: string;
  }) => Promise<Review>;
  
  deleteReview: (reviewId: string) => Promise<void>;
  
  // Review retrieval
  getPropertyReviews: (propertyId: string) => Promise<void>;
  getUserReviews: () => Promise<void>;
  getReviewById: (reviewId: string) => Promise<Review>;
  
  // Review statistics
  getPropertyStats: (propertyId: string) => Promise<any>;
  
  // Utility functions
  clearError: () => void;
  clearReviews: () => void;
  canUserReview: (propertyId: string, bookingId: string) => Promise<boolean>;
}

const ReviewsContext = createContext<ReviewsContextType | undefined>(undefined);

export const useReviews = (): ReviewsContextType => {
  const context = useContext(ReviewsContext);
  if (!context) {
    throw new Error('useReviews must be used within a ReviewsProvider');
  }
  return context;
};

interface ReviewsProviderProps {
  children: React.ReactNode;
}

export const ReviewsProvider: React.FC<ReviewsProviderProps> = ({ children }) => {
  const [reviews, setReviews] = useState<Review[]>([]);
  const [propertyReviews, setPropertyReviews] = useState<Review[]>([]);
  const [userReviews, setUserReviews] = useState<Review[]>([]);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const { isAuthenticated } = useAuth();

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const clearReviews = useCallback(() => {
    setReviews([]);
    setPropertyReviews([]);
    setUserReviews([]);
  }, []);

  const createReview = useCallback(async (reviewData: {
    propertyId: string;
    bookingId: string;
    rating: number;
    comment: string;
  }): Promise<Review> => {
    if (!isAuthenticated) {
      throw new Error('You must be logged in to write a review');
    }

    setSubmitting(true);
    setError(null);

    try {
      const response = await reviewsService.createReview(reviewData);
      const newReview = reviewsService.transformReview(response.review);
      
      // Update local state
      setReviews(prev => [newReview, ...prev]);
      setUserReviews(prev => [newReview, ...prev]);
      
      // Update property reviews if we're viewing the same property
      if (propertyReviews.length > 0 && propertyReviews[0]?.propertyId === reviewData.propertyId) {
        setPropertyReviews(prev => [newReview, ...prev]);
      }
      
      return newReview;
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to create review';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setSubmitting(false);
    }
  }, [isAuthenticated, propertyReviews]);

  const updateReview = useCallback(async (reviewId: string, reviewData: {
    rating: number;
    comment: string;
  }): Promise<Review> => {
    if (!isAuthenticated) {
      throw new Error('You must be logged in to update a review');
    }

    setSubmitting(true);
    setError(null);

    try {
      const response = await reviewsService.updateReview(reviewId, reviewData);
      const updatedReview = reviewsService.transformReview(response.review);
      
      // Update local state
      const updateReviewInArray = (reviews: Review[]) =>
        reviews.map(review => review.id === reviewId ? updatedReview : review);
      
      setReviews(updateReviewInArray);
      setUserReviews(updateReviewInArray);
      setPropertyReviews(updateReviewInArray);
      
      return updatedReview;
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to update review';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setSubmitting(false);
    }
  }, [isAuthenticated]);

  const deleteReview = useCallback(async (reviewId: string): Promise<void> => {
    if (!isAuthenticated) {
      throw new Error('You must be logged in to delete a review');
    }

    setSubmitting(true);
    setError(null);

    try {
      await reviewsService.deleteReview(reviewId);
      
      // Remove from local state
      const removeReviewFromArray = (reviews: Review[]) =>
        reviews.filter(review => review.id !== reviewId);
      
      setReviews(removeReviewFromArray);
      setUserReviews(removeReviewFromArray);
      setPropertyReviews(removeReviewFromArray);
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to delete review';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setSubmitting(false);
    }
  }, [isAuthenticated]);

  const getPropertyReviews = useCallback(async (propertyId: string) => {
    setLoading(true);
    setError(null);

    try {
      const response = await reviewsService.getPropertyReviews(propertyId);
      const transformedReviews = response.reviews.map((review: any) => 
        reviewsService.transformReview(review)
      );
      setPropertyReviews(transformedReviews);
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to fetch property reviews';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  const getUserReviews = useCallback(async () => {
    if (!isAuthenticated) return;

    setLoading(true);
    setError(null);

    try {
      const response = await reviewsService.getUserReviews();
      const transformedReviews = response.reviews.map((review: any) => 
        reviewsService.transformReview(review)
      );
      setUserReviews(transformedReviews);
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to fetch user reviews';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [isAuthenticated]);

  const getReviewById = useCallback(async (reviewId: string): Promise<Review> => {
    setLoading(true);
    setError(null);

    try {
      const response = await reviewsService.getReviewById(reviewId);
      return reviewsService.transformReview(response.review);
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to fetch review';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  const getPropertyStats = useCallback(async (propertyId: string) => {
    setLoading(true);
    setError(null);

    try {
      const response = await reviewsService.getPropertyStats(propertyId);
      return response.stats;
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to fetch property statistics';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  const canUserReview = useCallback(async (propertyId: string, bookingId: string): Promise<boolean> => {
    if (!isAuthenticated) return false;

    try {
      const response = await reviewsService.canUserReview(propertyId, bookingId);
      return response.canReview;
    } catch (err: any) {
      return false;
    }
  }, [isAuthenticated]);

  const value: ReviewsContextType = {
    // State
    reviews,
    propertyReviews,
    userReviews,
    loading,
    submitting,
    error,
    
    // Actions
    createReview,
    updateReview,
    deleteReview,
    getPropertyReviews,
    getUserReviews,
    getReviewById,
    getPropertyStats,
    
    // Utilities
    clearError,
    clearReviews,
    canUserReview
  };

  return (
    <ReviewsContext.Provider value={value}>
      {children}
    </ReviewsContext.Provider>
  );
};
