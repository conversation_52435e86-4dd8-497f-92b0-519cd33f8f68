// Properties API Service
const API_BASE_URL = 'http://localhost:3001/api';

class PropertiesService {
  async searchProperties(filters = {}) {
    try {
      const queryParams = new URLSearchParams();
      
      // Add filters to query parameters
      if (filters.location) queryParams.append('location', filters.location);
      if (filters.minPrice) queryParams.append('minPrice', filters.minPrice.toString());
      if (filters.maxPrice) queryParams.append('maxPrice', filters.maxPrice.toString());
      if (filters.guests) queryParams.append('guests', filters.guests.toString());
      if (filters.propertyType) queryParams.append('propertyType', filters.propertyType);
      if (filters.page) queryParams.append('page', filters.page.toString());
      if (filters.limit) queryParams.append('limit', filters.limit.toString());

      const url = `${API_BASE_URL}/properties${queryParams.toString() ? '?' + queryParams.toString() : ''}`;
      
      const response = await fetch(url);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error searching properties:', error);
      throw error;
    }
  }

  async getPropertyById(id) {
    try {
      const response = await fetch(`${API_BASE_URL}/properties/${id}`);
      
      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('Property not found');
        }
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error fetching property:', error);
      throw error;
    }
  }

  async checkAvailability(propertyId, startDate, endDate) {
    try {
      const queryParams = new URLSearchParams({
        startDate: startDate,
        endDate: endDate
      });

      const response = await fetch(`${API_BASE_URL}/properties/${propertyId}/availability?${queryParams.toString()}`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error checking availability:', error);
      throw error;
    }
  }

  // Transform backend property data to frontend format
  transformProperty(backendProperty) {
    return {
      id: backendProperty.id,
      title: backendProperty.title,
      location: backendProperty.location,
      price: backendProperty.pricePerNight,
      images: backendProperty.images?.map(img => img.url) || [backendProperty.primaryImage].filter(Boolean),
      description: backendProperty.description,
      available: true, // We'll determine this based on availability check
      propertyType: backendProperty.propertyType,
      maxGuests: backendProperty.maxGuests,
      bedrooms: backendProperty.bedrooms,
      bathrooms: backendProperty.bathrooms,
      amenities: backendProperty.amenities || [],
      averageRating: backendProperty.averageRating,
      reviewCount: backendProperty.reviewCount,
      owner: backendProperty.owner,
      coordinates: backendProperty.coordinates,
      checkInTime: backendProperty.checkInTime,
      checkOutTime: backendProperty.checkOutTime,
      cleaningFee: backendProperty.cleaningFee,
      createdAt: backendProperty.createdAt
    };
  }

  // Transform search results
  transformSearchResults(backendData) {
    return {
      properties: backendData.properties.map(property => this.transformProperty(property)),
      pagination: backendData.pagination
    };
  }

  // Get all properties (for featured properties, etc.)
  async getAllProperties(page = 1, limit = 12) {
    return this.searchProperties({ page, limit });
  }

  // Search by location (for Hero search)
  async searchByLocation(location, page = 1, limit = 12) {
    try {
      const result = await this.searchProperties({ location, page, limit });

      // If we get a successful response, transform it
      if (result && result.properties) {
        return this.transformSearchResults(result);
      }

      // If no results or API fails, return mock data for development
      return this.getMockPropertiesForLocation(location, page, limit);
    } catch (error) {
      // Return mock data as fallback
      return this.getMockPropertiesForLocation(location, page, limit);
    }
  }

  // Mock data for development/fallback
  getMockPropertiesForLocation(location, page = 1, limit = 12) {
    const mockProperties = [
      {
        id: '1',
        title: 'Luxury Apartment in Cape Town City Center',
        location: 'Cape Town, Western Cape',
        price: 1200,
        images: ['https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=800&h=600&fit=crop'],
        description: 'Beautiful modern apartment with stunning city views',
        available: true,
        propertyType: 'Apartment',
        maxGuests: 4,
        bedrooms: 2,
        bathrooms: 2,
        amenities: ['WiFi', 'Kitchen', 'Air Conditioning', 'Parking'],
        averageRating: 4.8,
        reviewCount: 24,
        owner: { firstName: 'John', lastName: 'Smith' }
      },
      {
        id: '2',
        title: 'Cozy Cottage near V&A Waterfront',
        location: 'Cape Town, Western Cape',
        price: 950,
        images: ['https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=800&h=600&fit=crop'],
        description: 'Charming cottage just minutes from the waterfront',
        available: true,
        propertyType: 'House',
        maxGuests: 6,
        bedrooms: 3,
        bathrooms: 2,
        amenities: ['WiFi', 'Kitchen', 'Garden', 'Parking'],
        averageRating: 4.6,
        reviewCount: 18,
        owner: { firstName: 'Sarah', lastName: 'Johnson' }
      },
      {
        id: '3',
        title: 'Modern Studio in Stellenbosch',
        location: 'Stellenbosch, Western Cape',
        price: 800,
        images: ['https://images.unsplash.com/photo-1502672260266-1c1ef2d93688?w=800&h=600&fit=crop'],
        description: 'Perfect for wine lovers, close to vineyards',
        available: true,
        propertyType: 'Studio',
        maxGuests: 2,
        bedrooms: 1,
        bathrooms: 1,
        amenities: ['WiFi', 'Kitchen', 'Balcony'],
        averageRating: 4.7,
        reviewCount: 12,
        owner: { firstName: 'Mike', lastName: 'Wilson' }
      }
    ];

    // Filter properties based on location search
    let filteredProperties;

    // If no specific location search, return all properties
    if (!location || location.trim() === '') {
      filteredProperties = mockProperties;
    } else {
      // Filter based on location or title
      filteredProperties = mockProperties.filter(property => {
        const searchTerm = location.toLowerCase();
        const locationMatch = property.location.toLowerCase().includes(searchTerm);
        const titleMatch = property.title.toLowerCase().includes(searchTerm);

        // Also check for partial matches like "cape" matching "Cape Town"
        const locationWords = property.location.toLowerCase().split(/[\s,]+/);
        const titleWords = property.title.toLowerCase().split(/[\s,]+/);
        const wordMatch = locationWords.some(word => word.includes(searchTerm)) ||
                         titleWords.some(word => word.includes(searchTerm));

        return locationMatch || titleMatch || wordMatch;
      });
    }

    return {
      properties: filteredProperties,
      pagination: {
        page,
        limit,
        total: filteredProperties.length,
        totalPages: Math.ceil(filteredProperties.length / limit)
      }
    };
  }

  // Get properties by price range
  async getPropertiesByPriceRange(minPrice, maxPrice, page = 1, limit = 12) {
    return this.searchProperties({ minPrice, maxPrice, page, limit });
  }

  // Get properties for specific guest count
  async getPropertiesForGuests(guests, page = 1, limit = 12) {
    return this.searchProperties({ guests, page, limit });
  }

  // Get properties by type
  async getPropertiesByType(propertyType, page = 1, limit = 12) {
    return this.searchProperties({ propertyType, page, limit });
  }
}

export default new PropertiesService();
