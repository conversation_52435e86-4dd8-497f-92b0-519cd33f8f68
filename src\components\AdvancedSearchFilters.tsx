import React, { useState } from 'react';
import { Filter, X, MapPin, Calendar, Users, DollarSign, Home, Wifi, Car, Waves, ChefHat, Dumbbell, Heart, Star } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';

interface SearchFilters {
  location: string;
  checkIn: string;
  checkOut: string;
  guests: number;
  priceRange: [number, number];
  propertyTypes: string[];
  amenities: string[];
  rating: number;
  instantBook: boolean;
  superhost: boolean;
  accessibility: string[];
  houseRules: string[];
}

interface AdvancedSearchFiltersProps {
  filters: SearchFilters;
  onFiltersChange: (filters: SearchFilters) => void;
  onApplyFilters: () => void;
  onClearFilters: () => void;
  isOpen: boolean;
  onClose: () => void;
  resultCount?: number;
}

export const AdvancedSearchFilters: React.FC<AdvancedSearchFiltersProps> = ({
  filters,
  onFiltersChange,
  onApplyFilters,
  onClearFilters,
  isOpen,
  onClose,
  resultCount = 0
}) => {
  const [activeTab, setActiveTab] = useState('price');

  const propertyTypes = [
    { id: 'apartment', label: 'Apartment', icon: Home },
    { id: 'house', label: 'House', icon: Home },
    { id: 'villa', label: 'Villa', icon: Home },
    { id: 'guesthouse', label: 'Guesthouse', icon: Home },
    { id: 'hotel', label: 'Hotel', icon: Home },
    { id: 'lodge', label: 'Lodge', icon: Home },
    { id: 'cottage', label: 'Cottage', icon: Home },
    { id: 'studio', label: 'Studio', icon: Home }
  ];

  const amenities = [
    { id: 'wifi', label: 'WiFi', icon: Wifi },
    { id: 'parking', label: 'Parking', icon: Car },
    { id: 'pool', label: 'Pool', icon: Waves },
    { id: 'kitchen', label: 'Kitchen', icon: ChefHat },
    { id: 'gym', label: 'Gym', icon: Dumbbell },
    { id: 'aircon', label: 'Air Conditioning', icon: Heart },
    { id: 'heating', label: 'Heating', icon: Heart },
    { id: 'tv', label: 'TV', icon: Heart },
    { id: 'washer', label: 'Washer', icon: Heart },
    { id: 'dryer', label: 'Dryer', icon: Heart },
    { id: 'balcony', label: 'Balcony', icon: Heart },
    { id: 'garden', label: 'Garden', icon: Heart }
  ];

  const accessibilityOptions = [
    'Wheelchair accessible',
    'Step-free access',
    'Wide doorways',
    'Accessible bathroom',
    'Accessible parking'
  ];

  const houseRulesOptions = [
    'Pets allowed',
    'Smoking allowed',
    'Events allowed',
    'Children welcome',
    'Suitable for infants'
  ];

  const tabs = [
    { id: 'price', label: 'Price & Type', icon: DollarSign },
    { id: 'amenities', label: 'Amenities', icon: Wifi },
    { id: 'accessibility', label: 'Accessibility', icon: Heart },
    { id: 'rules', label: 'House Rules', icon: Home }
  ];

  const updateFilter = (key: keyof SearchFilters, value: any) => {
    onFiltersChange({ ...filters, [key]: value });
  };

  const toggleArrayFilter = (key: keyof SearchFilters, value: string) => {
    const currentArray = filters[key] as string[];
    const newArray = currentArray.includes(value)
      ? currentArray.filter(item => item !== value)
      : [...currentArray, value];
    updateFilter(key, newArray);
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (filters.priceRange[0] > 0 || filters.priceRange[1] < 10000) count++;
    if (filters.propertyTypes.length > 0) count++;
    if (filters.amenities.length > 0) count++;
    if (filters.rating > 0) count++;
    if (filters.instantBook) count++;
    if (filters.superhost) count++;
    if (filters.accessibility.length > 0) count++;
    if (filters.houseRules.length > 0) count++;
    return count;
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-4xl max-h-[90vh] overflow-hidden">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Advanced Filters
            {getActiveFiltersCount() > 0 && (
              <Badge variant="secondary" className="ml-2">
                {getActiveFiltersCount()} active
              </Badge>
            )}
          </CardTitle>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </CardHeader>

        <CardContent className="p-0">
          {/* Filter Tabs */}
          <div className="flex border-b bg-gray-50">
            {tabs.map((tab) => {
              const IconComponent = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={cn(
                    "flex-1 flex items-center justify-center gap-2 px-4 py-3 text-sm font-medium transition-colors",
                    activeTab === tab.id
                      ? "bg-white text-sea-green-600 border-b-2 border-sea-green-600"
                      : "text-gray-600 hover:text-gray-900 hover:bg-gray-100"
                  )}
                >
                  <IconComponent className="h-4 w-4" />
                  {tab.label}
                </button>
              );
            })}
          </div>

          {/* Filter Content */}
          <div className="p-6 max-h-[60vh] overflow-y-auto">
            {activeTab === 'price' && (
              <div className="space-y-6">
                {/* Price Range */}
                <div>
                  <Label className="text-base font-semibold mb-4 block">Price Range (per night)</Label>
                  <div className="px-3">
                    <Slider
                      value={filters.priceRange}
                      onValueChange={(value) => updateFilter('priceRange', value)}
                      max={10000}
                      min={0}
                      step={50}
                      className="mb-4"
                    />
                    <div className="flex justify-between text-sm text-gray-600">
                      <span>R{filters.priceRange[0].toLocaleString()}</span>
                      <span>R{filters.priceRange[1].toLocaleString()}+</span>
                    </div>
                  </div>
                </div>

                <Separator />

                {/* Property Types */}
                <div>
                  <Label className="text-base font-semibold mb-4 block">Property Type</Label>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                    {propertyTypes.map((type) => {
                      const IconComponent = type.icon;
                      const isSelected = filters.propertyTypes.includes(type.id);
                      return (
                        <button
                          key={type.id}
                          onClick={() => toggleArrayFilter('propertyTypes', type.id)}
                          className={cn(
                            "flex flex-col items-center gap-2 p-4 rounded-lg border-2 transition-all",
                            isSelected
                              ? "border-sea-green-500 bg-sea-green-50 text-sea-green-700"
                              : "border-gray-200 hover:border-gray-300 hover:bg-gray-50"
                          )}
                        >
                          <IconComponent className="h-6 w-6" />
                          <span className="text-sm font-medium">{type.label}</span>
                        </button>
                      );
                    })}
                  </div>
                </div>

                <Separator />

                {/* Rating & Special Features */}
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <Label className="text-base font-semibold mb-4 block">Minimum Rating</Label>
                    <Select value={filters.rating.toString()} onValueChange={(value) => updateFilter('rating', parseInt(value))}>
                      <SelectTrigger>
                        <SelectValue placeholder="Any rating" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="0">Any rating</SelectItem>
                        <SelectItem value="3">3+ stars</SelectItem>
                        <SelectItem value="4">4+ stars</SelectItem>
                        <SelectItem value="5">5 stars only</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="instantBook"
                        checked={filters.instantBook}
                        onCheckedChange={(checked) => updateFilter('instantBook', checked)}
                      />
                      <Label htmlFor="instantBook" className="text-sm font-medium">
                        Instant Book
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="superhost"
                        checked={filters.superhost}
                        onCheckedChange={(checked) => updateFilter('superhost', checked)}
                      />
                      <Label htmlFor="superhost" className="text-sm font-medium">
                        Superhost
                      </Label>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'amenities' && (
              <div>
                <Label className="text-base font-semibold mb-4 block">Amenities</Label>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  {amenities.map((amenity) => {
                    const IconComponent = amenity.icon;
                    const isSelected = filters.amenities.includes(amenity.id);
                    return (
                      <button
                        key={amenity.id}
                        onClick={() => toggleArrayFilter('amenities', amenity.id)}
                        className={cn(
                          "flex items-center gap-3 p-3 rounded-lg border-2 transition-all text-left",
                          isSelected
                            ? "border-sea-green-500 bg-sea-green-50 text-sea-green-700"
                            : "border-gray-200 hover:border-gray-300 hover:bg-gray-50"
                        )}
                      >
                        <IconComponent className="h-5 w-5" />
                        <span className="text-sm font-medium">{amenity.label}</span>
                      </button>
                    );
                  })}
                </div>
              </div>
            )}

            {activeTab === 'accessibility' && (
              <div>
                <Label className="text-base font-semibold mb-4 block">Accessibility Features</Label>
                <div className="space-y-3">
                  {accessibilityOptions.map((option) => {
                    const isSelected = filters.accessibility.includes(option);
                    return (
                      <div key={option} className="flex items-center space-x-3">
                        <Checkbox
                          id={option}
                          checked={isSelected}
                          onCheckedChange={() => toggleArrayFilter('accessibility', option)}
                        />
                        <Label htmlFor={option} className="text-sm font-medium">
                          {option}
                        </Label>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}

            {activeTab === 'rules' && (
              <div>
                <Label className="text-base font-semibold mb-4 block">House Rules</Label>
                <div className="space-y-3">
                  {houseRulesOptions.map((rule) => {
                    const isSelected = filters.houseRules.includes(rule);
                    return (
                      <div key={rule} className="flex items-center space-x-3">
                        <Checkbox
                          id={rule}
                          checked={isSelected}
                          onCheckedChange={() => toggleArrayFilter('houseRules', rule)}
                        />
                        <Label htmlFor={rule} className="text-sm font-medium">
                          {rule}
                        </Label>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}
          </div>

          {/* Footer Actions */}
          <div className="border-t bg-gray-50 p-4 flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button variant="ghost" onClick={onClearFilters}>
                Clear all
              </Button>
              {resultCount > 0 && (
                <span className="text-sm text-gray-600">
                  {resultCount.toLocaleString()} properties found
                </span>
              )}
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button onClick={onApplyFilters} className="bg-sea-green-600 hover:bg-sea-green-700">
                Show Results
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
