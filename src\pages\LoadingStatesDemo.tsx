import React, { useState } from 'react';
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  LoadingSpinner, 
  StayFinderLoader, 
  ButtonLoader, 
  InlineLoader,
  ContentLoadingOverlay 
} from '@/components/ui/loading-spinner';
import { 
  PropertyCardSkeleton,
  PropertyGridSkeleton,
  SearchResultsSkeleton,
  DashboardSkeleton,
  ReviewCardSkeleton,
  BookingCardSkeleton,
  TableSkeleton,
  FormSkeleton,
  PageLoadingSkeleton
} from '@/components/ui/loading-skeletons';
import {
  PageTransition,
  SlideIn,
  ScaleIn,
  BounceIn,
  HoverAnimation,
  StaggeredAnimation
} from '@/components/ui/page-transitions';

export const LoadingStatesDemo = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [showSkeletons, setShowSkeletons] = useState(false);

  const simulateLoading = () => {
    setIsLoading(true);
    setTimeout(() => setIsLoading(false), 3000);
  };

  const toggleSkeletons = () => {
    setShowSkeletons(!showSkeletons);
  };

  return (
    <PageTransition>
      <div className="container mx-auto px-4 py-8 space-y-8">
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold text-gray-900">Loading States & Animations Demo</h1>
          <p className="text-lg text-gray-600">Showcase of all loading components and animations</p>
        </div>

        {/* Controls */}
        <div className="flex justify-center space-x-4">
          <Button onClick={simulateLoading} disabled={isLoading}>
            {isLoading ? <ButtonLoader className="mr-2" /> : null}
            {isLoading ? 'Loading...' : 'Test Loading States'}
          </Button>
          <Button onClick={toggleSkeletons} variant="outline">
            {showSkeletons ? 'Hide' : 'Show'} Skeleton Loaders
          </Button>
        </div>

        {/* Loading Spinners Section */}
        <SlideIn direction="up" delay={100}>
          <Card>
            <CardHeader>
              <CardTitle>Loading Spinners</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Basic Spinners */}
              <div>
                <h3 className="text-lg font-semibold mb-4">Basic Spinners</h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                  <div className="text-center space-y-2">
                    <LoadingSpinner size="sm" />
                    <p className="text-sm text-gray-600">Small</p>
                  </div>
                  <div className="text-center space-y-2">
                    <LoadingSpinner size="md" />
                    <p className="text-sm text-gray-600">Medium</p>
                  </div>
                  <div className="text-center space-y-2">
                    <LoadingSpinner size="lg" />
                    <p className="text-sm text-gray-600">Large</p>
                  </div>
                  <div className="text-center space-y-2">
                    <LoadingSpinner size="xl" />
                    <p className="text-sm text-gray-600">Extra Large</p>
                  </div>
                </div>
              </div>

              {/* Spinner Variants */}
              <div>
                <h3 className="text-lg font-semibold mb-4">Spinner Types</h3>
                <div className="grid grid-cols-2 md:grid-cols-5 gap-6">
                  <div className="text-center space-y-2">
                    <LoadingSpinner type="spinner" variant="primary" />
                    <p className="text-sm text-gray-600">Spinner</p>
                  </div>
                  <div className="text-center space-y-2">
                    <LoadingSpinner type="dots" variant="primary" />
                    <p className="text-sm text-gray-600">Dots</p>
                  </div>
                  <div className="text-center space-y-2">
                    <LoadingSpinner type="pulse" variant="primary" />
                    <p className="text-sm text-gray-600">Pulse</p>
                  </div>
                  <div className="text-center space-y-2">
                    <LoadingSpinner type="bounce" variant="primary" />
                    <p className="text-sm text-gray-600">Bounce</p>
                  </div>
                  <div className="text-center space-y-2">
                    <LoadingSpinner type="icon" variant="primary" />
                    <p className="text-sm text-gray-600">Icon</p>
                  </div>
                </div>
              </div>

              {/* Branded Loader */}
              <div>
                <h3 className="text-lg font-semibold mb-4">StayFinder Branded Loader</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="text-center space-y-2">
                    <StayFinderLoader size="sm" text="Loading properties..." />
                  </div>
                  <div className="text-center space-y-2">
                    <StayFinderLoader size="md" text="Searching locations..." />
                  </div>
                  <div className="text-center space-y-2">
                    <StayFinderLoader size="lg" text="Processing booking..." />
                  </div>
                </div>
              </div>

              {/* Inline Loaders */}
              <div>
                <h3 className="text-lg font-semibold mb-4">Inline Loaders</h3>
                <div className="space-y-4">
                  <InlineLoader text="Searching properties" size="sm" />
                  <InlineLoader text="Loading user data" size="md" />
                  <InlineLoader text="Processing payment" size="lg" />
                </div>
              </div>
            </CardContent>
          </Card>
        </SlideIn>

        {/* Skeleton Loaders Section */}
        <SlideIn direction="up" delay={200}>
          <Card>
            <CardHeader>
              <CardTitle>Skeleton Loaders</CardTitle>
            </CardHeader>
            <CardContent className="space-y-8">
              {showSkeletons ? (
                <>
                  <div>
                    <h3 className="text-lg font-semibold mb-4">Property Card Skeleton</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <PropertyCardSkeleton />
                      <PropertyCardSkeleton />
                      <PropertyCardSkeleton />
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-semibold mb-4">Dashboard Skeleton</h3>
                    <DashboardSkeleton />
                  </div>

                  <div>
                    <h3 className="text-lg font-semibold mb-4">Form Skeleton</h3>
                    <div className="max-w-md">
                      <FormSkeleton />
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-semibold mb-4">Table Skeleton</h3>
                    <TableSkeleton rows={5} columns={4} />
                  </div>
                </>
              ) : (
                <div className="text-center py-12">
                  <p className="text-gray-500">Click "Show Skeleton Loaders" to see skeleton components</p>
                </div>
              )}
            </CardContent>
          </Card>
        </SlideIn>

        {/* Animations Section */}
        <SlideIn direction="up" delay={300}>
          <Card>
            <CardHeader>
              <CardTitle>Page Animations</CardTitle>
            </CardHeader>
            <CardContent className="space-y-8">
              <div>
                <h3 className="text-lg font-semibold mb-4">Entrance Animations</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  <SlideIn direction="left" delay={100}>
                    <Card className="p-4 text-center bg-sea-green-50">
                      <p className="font-medium">Slide In Left</p>
                    </Card>
                  </SlideIn>
                  <SlideIn direction="right" delay={200}>
                    <Card className="p-4 text-center bg-ocean-blue-50">
                      <p className="font-medium">Slide In Right</p>
                    </Card>
                  </SlideIn>
                  <ScaleIn delay={300}>
                    <Card className="p-4 text-center bg-sunset-orange-50">
                      <p className="font-medium">Scale In</p>
                    </Card>
                  </ScaleIn>
                  <BounceIn delay={400}>
                    <Card className="p-4 text-center bg-purple-50">
                      <p className="font-medium">Bounce In</p>
                    </Card>
                  </BounceIn>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-semibold mb-4">Hover Animations</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  <HoverAnimation type="lift">
                    <Card className="p-4 text-center cursor-pointer">
                      <p className="font-medium">Hover Lift</p>
                    </Card>
                  </HoverAnimation>
                  <HoverAnimation type="scale">
                    <Card className="p-4 text-center cursor-pointer">
                      <p className="font-medium">Hover Scale</p>
                    </Card>
                  </HoverAnimation>
                  <HoverAnimation type="glow">
                    <Card className="p-4 text-center cursor-pointer">
                      <p className="font-medium">Hover Glow</p>
                    </Card>
                  </HoverAnimation>
                  <HoverAnimation type="rotate">
                    <Card className="p-4 text-center cursor-pointer">
                      <p className="font-medium">Hover Rotate</p>
                    </Card>
                  </HoverAnimation>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-semibold mb-4">Staggered Animation</h3>
                <StaggeredAnimation delay={150}>
                  {Array.from({ length: 6 }).map((_, index) => (
                    <Card key={index} className="p-4 mb-4">
                      <p className="font-medium">Staggered Item {index + 1}</p>
                      <p className="text-sm text-gray-600">This item animates in sequence</p>
                    </Card>
                  ))}
                </StaggeredAnimation>
              </div>
            </CardContent>
          </Card>
        </SlideIn>

        {/* Content Loading Overlay Demo */}
        <SlideIn direction="up" delay={400}>
          <Card>
            <CardHeader>
              <CardTitle>Content Loading Overlay</CardTitle>
            </CardHeader>
            <CardContent>
              <ContentLoadingOverlay isLoading={isLoading} text="Loading content...">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <Card className="p-6">
                    <h3 className="text-lg font-semibold mb-2">Sample Content</h3>
                    <p className="text-gray-600">This content will be overlaid with a loading state when the loading button is clicked.</p>
                  </Card>
                  <Card className="p-6">
                    <h3 className="text-lg font-semibold mb-2">More Content</h3>
                    <p className="text-gray-600">The overlay provides a smooth loading experience while maintaining the layout.</p>
                  </Card>
                </div>
              </ContentLoadingOverlay>
            </CardContent>
          </Card>
        </SlideIn>
      </div>
    </PageTransition>
  );
};
