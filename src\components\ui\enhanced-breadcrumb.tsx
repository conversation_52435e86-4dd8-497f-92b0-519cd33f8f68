import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { ChevronRight, Home } from 'lucide-react';
import { cn } from '@/lib/utils';
import { SlideIn } from './page-transitions';

interface BreadcrumbItem {
  label: string;
  href?: string;
  icon?: React.ReactNode;
  current?: boolean;
}

interface EnhancedBreadcrumbProps {
  items?: BreadcrumbItem[];
  separator?: React.ReactNode;
  showHome?: boolean;
  className?: string;
  maxItems?: number;
}

export const EnhancedBreadcrumb: React.FC<EnhancedBreadcrumbProps> = ({
  items,
  separator = <ChevronRight className="h-4 w-4 text-gray-400" />,
  showHome = true,
  className,
  maxItems = 5
}) => {
  const location = useLocation();
  
  // Auto-generate breadcrumbs from current path if items not provided
  const generateBreadcrumbs = (): BreadcrumbItem[] => {
    const pathSegments = location.pathname.split('/').filter(Boolean);
    const breadcrumbs: BreadcrumbItem[] = [];

    if (showHome) {
      breadcrumbs.push({
        label: 'Home',
        href: '/',
        icon: <Home className="h-4 w-4" />
      });
    }

    let currentPath = '';
    pathSegments.forEach((segment, index) => {
      currentPath += `/${segment}`;
      const isLast = index === pathSegments.length - 1;
      
      // Convert segment to readable label
      const label = segment
        .split('-')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');

      breadcrumbs.push({
        label,
        href: isLast ? undefined : currentPath,
        current: isLast
      });
    });

    return breadcrumbs;
  };

  const breadcrumbItems = items || generateBreadcrumbs();
  
  // Truncate items if they exceed maxItems
  const displayItems = breadcrumbItems.length > maxItems
    ? [
        ...breadcrumbItems.slice(0, 1),
        { label: '...', href: undefined },
        ...breadcrumbItems.slice(-maxItems + 2)
      ]
    : breadcrumbItems;

  if (displayItems.length <= 1) {
    return null; // Don't show breadcrumbs for home page or single item
  }

  return (
    <SlideIn direction="right" delay={100}>
      <nav 
        aria-label="Breadcrumb" 
        className={cn('flex items-center space-x-1 text-sm', className)}
      >
        <ol className="flex items-center space-x-1">
          {displayItems.map((item, index) => (
            <li key={index} className="flex items-center">
              {index > 0 && (
                <span className="mx-2 flex-shrink-0">
                  {separator}
                </span>
              )}
              
              {item.label === '...' ? (
                <span className="text-gray-500 px-2">...</span>
              ) : item.current || !item.href ? (
                <span 
                  className={cn(
                    'flex items-center space-x-1 font-medium',
                    item.current 
                      ? 'text-sea-green-600' 
                      : 'text-gray-500'
                  )}
                  aria-current={item.current ? 'page' : undefined}
                >
                  {item.icon}
                  <span>{item.label}</span>
                </span>
              ) : (
                <Link
                  to={item.href}
                  className="flex items-center space-x-1 text-gray-600 hover:text-sea-green-600 transition-colors duration-200 hover:underline"
                >
                  {item.icon}
                  <span>{item.label}</span>
                </Link>
              )}
            </li>
          ))}
        </ol>
      </nav>
    </SlideIn>
  );
};

// Predefined breadcrumb configurations for common pages
export const PropertyBreadcrumb: React.FC<{
  propertyTitle?: string;
  location?: string;
}> = ({ propertyTitle, location: propertyLocation }) => {
  const items: BreadcrumbItem[] = [
    { label: 'Home', href: '/', icon: <Home className="h-4 w-4" /> },
    { label: 'Search', href: '/search' },
  ];

  if (propertyLocation) {
    items.push({ label: propertyLocation, href: `/search?location=${encodeURIComponent(propertyLocation)}` });
  }

  if (propertyTitle) {
    items.push({ label: propertyTitle, current: true });
  }

  return <EnhancedBreadcrumb items={items} />;
};

export const DashboardBreadcrumb: React.FC<{
  section?: string;
}> = ({ section }) => {
  const items: BreadcrumbItem[] = [
    { label: 'Home', href: '/', icon: <Home className="h-4 w-4" /> },
    { label: 'Dashboard', href: '/dashboard' },
  ];

  if (section) {
    items.push({ label: section, current: true });
  }

  return <EnhancedBreadcrumb items={items} />;
};

export const SearchBreadcrumb: React.FC<{
  location?: string;
  filters?: string[];
}> = ({ location: searchLocation, filters }) => {
  const items: BreadcrumbItem[] = [
    { label: 'Home', href: '/', icon: <Home className="h-4 w-4" /> },
    { label: 'Search', href: '/search' },
  ];

  if (searchLocation) {
    items.push({ label: searchLocation, current: !filters?.length });
  }

  if (filters?.length) {
    items.push({ 
      label: `${filters.length} Filter${filters.length > 1 ? 's' : ''} Applied`, 
      current: true 
    });
  }

  return <EnhancedBreadcrumb items={items} />;
};

// Styled breadcrumb variants
export const CardBreadcrumb: React.FC<EnhancedBreadcrumbProps> = (props) => (
  <div className="bg-white rounded-lg shadow-sm border p-3 mb-4">
    <EnhancedBreadcrumb {...props} />
  </div>
);

export const MinimalBreadcrumb: React.FC<EnhancedBreadcrumbProps> = (props) => (
  <EnhancedBreadcrumb 
    {...props} 
    separator={<span className="text-gray-300">/</span>}
    className="text-xs text-gray-500"
  />
);

export const LargeBreadcrumb: React.FC<EnhancedBreadcrumbProps> = (props) => (
  <EnhancedBreadcrumb 
    {...props} 
    className="text-base font-medium"
  />
);
