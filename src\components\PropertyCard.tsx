
import { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Calendar, Phone, Mail, Star, Users, Bed, Bath, MessageSquare } from 'lucide-react';
import { BookingCalendar } from './BookingCalendar';
import { RatingDisplay } from './StarRating';
import { PropertyImageGallery } from './PropertyImageGallery';
import { HoverAnimation, SlideIn } from '@/components/ui/page-transitions';
import { ButtonLoader } from '@/components/ui/loading-spinner';

interface PropertyCardProps {
  property: {
    id: string;
    title: string;
    location: string;
    price: number;
    images: string[];
    description: string;
    available: boolean;
    propertyType?: string;
    maxGuests?: number;
    bedrooms?: number;
    bathrooms?: number;
    amenities?: string[];
    averageRating?: number;
    reviewCount?: number;
    owner?: {
      firstName: string;
      lastName: string;
    };
    coordinates?: {
      latitude: number;
      longitude: number;
    };
    cleaningFee?: number;
    special?: string;
    bookedDates?: string[];
    pendingBookings?: Array<{
      checkIn: string;
      checkOut: string;
      expiresAt: string;
    }>;
    createdAt?: string;
  };
  variant?: 'grid' | 'list';
}

export const PropertyCard = ({ property, variant = 'grid' }: PropertyCardProps) => {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [showCalendar, setShowCalendar] = useState(false);
  const [isBookingLoading, setIsBookingLoading] = useState(false);
  const [isContactLoading, setIsContactLoading] = useState(false);

  const nextImage = () => {
    setCurrentImageIndex((prev) => 
      prev === property.images.length - 1 ? 0 : prev + 1
    );
  };

  const prevImage = () => {
    setCurrentImageIndex((prev) => 
      prev === 0 ? property.images.length - 1 : prev - 1
    );
  };

  const handleBookingComplete = (booking: any) => {
    console.log('Booking completed:', booking);
    setShowCalendar(false);
    // You could show a success message or redirect here
  };

  const handleCloseCalendar = () => {
    setShowCalendar(false);
  };

  // List view layout
  if (variant === 'list') {
    return (
      <SlideIn direction="up" delay={100}>
        <HoverAnimation type="lift">
          <Card className="group hover:shadow-xl transition-all duration-300 overflow-hidden border-0 shadow-lg">
        <div className="flex">
          {/* Image */}
          <div className="relative w-64 h-48 flex-shrink-0 overflow-hidden">
            <img
              src={property.images[currentImageIndex] || property.images[0] || "https://images.unsplash.com/photo-1721322800607-8c38375eef04?w=400&h=300&fit=crop"}
              alt={property.title}
              className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
            />

            {/* Special Badge */}
            {property.special && (
              <Badge className="absolute top-3 left-3 bg-ocean-blue-500 text-white">
                {property.special}
              </Badge>
            )}

            {/* Availability Badge */}
            <Badge
              className={`absolute top-3 right-3 ${
                property.available
                  ? 'bg-green-500 text-white'
                  : 'bg-red-500 text-white'
              }`}
            >
              {property.available ? 'Available' : 'Booked'}
            </Badge>
          </div>

          {/* Content */}
          <CardContent className="flex-1 p-6">
            <div className="flex justify-between h-full">
              <div className="flex-1">
                <h3 className="text-xl font-semibold text-gray-900 mb-1">
                  {property.title}
                </h3>
                <p className="text-sea-green-600 font-medium mb-2">{property.location}</p>

                {/* Property Details */}
                <div className="flex items-center space-x-4 mb-2 text-sm text-gray-600">
                  {property.maxGuests && (
                    <div className="flex items-center">
                      <Users className="h-4 w-4 mr-1" />
                      {property.maxGuests} guests
                    </div>
                  )}
                  {property.bedrooms && (
                    <div className="flex items-center">
                      <Bed className="h-4 w-4 mr-1" />
                      {property.bedrooms} bed{property.bedrooms > 1 ? 's' : ''}
                    </div>
                  )}
                  {property.bathrooms && (
                    <div className="flex items-center">
                      <Bath className="h-4 w-4 mr-1" />
                      {property.bathrooms} bath{property.bathrooms > 1 ? 's' : ''}
                    </div>
                  )}
                  {property.propertyType && (
                    <Badge variant="outline" className="text-xs">
                      {property.propertyType}
                    </Badge>
                  )}
                </div>

                {/* Rating */}
                <div className="mb-2">
                  <RatingDisplay
                    rating={property.averageRating || 0}
                    reviewCount={property.reviewCount}
                    size="sm"
                  />
                </div>

                {/* Description */}
                <p className="text-gray-600 text-sm line-clamp-2">
                  {property.description}
                </p>
              </div>

              {/* Price and Actions */}
              <div className="flex flex-col justify-between items-end ml-6">
                <div className="text-right">
                  <span className="text-2xl font-bold text-ocean-blue-600">
                    R{property.price.toLocaleString()}
                  </span>
                  <span className="text-gray-500 ml-1">/ night</span>
                  {property.cleaningFee && property.cleaningFee > 0 && (
                    <div className="text-sm text-gray-500">
                      + R{property.cleaningFee.toLocaleString()} cleaning fee
                    </div>
                  )}
                </div>

                <div className="space-y-2">
                  <div className="flex space-x-2">
                    <Button size="sm" variant="outline">
                      <Phone className="h-4 w-4 mr-1" />
                      Call
                    </Button>
                    <Button size="sm" variant="outline">
                      <Mail className="h-4 w-4 mr-1" />
                      Email
                    </Button>
                  </div>
                  <Button
                    onClick={() => {
                      setIsBookingLoading(true);
                      setTimeout(() => {
                        setShowCalendar(!showCalendar);
                        setIsBookingLoading(false);
                      }, 500);
                    }}
                    disabled={isBookingLoading}
                    className="w-full bg-gradient-to-r from-sea-green-500 to-ocean-blue-500 hover:from-sea-green-600 hover:to-ocean-blue-600 text-white disabled:opacity-50"
                    size="sm"
                  >
                    {isBookingLoading ? (
                      <ButtonLoader size="sm" className="mr-1" />
                    ) : (
                      <Calendar className="h-4 w-4 mr-1" />
                    )}
                    {isBookingLoading ? 'Loading...' : 'Book Now'}
                  </Button>
                </div>
              </div>
            </div>

            {/* Booking Calendar */}
            {showCalendar && (
              <div className="mt-4">
                <BookingCalendar
                  propertyId={property.id}
                  propertyTitle={property.title}
                  propertyPrice={property.price}
                  maxGuests={property.maxGuests}
                  cleaningFee={property.cleaningFee}
                  bookedDates={property.bookedDates}
                  pendingBookings={property.pendingBookings}
                  onBookingComplete={handleBookingComplete}
                  onClose={handleCloseCalendar}
                />
              </div>
            )}
          </CardContent>
        </div>
          </Card>
        </HoverAnimation>
      </SlideIn>
    );
  }

  // Grid view layout (default)
  return (
    <SlideIn direction="up" delay={100}>
      <HoverAnimation type="lift">
        <Card className="group hover:shadow-xl transition-all duration-300 overflow-hidden border-0 shadow-lg">
      <div className="relative">
        {/* Image Gallery */}
        <div className="relative h-64 overflow-hidden">
          <img
            src={property.images[currentImageIndex] || property.images[0] || "https://images.unsplash.com/photo-1721322800607-8c38375eef04?w=400&h=300&fit=crop"}
            alt={property.title}
            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
          />
          
          {/* Navigation Arrows */}
          {property.images.length > 1 && (
            <>
              <button
                onClick={prevImage}
                className="absolute left-2 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white rounded-full p-2 opacity-0 group-hover:opacity-100 transition-opacity"
              >
                ←
              </button>
              <button
                onClick={nextImage}
                className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white rounded-full p-2 opacity-0 group-hover:opacity-100 transition-opacity"
              >
                →
              </button>
            </>
          )}

          {/* Image Indicators */}
          {property.images.length > 1 && (
            <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 flex space-x-1">
              {property.images.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentImageIndex(index)}
                  className={`w-2 h-2 rounded-full transition-all ${
                    index === currentImageIndex ? 'bg-white' : 'bg-white/50'
                  }`}
                />
              ))}
            </div>
          )}

          {/* Special Badge */}
          {property.special && (
            <Badge className="absolute top-3 left-3 bg-ocean-blue-500 text-white">
              {property.special}
            </Badge>
          )}

          {/* Availability Badge */}
          <Badge
            className={`absolute top-3 right-3 ${
              property.available
                ? 'bg-green-500 text-white'
                : 'bg-red-500 text-white'
            }`}
          >
            {property.available ? 'Available' : 'Booked'}
          </Badge>
        </div>

        <CardContent className="p-6">
          <div className="space-y-4">
            {/* Property Info */}
            <div>
              <h3 className="text-xl font-semibold text-gray-900 mb-1">
                {property.title}
              </h3>
              <p className="text-sea-green-600 font-medium">{property.location}</p>

              {/* Property Details */}
              <div className="flex items-center space-x-4 mt-2 text-sm text-gray-600">
                {property.maxGuests && (
                  <div className="flex items-center">
                    <Users className="h-4 w-4 mr-1" />
                    {property.maxGuests} guests
                  </div>
                )}
                {property.bedrooms && (
                  <div className="flex items-center">
                    <Bed className="h-4 w-4 mr-1" />
                    {property.bedrooms} bed{property.bedrooms > 1 ? 's' : ''}
                  </div>
                )}
                {property.bathrooms && (
                  <div className="flex items-center">
                    <Bath className="h-4 w-4 mr-1" />
                    {property.bathrooms} bath{property.bathrooms > 1 ? 's' : ''}
                  </div>
                )}
              </div>

              {/* Rating */}
              <div className="mt-2">
                <RatingDisplay
                  rating={property.averageRating || 0}
                  reviewCount={property.reviewCount}
                  size="sm"
                />
              </div>
            </div>

            {/* Price */}
            <div className="flex items-center justify-between">
              <div>
                <span className="text-2xl font-bold text-ocean-blue-600">
                  R{property.price.toLocaleString()}
                </span>
                <span className="text-gray-500 ml-1">/ night</span>
                {property.cleaningFee && property.cleaningFee > 0 && (
                  <div className="text-sm text-gray-500">
                    + R{property.cleaningFee.toLocaleString()} cleaning fee
                  </div>
                )}
              </div>
              {property.propertyType && (
                <Badge variant="outline" className="text-xs">
                  {property.propertyType}
                </Badge>
              )}
            </div>

            {/* Description */}
            <p className="text-gray-600 text-sm line-clamp-2">
              {property.description}
            </p>

            {/* Contact and Booking Buttons */}
            <div className="space-y-3">
              <div className="flex space-x-2">
                <Button
                  size="sm"
                  variant="outline"
                  className="flex-1 border-sea-green-300 text-sea-green-700 hover:bg-sea-green-50"
                >
                  <Phone className="h-4 w-4 mr-2" />
                  Call
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  className="flex-1 border-ocean-blue-300 text-ocean-blue-700 hover:bg-ocean-blue-50"
                >
                  <Mail className="h-4 w-4 mr-2" />
                  Email
                </Button>
              </div>
              
              <Button
                onClick={() => {
                  setIsBookingLoading(true);
                  setTimeout(() => {
                    setShowCalendar(!showCalendar);
                    setIsBookingLoading(false);
                  }, 500);
                }}
                disabled={isBookingLoading}
                className="w-full bg-gradient-to-r from-sea-green-500 to-ocean-blue-500 hover:from-sea-green-600 hover:to-ocean-blue-600 text-white disabled:opacity-50"
              >
                {isBookingLoading ? (
                  <ButtonLoader size="sm" className="mr-2" />
                ) : (
                  <Calendar className="h-4 w-4 mr-2" />
                )}
                {isBookingLoading ? 'Loading...' : (showCalendar ? 'Close Calendar' : 'Book Now')}
              </Button>
            </div>

            {/* Booking Calendar */}
            {showCalendar && (
              <div className="mt-4">
                <BookingCalendar
                  propertyId={property.id}
                  propertyTitle={property.title}
                  propertyPrice={property.price}
                  maxGuests={property.maxGuests}
                  cleaningFee={property.cleaningFee}
                  bookedDates={property.bookedDates}
                  pendingBookings={property.pendingBookings}
                  onBookingComplete={handleBookingComplete}
                  onClose={handleCloseCalendar}
                />
              </div>
            )}
          </div>
        </CardContent>
      </div>
        </Card>
      </HoverAnimation>
    </SlideIn>
  );
};
