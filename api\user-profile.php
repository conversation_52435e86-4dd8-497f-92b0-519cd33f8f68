<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: http://localhost:8081');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Credentials: true');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once 'config.php';
require_once 'auth.php';

function getUserProfile($userId) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            SELECT 
                u.id,
                u.first_name,
                u.last_name,
                u.email,
                u.phone,
                u.date_of_birth,
                u.profile_picture,
                u.bio,
                u.location,
                u.languages,
                u.occupation,
                u.emergency_contact_name,
                u.emergency_contact_phone,
                u.email_verified,
                u.phone_verified,
                u.id_verified,
                u.created_at,
                u.preferences,
                u.notification_settings,
                COUNT(DISTINCT b.id) as total_bookings,
                COUNT(DISTINCT r.id) as total_reviews,
                COALESCE(AVG(r.rating), 0) as average_rating
            FROM users u
            LEFT JOIN bookings b ON u.id = b.guest_id
            LEFT JOIN reviews r ON u.id = r.reviewer_id
            WHERE u.id = ?
            GROUP BY u.id
        ");
        
        $stmt->execute([$userId]);
        $profile = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$profile) {
            error_log("User profile not found for user ID: " . $userId);
            return null;
        }
        
        // Parse JSON fields
        $profile['languages'] = json_decode($profile['languages'] ?? '[]', true);
        $profile['preferences'] = json_decode($profile['preferences'] ?? '{}', true);
        $profile['notification_settings'] = json_decode($profile['notification_settings'] ?? '{}', true);
        
        // Convert to appropriate types
        $profile['total_bookings'] = (int)$profile['total_bookings'];
        $profile['total_reviews'] = (int)$profile['total_reviews'];
        $profile['average_rating'] = (float)$profile['average_rating'];
        $profile['email_verified'] = (bool)$profile['email_verified'];
        $profile['phone_verified'] = (bool)$profile['phone_verified'];
        $profile['id_verified'] = (bool)$profile['id_verified'];
        
        return $profile;
        
    } catch (Exception $e) {
        error_log("Error getting user profile: " . $e->getMessage());
        return null;
    }
}

function updateUserProfile($userId, $data) {
    global $pdo;
    
    try {
        $allowedFields = [
            'first_name', 'last_name', 'phone', 'date_of_birth', 'bio', 
            'location', 'languages', 'occupation', 'emergency_contact_name', 
            'emergency_contact_phone', 'preferences', 'notification_settings'
        ];
        
        $updateFields = [];
        $values = [];
        
        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $updateFields[] = "$field = ?";
                
                // Handle JSON fields
                if (in_array($field, ['languages', 'preferences', 'notification_settings'])) {
                    $values[] = json_encode($data[$field]);
                } else {
                    $values[] = $data[$field];
                }
            }
        }
        
        if (empty($updateFields)) {
            return false;
        }
        
        $values[] = $userId;
        $sql = "UPDATE users SET " . implode(', ', $updateFields) . ", updated_at = NOW() WHERE id = ?";
        
        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute($values);
        
        return $result;
        
    } catch (Exception $e) {
        error_log("Error updating user profile: " . $e->getMessage());
        return false;
    }
}

function uploadProfilePicture($userId, $file) {
    try {
        // Validate file
        $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
        if (!in_array($file['type'], $allowedTypes)) {
            throw new Exception('Invalid file type. Only JPEG, PNG, and WebP are allowed.');
        }
        
        if ($file['size'] > 5 * 1024 * 1024) { // 5MB limit
            throw new Exception('File size too large. Maximum 5MB allowed.');
        }
        
        // Create upload directory if it doesn't exist
        $uploadDir = '../uploads/profile-pictures/';
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }
        
        // Generate unique filename
        $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $filename = $userId . '_' . time() . '.' . $extension;
        $filepath = $uploadDir . $filename;
        
        // Move uploaded file
        if (!move_uploaded_file($file['tmp_name'], $filepath)) {
            throw new Exception('Failed to upload file.');
        }
        
        // Update database
        global $pdo;
        $stmt = $pdo->prepare("UPDATE users SET profile_picture = ?, updated_at = NOW() WHERE id = ?");
        $profilePictureUrl = '/stayfinder/uploads/profile-pictures/' . $filename;
        $stmt->execute([$profilePictureUrl, $userId]);
        
        return $profilePictureUrl;
        
    } catch (Exception $e) {
        error_log("Error uploading profile picture: " . $e->getMessage());
        throw $e;
    }
}

function verifyEmail($userId, $verificationCode) {
    global $pdo;
    
    try {
        // In a real implementation, you'd check the verification code
        // For now, we'll just mark as verified
        $stmt = $pdo->prepare("UPDATE users SET email_verified = 1, updated_at = NOW() WHERE id = ?");
        return $stmt->execute([$userId]);
        
    } catch (Exception $e) {
        error_log("Error verifying email: " . $e->getMessage());
        return false;
    }
}

function verifyPhone($userId, $verificationCode) {
    global $pdo;
    
    try {
        // In a real implementation, you'd check the SMS verification code
        // For now, we'll just mark as verified
        $stmt = $pdo->prepare("UPDATE users SET phone_verified = 1, updated_at = NOW() WHERE id = ?");
        return $stmt->execute([$userId]);
        
    } catch (Exception $e) {
        error_log("Error verifying phone: " . $e->getMessage());
        return false;
    }
}

function uploadIdDocument($userId, $file) {
    try {
        // Validate file
        $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'];
        if (!in_array($file['type'], $allowedTypes)) {
            throw new Exception('Invalid file type. Only JPEG, PNG, and PDF are allowed.');
        }
        
        if ($file['size'] > 10 * 1024 * 1024) { // 10MB limit
            throw new Exception('File size too large. Maximum 10MB allowed.');
        }
        
        // Create upload directory if it doesn't exist
        $uploadDir = '../uploads/id-documents/';
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }
        
        // Generate unique filename
        $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $filename = $userId . '_id_' . time() . '.' . $extension;
        $filepath = $uploadDir . $filename;
        
        // Move uploaded file
        if (!move_uploaded_file($file['tmp_name'], $filepath)) {
            throw new Exception('Failed to upload file.');
        }
        
        // Update database - mark as pending verification
        global $pdo;
        $stmt = $pdo->prepare("
            UPDATE users 
            SET id_document_path = ?, id_verification_status = 'pending', updated_at = NOW() 
            WHERE id = ?
        ");
        $idDocumentPath = '/stayfinder/uploads/id-documents/' . $filename;
        $stmt->execute([$idDocumentPath, $userId]);
        
        return $idDocumentPath;
        
    } catch (Exception $e) {
        error_log("Error uploading ID document: " . $e->getMessage());
        throw $e;
    }
}

// Handle the request
$method = $_SERVER['REQUEST_METHOD'];
$path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
$pathParts = explode('/', trim($path, '/'));

try {
    $user = getCurrentUser();
    if (!$user) {
        // For testing, use the first user in the database
        global $pdo;
        $stmt = $pdo->prepare("SELECT * FROM users ORDER BY created_at ASC LIMIT 1");
        $stmt->execute();
        $user = $stmt->fetch();

        if (!$user) {
            http_response_code(401);
            echo json_encode(['error' => 'No users found in database']);
            exit;
        }
    }
    
    switch ($method) {
        case 'GET':
            // Get user profile
            $profile = getUserProfile($user['id']);
            
            if ($profile) {
                echo json_encode([
                    'success' => true,
                    'data' => $profile
                ]);
            } else {
                http_response_code(404);
                echo json_encode(['error' => 'Profile not found']);
            }
            break;
            
        case 'PUT':
            // Update user profile
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input) {
                http_response_code(400);
                echo json_encode(['error' => 'Invalid input data']);
                exit;
            }
            
            $success = updateUserProfile($user['id'], $input);
            
            if ($success) {
                $updatedProfile = getUserProfile($user['id']);
                echo json_encode([
                    'success' => true,
                    'message' => 'Profile updated successfully',
                    'data' => $updatedProfile
                ]);
            } else {
                http_response_code(500);
                echo json_encode(['error' => 'Failed to update profile']);
            }
            break;
            
        case 'POST':
            // Handle file uploads and verification
            if (isset($_GET['action'])) {
                $action = $_GET['action'];
                
                switch ($action) {
                    case 'upload_profile_picture':
                        if (!isset($_FILES['profile_picture'])) {
                            http_response_code(400);
                            echo json_encode(['error' => 'No file uploaded']);
                            exit;
                        }
                        
                        try {
                            $profilePictureUrl = uploadProfilePicture($user['id'], $_FILES['profile_picture']);
                            echo json_encode([
                                'success' => true,
                                'message' => 'Profile picture uploaded successfully',
                                'profile_picture_url' => $profilePictureUrl
                            ]);
                        } catch (Exception $e) {
                            http_response_code(400);
                            echo json_encode(['error' => $e->getMessage()]);
                        }
                        break;
                        
                    case 'upload_id_document':
                        if (!isset($_FILES['id_document'])) {
                            http_response_code(400);
                            echo json_encode(['error' => 'No file uploaded']);
                            exit;
                        }
                        
                        try {
                            $idDocumentPath = uploadIdDocument($user['id'], $_FILES['id_document']);
                            echo json_encode([
                                'success' => true,
                                'message' => 'ID document uploaded successfully. Verification pending.',
                                'id_document_path' => $idDocumentPath
                            ]);
                        } catch (Exception $e) {
                            http_response_code(400);
                            echo json_encode(['error' => $e->getMessage()]);
                        }
                        break;
                        
                    case 'verify_email':
                        $input = json_decode(file_get_contents('php://input'), true);
                        $verificationCode = $input['verification_code'] ?? '';
                        
                        $success = verifyEmail($user['id'], $verificationCode);
                        
                        if ($success) {
                            echo json_encode([
                                'success' => true,
                                'message' => 'Email verified successfully'
                            ]);
                        } else {
                            http_response_code(400);
                            echo json_encode(['error' => 'Email verification failed']);
                        }
                        break;
                        
                    case 'verify_phone':
                        $input = json_decode(file_get_contents('php://input'), true);
                        $verificationCode = $input['verification_code'] ?? '';
                        
                        $success = verifyPhone($user['id'], $verificationCode);
                        
                        if ($success) {
                            echo json_encode([
                                'success' => true,
                                'message' => 'Phone verified successfully'
                            ]);
                        } else {
                            http_response_code(400);
                            echo json_encode(['error' => 'Phone verification failed']);
                        }
                        break;
                        
                    default:
                        http_response_code(400);
                        echo json_encode(['error' => 'Invalid action']);
                        break;
                }
            } else {
                http_response_code(400);
                echo json_encode(['error' => 'Action parameter required']);
            }
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            break;
    }
    
} catch (Exception $e) {
    error_log("User Profile API error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error']);
}
?>
