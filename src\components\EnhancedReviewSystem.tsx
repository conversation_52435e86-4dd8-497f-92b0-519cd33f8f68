import React, { useState, useRef } from 'react';
import { Star, Camera, Upload, X, ThumbsUp, ThumbsDown, Flag, MoreHorizontal, Filter, SortAsc } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { toast } from '@/components/ui/use-toast';
import { cn } from '@/lib/utils';

interface Review {
  id: string;
  userId: string;
  userName: string;
  userAvatar?: string;
  rating: number;
  title: string;
  content: string;
  photos: string[];
  date: Date;
  helpful: number;
  notHelpful: number;
  verified: boolean;
  stayDuration: number;
  roomType: string;
  travelType: 'business' | 'leisure' | 'family' | 'couple' | 'solo';
  categories: {
    cleanliness: number;
    communication: number;
    checkIn: number;
    accuracy: number;
    location: number;
    value: number;
  };
}

interface ReviewFormData {
  rating: number;
  title: string;
  content: string;
  photos: File[];
  categories: {
    cleanliness: number;
    communication: number;
    checkIn: number;
    accuracy: number;
    location: number;
    value: number;
  };
  travelType: 'business' | 'leisure' | 'family' | 'couple' | 'solo';
}

interface EnhancedReviewSystemProps {
  propertyId: string;
  reviews: Review[];
  canWriteReview?: boolean;
  onSubmitReview: (review: ReviewFormData) => void;
  onHelpfulVote: (reviewId: string, helpful: boolean) => void;
  onReportReview: (reviewId: string, reason: string) => void;
  className?: string;
}

export const EnhancedReviewSystem: React.FC<EnhancedReviewSystemProps> = ({
  propertyId,
  reviews,
  canWriteReview = false,
  onSubmitReview,
  onHelpfulVote,
  onReportReview,
  className
}) => {
  const [showWriteReview, setShowWriteReview] = useState(false);
  const [sortBy, setSortBy] = useState<'newest' | 'oldest' | 'highest' | 'lowest' | 'helpful'>('newest');
  const [filterBy, setFilterBy] = useState<'all' | '5' | '4' | '3' | '2' | '1'>('all');
  const [searchQuery, setSearchQuery] = useState('');
  
  const [reviewForm, setReviewForm] = useState<ReviewFormData>({
    rating: 0,
    title: '',
    content: '',
    photos: [],
    categories: {
      cleanliness: 0,
      communication: 0,
      checkIn: 0,
      accuracy: 0,
      location: 0,
      value: 0
    },
    travelType: 'leisure'
  });

  const fileInputRef = useRef<HTMLInputElement>(null);

  const categoryLabels = {
    cleanliness: 'Cleanliness',
    communication: 'Communication',
    checkIn: 'Check-in',
    accuracy: 'Accuracy',
    location: 'Location',
    value: 'Value'
  };

  const travelTypeLabels = {
    business: 'Business',
    leisure: 'Leisure',
    family: 'Family',
    couple: 'Couple',
    solo: 'Solo'
  };

  // Calculate average ratings
  const averageRating = reviews.length > 0 
    ? reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length 
    : 0;

  const categoryAverages = Object.keys(categoryLabels).reduce((acc, category) => {
    const key = category as keyof typeof categoryLabels;
    acc[key] = reviews.length > 0 
      ? reviews.reduce((sum, review) => sum + review.categories[key], 0) / reviews.length 
      : 0;
    return acc;
  }, {} as Record<keyof typeof categoryLabels, number>);

  // Filter and sort reviews
  const filteredReviews = reviews
    .filter(review => {
      if (filterBy !== 'all' && review.rating !== parseInt(filterBy)) return false;
      if (searchQuery && !review.content.toLowerCase().includes(searchQuery.toLowerCase()) && 
          !review.title.toLowerCase().includes(searchQuery.toLowerCase())) return false;
      return true;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'newest': return new Date(b.date).getTime() - new Date(a.date).getTime();
        case 'oldest': return new Date(a.date).getTime() - new Date(b.date).getTime();
        case 'highest': return b.rating - a.rating;
        case 'lowest': return a.rating - b.rating;
        case 'helpful': return b.helpful - a.helpful;
        default: return 0;
      }
    });

  const handleRatingChange = (rating: number, category?: keyof typeof categoryLabels) => {
    if (category) {
      setReviewForm(prev => ({
        ...prev,
        categories: { ...prev.categories, [category]: rating }
      }));
    } else {
      setReviewForm(prev => ({ ...prev, rating }));
    }
  };

  const handlePhotoUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (files.length + reviewForm.photos.length > 5) {
      toast({
        title: "Too many photos",
        description: "You can upload a maximum of 5 photos",
        variant: "destructive"
      });
      return;
    }

    setReviewForm(prev => ({
      ...prev,
      photos: [...prev.photos, ...files]
    }));
  };

  const removePhoto = (index: number) => {
    setReviewForm(prev => ({
      ...prev,
      photos: prev.photos.filter((_, i) => i !== index)
    }));
  };

  const handleSubmit = () => {
    if (reviewForm.rating === 0) {
      toast({
        title: "Rating required",
        description: "Please provide an overall rating",
        variant: "destructive"
      });
      return;
    }

    if (reviewForm.content.length < 10) {
      toast({
        title: "Review too short",
        description: "Please write at least 10 characters",
        variant: "destructive"
      });
      return;
    }

    onSubmitReview(reviewForm);
    setShowWriteReview(false);
    setReviewForm({
      rating: 0,
      title: '',
      content: '',
      photos: [],
      categories: {
        cleanliness: 0,
        communication: 0,
        checkIn: 0,
        accuracy: 0,
        location: 0,
        value: 0
      },
      travelType: 'leisure'
    });

    toast({
      title: "Review submitted",
      description: "Thank you for your feedback!",
    });
  };

  const StarRating: React.FC<{ 
    rating: number; 
    onRatingChange?: (rating: number) => void; 
    readonly?: boolean;
    size?: 'sm' | 'md' | 'lg';
  }> = ({ rating, onRatingChange, readonly = false, size = 'md' }) => {
    const sizeClasses = {
      sm: 'h-4 w-4',
      md: 'h-5 w-5',
      lg: 'h-6 w-6'
    };

    return (
      <div className="flex items-center gap-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <button
            key={star}
            type="button"
            disabled={readonly}
            onClick={() => onRatingChange?.(star)}
            className={cn(
              sizeClasses[size],
              "transition-colors",
              star <= rating ? "text-yellow-400" : "text-gray-300",
              !readonly && "hover:text-yellow-400 cursor-pointer"
            )}
          >
            <Star className="w-full h-full fill-current" />
          </button>
        ))}
      </div>
    );
  };

  return (
    <div className={cn("space-y-6", className)}>
      {/* Review Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Guest Reviews</span>
            <div className="flex items-center gap-2">
              <div className="flex items-center gap-1">
                <Star className="h-5 w-5 text-yellow-400 fill-current" />
                <span className="text-xl font-bold">{averageRating.toFixed(1)}</span>
              </div>
              <span className="text-gray-600">({reviews.length} reviews)</span>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Category Ratings */}
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-6">
            {Object.entries(categoryAverages).map(([category, average]) => (
              <div key={category} className="flex items-center justify-between">
                <span className="text-sm text-gray-600">
                  {categoryLabels[category as keyof typeof categoryLabels]}
                </span>
                <div className="flex items-center gap-2">
                  <StarRating rating={average} readonly size="sm" />
                  <span className="text-sm font-medium">{average.toFixed(1)}</span>
                </div>
              </div>
            ))}
          </div>

          {/* Write Review Button */}
          {canWriteReview && (
            <Button 
              onClick={() => setShowWriteReview(true)}
              className="w-full"
            >
              Write a Review
            </Button>
          )}
        </CardContent>
      </Card>

      {/* Write Review Form */}
      {showWriteReview && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              Write Your Review
              <Button variant="ghost" size="sm" onClick={() => setShowWriteReview(false)}>
                <X className="h-4 w-4" />
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Overall Rating */}
            <div>
              <Label className="text-base font-medium mb-3 block">Overall Rating *</Label>
              <StarRating 
                rating={reviewForm.rating} 
                onRatingChange={(rating) => handleRatingChange(rating)}
                size="lg"
              />
            </div>

            {/* Category Ratings */}
            <div>
              <Label className="text-base font-medium mb-3 block">Rate Your Experience</Label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {Object.entries(categoryLabels).map(([category, label]) => (
                  <div key={category} className="flex items-center justify-between">
                    <span className="text-sm">{label}</span>
                    <StarRating 
                      rating={reviewForm.categories[category as keyof typeof categoryLabels]} 
                      onRatingChange={(rating) => handleRatingChange(rating, category as keyof typeof categoryLabels)}
                    />
                  </div>
                ))}
              </div>
            </div>

            {/* Travel Type */}
            <div>
              <Label htmlFor="travelType">Travel Type</Label>
              <Select 
                value={reviewForm.travelType} 
                onValueChange={(value) => setReviewForm(prev => ({ ...prev, travelType: value as any }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(travelTypeLabels).map(([value, label]) => (
                    <SelectItem key={value} value={value}>{label}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Review Title */}
            <div>
              <Label htmlFor="title">Review Title</Label>
              <Input
                id="title"
                value={reviewForm.title}
                onChange={(e) => setReviewForm(prev => ({ ...prev, title: e.target.value }))}
                placeholder="Summarize your experience"
              />
            </div>

            {/* Review Content */}
            <div>
              <Label htmlFor="content">Your Review *</Label>
              <Textarea
                id="content"
                value={reviewForm.content}
                onChange={(e) => setReviewForm(prev => ({ ...prev, content: e.target.value }))}
                placeholder="Share your experience with future guests..."
                rows={4}
              />
              <p className="text-sm text-gray-500 mt-1">
                {reviewForm.content.length}/500 characters
              </p>
            </div>

            {/* Photo Upload */}
            <div>
              <Label>Add Photos (Optional)</Label>
              <div className="mt-2">
                <input
                  ref={fileInputRef}
                  type="file"
                  multiple
                  accept="image/*"
                  onChange={handlePhotoUpload}
                  className="hidden"
                />
                
                <div className="flex flex-wrap gap-3">
                  {reviewForm.photos.map((photo, index) => (
                    <div key={index} className="relative">
                      <img
                        src={URL.createObjectURL(photo)}
                        alt={`Upload ${index + 1}`}
                        className="w-20 h-20 object-cover rounded-lg"
                      />
                      <Button
                        size="sm"
                        variant="destructive"
                        className="absolute -top-2 -right-2 h-6 w-6 rounded-full p-0"
                        onClick={() => removePhoto(index)}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
                  ))}
                  
                  {reviewForm.photos.length < 5 && (
                    <button
                      type="button"
                      onClick={() => fileInputRef.current?.click()}
                      className="w-20 h-20 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center hover:border-gray-400 transition-colors"
                    >
                      <Camera className="h-6 w-6 text-gray-400" />
                    </button>
                  )}
                </div>
                <p className="text-sm text-gray-500 mt-2">
                  Upload up to 5 photos to help other guests
                </p>
              </div>
            </div>

            {/* Submit Button */}
            <div className="flex gap-3">
              <Button variant="outline" onClick={() => setShowWriteReview(false)} className="flex-1">
                Cancel
              </Button>
              <Button onClick={handleSubmit} className="flex-1">
                Submit Review
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Filters and Search */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <Input
                placeholder="Search reviews..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <Select value={filterBy} onValueChange={(value) => setFilterBy(value as any)}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All ratings</SelectItem>
                <SelectItem value="5">5 stars</SelectItem>
                <SelectItem value="4">4 stars</SelectItem>
                <SelectItem value="3">3 stars</SelectItem>
                <SelectItem value="2">2 stars</SelectItem>
                <SelectItem value="1">1 star</SelectItem>
              </SelectContent>
            </Select>
            <Select value={sortBy} onValueChange={(value) => setSortBy(value as any)}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="newest">Newest first</SelectItem>
                <SelectItem value="oldest">Oldest first</SelectItem>
                <SelectItem value="highest">Highest rated</SelectItem>
                <SelectItem value="lowest">Lowest rated</SelectItem>
                <SelectItem value="helpful">Most helpful</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Reviews List */}
      <div className="space-y-4">
        {filteredReviews.map((review) => (
          <Card key={review.id}>
            <CardContent className="p-6">
              <div className="flex items-start gap-4">
                <Avatar className="h-12 w-12">
                  <AvatarImage src={review.userAvatar} />
                  <AvatarFallback>{review.userName.charAt(0)}</AvatarFallback>
                </Avatar>
                
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-2">
                    <div>
                      <h4 className="font-semibold">{review.userName}</h4>
                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <span>{review.date.toLocaleDateString()}</span>
                        <span>•</span>
                        <span>{review.stayDuration} nights</span>
                        <span>•</span>
                        <Badge variant="secondary">{travelTypeLabels[review.travelType]}</Badge>
                        {review.verified && (
                          <>
                            <span>•</span>
                            <Badge variant="default" className="bg-green-600">Verified Stay</Badge>
                          </>
                        )}
                      </div>
                    </div>
                    <Button variant="ghost" size="sm">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </div>
                  
                  <div className="mb-3">
                    <StarRating rating={review.rating} readonly />
                  </div>
                  
                  {review.title && (
                    <h5 className="font-medium mb-2">{review.title}</h5>
                  )}
                  
                  <p className="text-gray-700 mb-4">{review.content}</p>
                  
                  {review.photos.length > 0 && (
                    <div className="flex gap-2 mb-4">
                      {review.photos.map((photo, index) => (
                        <img
                          key={index}
                          src={photo}
                          alt={`Review photo ${index + 1}`}
                          className="w-20 h-20 object-cover rounded-lg cursor-pointer hover:opacity-80 transition-opacity"
                        />
                      ))}
                    </div>
                  )}
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <button
                        onClick={() => onHelpfulVote(review.id, true)}
                        className="flex items-center gap-1 text-sm text-gray-600 hover:text-gray-800"
                      >
                        <ThumbsUp className="h-4 w-4" />
                        Helpful ({review.helpful})
                      </button>
                      <button
                        onClick={() => onHelpfulVote(review.id, false)}
                        className="flex items-center gap-1 text-sm text-gray-600 hover:text-gray-800"
                      >
                        <ThumbsDown className="h-4 w-4" />
                        ({review.notHelpful})
                      </button>
                    </div>
                    <button
                      onClick={() => onReportReview(review.id, 'inappropriate')}
                      className="flex items-center gap-1 text-sm text-gray-600 hover:text-red-600"
                    >
                      <Flag className="h-4 w-4" />
                      Report
                    </button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredReviews.length === 0 && (
        <Card>
          <CardContent className="p-8 text-center">
            <Star className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <h3 className="text-lg font-medium mb-2">No reviews found</h3>
            <p className="text-gray-600">
              {searchQuery || filterBy !== 'all' 
                ? 'Try adjusting your search or filters' 
                : 'Be the first to leave a review!'}
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
