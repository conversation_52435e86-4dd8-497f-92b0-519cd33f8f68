import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { userProfileService, UserProfile, UserProfileUpdate } from '../services/userProfileService';
import { 
  User, 
  Camera, 
  Shield, 
  Mail, 
  Phone, 
  MapPin,
  Calendar,
  Briefcase,
  Users,
  Settings,
  Upload,
  Check,
  X,
  AlertCircle,
  Star,
  Edit,
  Save
} from 'lucide-react';

interface UserProfileProps {
  className?: string;
  onProfileUpdate?: (profile: UserProfile) => void;
}

// Add this after the imports and before the main component
const ProfileVerification: React.FC<{ profile: UserProfile; onVerificationUpdate: () => void }> = ({
  profile,
  onVerificationUpdate
}) => {
  const [verifying, setVerifying] = useState<string | null>(null);
  const [verificationCode, setVerificationCode] = useState('');
  const [showVerificationInput, setShowVerificationInput] = useState<string | null>(null);

  const handleSendVerification = async (type: 'email' | 'phone') => {
    try {
      setVerifying(type);

      if (type === 'email') {
        await userProfileService.sendEmailVerification();
      } else {
        await userProfileService.sendPhoneVerification();
      }

      setShowVerificationInput(type);
    } catch (error) {
      console.error(`Error sending ${type} verification:`, error);
    } finally {
      setVerifying(null);
    }
  };

  const handleVerify = async (type: 'email' | 'phone') => {
    try {
      setVerifying(type);

      let success = false;
      if (type === 'email') {
        success = await userProfileService.verifyEmail(verificationCode);
      } else {
        success = await userProfileService.verifyPhone(verificationCode);
      }

      if (success) {
        setShowVerificationInput(null);
        setVerificationCode('');
        onVerificationUpdate();
      }
    } catch (error) {
      console.error(`Error verifying ${type}:`, error);
    } finally {
      setVerifying(null);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shield className="h-5 w-5" />
          Account Verification
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Email Verification */}
        <div className="flex items-center justify-between p-4 border rounded-lg">
          <div className="flex items-center gap-3">
            <Mail className="h-5 w-5 text-gray-500" />
            <div>
              <p className="font-medium">Email Verification</p>
              <p className="text-sm text-gray-600">{profile.email}</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            {profile.email_verified ? (
              <Badge className="bg-green-100 text-green-700 border-green-200">
                <Check className="h-3 w-3 mr-1" />
                Verified
              </Badge>
            ) : (
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleSendVerification('email')}
                disabled={verifying === 'email'}
              >
                {verifying === 'email' ? 'Sending...' : 'Verify Email'}
              </Button>
            )}
          </div>
        </div>

        {showVerificationInput === 'email' && (
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <p className="text-sm text-blue-700 mb-3">
              We've sent a verification code to your email. Please enter it below:
            </p>
            <div className="flex gap-2">
              <Input
                placeholder="Verification code"
                value={verificationCode}
                onChange={(e) => setVerificationCode(e.target.value)}
                className="flex-1"
              />
              <Button
                onClick={() => handleVerify('email')}
                disabled={!verificationCode || verifying === 'email'}
                size="sm"
              >
                Verify
              </Button>
            </div>
          </div>
        )}

        {/* Phone Verification */}
        {profile.phone && (
          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div className="flex items-center gap-3">
              <Phone className="h-5 w-5 text-gray-500" />
              <div>
                <p className="font-medium">Phone Verification</p>
                <p className="text-sm text-gray-600">{profile.phone}</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {profile.phone_verified ? (
                <Badge className="bg-green-100 text-green-700 border-green-200">
                  <Check className="h-3 w-3 mr-1" />
                  Verified
                </Badge>
              ) : (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleSendVerification('phone')}
                  disabled={verifying === 'phone'}
                >
                  {verifying === 'phone' ? 'Sending...' : 'Verify Phone'}
                </Button>
              )}
            </div>
          </div>
        )}

        {showVerificationInput === 'phone' && (
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <p className="text-sm text-blue-700 mb-3">
              We've sent a verification code to your phone. Please enter it below:
            </p>
            <div className="flex gap-2">
              <Input
                placeholder="Verification code"
                value={verificationCode}
                onChange={(e) => setVerificationCode(e.target.value)}
                className="flex-1"
              />
              <Button
                onClick={() => handleVerify('phone')}
                disabled={!verificationCode || verifying === 'phone'}
                size="sm"
              >
                Verify
              </Button>
            </div>
          </div>
        )}

        {/* ID Verification */}
        <div className="flex items-center justify-between p-4 border rounded-lg">
          <div className="flex items-center gap-3">
            <Shield className="h-5 w-5 text-gray-500" />
            <div>
              <p className="font-medium">Identity Verification</p>
              <p className="text-sm text-gray-600">Upload government-issued ID</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            {profile.id_verified ? (
              <Badge className="bg-green-100 text-green-700 border-green-200">
                <Check className="h-3 w-3 mr-1" />
                Verified
              </Badge>
            ) : (
              <label className="cursor-pointer">
                <Button variant="outline" size="sm" asChild>
                  <span>
                    <Upload className="h-3 w-3 mr-1" />
                    Upload ID
                  </span>
                </Button>
                <input
                  type="file"
                  accept="image/*,.pdf"
                  onChange={async (e) => {
                    const file = e.target.files?.[0];
                    if (file) {
                      try {
                        await userProfileService.uploadIdDocument(file);
                        onVerificationUpdate();
                      } catch (error) {
                        console.error('Error uploading ID document:', error);
                      }
                    }
                  }}
                  className="hidden"
                />
              </label>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export const UserProfile: React.FC<UserProfileProps> = ({
  className,
  onProfileUpdate
}) => {
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [editing, setEditing] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState<UserProfileUpdate>({});
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [uploadingPicture, setUploadingPicture] = useState(false);

  useEffect(() => {
    fetchProfile();
  }, []);

  const fetchProfile = async () => {
    try {
      setLoading(true);
      setError(null);
      const profileData = await userProfileService.getUserProfile();
      setProfile(profileData);
      setFormData({
        first_name: profileData.first_name,
        last_name: profileData.last_name,
        phone: profileData.phone || '',
        date_of_birth: profileData.date_of_birth || '',
        bio: profileData.bio || '',
        location: profileData.location || '',
        occupation: profileData.occupation || '',
        emergency_contact_name: profileData.emergency_contact_name || '',
        emergency_contact_phone: profileData.emergency_contact_phone || '',
        languages: profileData.languages || ['English'],
      });
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load profile');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof UserProfileUpdate, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear field error when user starts typing
    if (formErrors[field]) {
      setFormErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      setError(null);

      // Validate form data
      const validation = userProfileService.validateProfileData(formData);
      if (!validation.isValid) {
        setFormErrors(validation.errors);
        return;
      }

      const updatedProfile = await userProfileService.updateUserProfile(formData);
      setProfile(updatedProfile);
      setEditing(false);
      setFormErrors({});

      if (onProfileUpdate) {
        onProfileUpdate(updatedProfile);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update profile');
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    if (profile) {
      setFormData({
        first_name: profile.first_name,
        last_name: profile.last_name,
        phone: profile.phone || '',
        date_of_birth: profile.date_of_birth || '',
        bio: profile.bio || '',
        location: profile.location || '',
        occupation: profile.occupation || '',
        emergency_contact_name: profile.emergency_contact_name || '',
        emergency_contact_phone: profile.emergency_contact_phone || '',
        languages: profile.languages || ['English'],
      });
    }
    setEditing(false);
    setFormErrors({});
    setError(null);
  };

  const handleProfilePictureUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      setUploadingPicture(true);
      setError(null);

      // Validate file
      const validation = userProfileService.validateFile(file, 'profile_picture');
      if (!validation.isValid) {
        setError(validation.error || 'Invalid file');
        return;
      }

      const profilePictureUrl = await userProfileService.uploadProfilePicture(file);
      
      // Update profile with new picture URL
      if (profile) {
        const updatedProfile = { ...profile, profile_picture: profilePictureUrl };
        setProfile(updatedProfile);
        
        if (onProfileUpdate) {
          onProfileUpdate(updatedProfile);
        }
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to upload profile picture');
    } finally {
      setUploadingPicture(false);
      // Reset file input
      event.target.value = '';
    }
  };

  const getVerificationBadge = (verified: boolean, type: string) => {
    if (verified) {
      return (
        <Badge className="bg-green-100 text-green-700 border-green-200">
          <Check className="h-3 w-3 mr-1" />
          Verified
        </Badge>
      );
    } else {
      return (
        <Badge variant="outline" className="text-orange-600 border-orange-200">
          <AlertCircle className="h-3 w-3 mr-1" />
          Unverified
        </Badge>
      );
    }
  };

  const getCompletionPercentage = () => {
    if (!profile) return 0;
    
    const fields = [
      profile.first_name,
      profile.last_name,
      profile.email,
      profile.phone,
      profile.bio,
      profile.location,
      profile.occupation,
      profile.profile_picture,
      profile.date_of_birth,
    ];
    
    const completedFields = fields.filter(field => field && field.trim().length > 0).length;
    return Math.round((completedFields / fields.length) * 100);
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="animate-pulse space-y-4">
            <div className="flex items-center space-x-4">
              <div className="w-20 h-20 bg-gray-200 rounded-full"></div>
              <div className="space-y-2">
                <div className="h-4 bg-gray-200 rounded w-48"></div>
                <div className="h-3 bg-gray-200 rounded w-32"></div>
              </div>
            </div>
            <div className="space-y-3">
              <div className="h-4 bg-gray-200 rounded"></div>
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!profile) {
    return (
      <Card className={className}>
        <CardContent className="p-6 text-center">
          <User className="h-12 w-12 mx-auto mb-4 text-gray-400" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Profile not found
          </h3>
          <p className="text-gray-600 mb-4">
            {error || 'Unable to load your profile information.'}
          </p>
          <Button onClick={fetchProfile}>
            Try Again
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Profile Header */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-6">
            <div className="flex items-start gap-4">
              {/* Profile Picture */}
              <div className="relative">
                <div className="w-20 h-20 rounded-full overflow-hidden bg-gray-200 flex items-center justify-center">
                  {profile.profile_picture ? (
                    <img
                      src={`http://localhost${profile.profile_picture}`}
                      alt={`${profile.first_name} ${profile.last_name}`}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <User className="h-8 w-8 text-gray-400" />
                  )}
                </div>
                <label className="absolute -bottom-1 -right-1 bg-sea-green-500 text-white rounded-full p-1.5 cursor-pointer hover:bg-sea-green-600 transition-colors">
                  <Camera className="h-3 w-3" />
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleProfilePictureUpload}
                    className="hidden"
                    disabled={uploadingPicture}
                  />
                </label>
                {uploadingPicture && (
                  <div className="absolute inset-0 bg-black bg-opacity-50 rounded-full flex items-center justify-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  </div>
                )}
              </div>

              {/* Profile Info */}
              <div className="flex-1">
                <h2 className="text-2xl font-bold text-gray-900">
                  {profile.first_name} {profile.last_name}
                </h2>
                <p className="text-gray-600 mb-2">{profile.email}</p>
                
                {/* Verification Badges */}
                <div className="flex flex-wrap gap-2 mb-3">
                  {getVerificationBadge(profile.email_verified, 'Email')}
                  {getVerificationBadge(profile.phone_verified, 'Phone')}
                  {getVerificationBadge(profile.id_verified, 'ID')}
                </div>

                {/* Stats */}
                <div className="flex items-center gap-4 text-sm text-gray-600">
                  <div className="flex items-center gap-1">
                    <Calendar className="h-4 w-4" />
                    {profile.total_bookings} bookings
                  </div>
                  <div className="flex items-center gap-1">
                    <Star className="h-4 w-4" />
                    {profile.total_reviews} reviews
                  </div>
                  {profile.average_rating > 0 && (
                    <div className="flex items-center gap-1">
                      <Star className="h-4 w-4 text-yellow-400 fill-current" />
                      {profile.average_rating.toFixed(1)} rating
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Profile Completion */}
            <div className="text-center">
              <div className="text-2xl font-bold text-sea-green-600 mb-1">
                {getCompletionPercentage()}%
              </div>
              <div className="text-sm text-gray-600">Profile Complete</div>
              <div className="w-16 h-2 bg-gray-200 rounded-full mt-2 mx-auto">
                <div 
                  className="h-full bg-sea-green-500 rounded-full transition-all duration-300"
                  style={{ width: `${getCompletionPercentage()}%` }}
                ></div>
              </div>
            </div>
          </div>

          {/* Error Message */}
          {error && (
            <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-center gap-2 text-red-700">
                <AlertCircle className="h-4 w-4" />
                <span className="text-sm">{error}</span>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Profile Details */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Profile Information</CardTitle>
            <div className="flex items-center gap-2">
              {editing ? (
                <>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleCancel}
                    disabled={saving}
                  >
                    <X className="h-4 w-4 mr-1" />
                    Cancel
                  </Button>
                  <Button
                    size="sm"
                    onClick={handleSave}
                    disabled={saving}
                    className="bg-sea-green-500 hover:bg-sea-green-600"
                  >
                    {saving ? (
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-1"></div>
                    ) : (
                      <Save className="h-4 w-4 mr-1" />
                    )}
                    Save
                  </Button>
                </>
              ) : (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setEditing(true)}
                >
                  <Edit className="h-4 w-4 mr-1" />
                  Edit
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="first_name">First Name</Label>
              {editing ? (
                <Input
                  id="first_name"
                  value={formData.first_name || ''}
                  onChange={(e) => handleInputChange('first_name', e.target.value)}
                  className={formErrors.first_name ? 'border-red-300' : ''}
                />
              ) : (
                <p className="mt-1 text-gray-900">{profile.first_name}</p>
              )}
              {formErrors.first_name && (
                <p className="text-sm text-red-600 mt-1">{formErrors.first_name}</p>
              )}
            </div>

            <div>
              <Label htmlFor="last_name">Last Name</Label>
              {editing ? (
                <Input
                  id="last_name"
                  value={formData.last_name || ''}
                  onChange={(e) => handleInputChange('last_name', e.target.value)}
                  className={formErrors.last_name ? 'border-red-300' : ''}
                />
              ) : (
                <p className="mt-1 text-gray-900">{profile.last_name}</p>
              )}
              {formErrors.last_name && (
                <p className="text-sm text-red-600 mt-1">{formErrors.last_name}</p>
              )}
            </div>

            <div>
              <Label htmlFor="phone">Phone Number</Label>
              {editing ? (
                <Input
                  id="phone"
                  type="tel"
                  value={formData.phone || ''}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  placeholder="+27 123 456 789"
                  className={formErrors.phone ? 'border-red-300' : ''}
                />
              ) : (
                <p className="mt-1 text-gray-900">{profile.phone || 'Not provided'}</p>
              )}
              {formErrors.phone && (
                <p className="text-sm text-red-600 mt-1">{formErrors.phone}</p>
              )}
            </div>

            <div>
              <Label htmlFor="date_of_birth">Date of Birth</Label>
              {editing ? (
                <Input
                  id="date_of_birth"
                  type="date"
                  value={formData.date_of_birth || ''}
                  onChange={(e) => handleInputChange('date_of_birth', e.target.value)}
                  className={formErrors.date_of_birth ? 'border-red-300' : ''}
                />
              ) : (
                <p className="mt-1 text-gray-900">
                  {profile.date_of_birth ? new Date(profile.date_of_birth).toLocaleDateString() : 'Not provided'}
                </p>
              )}
              {formErrors.date_of_birth && (
                <p className="text-sm text-red-600 mt-1">{formErrors.date_of_birth}</p>
              )}
            </div>

            <div>
              <Label htmlFor="location">Location</Label>
              {editing ? (
                <Input
                  id="location"
                  value={formData.location || ''}
                  onChange={(e) => handleInputChange('location', e.target.value)}
                  placeholder="City, Country"
                />
              ) : (
                <p className="mt-1 text-gray-900">{profile.location || 'Not provided'}</p>
              )}
            </div>

            <div>
              <Label htmlFor="occupation">Occupation</Label>
              {editing ? (
                <Input
                  id="occupation"
                  value={formData.occupation || ''}
                  onChange={(e) => handleInputChange('occupation', e.target.value)}
                  placeholder="Your profession"
                />
              ) : (
                <p className="mt-1 text-gray-900">{profile.occupation || 'Not provided'}</p>
              )}
            </div>
          </div>

          {/* Bio */}
          <div>
            <Label htmlFor="bio">Bio</Label>
            {editing ? (
              <Textarea
                id="bio"
                value={formData.bio || ''}
                onChange={(e) => handleInputChange('bio', e.target.value)}
                placeholder="Tell us about yourself..."
                rows={3}
                className={formErrors.bio ? 'border-red-300' : ''}
              />
            ) : (
              <p className="mt-1 text-gray-900">{profile.bio || 'No bio provided'}</p>
            )}
            {formErrors.bio && (
              <p className="text-sm text-red-600 mt-1">{formErrors.bio}</p>
            )}
          </div>

          {/* Emergency Contact */}
          <div className="border-t pt-6">
            <h4 className="font-medium text-gray-900 mb-4">Emergency Contact</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="emergency_contact_name">Contact Name</Label>
                {editing ? (
                  <Input
                    id="emergency_contact_name"
                    value={formData.emergency_contact_name || ''}
                    onChange={(e) => handleInputChange('emergency_contact_name', e.target.value)}
                    placeholder="Full name"
                  />
                ) : (
                  <p className="mt-1 text-gray-900">{profile.emergency_contact_name || 'Not provided'}</p>
                )}
              </div>

              <div>
                <Label htmlFor="emergency_contact_phone">Contact Phone</Label>
                {editing ? (
                  <Input
                    id="emergency_contact_phone"
                    type="tel"
                    value={formData.emergency_contact_phone || ''}
                    onChange={(e) => handleInputChange('emergency_contact_phone', e.target.value)}
                    placeholder="+27 123 456 789"
                    className={formErrors.emergency_contact_phone ? 'border-red-300' : ''}
                  />
                ) : (
                  <p className="mt-1 text-gray-900">{profile.emergency_contact_phone || 'Not provided'}</p>
                )}
                {formErrors.emergency_contact_phone && (
                  <p className="text-sm text-red-600 mt-1">{formErrors.emergency_contact_phone}</p>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Profile Verification */}
      <ProfileVerification
        profile={profile}
        onVerificationUpdate={fetchProfile}
      />
    </div>
  );
};
