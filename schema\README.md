# 🗄️ StayFinder Database Schema

**Complete database schema for StayFinder holiday rental platform**  
**Coverage:** All 9 provinces of South Africa  
**Database:** PostgreSQL (Supabase)  
**Status:** Production Ready

---

## 📁 File Organization

### **📋 Core Schema Files (Execute in Order)**

#### **1. `create-schema.sql`** ⭐ **REQUIRED FIRST**
- **Purpose:** Creates all database tables, enums, and sequences
- **Contains:** 12 tables, 11 enums, primary structure
- **Execute:** Copy entire file to Supabase SQL Editor and run
- **Dependencies:** None (run first)

#### **2. `create-indexes-functions.sql`** ⭐ **REQUIRED SECOND**
- **Purpose:** Adds performance indexes, functions, and triggers
- **Contains:** 40+ indexes, 8 functions, automatic triggers
- **Execute:** Copy entire file to Supabase SQL Editor and run
- **Dependencies:** Requires tables from step 1

#### **3. `create-rls-policies.sql`** ⭐ **REQUIRED THIRD**
- **Purpose:** Implements Row Level Security policies
- **Contains:** 25+ security policies, user access control
- **Execute:** Copy entire file to Supabase SQL Editor and run
- **Dependencies:** Requires tables and functions from steps 1-2

#### **4. `seed-data.sql`** 🌟 **RECOMMENDED**
- **Purpose:** Populates initial data and sample content
- **Contains:** Amenities, sample users, properties from all provinces
- **Execute:** Copy entire file to Supabase SQL Editor and run
- **Dependencies:** Requires complete schema from steps 1-3

---

## 📚 Documentation Files

### **`schema.md`** - Complete Schema Reference
- **Detailed documentation** of all tables and columns
- **Relationships and constraints** explained
- **Data types and validation rules**
- **Performance considerations**

### **`MANUAL_SCHEMA_DEPLOYMENT.md`** - Deployment Guide
- **Step-by-step deployment instructions**
- **Troubleshooting guide**
- **Verification steps**
- **Post-deployment configuration**

### **`SOUTH_AFRICA_SCHEMA_SUMMARY.md`** - National Coverage Overview
- **Provincial representation** in sample data
- **Market segments covered**
- **Geographic features and pricing**
- **Business impact analysis**

---

## 🌟 Optional Enhancement Files

### **`south-africa-locations.sql`** - Location Reference Data
- **Purpose:** Complete database of SA cities and provinces
- **Contains:** 90+ cities with GPS coordinates
- **Use Case:** Location dropdowns, validation, distance calculations
- **Execute:** Optional - run after main schema if needed

---

## 🚀 Quick Deployment Guide

### **Step 1: Access Supabase**
1. Go to [https://app.supabase.com](https://app.supabase.com)
2. Select your StayFinder project
3. Navigate to **SQL Editor**

### **Step 2: Execute Schema Files**
Execute in **exact order**:

```sql
-- 1. Create Schema (REQUIRED)
-- Copy and paste: create-schema.sql

-- 2. Add Indexes & Functions (REQUIRED)  
-- Copy and paste: create-indexes-functions.sql

-- 3. Setup Security (REQUIRED)
-- Copy and paste: create-rls-policies.sql

-- 4. Add Sample Data (RECOMMENDED)
-- Copy and paste: seed-data.sql

-- 5. Add Location Data (OPTIONAL)
-- Copy and paste: south-africa-locations.sql
```

### **Step 3: Verify Deployment**
Run verification queries from `MANUAL_SCHEMA_DEPLOYMENT.md`

---

## 📊 Schema Overview

### **Database Tables (12 total)**
- **users** - User profiles and authentication
- **properties** - Holiday rental listings  
- **amenities** - Property features master list
- **property_amenities** - Property-feature relationships
- **property_images** - Property photos and media
- **bookings** - Reservation management
- **booking_payments** - Payment tracking
- **reviews** - Property reviews and ratings
- **messages** - Host-guest communication
- **notifications** - System notifications
- **user_preferences** - User settings
- **property_availability** - Availability calendar

### **Key Features**
- **National coverage** - All 9 SA provinces
- **Performance optimized** - 40+ indexes
- **Security hardened** - Row Level Security
- **Real-time ready** - Supabase integration
- **Scalable design** - Production ready

---

## 🇿🇦 Sample Data Coverage

### **Properties by Province (9 total)**
- **Western Cape** - Luxury Villa (Cape Town)
- **Gauteng** - Executive Apartment (Johannesburg)
- **KwaZulu-Natal** - Beachfront Villa (Durban)
- **Eastern Cape** - Safari Lodge (Port Elizabeth)
- **Free State** - Historic Guesthouse (Bloemfontein)
- **Mpumalanga** - Safari Lodge (Nelspruit)
- **Limpopo** - Cultural Lodge (Polokwane)
- **North West** - Resort Apartment (Rustenburg)
- **Northern Cape** - Desert Retreat (Kimberley)

### **Users by Role (13 total)**
- **1 System Admin** - Platform management
- **9 Provincial Hosts** - One from each province
- **3 Sample Guests** - From major cities

### **Amenities (50+ items)**
- **Categorized by type** - Basic, kitchen, bathroom, entertainment, outdoor, safety, accessibility
- **South African specific** - Braai facilities, security features
- **Tourism focused** - Beach access, mountain views, safari features

---

## 🔧 Troubleshooting

### **Common Issues**
- **Permission errors** - Ensure using service role key
- **Table exists errors** - Normal if re-running, tables will be updated
- **RLS policy errors** - Ensure tables exist before creating policies

### **Verification Commands**
```sql
-- Check tables created
SELECT table_name FROM information_schema.tables WHERE table_schema = 'public';

-- Check sample data loaded
SELECT COUNT(*) FROM amenities;
SELECT COUNT(*) FROM properties;
SELECT COUNT(*) FROM users;
```

---

## 📞 Support

### **Documentation References**
- **Complete schema details** - See `schema.md`
- **Deployment help** - See `MANUAL_SCHEMA_DEPLOYMENT.md`
- **National coverage info** - See `SOUTH_AFRICA_SCHEMA_SUMMARY.md`

### **File Dependencies**
```
create-schema.sql (FIRST)
    ↓
create-indexes-functions.sql (SECOND)
    ↓
create-rls-policies.sql (THIRD)
    ↓
seed-data.sql (FOURTH - Optional)
    ↓
south-africa-locations.sql (FIFTH - Optional)
```

---

## ✅ Success Criteria

After successful deployment, you should have:
- ✅ **12 database tables** created
- ✅ **40+ performance indexes** active
- ✅ **Row Level Security** enabled
- ✅ **50+ amenities** loaded
- ✅ **9 sample properties** from all provinces
- ✅ **13 sample users** ready for testing

---

**🎉 Your StayFinder database schema is ready to power holiday rentals across all of South Africa!**

*From Cape Town to Johannesburg, from Durban to Kimberley - comprehensive coverage for the entire Rainbow Nation.*
