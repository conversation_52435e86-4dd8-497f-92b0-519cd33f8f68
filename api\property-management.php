<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: http://localhost:8081');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Credentials: true');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once 'config.php';
require_once 'auth.php';

function getHostProperties($hostId, $limit = 20, $offset = 0, $status = null) {
    global $pdo;
    
    try {
        $whereClause = "WHERE p.host_id = ?";
        $params = [$hostId];
        
        if ($status && $status !== 'all') {
            $whereClause .= " AND p.status = ?";
            $params[] = $status;
        }
        
        $stmt = $pdo->prepare("
            SELECT 
                p.id,
                p.title,
                p.description,
                p.location,
                p.property_type,
                p.max_guests,
                p.bedrooms,
                p.bathrooms,
                p.price_per_night,
                p.cleaning_fee,
                p.status,
                p.created_at,
                p.updated_at,
                p.images,
                p.amenities,
                COALESCE(AVG(r.rating), 0) as average_rating,
                COUNT(DISTINCT r.id) as review_count,
                COUNT(DISTINCT b.id) as total_bookings,
                COUNT(DISTINCT CASE WHEN b.booking_status = 'confirmed' THEN b.id END) as confirmed_bookings,
                COALESCE(SUM(CASE WHEN b.booking_status = 'confirmed' THEN b.total_amount END), 0) as total_revenue,
                MAX(b.check_out_date) as last_booking_date
            FROM properties p
            LEFT JOIN reviews r ON p.id = r.property_id
            LEFT JOIN bookings b ON p.id = b.property_id
            $whereClause
            GROUP BY p.id
            ORDER BY p.created_at DESC
            LIMIT ? OFFSET ?
        ");
        
        $params[] = $limit;
        $params[] = $offset;
        $stmt->execute($params);
        $properties = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Format properties
        foreach ($properties as &$property) {
            $property['images'] = $property['images'] ? json_decode($property['images'], true) : [];
            $property['amenities'] = $property['amenities'] ? json_decode($property['amenities'], true) : [];
            $property['average_rating'] = round((float)$property['average_rating'], 1);
            $property['total_revenue'] = (float)$property['total_revenue'];
            $property['price_per_night'] = (float)$property['price_per_night'];
            $property['cleaning_fee'] = (float)$property['cleaning_fee'];
        }
        
        return $properties;
        
    } catch (Exception $e) {
        error_log("Error getting host properties: " . $e->getMessage());
        return [];
    }
}

function getPropertyAnalytics($propertyId, $hostId, $period = '30d') {
    global $pdo;
    
    try {
        // Verify property ownership
        $stmt = $pdo->prepare("SELECT id FROM properties WHERE id = ? AND host_id = ?");
        $stmt->execute([$propertyId, $hostId]);
        if (!$stmt->fetch()) {
            throw new Exception("Property not found or access denied");
        }
        
        // Calculate date range
        $endDate = date('Y-m-d');
        switch ($period) {
            case '7d':
                $startDate = date('Y-m-d', strtotime('-7 days'));
                break;
            case '30d':
                $startDate = date('Y-m-d', strtotime('-30 days'));
                break;
            case '90d':
                $startDate = date('Y-m-d', strtotime('-90 days'));
                break;
            case '1y':
                $startDate = date('Y-m-d', strtotime('-1 year'));
                break;
            default:
                $startDate = date('Y-m-d', strtotime('-30 days'));
        }
        
        // Get booking analytics
        $stmt = $pdo->prepare("
            SELECT 
                COUNT(*) as total_bookings,
                COUNT(CASE WHEN booking_status = 'confirmed' THEN 1 END) as confirmed_bookings,
                COUNT(CASE WHEN booking_status = 'cancelled' THEN 1 END) as cancelled_bookings,
                COALESCE(SUM(CASE WHEN booking_status = 'confirmed' THEN total_amount END), 0) as total_revenue,
                COALESCE(AVG(CASE WHEN booking_status = 'confirmed' THEN total_amount END), 0) as average_booking_value,
                COALESCE(AVG(CASE WHEN booking_status = 'confirmed' THEN DATEDIFF(check_out_date, check_in_date) END), 0) as average_stay_length
            FROM bookings 
            WHERE property_id = ? AND created_at >= ? AND created_at <= ?
        ");
        $stmt->execute([$propertyId, $startDate, $endDate]);
        $bookingStats = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // Get daily revenue data for chart
        $stmt = $pdo->prepare("
            SELECT 
                DATE(created_at) as date,
                COUNT(*) as bookings,
                COALESCE(SUM(CASE WHEN booking_status = 'confirmed' THEN total_amount END), 0) as revenue
            FROM bookings 
            WHERE property_id = ? AND created_at >= ? AND created_at <= ?
            GROUP BY DATE(created_at)
            ORDER BY date ASC
        ");
        $stmt->execute([$propertyId, $startDate, $endDate]);
        $dailyData = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Get occupancy rate
        $stmt = $pdo->prepare("
            SELECT 
                COUNT(DISTINCT DATE(check_in_date)) as booked_nights
            FROM bookings 
            WHERE property_id = ? AND booking_status = 'confirmed' 
            AND check_in_date >= ? AND check_out_date <= ?
        ");
        $stmt->execute([$propertyId, $startDate, $endDate]);
        $occupancyData = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $totalDays = (strtotime($endDate) - strtotime($startDate)) / (60 * 60 * 24);
        $occupancyRate = $totalDays > 0 ? ($occupancyData['booked_nights'] / $totalDays) * 100 : 0;
        
        return [
            'period' => $period,
            'start_date' => $startDate,
            'end_date' => $endDate,
            'booking_stats' => [
                'total_bookings' => (int)$bookingStats['total_bookings'],
                'confirmed_bookings' => (int)$bookingStats['confirmed_bookings'],
                'cancelled_bookings' => (int)$bookingStats['cancelled_bookings'],
                'total_revenue' => (float)$bookingStats['total_revenue'],
                'average_booking_value' => (float)$bookingStats['average_booking_value'],
                'average_stay_length' => (float)$bookingStats['average_stay_length'],
                'occupancy_rate' => round($occupancyRate, 1)
            ],
            'daily_data' => $dailyData
        ];
        
    } catch (Exception $e) {
        error_log("Error getting property analytics: " . $e->getMessage());
        throw $e;
    }
}

function getHostDashboardStats($hostId) {
    global $pdo;
    
    try {
        // Get overall stats
        $stmt = $pdo->prepare("
            SELECT 
                COUNT(DISTINCT p.id) as total_properties,
                COUNT(DISTINCT CASE WHEN p.status = 'active' THEN p.id END) as active_properties,
                COUNT(DISTINCT b.id) as total_bookings,
                COUNT(DISTINCT CASE WHEN b.booking_status = 'confirmed' THEN b.id END) as confirmed_bookings,
                COALESCE(SUM(CASE WHEN b.booking_status = 'confirmed' THEN b.total_amount END), 0) as total_revenue,
                COALESCE(AVG(r.rating), 0) as average_rating,
                COUNT(DISTINCT r.id) as total_reviews
            FROM properties p
            LEFT JOIN bookings b ON p.id = b.property_id
            LEFT JOIN reviews r ON p.id = r.property_id
            WHERE p.host_id = ?
        ");
        $stmt->execute([$hostId]);
        $stats = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // Get recent bookings
        $stmt = $pdo->prepare("
            SELECT 
                b.id,
                b.property_id,
                b.guest_id,
                b.check_in_date,
                b.check_out_date,
                b.total_amount,
                b.booking_status,
                b.created_at,
                p.title as property_title,
                CONCAT(u.first_name, ' ', u.last_name) as guest_name
            FROM bookings b
            JOIN properties p ON b.property_id = p.id
            JOIN users u ON b.guest_id = u.id
            WHERE p.host_id = ?
            ORDER BY b.created_at DESC
            LIMIT 5
        ");
        $stmt->execute([$hostId]);
        $recentBookings = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Get monthly revenue for the last 6 months
        $stmt = $pdo->prepare("
            SELECT 
                DATE_FORMAT(b.created_at, '%Y-%m') as month,
                COALESCE(SUM(CASE WHEN b.booking_status = 'confirmed' THEN b.total_amount END), 0) as revenue,
                COUNT(CASE WHEN b.booking_status = 'confirmed' THEN 1 END) as bookings
            FROM bookings b
            JOIN properties p ON b.property_id = p.id
            WHERE p.host_id = ? AND b.created_at >= DATE_SUB(NOW(), INTERVAL 6 MONTH)
            GROUP BY DATE_FORMAT(b.created_at, '%Y-%m')
            ORDER BY month ASC
        ");
        $stmt->execute([$hostId]);
        $monthlyRevenue = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        return [
            'overview' => [
                'total_properties' => (int)$stats['total_properties'],
                'active_properties' => (int)$stats['active_properties'],
                'total_bookings' => (int)$stats['total_bookings'],
                'confirmed_bookings' => (int)$stats['confirmed_bookings'],
                'total_revenue' => (float)$stats['total_revenue'],
                'average_rating' => round((float)$stats['average_rating'], 1),
                'total_reviews' => (int)$stats['total_reviews']
            ],
            'recent_bookings' => $recentBookings,
            'monthly_revenue' => $monthlyRevenue
        ];
        
    } catch (Exception $e) {
        error_log("Error getting host dashboard stats: " . $e->getMessage());
        throw $e;
    }
}

function updatePropertyStatus($propertyId, $hostId, $status) {
    global $pdo;
    
    try {
        // Verify property ownership
        $stmt = $pdo->prepare("SELECT id FROM properties WHERE id = ? AND host_id = ?");
        $stmt->execute([$propertyId, $hostId]);
        if (!$stmt->fetch()) {
            throw new Exception("Property not found or access denied");
        }
        
        // Validate status
        $validStatuses = ['active', 'inactive', 'pending', 'suspended'];
        if (!in_array($status, $validStatuses)) {
            throw new Exception("Invalid status");
        }
        
        // Update status
        $stmt = $pdo->prepare("
            UPDATE properties 
            SET status = ?, updated_at = NOW()
            WHERE id = ? AND host_id = ?
        ");
        $stmt->execute([$status, $propertyId, $hostId]);
        
        return $stmt->rowCount() > 0;
        
    } catch (Exception $e) {
        error_log("Error updating property status: " . $e->getMessage());
        throw $e;
    }
}

// Handle the request
$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['action'] ?? '';

try {
    $user = getCurrentUser();
    if (!$user) {
        http_response_code(401);
        echo json_encode(['error' => 'Authentication required']);
        exit;
    }
    
    switch ($method) {
        case 'GET':
            switch ($action) {
                case 'properties':
                    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 20;
                    $offset = isset($_GET['offset']) ? (int)$_GET['offset'] : 0;
                    $status = $_GET['status'] ?? null;
                    
                    $properties = getHostProperties($user['id'], $limit, $offset, $status);
                    
                    echo json_encode([
                        'success' => true,
                        'data' => $properties
                    ]);
                    break;
                    
                case 'analytics':
                    $propertyId = $_GET['property_id'] ?? '';
                    $period = $_GET['period'] ?? '30d';
                    
                    if (empty($propertyId)) {
                        http_response_code(400);
                        echo json_encode(['error' => 'Property ID is required']);
                        exit;
                    }
                    
                    $analytics = getPropertyAnalytics($propertyId, $user['id'], $period);
                    
                    echo json_encode([
                        'success' => true,
                        'data' => $analytics
                    ]);
                    break;
                    
                case 'dashboard':
                    $stats = getHostDashboardStats($user['id']);
                    
                    echo json_encode([
                        'success' => true,
                        'data' => $stats
                    ]);
                    break;
                    
                default:
                    http_response_code(400);
                    echo json_encode(['error' => 'Invalid action']);
                    break;
            }
            break;
            
        case 'PUT':
            if ($action === 'status') {
                $propertyId = $_GET['property_id'] ?? '';
                if (empty($propertyId)) {
                    http_response_code(400);
                    echo json_encode(['error' => 'Property ID is required']);
                    exit;
                }
                
                $input = json_decode(file_get_contents('php://input'), true);
                $status = $input['status'] ?? '';
                
                if (empty($status)) {
                    http_response_code(400);
                    echo json_encode(['error' => 'Status is required']);
                    exit;
                }
                
                $success = updatePropertyStatus($propertyId, $user['id'], $status);
                
                if ($success) {
                    echo json_encode([
                        'success' => true,
                        'message' => 'Property status updated successfully'
                    ]);
                } else {
                    http_response_code(500);
                    echo json_encode(['error' => 'Failed to update property status']);
                }
            } else {
                http_response_code(400);
                echo json_encode(['error' => 'Invalid action']);
            }
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            break;
    }
    
} catch (Exception $e) {
    error_log("Property Management API error: " . $e->getMessage());
    http_response_code(400);
    echo json_encode(['error' => $e->getMessage()]);
}
?>
