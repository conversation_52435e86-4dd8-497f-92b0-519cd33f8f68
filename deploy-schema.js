#!/usr/bin/env node

/**
 * StayFinder Database Schema Deployment Script
 * Deploys the complete database schema to Supabase
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { config } from 'dotenv';
config();

// ANSI color codes
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  magenta: '\x1b[35m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, colors.green);
}

function logError(message) {
  log(`❌ ${message}`, colors.red);
}

function logWarning(message) {
  log(`⚠️  ${message}`, colors.yellow);
}

function logInfo(message) {
  log(`ℹ️  ${message}`, colors.blue);
}

// Initialize Supabase admin client
const supabaseAdmin = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

// Deployment steps
const deploymentSteps = [
  {
    name: 'Create Schema',
    file: 'create-schema.sql',
    description: 'Creating tables, enums, and basic structure'
  },
  {
    name: 'Create Indexes & Functions',
    file: 'create-indexes-functions.sql',
    description: 'Adding indexes, functions, and triggers for performance'
  },
  {
    name: 'Setup Row Level Security',
    file: 'create-rls-policies.sql',
    description: 'Implementing security policies'
  },
  {
    name: 'Seed Initial Data',
    file: 'seed-data.sql',
    description: 'Adding initial amenities and sample data'
  }
];

// Function to execute SQL file
async function executeSQLFile(filePath, stepName) {
  try {
    logInfo(`Reading ${filePath}...`);
    
    if (!fs.existsSync(filePath)) {
      throw new Error(`SQL file not found: ${filePath}`);
    }
    
    const sqlContent = fs.readFileSync(filePath, 'utf8');
    
    if (!sqlContent.trim()) {
      throw new Error(`SQL file is empty: ${filePath}`);
    }
    
    logInfo(`Executing ${stepName}...`);
    
    // Split SQL content by semicolons and execute each statement
    const statements = sqlContent
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
    
    let successCount = 0;
    let errorCount = 0;
    const errors = [];
    
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      
      if (statement.length === 0) continue;
      
      try {
        const { error } = await supabaseAdmin.rpc('exec_sql', { 
          sql: statement + ';' 
        });
        
        if (error) {
          // Try alternative method for statements that don't work with rpc
          const { error: directError } = await supabaseAdmin
            .from('_temp_table_that_does_not_exist')
            .select('*');
          
          // If it's a table creation or similar DDL, we need to use a different approach
          logWarning(`Statement ${i + 1} may need manual execution: ${statement.substring(0, 50)}...`);
          errorCount++;
          errors.push({
            statement: statement.substring(0, 100) + '...',
            error: error.message
          });
        } else {
          successCount++;
        }
      } catch (execError) {
        errorCount++;
        errors.push({
          statement: statement.substring(0, 100) + '...',
          error: execError.message
        });
      }
    }
    
    logInfo(`Processed ${statements.length} statements: ${successCount} successful, ${errorCount} errors`);
    
    if (errors.length > 0) {
      logWarning(`Some statements need manual execution in Supabase SQL Editor:`);
      errors.forEach((err, idx) => {
        logWarning(`${idx + 1}. ${err.statement}`);
        logWarning(`   Error: ${err.error}`);
      });
    }
    
    return {
      success: successCount > 0,
      totalStatements: statements.length,
      successCount,
      errorCount,
      errors
    };
    
  } catch (error) {
    logError(`Failed to execute ${stepName}: ${error.message}`);
    return {
      success: false,
      error: error.message
    };
  }
}

// Function to verify schema deployment
async function verifyDeployment() {
  logInfo('Verifying schema deployment...');
  
  const verificationQueries = [
    {
      name: 'Check Tables',
      test: async () => {
        const { data, error } = await supabaseAdmin
          .from('information_schema.tables')
          .select('table_name')
          .eq('table_schema', 'public');
        
        if (error) throw error;
        
        const expectedTables = [
          'users', 'properties', 'amenities', 'property_amenities',
          'property_images', 'bookings', 'booking_payments', 'reviews',
          'messages', 'notifications', 'user_preferences', 'property_availability'
        ];
        
        const existingTables = data.map(t => t.table_name);
        const missingTables = expectedTables.filter(t => !existingTables.includes(t));
        
        return {
          success: missingTables.length === 0,
          details: missingTables.length === 0 
            ? `All ${expectedTables.length} tables created successfully`
            : `Missing tables: ${missingTables.join(', ')}`
        };
      }
    },
    {
      name: 'Check Amenities Data',
      test: async () => {
        const { data, error } = await supabaseAdmin
          .from('amenities')
          .select('count', { count: 'exact' });
        
        if (error) throw error;
        
        return {
          success: data.length > 0,
          details: `${data.length} amenities loaded`
        };
      }
    }
  ];
  
  const results = [];
  
  for (const query of verificationQueries) {
    try {
      const result = await query.test();
      results.push({
        name: query.name,
        ...result
      });
      
      if (result.success) {
        logSuccess(`${query.name}: ${result.details}`);
      } else {
        logError(`${query.name}: ${result.details}`);
      }
    } catch (error) {
      results.push({
        name: query.name,
        success: false,
        details: error.message
      });
      logError(`${query.name}: ${error.message}`);
    }
  }
  
  return results;
}

// Main deployment function
async function deploySchema() {
  log('🚀 STAYFINDER DATABASE SCHEMA DEPLOYMENT', colors.bright);
  log('='.repeat(60), colors.cyan);
  
  const startTime = Date.now();
  const deploymentResults = [];
  
  // Check Supabase connection
  logInfo('Checking Supabase connection...');
  try {
    const { data, error } = await supabaseAdmin.rpc('version');
    if (error && !error.message.includes('function version() does not exist')) {
      throw new Error(`Connection failed: ${error.message}`);
    }
    logSuccess('Connected to Supabase successfully');
  } catch (error) {
    logError(`Failed to connect to Supabase: ${error.message}`);
    process.exit(1);
  }
  
  // Execute deployment steps
  for (const step of deploymentSteps) {
    log(`\n📋 ${step.name}`, colors.magenta);
    log('-'.repeat(40), colors.cyan);
    logInfo(step.description);
    
    const result = await executeSQLFile(step.file, step.name);
    deploymentResults.push({
      step: step.name,
      ...result
    });
    
    if (result.success) {
      logSuccess(`${step.name} completed`);
    } else {
      logError(`${step.name} failed`);
    }
  }
  
  // Verify deployment
  log('\n🔍 VERIFICATION', colors.magenta);
  log('-'.repeat(40), colors.cyan);
  const verificationResults = await verifyDeployment();
  
  const duration = ((Date.now() - startTime) / 1000).toFixed(2);
  
  // Generate summary
  log('\n' + '='.repeat(60), colors.cyan);
  log('📊 DEPLOYMENT SUMMARY', colors.bright);
  log('='.repeat(60), colors.cyan);
  
  const successfulSteps = deploymentResults.filter(r => r.success).length;
  const totalSteps = deploymentResults.length;
  
  logInfo(`Duration: ${duration}s`);
  logInfo(`Steps completed: ${successfulSteps}/${totalSteps}`);
  
  deploymentResults.forEach(result => {
    if (result.success) {
      logSuccess(`${result.step}: SUCCESS`);
    } else {
      logError(`${result.step}: FAILED`);
    }
  });
  
  const verificationPassed = verificationResults.filter(r => r.success).length;
  const totalVerifications = verificationResults.length;
  
  logInfo(`Verification: ${verificationPassed}/${totalVerifications} passed`);
  
  // Final status
  if (successfulSteps === totalSteps && verificationPassed === totalVerifications) {
    log('\n🎉 SCHEMA DEPLOYMENT SUCCESSFUL!', colors.green);
    log('✅ Your StayFinder database is ready for use', colors.green);
    log('\n📋 Next Steps:', colors.blue);
    log('1. Update your application code to use the new schema', colors.blue);
    log('2. Test all CRUD operations', colors.blue);
    log('3. Configure real-time subscriptions for required tables', colors.blue);
    log('4. Set up your frontend to connect to Supabase', colors.blue);
  } else {
    log('\n⚠️  DEPLOYMENT COMPLETED WITH ISSUES', colors.yellow);
    log('Some steps may need manual execution in Supabase SQL Editor', colors.yellow);
    log('\n📋 Manual Steps Required:', colors.yellow);
    log('1. Open Supabase Dashboard > SQL Editor', colors.yellow);
    log('2. Execute any failed SQL statements manually', colors.yellow);
    log('3. Verify all tables and functions are created', colors.yellow);
    log('4. Test the schema with sample queries', colors.yellow);
  }
  
  // Save deployment report
  const report = {
    timestamp: new Date().toISOString(),
    duration: `${duration}s`,
    deploymentResults,
    verificationResults,
    summary: {
      totalSteps,
      successfulSteps,
      verificationPassed,
      totalVerifications
    }
  };
  
  const reportPath = 'schema-deployment-report.json';
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  logInfo(`Deployment report saved to: ${reportPath}`);
  
  process.exit(successfulSteps === totalSteps ? 0 : 1);
}

// Run deployment
deploySchema().catch(error => {
  logError(`Deployment error: ${error.message}`);
  console.error(error);
  process.exit(1);
});
