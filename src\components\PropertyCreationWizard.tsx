import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { 
  ChevronLeft, 
  ChevronRight, 
  Home,
  MapPin,
  Camera,
  DollarSign,
  Settings,
  CheckCircle,
  AlertCircle,
  Upload,
  X,
  Plus,
  Star,
  Wifi,
  Car,
  Coffee,
  Waves,
  Mountain,
  Trees,
  Building,
  Users,
  Bed,
  Bath,
  Calendar,
  Clock
} from 'lucide-react';
import { 
  HoverAnimation, 
  SlideIn, 
  StaggeredAnimation,
  ScaleIn 
} from '@/components/ui/page-transitions';
import { QuickTooltip } from '@/components/ui/enhanced-tooltip';

interface PropertyFormData {
  // Basic Information
  title: string;
  description: string;
  propertyType: string;
  location: string;
  address: string;
  coordinates: { latitude: number; longitude: number } | null;
  
  // Capacity
  maxGuests: number;
  bedrooms: number;
  bathrooms: number;
  
  // Amenities
  amenities: string[];
  
  // Pricing
  basePrice: number;
  cleaningFee: number;
  weeklyDiscount: number;
  monthlyDiscount: number;
  
  // Rules & Policies
  checkInTime: string;
  checkOutTime: string;
  houseRules: string[];
  cancellationPolicy: string;
  
  // Images
  images: File[];
  imageUrls: string[];
}

interface PropertyCreationWizardProps {
  onComplete: (propertyData: PropertyFormData) => void;
  onCancel: () => void;
  initialData?: Partial<PropertyFormData>;
}

const PROPERTY_TYPES = [
  { id: 'apartment', label: 'Apartment', icon: Building },
  { id: 'house', label: 'House', icon: Home },
  { id: 'villa', label: 'Villa', icon: Mountain },
  { id: 'cottage', label: 'Cottage', icon: Trees },
  { id: 'guesthouse', label: 'Guesthouse', icon: Building }
];

const AMENITIES_LIST = [
  { id: 'wifi', label: 'WiFi', icon: Wifi, category: 'essentials' },
  { id: 'parking', label: 'Free Parking', icon: Car, category: 'essentials' },
  { id: 'kitchen', label: 'Kitchen', icon: Coffee, category: 'essentials' },
  { id: 'pool', label: 'Swimming Pool', icon: Waves, category: 'luxury' },
  { id: 'aircon', label: 'Air Conditioning', icon: Star, category: 'comfort' },
  { id: 'heating', label: 'Heating', icon: Star, category: 'comfort' },
  { id: 'tv', label: 'TV', icon: Star, category: 'entertainment' },
  { id: 'washer', label: 'Washing Machine', icon: Star, category: 'essentials' },
  { id: 'dryer', label: 'Dryer', icon: Star, category: 'essentials' },
  { id: 'gym', label: 'Gym Access', icon: Star, category: 'luxury' },
  { id: 'spa', label: 'Spa', icon: Star, category: 'luxury' },
  { id: 'garden', label: 'Garden', icon: Trees, category: 'outdoor' },
  { id: 'balcony', label: 'Balcony', icon: Star, category: 'outdoor' },
  { id: 'ocean_view', label: 'Ocean View', icon: Waves, category: 'views' },
  { id: 'mountain_view', label: 'Mountain View', icon: Mountain, category: 'views' }
];

const CANCELLATION_POLICIES = [
  { id: 'flexible', label: 'Flexible', description: 'Full refund 1 day prior to arrival' },
  { id: 'moderate', label: 'Moderate', description: 'Full refund 5 days prior to arrival' },
  { id: 'strict', label: 'Strict', description: 'Full refund 14 days prior to arrival' }
];

export const PropertyCreationWizard: React.FC<PropertyCreationWizardProps> = ({
  onComplete,
  onCancel,
  initialData
}) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<PropertyFormData>({
    title: '',
    description: '',
    propertyType: '',
    location: '',
    address: '',
    coordinates: null,
    maxGuests: 1,
    bedrooms: 1,
    bathrooms: 1,
    amenities: [],
    basePrice: 0,
    cleaningFee: 0,
    weeklyDiscount: 0,
    monthlyDiscount: 0,
    checkInTime: '15:00',
    checkOutTime: '11:00',
    houseRules: [],
    cancellationPolicy: 'moderate',
    images: [],
    imageUrls: [],
    ...initialData
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const steps = [
    { id: 1, title: 'Basic Info', icon: Home, description: 'Property details and location' },
    { id: 2, title: 'Capacity', icon: Users, description: 'Guests, bedrooms, and bathrooms' },
    { id: 3, title: 'Amenities', icon: Star, description: 'Features and facilities' },
    { id: 4, title: 'Photos', icon: Camera, description: 'Upload property images' },
    { id: 5, title: 'Pricing', icon: DollarSign, description: 'Set your rates and fees' },
    { id: 6, title: 'Rules', icon: Settings, description: 'House rules and policies' },
    { id: 7, title: 'Review', icon: CheckCircle, description: 'Review and submit' }
  ];

  const updateFormData = (field: keyof PropertyFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when field is updated
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateStep = (step: number): boolean => {
    const newErrors: Record<string, string> = {};

    switch (step) {
      case 1:
        if (!formData.title.trim()) newErrors.title = 'Property title is required';
        if (!formData.description.trim()) newErrors.description = 'Description is required';
        if (!formData.propertyType) newErrors.propertyType = 'Property type is required';
        if (!formData.location.trim()) newErrors.location = 'Location is required';
        break;
      case 2:
        if (formData.maxGuests < 1) newErrors.maxGuests = 'Must accommodate at least 1 guest';
        if (formData.bedrooms < 1) newErrors.bedrooms = 'Must have at least 1 bedroom';
        if (formData.bathrooms < 1) newErrors.bathrooms = 'Must have at least 1 bathroom';
        break;
      case 5:
        if (formData.basePrice <= 0) newErrors.basePrice = 'Base price must be greater than 0';
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const nextStep = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, steps.length));
    }
  };

  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  const handleSubmit = async () => {
    if (!validateStep(currentStep)) return;

    setIsSubmitting(true);
    try {
      await onComplete(formData);
    } catch (error) {
      console.error('Error submitting property:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const toggleAmenity = (amenityId: string) => {
    const newAmenities = formData.amenities.includes(amenityId)
      ? formData.amenities.filter(id => id !== amenityId)
      : [...formData.amenities, amenityId];
    updateFormData('amenities', newAmenities);
  };

  const addHouseRule = () => {
    const rule = prompt('Enter a house rule:');
    if (rule && rule.trim()) {
      updateFormData('houseRules', [...formData.houseRules, rule.trim()]);
    }
  };

  const removeHouseRule = (index: number) => {
    updateFormData('houseRules', formData.houseRules.filter((_, i) => i !== index));
  };

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    updateFormData('images', [...formData.images, ...files]);
    
    // Create preview URLs
    const newUrls = files.map(file => URL.createObjectURL(file));
    updateFormData('imageUrls', [...formData.imageUrls, ...newUrls]);
  };

  const removeImage = (index: number) => {
    const newImages = formData.images.filter((_, i) => i !== index);
    const newUrls = formData.imageUrls.filter((_, i) => i !== index);
    updateFormData('images', newImages);
    updateFormData('imageUrls', newUrls);
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Property Title *
              </label>
              <Input
                value={formData.title}
                onChange={(e) => updateFormData('title', e.target.value)}
                placeholder="e.g., Beautiful Beachfront Villa in Cape Town"
                className={errors.title ? 'border-red-500' : ''}
              />
              {errors.title && <p className="text-red-500 text-sm mt-1">{errors.title}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Description *
              </label>
              <Textarea
                value={formData.description}
                onChange={(e) => updateFormData('description', e.target.value)}
                placeholder="Describe your property, its unique features, and what makes it special..."
                rows={4}
                className={errors.description ? 'border-red-500' : ''}
              />
              {errors.description && <p className="text-red-500 text-sm mt-1">{errors.description}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Property Type *
              </label>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                {PROPERTY_TYPES.map((type) => {
                  const Icon = type.icon;
                  return (
                    <HoverAnimation key={type.id} type="scale">
                      <button
                        type="button"
                        onClick={() => updateFormData('propertyType', type.id)}
                        className={`p-4 border-2 rounded-lg text-center transition-all ${
                          formData.propertyType === type.id
                            ? 'border-sea-green-500 bg-sea-green-50 text-sea-green-700'
                            : 'border-gray-200 hover:border-sea-green-300'
                        }`}
                      >
                        <Icon className="h-8 w-8 mx-auto mb-2" />
                        <span className="font-medium">{type.label}</span>
                      </button>
                    </HoverAnimation>
                  );
                })}
              </div>
              {errors.propertyType && <p className="text-red-500 text-sm mt-1">{errors.propertyType}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Location *
              </label>
              <Input
                value={formData.location}
                onChange={(e) => updateFormData('location', e.target.value)}
                placeholder="e.g., Cape Town, Western Cape"
                className={errors.location ? 'border-red-500' : ''}
              />
              {errors.location && <p className="text-red-500 text-sm mt-1">{errors.location}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Full Address
              </label>
              <Input
                value={formData.address}
                onChange={(e) => updateFormData('address', e.target.value)}
                placeholder="Street address (optional, for precise location)"
              />
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Maximum Guests *
                </label>
                <div className="flex items-center space-x-3">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => updateFormData('maxGuests', Math.max(1, formData.maxGuests - 1))}
                  >
                    -
                  </Button>
                  <span className="text-xl font-semibold w-12 text-center">{formData.maxGuests}</span>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => updateFormData('maxGuests', formData.maxGuests + 1)}
                  >
                    +
                  </Button>
                </div>
                {errors.maxGuests && <p className="text-red-500 text-sm mt-1">{errors.maxGuests}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Bedrooms *
                </label>
                <div className="flex items-center space-x-3">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => updateFormData('bedrooms', Math.max(1, formData.bedrooms - 1))}
                  >
                    -
                  </Button>
                  <span className="text-xl font-semibold w-12 text-center">{formData.bedrooms}</span>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => updateFormData('bedrooms', formData.bedrooms + 1)}
                  >
                    +
                  </Button>
                </div>
                {errors.bedrooms && <p className="text-red-500 text-sm mt-1">{errors.bedrooms}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Bathrooms *
                </label>
                <div className="flex items-center space-x-3">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => updateFormData('bathrooms', Math.max(1, formData.bathrooms - 1))}
                  >
                    -
                  </Button>
                  <span className="text-xl font-semibold w-12 text-center">{formData.bathrooms}</span>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => updateFormData('bathrooms', formData.bathrooms + 1)}
                  >
                    +
                  </Button>
                </div>
                {errors.bathrooms && <p className="text-red-500 text-sm mt-1">{errors.bathrooms}</p>}
              </div>
            </div>

            <div className="bg-blue-50 p-6 rounded-lg">
              <h4 className="font-semibold text-blue-900 mb-2">Capacity Summary</h4>
              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <Users className="h-8 w-8 text-blue-600 mx-auto mb-1" />
                  <div className="text-2xl font-bold text-blue-900">{formData.maxGuests}</div>
                  <div className="text-sm text-blue-700">Guests</div>
                </div>
                <div>
                  <Bed className="h-8 w-8 text-blue-600 mx-auto mb-1" />
                  <div className="text-2xl font-bold text-blue-900">{formData.bedrooms}</div>
                  <div className="text-sm text-blue-700">Bedrooms</div>
                </div>
                <div>
                  <Bath className="h-8 w-8 text-blue-600 mx-auto mb-1" />
                  <div className="text-2xl font-bold text-blue-900">{formData.bathrooms}</div>
                  <div className="text-sm text-blue-700">Bathrooms</div>
                </div>
              </div>
            </div>
          </div>
        );

      case 3:
        const amenitiesByCategory = AMENITIES_LIST.reduce((acc, amenity) => {
          if (!acc[amenity.category]) acc[amenity.category] = [];
          acc[amenity.category].push(amenity);
          return acc;
        }, {} as Record<string, typeof AMENITIES_LIST>);

        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">What amenities do you offer?</h3>
              <p className="text-gray-600">Select all amenities available at your property</p>
            </div>

            {Object.entries(amenitiesByCategory).map(([category, amenities]) => (
              <div key={category}>
                <h4 className="font-semibold text-gray-900 mb-3 capitalize">
                  {category.replace('_', ' ')}
                </h4>
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                  <StaggeredAnimation delay={50}>
                    {amenities.map((amenity) => {
                      const Icon = amenity.icon;
                      const isSelected = formData.amenities.includes(amenity.id);
                      return (
                        <HoverAnimation key={amenity.id} type="scale">
                          <button
                            type="button"
                            onClick={() => toggleAmenity(amenity.id)}
                            className={`p-4 border-2 rounded-lg text-center transition-all ${
                              isSelected
                                ? 'border-sea-green-500 bg-sea-green-50 text-sea-green-700'
                                : 'border-gray-200 hover:border-sea-green-300'
                            }`}
                          >
                            <Icon className="h-6 w-6 mx-auto mb-2" />
                            <span className="text-sm font-medium">{amenity.label}</span>
                          </button>
                        </HoverAnimation>
                      );
                    })}
                  </StaggeredAnimation>
                </div>
              </div>
            ))}

            <div className="bg-sea-green-50 p-4 rounded-lg">
              <p className="text-sea-green-800 text-sm">
                <strong>Selected:</strong> {formData.amenities.length} amenities
              </p>
            </div>
          </div>
        );

      case 4:
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Upload Property Photos</h3>
              <p className="text-gray-600">Add high-quality photos to showcase your property</p>
            </div>

            <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-sea-green-400 transition-colors">
              <input
                type="file"
                multiple
                accept="image/*"
                onChange={handleImageUpload}
                className="hidden"
                id="image-upload"
              />
              <label htmlFor="image-upload" className="cursor-pointer">
                <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-lg font-medium text-gray-900 mb-2">
                  Click to upload photos
                </p>
                <p className="text-gray-600">
                  or drag and drop images here
                </p>
              </label>
            </div>

            {formData.imageUrls.length > 0 && (
              <div>
                <h4 className="font-semibold text-gray-900 mb-3">
                  Uploaded Photos ({formData.imageUrls.length})
                </h4>
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {formData.imageUrls.map((url, index) => (
                    <div key={index} className="relative group">
                      <img
                        src={url}
                        alt={`Property photo ${index + 1}`}
                        className="w-full h-32 object-cover rounded-lg"
                      />
                      <button
                        type="button"
                        onClick={() => removeImage(index)}
                        className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                      >
                        <X className="h-4 w-4" />
                      </button>
                      {index === 0 && (
                        <Badge className="absolute bottom-2 left-2 bg-yellow-500 text-white">
                          Primary
                        </Badge>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        );

      case 5:
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Set Your Pricing</h3>
              <p className="text-gray-600">Competitive pricing helps attract more guests</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Base Price per Night (ZAR) *
                </label>
                <Input
                  type="number"
                  value={formData.basePrice}
                  onChange={(e) => updateFormData('basePrice', Number(e.target.value))}
                  placeholder="0"
                  className={errors.basePrice ? 'border-red-500' : ''}
                />
                {errors.basePrice && <p className="text-red-500 text-sm mt-1">{errors.basePrice}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Cleaning Fee (ZAR)
                </label>
                <Input
                  type="number"
                  value={formData.cleaningFee}
                  onChange={(e) => updateFormData('cleaningFee', Number(e.target.value))}
                  placeholder="0"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Weekly Discount (%)
                </label>
                <Input
                  type="number"
                  value={formData.weeklyDiscount}
                  onChange={(e) => updateFormData('weeklyDiscount', Number(e.target.value))}
                  placeholder="0"
                  max="50"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Monthly Discount (%)
                </label>
                <Input
                  type="number"
                  value={formData.monthlyDiscount}
                  onChange={(e) => updateFormData('monthlyDiscount', Number(e.target.value))}
                  placeholder="0"
                  max="50"
                />
              </div>
            </div>

            <div className="bg-green-50 p-6 rounded-lg">
              <h4 className="font-semibold text-green-900 mb-3">Pricing Summary</h4>
              <div className="space-y-2 text-green-800">
                <div className="flex justify-between">
                  <span>Base price per night:</span>
                  <span className="font-semibold">R{formData.basePrice}</span>
                </div>
                <div className="flex justify-between">
                  <span>Cleaning fee:</span>
                  <span className="font-semibold">R{formData.cleaningFee}</span>
                </div>
                {formData.weeklyDiscount > 0 && (
                  <div className="flex justify-between">
                    <span>Weekly stay (7+ nights):</span>
                    <span className="font-semibold">R{Math.round(formData.basePrice * (1 - formData.weeklyDiscount / 100))}/night</span>
                  </div>
                )}
                {formData.monthlyDiscount > 0 && (
                  <div className="flex justify-between">
                    <span>Monthly stay (28+ nights):</span>
                    <span className="font-semibold">R{Math.round(formData.basePrice * (1 - formData.monthlyDiscount / 100))}/night</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        );

      case 6:
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">House Rules & Policies</h3>
              <p className="text-gray-600">Set clear expectations for your guests</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Check-in Time
                </label>
                <Input
                  type="time"
                  value={formData.checkInTime}
                  onChange={(e) => updateFormData('checkInTime', e.target.value)}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Check-out Time
                </label>
                <Input
                  type="time"
                  value={formData.checkOutTime}
                  onChange={(e) => updateFormData('checkOutTime', e.target.value)}
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Cancellation Policy
              </label>
              <div className="space-y-3">
                {CANCELLATION_POLICIES.map((policy) => (
                  <label key={policy.id} className="flex items-start space-x-3 cursor-pointer">
                    <input
                      type="radio"
                      name="cancellationPolicy"
                      value={policy.id}
                      checked={formData.cancellationPolicy === policy.id}
                      onChange={(e) => updateFormData('cancellationPolicy', e.target.value)}
                      className="mt-1"
                    />
                    <div>
                      <div className="font-medium text-gray-900">{policy.label}</div>
                      <div className="text-sm text-gray-600">{policy.description}</div>
                    </div>
                  </label>
                ))}
              </div>
            </div>

            <div>
              <div className="flex items-center justify-between mb-3">
                <label className="block text-sm font-medium text-gray-700">
                  House Rules
                </label>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={addHouseRule}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Rule
                </Button>
              </div>

              {formData.houseRules.length > 0 ? (
                <div className="space-y-2">
                  {formData.houseRules.map((rule, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <span className="text-gray-900">{rule}</span>
                      <button
                        type="button"
                        onClick={() => removeHouseRule(index)}
                        className="text-red-500 hover:text-red-700"
                      >
                        <X className="h-4 w-4" />
                      </button>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 text-sm italic">No house rules added yet</p>
              )}
            </div>
          </div>
        );

      case 7:
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Review Your Property</h3>
              <p className="text-gray-600">Please review all details before submitting</p>
            </div>

            <div className="space-y-6">
              {/* Basic Info Summary */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Home className="h-5 w-5" />
                    Basic Information
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <span className="text-sm text-gray-600">Title:</span>
                      <p className="font-medium">{formData.title}</p>
                    </div>
                    <div>
                      <span className="text-sm text-gray-600">Type:</span>
                      <p className="font-medium capitalize">{formData.propertyType}</p>
                    </div>
                    <div>
                      <span className="text-sm text-gray-600">Location:</span>
                      <p className="font-medium">{formData.location}</p>
                    </div>
                    <div>
                      <span className="text-sm text-gray-600">Capacity:</span>
                      <p className="font-medium">{formData.maxGuests} guests, {formData.bedrooms} bed, {formData.bathrooms} bath</p>
                    </div>
                  </div>
                  <div className="mt-4">
                    <span className="text-sm text-gray-600">Description:</span>
                    <p className="text-gray-900 mt-1">{formData.description}</p>
                  </div>
                </CardContent>
              </Card>

              {/* Pricing Summary */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <DollarSign className="h-5 w-5" />
                    Pricing
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div>
                      <span className="text-sm text-gray-600">Base Price:</span>
                      <p className="font-medium">R{formData.basePrice}/night</p>
                    </div>
                    <div>
                      <span className="text-sm text-gray-600">Cleaning Fee:</span>
                      <p className="font-medium">R{formData.cleaningFee}</p>
                    </div>
                    <div>
                      <span className="text-sm text-gray-600">Weekly Discount:</span>
                      <p className="font-medium">{formData.weeklyDiscount}%</p>
                    </div>
                    <div>
                      <span className="text-sm text-gray-600">Monthly Discount:</span>
                      <p className="font-medium">{formData.monthlyDiscount}%</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Amenities Summary */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Star className="h-5 w-5" />
                    Amenities ({formData.amenities.length})
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    {formData.amenities.map((amenityId) => {
                      const amenity = AMENITIES_LIST.find(a => a.id === amenityId);
                      return amenity ? (
                        <Badge key={amenityId} variant="secondary">
                          {amenity.label}
                        </Badge>
                      ) : null;
                    })}
                  </div>
                </CardContent>
              </Card>

              {/* Photos Summary */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Camera className="h-5 w-5" />
                    Photos ({formData.imageUrls.length})
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {formData.imageUrls.length > 0 ? (
                    <div className="grid grid-cols-4 md:grid-cols-6 gap-2">
                      {formData.imageUrls.slice(0, 6).map((url, index) => (
                        <img
                          key={index}
                          src={url}
                          alt={`Property photo ${index + 1}`}
                          className="w-full h-16 object-cover rounded"
                        />
                      ))}
                      {formData.imageUrls.length > 6 && (
                        <div className="w-full h-16 bg-gray-100 rounded flex items-center justify-center text-gray-600 text-sm">
                          +{formData.imageUrls.length - 6} more
                        </div>
                      )}
                    </div>
                  ) : (
                    <p className="text-gray-500">No photos uploaded</p>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        );

      default:
        return <div>Step content for step {currentStep}</div>;
    }
  };

  return (
    <SlideIn direction="up" delay={100}>
      <div className="max-w-4xl mx-auto">
        {/* Progress Steps */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            {steps.map((step, index) => {
              const Icon = step.icon;
              const isActive = currentStep === step.id;
              const isCompleted = currentStep > step.id;
              
              return (
                <div key={step.id} className="flex items-center">
                  <div className={`flex items-center justify-center w-12 h-12 rounded-full border-2 transition-all ${
                    isCompleted 
                      ? 'bg-sea-green-500 border-sea-green-500 text-white'
                      : isActive
                      ? 'border-sea-green-500 text-sea-green-500'
                      : 'border-gray-300 text-gray-400'
                  }`}>
                    {isCompleted ? <CheckCircle className="h-6 w-6" /> : <Icon className="h-6 w-6" />}
                  </div>
                  
                  {index < steps.length - 1 && (
                    <div className={`w-16 h-1 mx-2 ${
                      isCompleted ? 'bg-sea-green-500' : 'bg-gray-300'
                    }`} />
                  )}
                </div>
              );
            })}
          </div>
          
          <div className="mt-4 text-center">
            <h2 className="text-2xl font-bold text-gray-900">
              {steps[currentStep - 1].title}
            </h2>
            <p className="text-gray-600">
              {steps[currentStep - 1].description}
            </p>
          </div>
        </div>

        {/* Step Content */}
        <Card className="border-0 shadow-lg">
          <CardContent className="p-8">
            {renderStepContent()}
          </CardContent>
        </Card>

        {/* Navigation */}
        <div className="flex justify-between mt-8">
          <Button
            variant="outline"
            onClick={currentStep === 1 ? onCancel : prevStep}
            className="flex items-center gap-2"
          >
            {currentStep === 1 ? (
              <>
                <X className="h-4 w-4" />
                Cancel
              </>
            ) : (
              <>
                <ChevronLeft className="h-4 w-4" />
                Previous
              </>
            )}
          </Button>

          <Button
            onClick={currentStep === steps.length ? handleSubmit : nextStep}
            disabled={isSubmitting}
            className="flex items-center gap-2 bg-sea-green-500 hover:bg-sea-green-600"
          >
            {currentStep === steps.length ? (
              isSubmitting ? (
                'Creating Property...'
              ) : (
                <>
                  <CheckCircle className="h-4 w-4" />
                  Create Property
                </>
              )
            ) : (
              <>
                Next
                <ChevronRight className="h-4 w-4" />
              </>
            )}
          </Button>
        </div>
      </div>
    </SlideIn>
  );
};
