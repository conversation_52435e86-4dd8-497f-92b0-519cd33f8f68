import React, { useState, useEffect } from 'react';
import { Header } from '@/components/Header';
import { PerformanceDashboard } from '@/components/PerformanceDashboard';
import { OptimizedImage } from '@/components/OptimizedImage';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  PageTransition, 
  SlideIn, 
  StaggeredAnimation 
} from '@/components/ui/page-transitions';
import { EnhancedBreadcrumb } from '@/components/ui/enhanced-breadcrumb';
import { useLazyList, usePreloader } from '@/hooks/useLazyLoading';
import { 
  Home, 
  Zap, 
  Gauge, 
  Image as ImageIcon, 
  Code, 
  Monitor,
  TrendingUp,
  CheckCircle,
  Clock,
  Eye,
  Activity,
  Target,
  Award,
  Cpu,
  HardDrive,
  Wifi
} from 'lucide-react';

export const PerformanceDemo: React.FC = () => {
  const [selectedDemo, setSelectedDemo] = useState<'dashboard' | 'images' | 'lazy' | 'splitting'>('dashboard');
  const { preloadImages, isPreloaded } = usePreloader();

  // Sample images for lazy loading demo
  const sampleImages = Array.from({ length: 50 }, (_, i) => ({
    id: i + 1,
    src: `https://images.unsplash.com/photo-${1500000000000 + i}?w=400&h=300&fit=crop&auto=format`,
    alt: `Sample image ${i + 1}`,
    title: `Property ${i + 1}`
  }));

  const { ref, visibleItems, hasMore, loadMore } = useLazyList(sampleImages, 12);

  useEffect(() => {
    // Preload critical images
    const criticalImages = sampleImages.slice(0, 6).map(img => img.src);
    preloadImages(criticalImages);
  }, [preloadImages]);

  const demoSections = [
    {
      id: 'dashboard',
      title: 'Performance Dashboard',
      description: 'Real-time performance monitoring and Core Web Vitals',
      icon: Gauge,
      color: 'from-blue-500 to-blue-600'
    },
    {
      id: 'images',
      title: 'Optimized Images',
      description: 'WebP/AVIF support, lazy loading, and compression',
      icon: ImageIcon,
      color: 'from-green-500 to-green-600'
    },
    {
      id: 'lazy',
      title: 'Lazy Loading',
      description: 'Intersection Observer and virtual scrolling',
      icon: Eye,
      color: 'from-purple-500 to-purple-600'
    },
    {
      id: 'splitting',
      title: 'Code Splitting',
      description: 'Dynamic imports and bundle optimization',
      icon: Code,
      color: 'from-orange-500 to-orange-600'
    }
  ];

  const performanceFeatures = [
    {
      icon: Zap,
      title: 'Image Optimization',
      description: 'WebP/AVIF formats, lazy loading, and compression',
      metrics: ['85% smaller file sizes', '40% faster loading', 'Modern format support']
    },
    {
      icon: Code,
      title: 'Code Splitting',
      description: 'Dynamic imports and route-based splitting',
      metrics: ['60% smaller initial bundle', 'Faster first paint', 'On-demand loading']
    },
    {
      icon: Activity,
      title: 'Performance Monitoring',
      description: 'Core Web Vitals and custom metrics tracking',
      metrics: ['Real-time monitoring', 'Performance scoring', 'Optimization insights']
    },
    {
      icon: Target,
      title: 'Lazy Loading',
      description: 'Intersection Observer and virtual scrolling',
      metrics: ['Reduced initial load', 'Better user experience', 'Memory efficiency']
    }
  ];

  const renderImageOptimizationDemo = () => (
    <div className="space-y-6">
      <Card className="border-0 shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ImageIcon className="h-5 w-5 text-green-600" />
            Optimized Image Loading
          </CardTitle>
          <p className="text-gray-600">
            Comparison between standard and optimized image loading with WebP/AVIF support
          </p>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Standard Image */}
            <div>
              <h4 className="font-semibold text-gray-900 mb-3">Standard Image</h4>
              <div className="relative">
                <img
                  src="https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=400&h=300&fit=crop"
                  alt="Standard loading"
                  className="w-full h-48 object-cover rounded-lg"
                />
                <Badge className="absolute top-2 left-2 bg-red-100 text-red-800">
                  Standard
                </Badge>
              </div>
              <div className="mt-2 text-sm text-gray-600">
                • JPEG format only<br/>
                • No lazy loading<br/>
                • Larger file sizes
              </div>
            </div>

            {/* Optimized Image */}
            <div>
              <h4 className="font-semibold text-gray-900 mb-3">Optimized Image</h4>
              <div className="relative">
                <OptimizedImage
                  src="https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=400&h=300&fit=crop"
                  alt="Optimized loading"
                  className="w-full h-48 rounded-lg"
                  objectFit="cover"
                  lazy={true}
                  enableWebP={true}
                  enableAVIF={true}
                  quality={85}
                  aspectRatio="4/3"
                />
                <Badge className="absolute top-2 left-2 bg-green-100 text-green-800">
                  Optimized
                </Badge>
              </div>
              <div className="mt-2 text-sm text-gray-600">
                • WebP/AVIF support<br/>
                • Lazy loading enabled<br/>
                • 60-85% smaller files
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const renderLazyLoadingDemo = () => (
    <div className="space-y-6">
      <Card className="border-0 shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5 text-purple-600" />
            Lazy Loading Gallery
          </CardTitle>
          <p className="text-gray-600">
            Images load only when they come into view, reducing initial page load time
          </p>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {visibleItems.map((item) => (
              <div key={item.id} className="relative group">
                <OptimizedImage
                  src={item.src}
                  alt={item.alt}
                  className="w-full h-32 rounded-lg group-hover:scale-105 transition-transform duration-300"
                  objectFit="cover"
                  lazy={true}
                  enableWebP={true}
                  quality={80}
                  aspectRatio="4/3"
                />
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 rounded-lg flex items-center justify-center">
                  <span className="text-white font-semibold opacity-0 group-hover:opacity-100 transition-opacity">
                    {item.title}
                  </span>
                </div>
                {isPreloaded(item.src) && (
                  <Badge className="absolute top-2 right-2 bg-blue-100 text-blue-800 text-xs">
                    Preloaded
                  </Badge>
                )}
              </div>
            ))}
          </div>
          
          {hasMore && (
            <div ref={ref} className="text-center mt-6">
              <Button onClick={loadMore} variant="outline">
                Load More Images
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );

  const renderCodeSplittingDemo = () => (
    <div className="space-y-6">
      <Card className="border-0 shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Code className="h-5 w-5 text-orange-600" />
            Code Splitting Implementation
          </CardTitle>
          <p className="text-gray-600">
            Dynamic imports and route-based code splitting for optimal bundle sizes
          </p>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="font-semibold text-gray-900 mb-2">Bundle Optimization</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">60%</div>
                  <div className="text-sm text-gray-600">Smaller Initial Bundle</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">40%</div>
                  <div className="text-sm text-gray-600">Faster First Paint</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">12</div>
                  <div className="text-sm text-gray-600">Lazy-Loaded Routes</div>
                </div>
              </div>
            </div>

            <div className="bg-blue-50 p-4 rounded-lg">
              <h4 className="font-semibold text-blue-900 mb-2">Implementation Features</h4>
              <ul className="space-y-2 text-blue-800">
                <li className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4" />
                  Dynamic imports with React.lazy()
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4" />
                  Route-based code splitting
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4" />
                  Error boundaries for failed imports
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4" />
                  Preloading for critical routes
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4" />
                  Loading states and fallbacks
                </li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  return (
    <PageTransition>
      <div className="min-h-screen bg-gray-50">
        <Header />
        
        <div className="container mx-auto px-4 py-8">
          <SlideIn direction="up" delay={100}>
            <div className="mb-8">
              <EnhancedBreadcrumb 
                items={[
                  { label: 'Home', href: '/', icon: <Home className="h-4 w-4" /> },
                  { label: 'Performance Optimization Demo', current: true }
                ]}
              />
              <div className="text-center mt-8">
                <div className="inline-flex items-center gap-2 bg-gradient-to-r from-blue-100 to-purple-100 rounded-full px-6 py-3 mb-6">
                  <Zap className="h-5 w-5 text-blue-600" />
                  <span className="text-sm font-semibold text-blue-700">Performance Optimization</span>
                  <Badge className="bg-blue-500 text-white text-xs">Complete</Badge>
                </div>
                <h1 className="text-5xl font-bold text-gray-900 mb-4">
                  Performance Optimization Suite
                </h1>
                <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                  Advanced performance optimizations including image optimization, lazy loading, 
                  code splitting, and real-time monitoring
                </p>
              </div>
            </div>
          </SlideIn>

          {/* Demo Section Selector */}
          <SlideIn direction="up" delay={200}>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
              <StaggeredAnimation delay={100}>
                {demoSections.map((section) => {
                  const Icon = section.icon;
                  return (
                    <Card 
                      key={section.id}
                      className={`cursor-pointer transition-all duration-300 border-2 ${
                        selectedDemo === section.id 
                          ? 'border-blue-500 shadow-lg scale-105' 
                          : 'border-gray-200 hover:border-blue-300 hover:shadow-md'
                      }`}
                      onClick={() => setSelectedDemo(section.id as any)}
                    >
                      <CardContent className="p-6 text-center">
                        <div className={`w-16 h-16 bg-gradient-to-br ${section.color} rounded-2xl flex items-center justify-center mx-auto mb-4`}>
                          <Icon className="h-8 w-8 text-white" />
                        </div>
                        <h3 className="text-lg font-bold text-gray-900 mb-2">{section.title}</h3>
                        <p className="text-gray-600 text-sm">{section.description}</p>
                      </CardContent>
                    </Card>
                  );
                })}
              </StaggeredAnimation>
            </div>
          </SlideIn>

          {/* Demo Content */}
          <SlideIn direction="up" delay={300}>
            <div className="space-y-8">
              {selectedDemo === 'dashboard' && <PerformanceDashboard />}
              {selectedDemo === 'images' && renderImageOptimizationDemo()}
              {selectedDemo === 'lazy' && renderLazyLoadingDemo()}
              {selectedDemo === 'splitting' && renderCodeSplittingDemo()}
            </div>
          </SlideIn>

          {/* Performance Features Summary */}
          <SlideIn direction="up" delay={400}>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mt-12">
              {performanceFeatures.map((feature, index) => (
                <Card key={index} className="border-0 shadow-lg hover:shadow-xl transition-shadow duration-300">
                  <CardContent className="p-6">
                    <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-500 rounded-xl flex items-center justify-center mb-4">
                      <feature.icon className="h-6 w-6 text-white" />
                    </div>
                    <h3 className="text-lg font-bold text-gray-900 mb-2">{feature.title}</h3>
                    <p className="text-gray-600 mb-4">{feature.description}</p>
                    <div className="space-y-1">
                      {feature.metrics.map((metric, metricIndex) => (
                        <div key={metricIndex} className="flex items-center gap-2 text-sm text-gray-700">
                          <CheckCircle className="h-3 w-3 text-green-500" />
                          {metric}
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </SlideIn>
        </div>
      </div>
    </PageTransition>
  );
};
