import React, { useState } from 'react';
import { X, Star, Users, Bed, Bath, Wifi, Car, Waves, ChefHat, Snowflake, Thermometer, Tv, WashingMachine, Dumbbell, Sparkles, Heart, Ban, Clock, Calendar, Shield } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { useComparison, Property } from '@/contexts/ComparisonContext';
import { cn } from '@/lib/utils';

interface PropertyComparisonProps {
  isOpen: boolean;
  onClose: () => void;
}

export const PropertyComparison: React.FC<PropertyComparisonProps> = ({
  isOpen,
  onClose
}) => {
  const { comparedProperties, removeFromComparison, clearComparison } = useComparison();
  const [selectedCategory, setSelectedCategory] = useState<string>('overview');

  if (!isOpen || comparedProperties.length === 0) {
    return null;
  }

  const categories = [
    { id: 'overview', label: 'Overview', icon: Star },
    { id: 'amenities', label: 'Amenities', icon: Wifi },
    { id: 'policies', label: 'Policies', icon: Shield },
    { id: 'host', label: 'Host Info', icon: Users }
  ];

  const getAmenityIcon = (amenity: string) => {
    const icons: Record<string, React.ComponentType<any>> = {
      wifi: Wifi,
      parking: Car,
      pool: Waves,
      kitchen: ChefHat,
      airConditioning: Snowflake,
      heating: Thermometer,
      tv: Tv,
      washer: WashingMachine,
      dryer: WashingMachine,
      gym: Dumbbell,
      spa: Sparkles,
      petFriendly: Heart
    };
    return icons[amenity] || Star;
  };

  const renderOverview = () => (
    <div className="space-y-6">
      {/* Basic Info */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {comparedProperties.map((property) => (
          <Card key={property.id} className="relative">
            <CardHeader className="pb-3">
              <div className="flex justify-between items-start">
                <CardTitle className="text-lg font-semibold line-clamp-2">
                  {property.title}
                </CardTitle>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => removeFromComparison(property.id)}
                  className="text-gray-400 hover:text-red-500"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
              <p className="text-sm text-gray-600">{property.location}</p>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Image */}
              <div className="aspect-video rounded-lg overflow-hidden bg-gray-100">
                <img
                  src={property.images[0] || '/placeholder-property.jpg'}
                  alt={property.title}
                  className="w-full h-full object-cover"
                />
              </div>

              {/* Price & Rating */}
              <div className="flex justify-between items-center">
                <div className="text-2xl font-bold text-sea-green-600">
                  R{property.price.toLocaleString()}
                  <span className="text-sm font-normal text-gray-500">/night</span>
                </div>
                <div className="flex items-center gap-1">
                  <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                  <span className="font-medium">{property.rating}</span>
                  <span className="text-sm text-gray-500">({property.reviewCount})</span>
                </div>
              </div>

              {/* Property Details */}
              <div className="grid grid-cols-3 gap-2 text-sm">
                <div className="flex items-center gap-1">
                  <Users className="h-4 w-4 text-gray-400" />
                  <span>{property.guests} guests</span>
                </div>
                <div className="flex items-center gap-1">
                  <Bed className="h-4 w-4 text-gray-400" />
                  <span>{property.bedrooms} beds</span>
                </div>
                <div className="flex items-center gap-1">
                  <Bath className="h-4 w-4 text-gray-400" />
                  <span>{property.bathrooms} baths</span>
                </div>
              </div>

              <Badge variant="secondary" className="w-fit">
                {property.propertyType}
              </Badge>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );

  const renderAmenities = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {comparedProperties.map((property) => (
          <Card key={property.id}>
            <CardHeader>
              <CardTitle className="text-lg">{property.title}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-3">
                {Object.entries(property.features).map(([feature, available]) => {
                  const IconComponent = getAmenityIcon(feature);
                  return (
                    <div
                      key={feature}
                      className={cn(
                        "flex items-center gap-2 p-2 rounded-lg text-sm",
                        available 
                          ? "bg-green-50 text-green-700" 
                          : "bg-gray-50 text-gray-400"
                      )}
                    >
                      <IconComponent className="h-4 w-4" />
                      <span className="capitalize">
                        {feature.replace(/([A-Z])/g, ' $1').trim()}
                      </span>
                      {!available && <Ban className="h-3 w-3 ml-auto" />}
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );

  const renderPolicies = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {comparedProperties.map((property) => (
          <Card key={property.id}>
            <CardHeader>
              <CardTitle className="text-lg">{property.title}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-gray-400" />
                  <div>
                    <p className="text-sm font-medium">Check-in</p>
                    <p className="text-sm text-gray-600">{property.policies.checkIn}</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-gray-400" />
                  <div>
                    <p className="text-sm font-medium">Check-out</p>
                    <p className="text-sm text-gray-600">{property.policies.checkOut}</p>
                  </div>
                </div>

                <div>
                  <p className="text-sm font-medium mb-1">Cancellation</p>
                  <p className="text-sm text-gray-600">{property.policies.cancellation}</p>
                </div>

                <Separator />

                <div className="space-y-2">
                  <div className={cn(
                    "flex items-center gap-2 text-sm",
                    property.policies.smoking ? "text-green-600" : "text-red-600"
                  )}>
                    {property.policies.smoking ? (
                      <span>✓ Smoking allowed</span>
                    ) : (
                      <span>✗ No smoking</span>
                    )}
                  </div>
                  
                  <div className={cn(
                    "flex items-center gap-2 text-sm",
                    property.policies.parties ? "text-green-600" : "text-red-600"
                  )}>
                    {property.policies.parties ? (
                      <span>✓ Parties allowed</span>
                    ) : (
                      <span>✗ No parties</span>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );

  const renderHostInfo = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {comparedProperties.map((property) => (
          <Card key={property.id}>
            <CardHeader>
              <CardTitle className="text-lg">{property.title}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 rounded-full bg-gray-200 flex items-center justify-center">
                  {property.host.avatar ? (
                    <img
                      src={property.host.avatar}
                      alt={property.host.name}
                      className="w-full h-full rounded-full object-cover"
                    />
                  ) : (
                    <Users className="h-6 w-6 text-gray-400" />
                  )}
                </div>
                <div>
                  <p className="font-medium">{property.host.name}</p>
                  {property.host.verified && (
                    <Badge variant="secondary" className="text-xs">
                      ✓ Verified Host
                    </Badge>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );

  const renderContent = () => {
    switch (selectedCategory) {
      case 'overview':
        return renderOverview();
      case 'amenities':
        return renderAmenities();
      case 'policies':
        return renderPolicies();
      case 'host':
        return renderHostInfo();
      default:
        return renderOverview();
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-xl shadow-2xl w-full max-w-7xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div>
            <h2 className="text-2xl font-bold">Compare Properties</h2>
            <p className="text-gray-600">
              Comparing {comparedProperties.length} properties
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              onClick={clearComparison}
              className="text-red-600 border-red-200 hover:bg-red-50"
            >
              Clear All
            </Button>
            <Button variant="ghost" onClick={onClose}>
              <X className="h-5 w-5" />
            </Button>
          </div>
        </div>

        {/* Category Tabs */}
        <div className="flex border-b bg-gray-50">
          {categories.map((category) => {
            const IconComponent = category.icon;
            return (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                className={cn(
                  "flex items-center gap-2 px-6 py-3 text-sm font-medium transition-colors",
                  selectedCategory === category.id
                    ? "bg-white text-sea-green-600 border-b-2 border-sea-green-600"
                    : "text-gray-600 hover:text-gray-900 hover:bg-gray-100"
                )}
              >
                <IconComponent className="h-4 w-4" />
                {category.label}
              </button>
            );
          })}
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
          {renderContent()}
        </div>
      </div>
    </div>
  );
};
