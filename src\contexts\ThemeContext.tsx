import React, { createContext, useContext, useEffect, useState } from 'react';

type Theme = 'light' | 'dark' | 'system';

interface ThemeContextType {
  theme: Theme;
  actualTheme: 'light' | 'dark';
  setTheme: (theme: Theme) => void;
  toggleTheme: () => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

interface ThemeProviderProps {
  children: React.ReactNode;
  defaultTheme?: Theme;
  storageKey?: string;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({
  children,
  defaultTheme = 'system',
  storageKey = 'stayfinder-theme'
}) => {
  const [theme, setThemeState] = useState<Theme>(defaultTheme);
  const [actualTheme, setActualTheme] = useState<'light' | 'dark'>('light');

  // Get system theme preference
  const getSystemTheme = (): 'light' | 'dark' => {
    if (typeof window !== 'undefined') {
      return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
    }
    return 'light';
  };

  // Calculate actual theme based on theme setting
  const calculateActualTheme = (themeValue: Theme): 'light' | 'dark' => {
    if (themeValue === 'system') {
      return getSystemTheme();
    }
    return themeValue;
  };

  // Load theme from localStorage on mount
  useEffect(() => {
    try {
      const storedTheme = localStorage.getItem(storageKey) as Theme;
      if (storedTheme && ['light', 'dark', 'system'].includes(storedTheme)) {
        setThemeState(storedTheme);
      }
    } catch (error) {
      console.warn('Failed to load theme from localStorage:', error);
    }
  }, [storageKey]);

  // Update actual theme when theme changes
  useEffect(() => {
    const newActualTheme = calculateActualTheme(theme);
    setActualTheme(newActualTheme);

    // Apply theme to document
    const root = window.document.documentElement;
    root.classList.remove('light', 'dark');
    root.classList.add(newActualTheme);

    // Update meta theme-color for mobile browsers
    const metaThemeColor = document.querySelector('meta[name="theme-color"]');
    if (metaThemeColor) {
      metaThemeColor.setAttribute(
        'content',
        newActualTheme === 'dark' ? '#1f2937' : '#ffffff'
      );
    }
  }, [theme]);

  // Listen for system theme changes
  useEffect(() => {
    if (theme !== 'system') return;

    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    const handleChange = () => {
      setActualTheme(getSystemTheme());
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, [theme]);

  // Set theme and save to localStorage
  const setTheme = (newTheme: Theme) => {
    try {
      localStorage.setItem(storageKey, newTheme);
    } catch (error) {
      console.warn('Failed to save theme to localStorage:', error);
    }
    setThemeState(newTheme);
  };

  // Toggle between light and dark (skip system)
  const toggleTheme = () => {
    if (theme === 'light') {
      setTheme('dark');
    } else if (theme === 'dark') {
      setTheme('light');
    } else {
      // If system, toggle to opposite of current actual theme
      setTheme(actualTheme === 'light' ? 'dark' : 'light');
    }
  };

  const value: ThemeContextType = {
    theme,
    actualTheme,
    setTheme,
    toggleTheme
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};

// Hook for theme-aware styling
export const useThemeClasses = () => {
  const { actualTheme } = useTheme();
  
  return {
    // Background classes
    bg: {
      primary: actualTheme === 'dark' ? 'bg-gray-900' : 'bg-white',
      secondary: actualTheme === 'dark' ? 'bg-gray-800' : 'bg-gray-50',
      tertiary: actualTheme === 'dark' ? 'bg-gray-700' : 'bg-gray-100',
      card: actualTheme === 'dark' ? 'bg-gray-800' : 'bg-white',
      overlay: actualTheme === 'dark' ? 'bg-gray-900/80' : 'bg-white/80',
    },
    
    // Text classes
    text: {
      primary: actualTheme === 'dark' ? 'text-white' : 'text-gray-900',
      secondary: actualTheme === 'dark' ? 'text-gray-300' : 'text-gray-600',
      tertiary: actualTheme === 'dark' ? 'text-gray-400' : 'text-gray-500',
      muted: actualTheme === 'dark' ? 'text-gray-500' : 'text-gray-400',
      accent: actualTheme === 'dark' ? 'text-sea-green-400' : 'text-sea-green-600',
    },
    
    // Border classes
    border: {
      primary: actualTheme === 'dark' ? 'border-gray-700' : 'border-gray-200',
      secondary: actualTheme === 'dark' ? 'border-gray-600' : 'border-gray-300',
      accent: actualTheme === 'dark' ? 'border-sea-green-500' : 'border-sea-green-500',
    },
    
    // Hover classes
    hover: {
      bg: actualTheme === 'dark' ? 'hover:bg-gray-700' : 'hover:bg-gray-50',
      text: actualTheme === 'dark' ? 'hover:text-white' : 'hover:text-gray-900',
    },
    
    // Focus classes
    focus: {
      ring: actualTheme === 'dark' ? 'focus:ring-sea-green-400' : 'focus:ring-sea-green-500',
      border: actualTheme === 'dark' ? 'focus:border-sea-green-400' : 'focus:border-sea-green-500',
    },
    
    // Utility
    isDark: actualTheme === 'dark',
    isLight: actualTheme === 'light',
  };
};
