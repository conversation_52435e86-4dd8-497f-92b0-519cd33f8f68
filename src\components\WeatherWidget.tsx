import React, { useState, useEffect } from 'react';
import { Cloud, Sun, CloudRain, CloudSnow, Wind, Thermometer, Droplets, Eye, Gauge, MapPin } from 'lucide-react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { cn } from '@/lib/utils';

interface WeatherData {
  location: {
    name: string;
    country: string;
    lat: number;
    lon: number;
  };
  current: {
    temperature: number;
    feelsLike: number;
    condition: string;
    icon: string;
    humidity: number;
    windSpeed: number;
    windDirection: string;
    pressure: number;
    visibility: number;
    uvIndex: number;
    isDay: boolean;
  };
  forecast: Array<{
    date: string;
    day: string;
    maxTemp: number;
    minTemp: number;
    condition: string;
    icon: string;
    chanceOfRain: number;
  }>;
}

interface WeatherWidgetProps {
  location: string;
  coordinates?: { lat: number; lon: number };
  showForecast?: boolean;
  compact?: boolean;
  className?: string;
}

export const WeatherWidget: React.FC<WeatherWidgetProps> = ({
  location,
  coordinates,
  showForecast = true,
  compact = false,
  className
}) => {
  const [weather, setWeather] = useState<WeatherData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchWeatherData();
  }, [location, coordinates]);

  const fetchWeatherData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Mock weather data for demonstration
      // In production, replace with actual weather API call
      const mockWeatherData: WeatherData = {
        location: {
          name: location,
          country: 'South Africa',
          lat: coordinates?.lat || -33.9249,
          lon: coordinates?.lon || 18.4241
        },
        current: {
          temperature: Math.round(Math.random() * 15 + 15), // 15-30°C
          feelsLike: Math.round(Math.random() * 15 + 15),
          condition: getRandomCondition(),
          icon: 'sunny',
          humidity: Math.round(Math.random() * 40 + 40), // 40-80%
          windSpeed: Math.round(Math.random() * 20 + 5), // 5-25 km/h
          windDirection: getRandomDirection(),
          pressure: Math.round(Math.random() * 50 + 1000), // 1000-1050 hPa
          visibility: Math.round(Math.random() * 10 + 10), // 10-20 km
          uvIndex: Math.round(Math.random() * 8 + 1), // 1-9
          isDay: new Date().getHours() >= 6 && new Date().getHours() < 18
        },
        forecast: generateForecast()
      };

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setWeather(mockWeatherData);
    } catch (err) {
      setError('Failed to fetch weather data');
      console.error('Weather API error:', err);
    } finally {
      setLoading(false);
    }
  };

  const getRandomCondition = () => {
    const conditions = ['Sunny', 'Partly Cloudy', 'Cloudy', 'Light Rain', 'Clear'];
    return conditions[Math.floor(Math.random() * conditions.length)];
  };

  const getRandomDirection = () => {
    const directions = ['N', 'NE', 'E', 'SE', 'S', 'SW', 'W', 'NW'];
    return directions[Math.floor(Math.random() * directions.length)];
  };

  const generateForecast = () => {
    const days = ['Today', 'Tomorrow', 'Wednesday', 'Thursday', 'Friday'];
    return days.map((day, index) => ({
      date: new Date(Date.now() + index * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      day,
      maxTemp: Math.round(Math.random() * 10 + 20),
      minTemp: Math.round(Math.random() * 10 + 10),
      condition: getRandomCondition(),
      icon: 'sunny',
      chanceOfRain: Math.round(Math.random() * 100)
    }));
  };

  const getWeatherIcon = (condition: string, isDay: boolean = true) => {
    const iconClass = "h-6 w-6";
    
    switch (condition.toLowerCase()) {
      case 'sunny':
      case 'clear':
        return <Sun className={cn(iconClass, "text-yellow-500")} />;
      case 'partly cloudy':
        return <Cloud className={cn(iconClass, "text-gray-400")} />;
      case 'cloudy':
        return <Cloud className={cn(iconClass, "text-gray-500")} />;
      case 'light rain':
      case 'rain':
        return <CloudRain className={cn(iconClass, "text-blue-500")} />;
      case 'snow':
        return <CloudSnow className={cn(iconClass, "text-blue-200")} />;
      default:
        return isDay ? 
          <Sun className={cn(iconClass, "text-yellow-500")} /> : 
          <Cloud className={cn(iconClass, "text-gray-400")} />;
    }
  };

  const getUVIndexColor = (uvIndex: number) => {
    if (uvIndex <= 2) return 'bg-green-100 text-green-800';
    if (uvIndex <= 5) return 'bg-yellow-100 text-yellow-800';
    if (uvIndex <= 7) return 'bg-orange-100 text-orange-800';
    if (uvIndex <= 10) return 'bg-red-100 text-red-800';
    return 'bg-purple-100 text-purple-800';
  };

  const getUVIndexLabel = (uvIndex: number) => {
    if (uvIndex <= 2) return 'Low';
    if (uvIndex <= 5) return 'Moderate';
    if (uvIndex <= 7) return 'High';
    if (uvIndex <= 10) return 'Very High';
    return 'Extreme';
  };

  if (loading) {
    return (
      <Card className={cn("w-full", className)}>
        <CardHeader>
          <Skeleton className="h-6 w-32" />
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center gap-4">
              <Skeleton className="h-12 w-12 rounded-full" />
              <div className="space-y-2">
                <Skeleton className="h-8 w-20" />
                <Skeleton className="h-4 w-24" />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <Skeleton className="h-16" />
              <Skeleton className="h-16" />
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error || !weather) {
    return (
      <Card className={cn("w-full", className)}>
        <CardContent className="p-6 text-center">
          <Cloud className="h-12 w-12 mx-auto mb-4 text-gray-400" />
          <p className="text-gray-600">{error || 'Weather data unavailable'}</p>
        </CardContent>
      </Card>
    );
  }

  if (compact) {
    return (
      <div className={cn("flex items-center gap-3 p-3 bg-white rounded-lg shadow-sm", className)}>
        <div className="flex items-center gap-2">
          {getWeatherIcon(weather.current.condition, weather.current.isDay)}
          <span className="font-semibold">{weather.current.temperature}°C</span>
        </div>
        <div className="text-sm text-gray-600">
          <div className="flex items-center gap-1">
            <MapPin className="h-3 w-3" />
            {weather.location.name}
          </div>
          <div>{weather.current.condition}</div>
        </div>
      </div>
    );
  }

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MapPin className="h-5 w-5" />
          Weather in {weather.location.name}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Current Weather */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="p-3 bg-gradient-to-br from-blue-50 to-blue-100 rounded-full">
              {getWeatherIcon(weather.current.condition, weather.current.isDay)}
            </div>
            <div>
              <div className="text-3xl font-bold">{weather.current.temperature}°C</div>
              <div className="text-gray-600">{weather.current.condition}</div>
              <div className="text-sm text-gray-500">
                Feels like {weather.current.feelsLike}°C
              </div>
            </div>
          </div>
          <Badge className={getUVIndexColor(weather.current.uvIndex)}>
            UV {weather.current.uvIndex} ({getUVIndexLabel(weather.current.uvIndex)})
          </Badge>
        </div>

        {/* Weather Details */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="flex items-center gap-2 p-3 bg-gray-50 rounded-lg">
            <Wind className="h-4 w-4 text-gray-600" />
            <div>
              <div className="text-sm font-medium">{weather.current.windSpeed} km/h</div>
              <div className="text-xs text-gray-500">{weather.current.windDirection}</div>
            </div>
          </div>
          
          <div className="flex items-center gap-2 p-3 bg-gray-50 rounded-lg">
            <Droplets className="h-4 w-4 text-blue-600" />
            <div>
              <div className="text-sm font-medium">{weather.current.humidity}%</div>
              <div className="text-xs text-gray-500">Humidity</div>
            </div>
          </div>
          
          <div className="flex items-center gap-2 p-3 bg-gray-50 rounded-lg">
            <Gauge className="h-4 w-4 text-gray-600" />
            <div>
              <div className="text-sm font-medium">{weather.current.pressure} hPa</div>
              <div className="text-xs text-gray-500">Pressure</div>
            </div>
          </div>
          
          <div className="flex items-center gap-2 p-3 bg-gray-50 rounded-lg">
            <Eye className="h-4 w-4 text-gray-600" />
            <div>
              <div className="text-sm font-medium">{weather.current.visibility} km</div>
              <div className="text-xs text-gray-500">Visibility</div>
            </div>
          </div>
        </div>

        {/* 5-Day Forecast */}
        {showForecast && (
          <div>
            <h4 className="font-semibold mb-3">5-Day Forecast</h4>
            <div className="space-y-2">
              {weather.forecast.map((day, index) => (
                <div key={index} className="flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg transition-colors">
                  <div className="flex items-center gap-3">
                    {getWeatherIcon(day.condition)}
                    <div>
                      <div className="font-medium">{day.day}</div>
                      <div className="text-sm text-gray-600">{day.condition}</div>
                    </div>
                  </div>
                  <div className="flex items-center gap-4">
                    {day.chanceOfRain > 0 && (
                      <div className="flex items-center gap-1 text-blue-600">
                        <Droplets className="h-3 w-3" />
                        <span className="text-xs">{day.chanceOfRain}%</span>
                      </div>
                    )}
                    <div className="text-right">
                      <div className="font-semibold">{day.maxTemp}°</div>
                      <div className="text-sm text-gray-500">{day.minTemp}°</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Weather Tips */}
        <div className="p-4 bg-blue-50 rounded-lg">
          <h5 className="font-medium text-blue-900 mb-2">Travel Tips</h5>
          <div className="text-sm text-blue-800 space-y-1">
            {weather.current.temperature > 25 && (
              <p>• Perfect weather for outdoor activities and beach visits</p>
            )}
            {weather.current.uvIndex > 6 && (
              <p>• High UV levels - don't forget sunscreen and a hat</p>
            )}
            {weather.current.windSpeed > 20 && (
              <p>• Windy conditions - secure loose items</p>
            )}
            {weather.current.humidity > 70 && (
              <p>• High humidity - stay hydrated and dress lightly</p>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
