<?php
header('Access-Control-Allow-Origin: http://localhost:8081');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once 'config.php';

try {
    $query = isset($_GET['q']) ? trim($_GET['q']) : '';
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;
    
    if (empty($query) || strlen($query) < 2) {
        echo json_encode([
            'success' => true,
            'suggestions' => []
        ]);
        exit;
    }
    
    // Get unique locations from properties table where there are active properties
    $sql = "
        SELECT 
            location,
            COUNT(*) as property_count,
            MIN(price_per_night) as min_price,
            MAX(price_per_night) as max_price,
            AVG(price_per_night) as avg_price
        FROM properties 
        WHERE status = 'active' 
        AND location LIKE :query
        GROUP BY location
        HAVING property_count > 0
        ORDER BY property_count DESC, location ASC
        LIMIT :limit
    ";
    
    $stmt = $pdo->prepare($sql);
    $searchQuery = '%' . $query . '%';
    $stmt->bindParam(':query', $searchQuery, PDO::PARAM_STR);
    $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
    $stmt->execute();
    
    $locations = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $suggestions = [];
    
    foreach ($locations as $location) {
        // Parse location to extract city and province
        $locationParts = explode(',', $location['location']);
        $city = trim($locationParts[0]);
        $province = isset($locationParts[1]) ? trim($locationParts[1]) : '';
        
        // Determine suggestion type based on location structure
        $type = 'location';
        $icon = 'MapPin';
        
        // Check if it's a major city
        $majorCities = ['Cape Town', 'Johannesburg', 'Durban', 'Pretoria', 'Port Elizabeth'];
        if (in_array($city, $majorCities)) {
            $type = 'city';
            $icon = 'Building';
        }
        
        // Check if it's a tourist destination
        $touristDestinations = ['Kruger Park', 'Stellenbosch', 'Hermanus', 'Camps Bay', 'Knysna'];
        foreach ($touristDestinations as $destination) {
            if (strpos($city, $destination) !== false) {
                $type = 'tourist';
                $icon = 'Camera';
                break;
            }
        }
        
        $suggestions[] = [
            'id' => 'loc_' . md5($location['location']),
            'text' => $city,
            'subtitle' => $province . ' • ' . $location['property_count'] . ' properties',
            'full_location' => $location['location'],
            'type' => $type,
            'icon' => $icon,
            'property_count' => (int)$location['property_count'],
            'price_range' => [
                'min' => (float)$location['min_price'],
                'max' => (float)$location['max_price'],
                'avg' => (float)$location['avg_price']
            ]
        ];
    }
    
    // Also search for provinces if query matches
    $provinceSql = "
        SELECT 
            SUBSTRING_INDEX(location, ',', -1) as province,
            COUNT(*) as property_count,
            MIN(price_per_night) as min_price,
            MAX(price_per_night) as max_price
        FROM properties 
        WHERE status = 'active' 
        AND SUBSTRING_INDEX(location, ',', -1) LIKE :query
        GROUP BY province
        HAVING property_count > 0
        ORDER BY property_count DESC
        LIMIT 3
    ";
    
    $provinceStmt = $pdo->prepare($provinceSql);
    $provinceStmt->bindParam(':query', $searchQuery, PDO::PARAM_STR);
    $provinceStmt->execute();
    
    $provinces = $provinceStmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($provinces as $province) {
        $provinceName = trim($province['province']);
        
        // Skip if already added as a city
        $alreadyAdded = false;
        foreach ($suggestions as $suggestion) {
            if (strpos($suggestion['subtitle'], $provinceName) !== false) {
                $alreadyAdded = true;
                break;
            }
        }
        
        if (!$alreadyAdded && !empty($provinceName)) {
            $suggestions[] = [
                'id' => 'prov_' . md5($provinceName),
                'text' => $provinceName,
                'subtitle' => 'Province • ' . $province['property_count'] . ' properties',
                'full_location' => $provinceName,
                'type' => 'province',
                'icon' => 'Map',
                'property_count' => (int)$province['property_count'],
                'price_range' => [
                    'min' => (float)$province['min_price'],
                    'max' => (float)$province['max_price'],
                    'avg' => 0
                ]
            ];
        }
    }
    
    // Sort suggestions by relevance (exact matches first, then by property count)
    usort($suggestions, function($a, $b) use ($query) {
        $queryLower = strtolower($query);
        
        // Exact matches first
        $aExact = strtolower($a['text']) === $queryLower ? 1 : 0;
        $bExact = strtolower($b['text']) === $queryLower ? 1 : 0;
        
        if ($aExact !== $bExact) {
            return $bExact - $aExact;
        }
        
        // Then by starts with
        $aStarts = strpos(strtolower($a['text']), $queryLower) === 0 ? 1 : 0;
        $bStarts = strpos(strtolower($b['text']), $queryLower) === 0 ? 1 : 0;
        
        if ($aStarts !== $bStarts) {
            return $bStarts - $aStarts;
        }
        
        // Finally by property count
        return $b['property_count'] - $a['property_count'];
    });
    
    // Limit final results
    $suggestions = array_slice($suggestions, 0, $limit);
    
    echo json_encode([
        'success' => true,
        'suggestions' => $suggestions,
        'query' => $query,
        'total_found' => count($suggestions)
    ]);

} catch (Exception $e) {
    error_log("Search suggestions error: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'error' => 'Failed to fetch search suggestions',
        'suggestions' => []
    ]);
}
