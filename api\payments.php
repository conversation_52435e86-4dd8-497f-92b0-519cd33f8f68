<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: http://localhost:8081');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Credentials: true');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once 'config.php';
require_once 'auth.php';

// PayFast configuration
define('PAYFAST_MERCHANT_ID', '10000100');  // Test merchant ID
define('PAYFAST_MERCHANT_KEY', '46f0cd694581a');  // Test merchant key
define('PAYFAST_PASSPHRASE', 'jt7NOE43FZPn');  // Test passphrase
define('PAYFAST_URL', 'https://sandbox.payfast.co.za/eng/process');  // Sandbox URL

function createPayFastPayment($bookingData, $userData) {
    global $pdo;
    
    try {
        // Generate unique payment reference
        $paymentRef = 'SF-' . $bookingData['id'] . '-' . time();
        
        // Calculate amounts
        $amount = number_format($bookingData['total_amount'], 2, '.', '');
        $platformFee = number_format($bookingData['total_amount'] * 0.05, 2, '.', ''); // 5% platform fee
        $hostAmount = number_format($bookingData['total_amount'] - $platformFee, 2, '.', '');
        
        // PayFast payment data
        $paymentData = [
            'merchant_id' => PAYFAST_MERCHANT_ID,
            'merchant_key' => PAYFAST_MERCHANT_KEY,
            'return_url' => 'http://localhost:5173/payment/success',
            'cancel_url' => 'http://localhost:5173/payment/cancel',
            'notify_url' => 'http://localhost/stayfinder/api/payments.php?action=notify',
            'name_first' => $userData['first_name'],
            'name_last' => $userData['last_name'],
            'email_address' => $userData['email'],
            'cell_number' => $userData['phone'] ?? '',
            'm_payment_id' => $paymentRef,
            'amount' => $amount,
            'item_name' => 'StayFinder Booking - ' . $bookingData['property_title'],
            'item_description' => 'Accommodation booking from ' . $bookingData['check_in_date'] . ' to ' . $bookingData['check_out_date'],
            'custom_str1' => $bookingData['id'], // Booking ID
            'custom_str2' => $hostAmount, // Amount for host
            'custom_str3' => $platformFee, // Platform fee
        ];
        
        // Generate signature
        $signature = generatePayFastSignature($paymentData);
        $paymentData['signature'] = $signature;
        
        // Store payment record
        $paymentId = 'payment-' . uniqid();
        $stmt = $pdo->prepare("
            INSERT INTO payments (
                id, booking_id, payment_reference, amount, platform_fee, host_amount,
                payment_method, payment_status, payment_data, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, 'payfast', 'pending', ?, NOW())
        ");
        
        $stmt->execute([
            $paymentId,
            $bookingData['id'],
            $paymentRef,
            $amount,
            $platformFee,
            $hostAmount,
            json_encode($paymentData)
        ]);
        
        return [
            'payment_id' => $paymentId,
            'payment_url' => PAYFAST_URL,
            'payment_data' => $paymentData,
            'payment_reference' => $paymentRef
        ];
        
    } catch (Exception $e) {
        error_log("Error creating PayFast payment: " . $e->getMessage());
        throw $e;
    }
}

function generatePayFastSignature($data) {
    // Remove signature if it exists
    unset($data['signature']);
    
    // Sort data by key
    ksort($data);
    
    // Create parameter string
    $paramString = '';
    foreach ($data as $key => $value) {
        if ($value !== '' && $value !== null) {
            $paramString .= $key . '=' . urlencode(trim($value)) . '&';
        }
    }
    
    // Remove last ampersand
    $paramString = rtrim($paramString, '&');
    
    // Add passphrase if set
    if (PAYFAST_PASSPHRASE) {
        $paramString .= '&passphrase=' . urlencode(PAYFAST_PASSPHRASE);
    }
    
    return md5($paramString);
}

function handlePayFastNotification() {
    global $pdo;
    
    try {
        // Get POST data
        $postData = $_POST;
        
        // Verify signature
        $signature = $postData['signature'];
        unset($postData['signature']);
        
        $calculatedSignature = generatePayFastSignature($postData);
        
        if ($signature !== $calculatedSignature) {
            error_log("PayFast signature verification failed");
            http_response_code(400);
            echo "Invalid signature";
            return;
        }
        
        // Get payment details
        $paymentRef = $postData['m_payment_id'];
        $paymentStatus = $postData['payment_status'];
        $bookingId = $postData['custom_str1'];
        
        // Update payment status
        $stmt = $pdo->prepare("
            UPDATE payments 
            SET payment_status = ?, payfast_payment_id = ?, notification_data = ?, updated_at = NOW()
            WHERE payment_reference = ?
        ");
        
        $stmt->execute([
            $paymentStatus === 'COMPLETE' ? 'completed' : 'failed',
            $postData['pf_payment_id'] ?? null,
            json_encode($postData),
            $paymentRef
        ]);
        
        // Update booking payment status
        if ($paymentStatus === 'COMPLETE') {
            $stmt = $pdo->prepare("
                UPDATE bookings 
                SET payment_status = 'paid', booking_status = 'confirmed', updated_at = NOW()
                WHERE id = ?
            ");
            $stmt->execute([$bookingId]);
            
            // TODO: Send confirmation email to guest and host
            
        } else {
            $stmt = $pdo->prepare("
                UPDATE bookings 
                SET payment_status = 'failed', updated_at = NOW()
                WHERE id = ?
            ");
            $stmt->execute([$bookingId]);
        }
        
        echo "OK";
        
    } catch (Exception $e) {
        error_log("Error handling PayFast notification: " . $e->getMessage());
        http_response_code(500);
        echo "Error processing notification";
    }
}

function getPaymentDetails($paymentId, $userId) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            SELECT 
                p.*,
                b.property_id,
                b.guest_id,
                b.check_in_date,
                b.check_out_date,
                b.booking_status,
                pr.title as property_title,
                pr.host_id
            FROM payments p
            JOIN bookings b ON p.booking_id = b.id
            JOIN properties pr ON b.property_id = pr.id
            WHERE p.id = ? AND (b.guest_id = ? OR pr.host_id = ?)
        ");
        
        $stmt->execute([$paymentId, $userId, $userId]);
        $payment = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($payment) {
            $payment['payment_data'] = json_decode($payment['payment_data'], true);
            $payment['notification_data'] = $payment['notification_data'] ? json_decode($payment['notification_data'], true) : null;
        }
        
        return $payment;
        
    } catch (Exception $e) {
        error_log("Error getting payment details: " . $e->getMessage());
        throw $e;
    }
}

function processRefund($paymentId, $userId, $refundAmount = null) {
    global $pdo;
    
    try {
        // Get payment details
        $payment = getPaymentDetails($paymentId, $userId);
        
        if (!$payment) {
            throw new Exception("Payment not found or access denied");
        }
        
        if ($payment['payment_status'] !== 'completed') {
            throw new Exception("Cannot refund incomplete payment");
        }
        
        // Calculate refund amount
        $maxRefund = (float)$payment['amount'];
        $refundAmount = $refundAmount ? min((float)$refundAmount, $maxRefund) : $maxRefund;
        
        // Create refund record
        $refundId = 'refund-' . uniqid();
        $stmt = $pdo->prepare("
            INSERT INTO refunds (
                id, payment_id, booking_id, refund_amount, refund_status, 
                refund_reason, created_at
            ) VALUES (?, ?, ?, ?, 'pending', 'Booking cancellation', NOW())
        ");
        
        $stmt->execute([
            $refundId,
            $paymentId,
            $payment['booking_id'],
            $refundAmount
        ]);
        
        // Update payment status
        $stmt = $pdo->prepare("
            UPDATE payments 
            SET payment_status = 'refunded', refund_amount = ?, updated_at = NOW()
            WHERE id = ?
        ");
        $stmt->execute([$refundAmount, $paymentId]);
        
        // Update booking status
        $stmt = $pdo->prepare("
            UPDATE bookings 
            SET payment_status = 'refunded', booking_status = 'cancelled', updated_at = NOW()
            WHERE id = ?
        ");
        $stmt->execute([$payment['booking_id']]);
        
        return [
            'refund_id' => $refundId,
            'refund_amount' => $refundAmount,
            'status' => 'pending'
        ];
        
    } catch (Exception $e) {
        error_log("Error processing refund: " . $e->getMessage());
        throw $e;
    }
}

// Handle the request
$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['action'] ?? '';

try {
    switch ($method) {
        case 'GET':
            switch ($action) {
                case 'payment':
                    $user = getCurrentUser();
                    if (!$user) {
                        http_response_code(401);
                        echo json_encode(['error' => 'Authentication required']);
                        exit;
                    }
                    
                    $paymentId = $_GET['payment_id'] ?? '';
                    if (empty($paymentId)) {
                        http_response_code(400);
                        echo json_encode(['error' => 'Payment ID is required']);
                        exit;
                    }
                    
                    $payment = getPaymentDetails($paymentId, $user['id']);
                    
                    if (!$payment) {
                        http_response_code(404);
                        echo json_encode(['error' => 'Payment not found']);
                        exit;
                    }
                    
                    echo json_encode([
                        'success' => true,
                        'data' => $payment
                    ]);
                    break;
                    
                default:
                    http_response_code(400);
                    echo json_encode(['error' => 'Invalid action']);
                    break;
            }
            break;
            
        case 'POST':
            switch ($action) {
                case 'create':
                    $user = getCurrentUser();
                    if (!$user) {
                        http_response_code(401);
                        echo json_encode(['error' => 'Authentication required']);
                        exit;
                    }
                    
                    $input = json_decode(file_get_contents('php://input'), true);
                    $bookingId = $input['booking_id'] ?? '';
                    
                    if (empty($bookingId)) {
                        http_response_code(400);
                        echo json_encode(['error' => 'Booking ID is required']);
                        exit;
                    }
                    
                    // Get booking details
                    $stmt = $pdo->prepare("
                        SELECT 
                            b.*,
                            p.title as property_title,
                            p.host_id
                        FROM bookings b
                        JOIN properties p ON b.property_id = p.id
                        WHERE b.id = ? AND b.guest_id = ?
                    ");
                    $stmt->execute([$bookingId, $user['id']]);
                    $booking = $stmt->fetch(PDO::FETCH_ASSOC);
                    
                    if (!$booking) {
                        http_response_code(404);
                        echo json_encode(['error' => 'Booking not found']);
                        exit;
                    }
                    
                    if ($booking['payment_status'] !== 'pending') {
                        http_response_code(400);
                        echo json_encode(['error' => 'Payment already processed']);
                        exit;
                    }
                    
                    $paymentData = createPayFastPayment($booking, $user);
                    
                    echo json_encode([
                        'success' => true,
                        'data' => $paymentData
                    ]);
                    break;
                    
                case 'notify':
                    handlePayFastNotification();
                    break;
                    
                case 'refund':
                    $user = getCurrentUser();
                    if (!$user) {
                        http_response_code(401);
                        echo json_encode(['error' => 'Authentication required']);
                        exit;
                    }
                    
                    $input = json_decode(file_get_contents('php://input'), true);
                    $paymentId = $input['payment_id'] ?? '';
                    $refundAmount = $input['refund_amount'] ?? null;
                    
                    if (empty($paymentId)) {
                        http_response_code(400);
                        echo json_encode(['error' => 'Payment ID is required']);
                        exit;
                    }
                    
                    $refund = processRefund($paymentId, $user['id'], $refundAmount);
                    
                    echo json_encode([
                        'success' => true,
                        'data' => $refund
                    ]);
                    break;
                    
                default:
                    http_response_code(400);
                    echo json_encode(['error' => 'Invalid action']);
                    break;
            }
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            break;
    }
    
} catch (Exception $e) {
    error_log("Payments API error: " . $e->getMessage());
    http_response_code(400);
    echo json_encode(['error' => $e->getMessage()]);
}
?>
