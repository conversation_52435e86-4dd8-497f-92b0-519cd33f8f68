import React, { useState } from 'react';
import { Calendar, Clock, User, MapPin, Phone, Mail, MessageCircle, CheckCircle, XCircle, AlertTriangle, Filter, Search, Download } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { toast } from '@/components/ui/use-toast';
import { cn } from '@/lib/utils';

interface Booking {
  id: string;
  propertyId: string;
  propertyName: string;
  guest: {
    id: string;
    name: string;
    email: string;
    phone: string;
    avatar?: string;
  };
  checkIn: Date;
  checkOut: Date;
  guests: number;
  status: 'pending' | 'confirmed' | 'checked_in' | 'checked_out' | 'cancelled';
  totalAmount: number;
  paymentStatus: 'pending' | 'paid' | 'refunded';
  specialRequests?: string;
  createdAt: Date;
  lastUpdated: Date;
}

interface BookingManagementProps {
  bookings: Booking[];
  onBookingUpdate: (bookingId: string, updates: Partial<Booking>) => void;
  onSendMessage: (guestId: string, message: string) => void;
  className?: string;
}

export const BookingManagement: React.FC<BookingManagementProps> = ({
  bookings,
  onBookingUpdate,
  onSendMessage,
  className
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [dateFilter, setDateFilter] = useState<string>('all');
  const [selectedBooking, setSelectedBooking] = useState<Booking | null>(null);

  const statusConfig = {
    pending: { label: 'Pending', color: 'bg-yellow-100 text-yellow-800', icon: Clock },
    confirmed: { label: 'Confirmed', color: 'bg-blue-100 text-blue-800', icon: CheckCircle },
    checked_in: { label: 'Checked In', color: 'bg-green-100 text-green-800', icon: CheckCircle },
    checked_out: { label: 'Checked Out', color: 'bg-gray-100 text-gray-800', icon: CheckCircle },
    cancelled: { label: 'Cancelled', color: 'bg-red-100 text-red-800', icon: XCircle }
  };

  const paymentStatusConfig = {
    pending: { label: 'Payment Pending', color: 'bg-orange-100 text-orange-800' },
    paid: { label: 'Paid', color: 'bg-green-100 text-green-800' },
    refunded: { label: 'Refunded', color: 'bg-gray-100 text-gray-800' }
  };

  // Filter bookings
  const filteredBookings = bookings.filter(booking => {
    const matchesSearch = searchQuery === '' || 
      booking.guest.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      booking.propertyName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      booking.id.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || booking.status === statusFilter;
    
    let matchesDate = true;
    if (dateFilter !== 'all') {
      const now = new Date();
      const checkIn = booking.checkIn;
      
      switch (dateFilter) {
        case 'today':
          matchesDate = checkIn.toDateString() === now.toDateString();
          break;
        case 'this_week':
          const weekStart = new Date(now.setDate(now.getDate() - now.getDay()));
          const weekEnd = new Date(now.setDate(now.getDate() - now.getDay() + 6));
          matchesDate = checkIn >= weekStart && checkIn <= weekEnd;
          break;
        case 'this_month':
          matchesDate = checkIn.getMonth() === now.getMonth() && checkIn.getFullYear() === now.getFullYear();
          break;
      }
    }
    
    return matchesSearch && matchesStatus && matchesDate;
  });

  // Group bookings by status
  const bookingsByStatus = {
    pending: filteredBookings.filter(b => b.status === 'pending'),
    confirmed: filteredBookings.filter(b => b.status === 'confirmed'),
    checked_in: filteredBookings.filter(b => b.status === 'checked_in'),
    checked_out: filteredBookings.filter(b => b.status === 'checked_out'),
    cancelled: filteredBookings.filter(b => b.status === 'cancelled')
  };

  const handleStatusUpdate = (bookingId: string, newStatus: Booking['status']) => {
    onBookingUpdate(bookingId, { status: newStatus, lastUpdated: new Date() });
    toast({
      title: "Booking updated",
      description: `Booking status changed to ${statusConfig[newStatus].label}`,
    });
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-ZA', { 
      weekday: 'short',
      year: 'numeric', 
      month: 'short', 
      day: 'numeric' 
    });
  };

  const formatDateRange = (checkIn: Date, checkOut: Date) => {
    const nights = Math.ceil((checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60 * 24));
    return `${formatDate(checkIn)} - ${formatDate(checkOut)} (${nights} nights)`;
  };

  const getUpcomingBookings = () => {
    const now = new Date();
    const upcoming = bookings.filter(booking => 
      booking.checkIn > now && 
      (booking.status === 'confirmed' || booking.status === 'pending')
    ).sort((a, b) => a.checkIn.getTime() - b.checkIn.getTime());
    
    return upcoming.slice(0, 5);
  };

  const BookingCard: React.FC<{ booking: Booking; compact?: boolean }> = ({ booking, compact = false }) => {
    const statusInfo = statusConfig[booking.status];
    const StatusIcon = statusInfo.icon;
    
    return (
      <Card className="hover:shadow-md transition-shadow cursor-pointer" onClick={() => setSelectedBooking(booking)}>
        <CardContent className={cn("p-4", compact && "p-3")}>
          <div className="flex items-start justify-between mb-3">
            <div className="flex items-center gap-3">
              <Avatar className={compact ? "h-8 w-8" : "h-10 w-10"}>
                <AvatarImage src={booking.guest.avatar} />
                <AvatarFallback>{booking.guest.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
              </Avatar>
              <div>
                <h3 className={cn("font-semibold", compact ? "text-sm" : "text-base")}>
                  {booking.guest.name}
                </h3>
                <p className={cn("text-gray-600", compact ? "text-xs" : "text-sm")}>
                  {booking.propertyName}
                </p>
              </div>
            </div>
            
            <div className="flex flex-col items-end gap-2">
              <Badge className={statusInfo.color}>
                <StatusIcon className="h-3 w-3 mr-1" />
                {statusInfo.label}
              </Badge>
              {booking.paymentStatus !== 'paid' && (
                <Badge className={paymentStatusConfig[booking.paymentStatus].color}>
                  {paymentStatusConfig[booking.paymentStatus].label}
                </Badge>
              )}
            </div>
          </div>
          
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <Calendar className="h-4 w-4" />
              <span>{formatDateRange(booking.checkIn, booking.checkOut)}</span>
            </div>
            
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4 text-sm text-gray-600">
                <div className="flex items-center gap-1">
                  <User className="h-4 w-4" />
                  <span>{booking.guests} guests</span>
                </div>
                <div className="font-semibold text-gray-900">
                  R{booking.totalAmount.toLocaleString()}
                </div>
              </div>
              
              <div className="flex items-center gap-1">
                <Button variant="ghost" size="sm" onClick={(e) => {
                  e.stopPropagation();
                  onSendMessage(booking.guest.id, '');
                }}>
                  <MessageCircle className="h-4 w-4" />
                </Button>
                
                {booking.status === 'pending' && (
                  <Button 
                    variant="ghost" 
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleStatusUpdate(booking.id, 'confirmed');
                    }}
                  >
                    <CheckCircle className="h-4 w-4" />
                  </Button>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              <span>Booking Management</span>
              <Badge variant="secondary">{bookings.length}</Badge>
            </div>
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Filters */}
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search bookings..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-9"
              />
            </div>
            
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All statuses</SelectItem>
                {Object.entries(statusConfig).map(([value, config]) => (
                  <SelectItem key={value} value={value}>{config.label}</SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            <Select value={dateFilter} onValueChange={setDateFilter}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All dates</SelectItem>
                <SelectItem value="today">Today</SelectItem>
                <SelectItem value="this_week">This week</SelectItem>
                <SelectItem value="this_month">This month</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Quick Stats */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
        {Object.entries(bookingsByStatus).map(([status, bookings]) => {
          const config = statusConfig[status as keyof typeof statusConfig];
          return (
            <Card key={status}>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold">{bookings.length}</div>
                <div className={cn("text-sm font-medium", config.color.replace('bg-', 'text-').replace('-100', '-600'))}>
                  {config.label}
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Main Content */}
      <Tabs defaultValue="all" className="space-y-4">
        <TabsList>
          <TabsTrigger value="all">All Bookings ({filteredBookings.length})</TabsTrigger>
          <TabsTrigger value="upcoming">Upcoming ({getUpcomingBookings().length})</TabsTrigger>
          <TabsTrigger value="pending">Pending ({bookingsByStatus.pending.length})</TabsTrigger>
          <TabsTrigger value="active">Active ({bookingsByStatus.checked_in.length})</TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="space-y-4">
          {filteredBookings.length > 0 ? (
            filteredBookings.map((booking) => (
              <BookingCard key={booking.id} booking={booking} />
            ))
          ) : (
            <Card>
              <CardContent className="p-8 text-center">
                <Calendar className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                <h3 className="text-lg font-medium mb-2">No bookings found</h3>
                <p className="text-gray-600">
                  {searchQuery || statusFilter !== 'all' || dateFilter !== 'all'
                    ? 'Try adjusting your search or filters'
                    : 'Your bookings will appear here once guests start making reservations'}
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="upcoming" className="space-y-4">
          {getUpcomingBookings().map((booking) => (
            <BookingCard key={booking.id} booking={booking} />
          ))}
        </TabsContent>

        <TabsContent value="pending" className="space-y-4">
          {bookingsByStatus.pending.map((booking) => (
            <BookingCard key={booking.id} booking={booking} />
          ))}
        </TabsContent>

        <TabsContent value="active" className="space-y-4">
          {bookingsByStatus.checked_in.map((booking) => (
            <BookingCard key={booking.id} booking={booking} />
          ))}
        </TabsContent>
      </Tabs>

      {/* Booking Detail Modal */}
      {selectedBooking && (
        <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
          <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                Booking Details
                <Button variant="ghost" size="sm" onClick={() => setSelectedBooking(null)}>
                  ×
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Guest Info */}
              <div>
                <h3 className="font-semibold mb-3">Guest Information</h3>
                <div className="flex items-center gap-4 mb-4">
                  <Avatar className="h-12 w-12">
                    <AvatarImage src={selectedBooking.guest.avatar} />
                    <AvatarFallback>{selectedBooking.guest.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="font-medium">{selectedBooking.guest.name}</div>
                    <div className="text-sm text-gray-600">{selectedBooking.guest.email}</div>
                    <div className="text-sm text-gray-600">{selectedBooking.guest.phone}</div>
                  </div>
                </div>
                
                <div className="flex gap-2">
                  <Button variant="outline" size="sm">
                    <Mail className="h-4 w-4 mr-2" />
                    Email
                  </Button>
                  <Button variant="outline" size="sm">
                    <Phone className="h-4 w-4 mr-2" />
                    Call
                  </Button>
                  <Button variant="outline" size="sm">
                    <MessageCircle className="h-4 w-4 mr-2" />
                    Message
                  </Button>
                </div>
              </div>

              <Separator />

              {/* Booking Details */}
              <div>
                <h3 className="font-semibold mb-3">Booking Details</h3>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600">Booking ID:</span>
                    <div className="font-medium">{selectedBooking.id}</div>
                  </div>
                  <div>
                    <span className="text-gray-600">Property:</span>
                    <div className="font-medium">{selectedBooking.propertyName}</div>
                  </div>
                  <div>
                    <span className="text-gray-600">Check-in:</span>
                    <div className="font-medium">{formatDate(selectedBooking.checkIn)}</div>
                  </div>
                  <div>
                    <span className="text-gray-600">Check-out:</span>
                    <div className="font-medium">{formatDate(selectedBooking.checkOut)}</div>
                  </div>
                  <div>
                    <span className="text-gray-600">Guests:</span>
                    <div className="font-medium">{selectedBooking.guests}</div>
                  </div>
                  <div>
                    <span className="text-gray-600">Total Amount:</span>
                    <div className="font-medium">R{selectedBooking.totalAmount.toLocaleString()}</div>
                  </div>
                </div>
              </div>

              {selectedBooking.specialRequests && (
                <>
                  <Separator />
                  <div>
                    <h3 className="font-semibold mb-2">Special Requests</h3>
                    <p className="text-sm text-gray-700">{selectedBooking.specialRequests}</p>
                  </div>
                </>
              )}

              <Separator />

              {/* Status Actions */}
              <div>
                <h3 className="font-semibold mb-3">Update Status</h3>
                <div className="flex flex-wrap gap-2">
                  {Object.entries(statusConfig).map(([status, config]) => {
                    if (status === selectedBooking.status) return null;
                    return (
                      <Button
                        key={status}
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          handleStatusUpdate(selectedBooking.id, status as Booking['status']);
                          setSelectedBooking(null);
                        }}
                      >
                        {config.label}
                      </Button>
                    );
                  })}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};
