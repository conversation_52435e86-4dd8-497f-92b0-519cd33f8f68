<?php
// StayFinder Database Setup Script
// This script will automatically create the database and tables

header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html>
<head>
    <title>StayFinder Database Setup</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .btn { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; }
        .btn:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏖️ KZN StayFinder - Database Setup</h1>
        
        <?php
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['setup'])) {
            echo "<h2>Setting up database...</h2>";
            
            try {
                // Database connection
                $host = 'localhost';
                $username = 'root';
                $password = '';
                
                // Connect to MySQL
                $pdo = new PDO("mysql:host=$host", $username, $password);
                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                
                echo "<div class='success'>✅ Connected to MySQL successfully</div>";
                
                // Create database
                $pdo->exec("CREATE DATABASE IF NOT EXISTS stayfinder_dev CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
                echo "<div class='success'>✅ Database 'stayfinder_dev' created</div>";
                
                // Use the database
                $pdo->exec("USE stayfinder_dev");
                
                // Read and execute SQL file
                $sqlFile = __DIR__ . '/database_init.sql';
                if (file_exists($sqlFile)) {
                    $sql = file_get_contents($sqlFile);
                    
                    // Split SQL into individual statements
                    $statements = array_filter(array_map('trim', explode(';', $sql)));
                    
                    foreach ($statements as $statement) {
                        if (!empty($statement) && !preg_match('/^(CREATE DATABASE|USE)/i', $statement)) {
                            try {
                                $pdo->exec($statement);
                            } catch (Exception $e) {
                                // Skip errors for statements that might already exist
                                if (!strpos($e->getMessage(), 'already exists')) {
                                    throw $e;
                                }
                            }
                        }
                    }
                    
                    echo "<div class='success'>✅ Database tables and sample data created</div>";
                } else {
                    echo "<div class='error'>❌ database_init.sql file not found</div>";
                }
                
                // Verify tables were created
                $stmt = $pdo->query("SHOW TABLES");
                $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
                
                echo "<div class='info'>";
                echo "<strong>📊 Tables created:</strong><br>";
                foreach ($tables as $table) {
                    echo "• $table<br>";
                }
                echo "</div>";
                
                // Show sample data counts
                $counts = [];
                foreach (['users', 'properties', 'bookings', 'reviews'] as $table) {
                    if (in_array($table, $tables)) {
                        $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
                        $count = $stmt->fetchColumn();
                        $counts[$table] = $count;
                    }
                }
                
                echo "<div class='info'>";
                echo "<strong>📈 Sample data loaded:</strong><br>";
                foreach ($counts as $table => $count) {
                    echo "• $table: $count records<br>";
                }
                echo "</div>";
                
                echo "<div class='success'>";
                echo "<h3>🎉 Database setup completed successfully!</h3>";
                echo "<p><strong>Test Login Credentials:</strong></p>";
                echo "<ul>";
                echo "<li><strong>Email:</strong> <EMAIL></li>";
                echo "<li><strong>Password:</strong> password123</li>";
                echo "<li><strong>Role:</strong> Guest</li>";
                echo "</ul>";
                echo "<p><strong>Other test accounts:</strong></p>";
                echo "<ul>";
                echo "<li><EMAIL> (Host)</li>";
                echo "<li><EMAIL> (Admin)</li>";
                echo "</ul>";
                echo "</div>";
                
                echo "<div class='info'>";
                echo "<h3>🚀 Next Steps:</h3>";
                echo "<ol>";
                echo "<li>Database is ready ✅</li>";
                echo "<li>Now we need to start the backend server</li>";
                echo "<li>Then start the frontend server</li>";
                echo "</ol>";
                echo "</div>";
                
            } catch (Exception $e) {
                echo "<div class='error'>❌ Error: " . $e->getMessage() . "</div>";
            }
        } else {
            ?>
            <div class="info">
                <h2>🔧 Automatic Database Setup</h2>
                <p>This will automatically:</p>
                <ul>
                    <li>Create the <strong>stayfinder_dev</strong> database</li>
                    <li>Create all required tables (users, properties, bookings, reviews, etc.)</li>
                    <li>Insert sample data for testing</li>
                    <li>Set up test user accounts</li>
                </ul>
                <p><strong>Requirements:</strong></p>
                <ul>
                    <li>✅ XAMPP MySQL is running (Port 3306)</li>
                    <li>✅ Default MySQL user: root (no password)</li>
                </ul>
            </div>
            
            <form method="POST">
                <button type="submit" name="setup" class="btn">🚀 Setup Database Now</button>
            </form>
            <?php
        }
        ?>
        
        <hr style="margin: 30px 0;">
        <div class="info">
            <h3>📋 Manual phpMyAdmin Access</h3>
            <p>If you prefer to set up manually:</p>
            <p><a href="http://localhost/phpmyadmin" target="_blank">Open phpMyAdmin</a></p>
        </div>
    </div>
</body>
</html>
