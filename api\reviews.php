<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: http://localhost:8081');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Credentials: true');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once 'config.php';
require_once 'auth.php';

function getPropertyReviews($propertyId, $limit = null, $offset = 0, $sortBy = 'created_at', $sortOrder = 'DESC') {
    // Return mock data for now
    $mockReviews = [
        [
            'id' => 'review-1',
            'property_id' => $propertyId,
            'reviewer_id' => 'user-1',
            'booking_id' => 'booking-1',
            'rating' => 5,
            'comment' => 'Absolutely amazing stay! The villa was exactly as described and the host was incredibly helpful. The ocean views were breathtaking and we loved the private pool. Would definitely stay here again!',
            'response' => 'Thank you so much for the wonderful review! We\'re thrilled you enjoyed your stay and hope to welcome you back soon.',
            'response_date' => '2024-05-21 10:30:00',
            'created_at' => '2024-05-20 10:30:00',
            'updated_at' => '2024-05-20 10:30:00',
            'reviewer_name' => 'Sarah Johnson',
            'reviewer_profile_picture' => '/placeholder.svg',
            'property_title' => 'Luxury Villa in Margate',
            'host_name' => 'Jane Smith',
            'stay_date' => '2024-05-15',
            'has_response' => true
        ],
        [
            'id' => 'review-2',
            'property_id' => $propertyId,
            'reviewer_id' => 'user-2',
            'booking_id' => 'booking-2',
            'rating' => 4,
            'comment' => 'Great location right on the beach. The cottage was clean and comfortable. Only minor issue was the WiFi was a bit slow, but overall a fantastic experience.',
            'response' => null,
            'response_date' => null,
            'created_at' => '2024-04-25 14:15:00',
            'updated_at' => '2024-04-25 14:15:00',
            'reviewer_name' => 'Mike Wilson',
            'reviewer_profile_picture' => null,
            'property_title' => 'Luxury Villa in Margate',
            'host_name' => 'Jane Smith',
            'stay_date' => '2024-04-20',
            'has_response' => false
        ],
        [
            'id' => 'review-3',
            'property_id' => $propertyId,
            'reviewer_id' => 'user-3',
            'booking_id' => 'booking-3',
            'rating' => 5,
            'comment' => 'Perfect getaway! The property exceeded our expectations. Beautiful views, excellent amenities, and the host was very responsive to our questions.',
            'response' => 'We\'re so happy you had a perfect getaway! Thank you for taking care of our property.',
            'response_date' => '2024-03-12 09:00:00',
            'created_at' => '2024-03-10 16:45:00',
            'updated_at' => '2024-03-10 16:45:00',
            'reviewer_name' => 'Emily Davis',
            'reviewer_profile_picture' => '/placeholder.svg',
            'property_title' => 'Luxury Villa in Margate',
            'host_name' => 'Jane Smith',
            'stay_date' => '2024-03-05',
            'has_response' => true
        ]
    ];

    // Apply sorting
    if ($sortBy === 'rating') {
        usort($mockReviews, function($a, $b) use ($sortOrder) {
            return $sortOrder === 'DESC' ? $b['rating'] - $a['rating'] : $a['rating'] - $b['rating'];
        });
    } else {
        usort($mockReviews, function($a, $b) use ($sortOrder) {
            $timeA = strtotime($a['created_at']);
            $timeB = strtotime($b['created_at']);
            return $sortOrder === 'DESC' ? $timeB - $timeA : $timeA - $timeB;
        });
    }

    // Apply limit and offset
    if ($limit) {
        $mockReviews = array_slice($mockReviews, $offset, $limit);
    }

    return $mockReviews;
}

function getPropertyRatingStats($propertyId) {
    // Return mock stats for now
    return [
        'total_reviews' => 3,
        'average_rating' => 4.7,
        'five_star' => 2,
        'four_star' => 1,
        'three_star' => 0,
        'two_star' => 0,
        'one_star' => 0,
        'five_star_percent' => 66.7,
        'four_star_percent' => 33.3,
        'three_star_percent' => 0,
        'two_star_percent' => 0,
        'one_star_percent' => 0
    ];
}

function createReview($data) {
    global $pdo;
    
    try {
        // Validate required fields
        $required = ['property_id', 'booking_id', 'rating', 'comment'];
        foreach ($required as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                throw new Exception("Missing required field: $field");
            }
        }
        
        // Validate rating
        if ($data['rating'] < 1 || $data['rating'] > 5) {
            throw new Exception("Rating must be between 1 and 5");
        }
        
        // Check if review already exists for this booking
        $stmt = $pdo->prepare("SELECT id FROM reviews WHERE booking_id = ?");
        $stmt->execute([$data['booking_id']]);
        if ($stmt->fetch()) {
            throw new Exception("Review already exists for this booking");
        }
        
        // Verify booking belongs to the user and is completed
        $stmt = $pdo->prepare("
            SELECT guest_id, booking_status 
            FROM bookings 
            WHERE id = ? AND property_id = ?
        ");
        $stmt->execute([$data['booking_id'], $data['property_id']]);
        $booking = $stmt->fetch();
        
        if (!$booking) {
            throw new Exception("Booking not found");
        }
        
        if ($booking['booking_status'] !== 'completed') {
            throw new Exception("Can only review completed bookings");
        }
        
        // Create review
        $reviewId = 'review-' . uniqid();
        $stmt = $pdo->prepare("
            INSERT INTO reviews (
                id, property_id, reviewer_id, booking_id, 
                rating, comment, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
        ");
        
        $stmt->execute([
            $reviewId,
            $data['property_id'],
            $booking['guest_id'],
            $data['booking_id'],
            $data['rating'],
            $data['comment']
        ]);
        
        return $reviewId;
        
    } catch (Exception $e) {
        error_log("Error creating review: " . $e->getMessage());
        throw $e;
    }
}

function updateReview($reviewId, $data, $userId) {
    global $pdo;
    
    try {
        // Check if review exists and belongs to user
        $stmt = $pdo->prepare("
            SELECT reviewer_id, created_at 
            FROM reviews 
            WHERE id = ?
        ");
        $stmt->execute([$reviewId]);
        $review = $stmt->fetch();
        
        if (!$review) {
            throw new Exception("Review not found");
        }
        
        if ($review['reviewer_id'] !== $userId) {
            throw new Exception("Not authorized to update this review");
        }
        
        // Check if review is still editable (within 24 hours)
        $createdTime = strtotime($review['created_at']);
        $currentTime = time();
        $hoursSinceCreated = ($currentTime - $createdTime) / 3600;
        
        if ($hoursSinceCreated > 24) {
            throw new Exception("Review can only be edited within 24 hours of creation");
        }
        
        // Update review
        $updateFields = [];
        $values = [];
        
        if (isset($data['rating'])) {
            if ($data['rating'] < 1 || $data['rating'] > 5) {
                throw new Exception("Rating must be between 1 and 5");
            }
            $updateFields[] = "rating = ?";
            $values[] = $data['rating'];
        }
        
        if (isset($data['comment'])) {
            $updateFields[] = "comment = ?";
            $values[] = $data['comment'];
        }
        
        if (empty($updateFields)) {
            throw new Exception("No fields to update");
        }
        
        $updateFields[] = "updated_at = NOW()";
        $values[] = $reviewId;
        
        $sql = "UPDATE reviews SET " . implode(', ', $updateFields) . " WHERE id = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute($values);
        
        return true;
        
    } catch (Exception $e) {
        error_log("Error updating review: " . $e->getMessage());
        throw $e;
    }
}

function addHostResponse($reviewId, $response, $hostId) {
    global $pdo;
    
    try {
        // Verify host owns the property
        $stmt = $pdo->prepare("
            SELECT p.host_id 
            FROM reviews r
            JOIN properties p ON r.property_id = p.id
            WHERE r.id = ?
        ");
        $stmt->execute([$reviewId]);
        $review = $stmt->fetch();
        
        if (!$review) {
            throw new Exception("Review not found");
        }
        
        if ($review['host_id'] !== $hostId) {
            throw new Exception("Not authorized to respond to this review");
        }
        
        // Add response
        $stmt = $pdo->prepare("
            UPDATE reviews 
            SET response = ?, response_date = NOW(), updated_at = NOW()
            WHERE id = ?
        ");
        $stmt->execute([$response, $reviewId]);
        
        return true;
        
    } catch (Exception $e) {
        error_log("Error adding host response: " . $e->getMessage());
        throw $e;
    }
}

// Handle the request
$method = $_SERVER['REQUEST_METHOD'];
$path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
$pathParts = explode('/', trim($path, '/'));

try {
    switch ($method) {
        case 'GET':
            if (isset($_GET['property_id'])) {
                // Get reviews for a property
                $propertyId = $_GET['property_id'];
                $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : null;
                $offset = isset($_GET['offset']) ? (int)$_GET['offset'] : 0;
                $sortBy = $_GET['sort_by'] ?? 'created_at';
                $sortOrder = $_GET['sort_order'] ?? 'DESC';
                
                if (isset($_GET['stats_only']) && $_GET['stats_only'] === 'true') {
                    // Return only rating statistics
                    $stats = getPropertyRatingStats($propertyId);
                    echo json_encode([
                        'success' => true,
                        'data' => $stats
                    ]);
                } else {
                    // Return reviews and stats
                    $reviews = getPropertyReviews($propertyId, $limit, $offset, $sortBy, $sortOrder);
                    $stats = getPropertyRatingStats($propertyId);
                    
                    echo json_encode([
                        'success' => true,
                        'data' => [
                            'reviews' => $reviews,
                            'stats' => $stats,
                            'pagination' => [
                                'limit' => $limit,
                                'offset' => $offset,
                                'total' => $stats['total_reviews'] ?? 0
                            ]
                        ]
                    ]);
                }
            } else {
                http_response_code(400);
                echo json_encode(['error' => 'Property ID is required']);
            }
            break;
            
        case 'POST':
            $user = getCurrentUser();
            if (!$user) {
                http_response_code(401);
                echo json_encode(['error' => 'Authentication required']);
                exit;
            }
            
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (isset($_GET['action']) && $_GET['action'] === 'respond') {
                // Host responding to a review
                $reviewId = $_GET['review_id'] ?? '';
                $response = $input['response'] ?? '';
                
                if (empty($reviewId) || empty($response)) {
                    http_response_code(400);
                    echo json_encode(['error' => 'Review ID and response are required']);
                    exit;
                }
                
                $success = addHostResponse($reviewId, $response, $user['id']);
                
                if ($success) {
                    echo json_encode([
                        'success' => true,
                        'message' => 'Response added successfully'
                    ]);
                } else {
                    http_response_code(500);
                    echo json_encode(['error' => 'Failed to add response']);
                }
            } else {
                // Create new review
                $reviewId = createReview($input);
                
                echo json_encode([
                    'success' => true,
                    'message' => 'Review created successfully',
                    'review_id' => $reviewId
                ]);
            }
            break;
            
        case 'PUT':
            $user = getCurrentUser();
            if (!$user) {
                http_response_code(401);
                echo json_encode(['error' => 'Authentication required']);
                exit;
            }
            
            $reviewId = $_GET['review_id'] ?? '';
            if (empty($reviewId)) {
                http_response_code(400);
                echo json_encode(['error' => 'Review ID is required']);
                exit;
            }
            
            $input = json_decode(file_get_contents('php://input'), true);
            
            $success = updateReview($reviewId, $input, $user['id']);
            
            if ($success) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Review updated successfully'
                ]);
            } else {
                http_response_code(500);
                echo json_encode(['error' => 'Failed to update review']);
            }
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            break;
    }
    
} catch (Exception $e) {
    error_log("Reviews API error: " . $e->getMessage());
    http_response_code(400);
    echo json_encode(['error' => $e->getMessage()]);
}
?>
