import React, { useState, useEffect } from 'react';
import { Search, MapPin, Calendar, Users, Star, TrendingUp, Award, Shield } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';
import { SlideIn } from '@/components/ui/slide-in';
import { HoverAnimation } from '@/components/ui/hover-animation';
import { StaggeredAnimation } from '@/components/ui/staggered-animation';
import { useNavigate } from 'react-router-dom';
import { usePropertyTracking } from '@/hooks/usePropertyTracking';

interface HeroProps {
  onSearch?: (query: string) => void;
  className?: string;
}

export const Hero: React.FC<HeroProps> = ({ onSearch, className = '' }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [checkIn, setCheckIn] = useState('');
  const [checkOut, setCheckOut] = useState('');
  const [guests, setGuests] = useState('2');
  const navigate = useNavigate();
  const { trackSearch } = usePropertyTracking();

  const handleSearch = async () => {
    // Build search parameters
    const params = new URLSearchParams();

    if (searchQuery.trim()) {
      params.set('location', searchQuery.trim());
    }

    if (checkIn) {
      params.set('checkIn', checkIn);
    }

    if (checkOut) {
      params.set('checkOut', checkOut);
    }

    if (guests) {
      params.set('guests', guests);
    }

    // Track the search
    const filters = {
      location: searchQuery.trim(),
      checkIn,
      checkOut,
      guests
    };

    await trackSearch(searchQuery.trim() || 'all properties', filters);

    // Navigate to search page with parameters
    const searchPath = params.toString() ? `/search?${params.toString()}` : '/search';
    navigate(searchPath);

    // Also call the optional onSearch prop for backward compatibility
    if (onSearch) {
      onSearch(searchQuery);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  return (
    <div className={`relative min-h-screen bg-gradient-to-br from-sea-green-50 via-white to-ocean-blue-50 overflow-hidden ${className}`}>
      {/* Background Elements */}
      <div className="absolute inset-0 bg-[url('/hero-pattern.svg')] opacity-5"></div>
      
      <div className="relative z-10 container mx-auto px-4 pt-20 pb-16">
        <div className="max-w-6xl mx-auto text-center">
          {/* Main Hero Content */}
          <SlideIn direction="up" delay={200}>
            <div className="mb-12">
              <h1 className="text-5xl md:text-7xl font-bold text-gray-900 mb-6 leading-tight">
                Discover Your Perfect
                <span className="block bg-gradient-to-r from-sea-green-600 to-ocean-blue-600 bg-clip-text text-transparent">
                  South African Getaway
                </span>
              </h1>
              <p className="text-xl md:text-2xl text-gray-600 mb-8 max-w-3xl mx-auto leading-relaxed">
                From Cape Town's stunning coastline to Johannesburg's vibrant culture, 
                find unique accommodations across all 9 provinces of South Africa.
              </p>
            </div>
          </SlideIn>

          {/* Search Section */}
          <SlideIn direction="up" delay={400}>
            <div className="bg-white rounded-2xl shadow-2xl p-8 mb-12 max-w-4xl mx-auto border border-gray-100">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div className="relative">
                  <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                  <Input
                    placeholder="Where to?"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    onKeyPress={handleKeyPress}
                    className="pl-10 h-12 text-lg border-gray-200 focus:border-sea-green-500"
                  />
                </div>
                <div className="relative">
                  <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                  <Input
                    type="date"
                    value={checkIn}
                    onChange={(e) => setCheckIn(e.target.value)}
                    className="pl-10 h-12 text-lg border-gray-200 focus:border-sea-green-500"
                  />
                </div>
                <div className="relative">
                  <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                  <Input
                    type="date"
                    value={checkOut}
                    onChange={(e) => setCheckOut(e.target.value)}
                    className="pl-10 h-12 text-lg border-gray-200 focus:border-sea-green-500"
                  />
                </div>
                <div className="relative">
                  <Users className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                  <select
                    value={guests}
                    onChange={(e) => setGuests(e.target.value)}
                    className="w-full pl-10 h-12 text-lg border border-gray-200 rounded-md focus:border-sea-green-500 focus:outline-none"
                  >
                    <option value="1">1 Guest</option>
                    <option value="2">2 Guests</option>
                    <option value="3">3 Guests</option>
                    <option value="4">4 Guests</option>
                    <option value="5">5+ Guests</option>
                  </select>
                </div>
              </div>
              
              <Button 
                onClick={handleSearch}
                size="lg" 
                className="w-full md:w-auto px-12 py-4 text-lg bg-gradient-to-r from-sea-green-600 to-ocean-blue-600 hover:from-sea-green-700 hover:to-ocean-blue-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300"
              >
                <Search className="mr-2 h-5 w-5" />
                Search Properties
              </Button>
            </div>
          </SlideIn>

          {/* Popular Destinations */}
          <SlideIn direction="up" delay={600}>
            <div className="mb-16">
              <h3 className="text-2xl font-semibold text-gray-800 mb-6">Popular Destinations</h3>
              <div className="flex flex-wrap justify-center gap-3">
                <StaggeredAnimation delay={100}>
                  {['Cape Town', 'Johannesburg', 'Durban', 'Stellenbosch', 'Port Elizabeth', 'Knysna'].map((location) => (
                    <HoverAnimation key={location} type="scale">
                      <button
                        onClick={() => setSearchQuery(location)}
                        className="px-5 py-3 text-sm bg-gradient-to-r from-white to-gray-50 hover:from-sea-green-50 hover:to-ocean-blue-50 text-gray-700 hover:text-sea-green-700 rounded-full transition-all duration-300 border border-gray-200 hover:border-sea-green-300 hover:shadow-lg font-medium transform hover:-translate-y-0.5"
                      >
                        {location}
                      </button>
                    </HoverAnimation>
                  ))}
                </StaggeredAnimation>
              </div>
            </div>
          </SlideIn>

          {/* Stats Section */}
          <SlideIn direction="up" delay={700}>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto mb-16">
              <StaggeredAnimation delay={100}>
                <HoverAnimation type="lift">
                  <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-gray-50 hover:shadow-xl transition-all duration-300">
                    <CardContent className="p-6 text-center">
                      <div className="text-3xl font-bold text-sea-green-600 mb-2">1000+</div>
                      <div className="text-gray-600 font-medium">Properties</div>
                    </CardContent>
                  </Card>
                </HoverAnimation>
              </StaggeredAnimation>

              <StaggeredAnimation delay={200}>
                <HoverAnimation type="lift">
                  <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-gray-50 hover:shadow-xl transition-all duration-300">
                    <CardContent className="p-6 text-center">
                      <div className="text-3xl font-bold text-ocean-blue-600 mb-2">9</div>
                      <div className="text-gray-600 font-medium">Provinces</div>
                    </CardContent>
                  </Card>
                </HoverAnimation>
              </StaggeredAnimation>

              <StaggeredAnimation delay={300}>
                <HoverAnimation type="lift">
                  <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-gray-50 hover:shadow-xl transition-all duration-300">
                    <CardContent className="p-6 text-center">
                      <div className="text-3xl font-bold text-sea-green-600 mb-2">50K+</div>
                      <div className="text-gray-600 font-medium">Happy Guests</div>
                    </CardContent>
                  </Card>
                </HoverAnimation>
              </StaggeredAnimation>

              <StaggeredAnimation delay={400}>
                <HoverAnimation type="lift">
                  <Card className="border-0 shadow-lg bg-gradient-to-br from-white to-gray-50 hover:shadow-xl transition-all duration-300">
                    <CardContent className="p-6 text-center">
                      <div className="text-3xl font-bold text-ocean-blue-600 mb-2">4.8</div>
                      <div className="text-gray-600 font-medium">Average Rating</div>
                    </CardContent>
                  </Card>
                </HoverAnimation>
              </StaggeredAnimation>
            </div>
          </SlideIn>
        </div>
      </div>
    </div>
  );
};