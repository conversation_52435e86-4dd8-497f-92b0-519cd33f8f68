
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Search, Calendar, Users, MapPin, ArrowRight, Sparkles, Play, TrendingUp, Award, Shield, Zap, Heart, Globe, Camera, Star, Home } from 'lucide-react';
import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { SearchResults } from './SearchResults';
import { ModernSearchInput } from './ModernSearchInput';
import searchSuggestionsService from '../services/searchSuggestions';
import {
  PageTransition,
  SlideIn,
  ScaleIn,
  StaggeredAnimation,
  HoverAnimation
} from '@/components/ui/page-transitions';

export const Hero = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [guests, setGuests] = useState('');
  const [showResults, setShowResults] = useState(false);
  const [popularDestinations, setPopularDestinations] = useState([]);
  const [loadingDestinations, setLoadingDestinations] = useState(true);
  const navigate = useNavigate();

  const handleSearch = () => {
    if (searchQuery.trim()) {
      // Navigate to search page with query parameters
      const searchParams = new URLSearchParams();
      searchParams.set('location', searchQuery.trim());
      if (guests) {
        searchParams.set('guests', guests);
      }
      navigate(`/search?${searchParams.toString()}`);
    }
  };

  const handleQuickSearch = () => {
    if (searchQuery.trim()) {
      setShowResults(true);
    }
  };

  const handleSuggestionSelect = (suggestion: any) => {
    const searchValue = suggestion.fullLocation || suggestion.text;

    // Set the search query first
    setSearchQuery(searchValue);

    // Then trigger the search after a brief delay to ensure state update
    setTimeout(() => {
      if (searchValue.trim()) {
        setShowResults(true);
      }
    }, 100);
  };

  // Load popular destinations on component mount
  useEffect(() => {
    const loadPopularDestinations = async () => {
      try {
        setLoadingDestinations(true);

        // Try API first
        const destinations = await searchSuggestionsService.getPopularDestinations(8);

        if (destinations && destinations.length > 0) {
          setPopularDestinations(destinations);
        } else {
          throw new Error('No API destinations returned');
        }
      } catch (error) {
        console.error('Error loading popular destinations, using fallback:', error);

        // Fallback to static popular destinations
        const fallbackDestinations = [
          { id: 'cape-town', text: 'Cape Town', fullLocation: 'Cape Town, Western Cape', propertyCount: 3 },
          { id: 'johannesburg', text: 'Johannesburg', fullLocation: 'Johannesburg, Gauteng', propertyCount: 2 },
          { id: 'durban', text: 'Durban', fullLocation: 'Durban, KwaZulu-Natal', propertyCount: 1 },
          { id: 'stellenbosch', text: 'Stellenbosch', fullLocation: 'Stellenbosch, Western Cape', propertyCount: 1 },
          { id: 'hermanus', text: 'Hermanus', fullLocation: 'Hermanus, Western Cape', propertyCount: 1 },
          { id: 'kruger', text: 'Kruger Park', fullLocation: 'Kruger Park Area, Mpumalanga', propertyCount: 1 }
        ];

        setPopularDestinations(fallbackDestinations);
      } finally {
        setLoadingDestinations(false);
      }
    };

    loadPopularDestinations();
  }, []);

  return (
    <PageTransition>
      <div className="relative min-h-[700px] flex items-center justify-center overflow-hidden">
        {/* Enhanced Background with Parallax Effect */}
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat transform scale-110 transition-transform duration-1000"
          style={{
            backgroundImage: 'linear-gradient(135deg, rgba(20,184,166,0.3), rgba(59,130,246,0.3)), linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.4)), url("https://images.unsplash.com/photo-1500375592092-40eb2168fd21?w=1200&h=600&fit=crop")'
          }}
        />

        {/* Floating Elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-20 left-10 w-20 h-20 bg-white/10 rounded-full blur-xl animate-pulse"></div>
          <div className="absolute top-40 right-20 w-32 h-32 bg-sea-green-500/20 rounded-full blur-2xl animate-pulse delay-1000"></div>
          <div className="absolute bottom-32 left-1/4 w-24 h-24 bg-ocean-blue-500/20 rounded-full blur-xl animate-pulse delay-500"></div>
        </div>

        {/* Content */}
        <div className="relative z-10 text-center text-white px-4 max-w-6xl mx-auto">
          {/* Enhanced Badge */}
          <SlideIn direction="up" delay={100}>
            <div className="inline-flex items-center gap-2 bg-white/15 backdrop-blur-md border border-white/30 rounded-full px-6 py-3 mb-8 shadow-lg">
              <Sparkles className="h-5 w-5 text-sea-green-300 animate-pulse" />
              <span className="text-sm font-semibold text-white">Discover South Africa's Hidden Gems</span>
              <Badge className="bg-sea-green-500 text-white text-xs px-2 py-1">New</Badge>
            </div>
          </SlideIn>

          <SlideIn direction="up" delay={200}>
            <h1 className="text-5xl md:text-7xl lg:text-8xl font-bold mb-8 leading-tight">
              <span className="block animate-fade-in">Find Your Perfect</span>
              <span className="block text-transparent bg-clip-text bg-gradient-to-r from-sea-green-300 via-ocean-blue-300 to-sunset-orange-300 animate-slide-in-left delay-300">
                South African
              </span>
              <span className="block animate-slide-in-right delay-500">Getaway</span>
            </h1>
          </SlideIn>

          <SlideIn direction="up" delay={400}>
            <p className="text-xl md:text-2xl mb-12 text-gray-200 max-w-4xl mx-auto leading-relaxed">
              From Cape Town's stunning coastline to Kruger's wildlife adventures, discover unique accommodations across the Rainbow Nation
            </p>
          </SlideIn>

          {/* Stats Bar */}
          <SlideIn direction="up" delay={500}>
            <div className="flex justify-center items-center gap-8 mb-12">
              <StaggeredAnimation delay={100}>
                {[
                  { icon: Home, label: '500+ Properties', value: '500+' },
                  { icon: Star, label: '4.8 Rating', value: '4.8★' },
                  { icon: Users, label: '10k+ Guests', value: '10k+' },
                  { icon: Award, label: 'Top Rated', value: '#1' }
                ].map((stat, index) => {
                  const Icon = stat.icon;
                  return (
                    <HoverAnimation key={index} type="scale">
                      <div className="text-center">
                        <div className="flex items-center justify-center w-12 h-12 bg-white/20 backdrop-blur-sm rounded-full mb-2 mx-auto">
                          <Icon className="h-6 w-6 text-white" />
                        </div>
                        <div className="text-lg font-bold text-white">{stat.value}</div>
                        <div className="text-xs text-gray-300">{stat.label}</div>
                      </div>
                    </HoverAnimation>
                  );
                })}
              </StaggeredAnimation>
            </div>
          </SlideIn>

          {/* Enhanced Modern Search Bar */}
          <SlideIn direction="up" delay={600}>
            <div className="max-w-5xl mx-auto mb-12">
              <HoverAnimation type="lift">
                <div className="bg-white/95 backdrop-blur-md rounded-3xl p-8 shadow-2xl border border-white/30 hover:shadow-3xl transition-all duration-300">
                  <div className="grid grid-cols-1 lg:grid-cols-12 gap-6 items-end">
                {/* Location Input */}
                <div className="lg:col-span-6 space-y-3">
                  <label className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-sea-green-600" />
                    Where to?
                  </label>
                  <ModernSearchInput
                    value={searchQuery}
                    onChange={setSearchQuery}
                    onSearch={handleSearch}
                    onSuggestionSelect={handleSuggestionSelect}
                    placeholder="Search destinations, cities, attractions..."
                    size="lg"
                    className="w-full"
                  />
                </div>

                {/* Guests Select */}
                <div className="lg:col-span-3 space-y-3">
                  <label className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                    <Users className="h-4 w-4 text-sea-green-600" />
                    Guests
                  </label>
                  <Select value={guests} onValueChange={setGuests}>
                    <SelectTrigger className="h-14 text-base border-gray-200 rounded-xl hover:border-gray-300 focus:border-sea-green-500 focus:ring-2 focus:ring-sea-green-500/20">
                      <SelectValue placeholder="Add guests" />
                    </SelectTrigger>
                    <SelectContent className="rounded-xl border-gray-200">
                      {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((num) => (
                        <SelectItem key={num} value={num.toString()} className="rounded-lg">
                          {num} {num === 1 ? 'guest' : 'guests'}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Search Button */}
                <div className="lg:col-span-3">
                  <div className="space-y-3">
                    <div className="h-6"></div> {/* Spacer to align with other inputs */}
                    <Button
                      size="lg"
                      onClick={handleSearch}
                      className="w-full h-14 bg-gradient-to-r from-sea-green-500 via-ocean-blue-500 to-sea-green-600 hover:from-sea-green-600 hover:via-ocean-blue-600 hover:to-sea-green-700 text-white rounded-xl font-semibold text-base shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
                    >
                      <Search className="h-5 w-5 mr-2" />
                      Search Properties
                      <ArrowRight className="h-5 w-5 ml-2" />
                    </Button>
                  </div>
                </div>
              </div>

                  {/* Enhanced Popular Destinations */}
                  <div className="mt-8 pt-6 border-t border-gray-200/50" data-popular-destinations>
                    <p className="text-sm font-semibold text-gray-700 mb-6 flex items-center gap-2">
                      <TrendingUp className="h-4 w-4 text-sea-green-600" />
                      Trending destinations
                    </p>

                    {loadingDestinations ? (
                      <div className="flex gap-3">
                        {[1, 2, 3, 4, 5, 6].map((i) => (
                          <div key={i} className="h-10 w-28 bg-gray-200 rounded-full animate-pulse"></div>
                        ))}
                      </div>
                    ) : (
                      <StaggeredAnimation delay={100}>
                        {popularDestinations.length > 0 ? (
                          popularDestinations.map((destination: any) => (
                            <HoverAnimation type="scale">
                              <button
                                key={destination.id}
                                onClick={() => setSearchQuery(destination.fullLocation || destination.text)}
                                className="px-5 py-3 text-sm bg-gradient-to-r from-white to-gray-50 hover:from-sea-green-50 hover:to-ocean-blue-50 text-gray-700 hover:text-sea-green-700 rounded-full transition-all duration-300 border border-gray-200 hover:border-sea-green-300 hover:shadow-lg group transform hover:-translate-y-0.5"
                                title={`${destination.propertyCount} properties available`}
                              >
                                <span className="font-medium">{destination.text}</span>
                                {destination.propertyCount && (
                                  <span className="ml-2 text-xs text-gray-500 group-hover:text-sea-green-600 bg-gray-100 group-hover:bg-sea-green-100 px-2 py-1 rounded-full">
                                    {destination.propertyCount}
                                  </span>
                                )}
                              </button>
                            </HoverAnimation>
                      ))
                    ) : (
                          // Fallback destinations if API fails
                          ['Cape Town', 'Johannesburg', 'Durban', 'Stellenbosch', 'Port Elizabeth', 'Knysna'].map((location) => (
                            <HoverAnimation key={location} type="scale">
                              <button
                                onClick={() => setSearchQuery(location)}
                                className="px-5 py-3 text-sm bg-gradient-to-r from-white to-gray-50 hover:from-sea-green-50 hover:to-ocean-blue-50 text-gray-700 hover:text-sea-green-700 rounded-full transition-all duration-300 border border-gray-200 hover:border-sea-green-300 hover:shadow-lg font-medium transform hover:-translate-y-0.5"
                              >
                                {location}
                              </button>
                            </HoverAnimation>
                          ))
                    )}
                    </StaggeredAnimation>
                  </div>
                )}
                </div>
              </HoverAnimation>
            </div>
          </SlideIn>

          {/* Enhanced Stats Section */}
          <SlideIn direction="up" delay={700}>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto mb-16">
              <StaggeredAnimation delay={150}>
                {[
                  { number: '500+', label: 'Properties', icon: '🏠', color: 'from-sea-green-500 to-sea-green-600' },
                  { number: '9', label: 'Provinces', icon: '📍', color: 'from-ocean-blue-500 to-ocean-blue-600' },
                  { number: '10k+', label: 'Happy Guests', icon: '😊', color: 'from-sunset-orange-500 to-sunset-orange-600' },
                  { number: '4.8★', label: 'Average Rating', icon: '⭐', color: 'from-yellow-500 to-yellow-600' }
                ].map((stat, index) => (
                  <HoverAnimation key={index} type="lift">
                    <div className="text-center group">
                      <div className="bg-white/15 backdrop-blur-md rounded-3xl p-6 border border-white/30 hover:bg-white/25 transition-all duration-500 group-hover:scale-110 shadow-lg hover:shadow-2xl">
                        <div className="text-3xl mb-3 transform group-hover:scale-110 transition-transform duration-300">{stat.icon}</div>
                        <div className={`text-3xl md:text-4xl font-bold bg-gradient-to-r ${stat.color} bg-clip-text text-transparent mb-2`}>{stat.number}</div>
                        <div className="text-sm text-gray-200 font-medium">{stat.label}</div>
                      </div>
                    </div>
                  </HoverAnimation>
                ))}
              </StaggeredAnimation>
            </div>
          </SlideIn>

          {/* Enhanced CTA Buttons */}
          <SlideIn direction="up" delay={800}>
            <div className="flex flex-col md:flex-row items-center justify-center space-y-6 md:space-y-0 md:space-x-8">
              <HoverAnimation type="lift">
                <Button
                  size="xl"
                  variant="outline"
                  onClick={handleQuickSearch}
                  className="bg-white/15 border-white/30 text-white hover:bg-white/25 backdrop-blur-md px-10 py-5 rounded-2xl font-bold transition-all duration-500 hover:scale-110 shadow-lg hover:shadow-2xl group"
                >
                  <Search className="h-6 w-6 mr-3 group-hover:rotate-12 transition-transform duration-300" />
                  Explore Properties
                </Button>
              </HoverAnimation>

              <HoverAnimation type="lift">
                <Button
                  size="xl"
                  className="bg-gradient-to-r from-sunset-orange-500 via-red-500 to-pink-500 hover:from-sunset-orange-600 hover:via-red-600 hover:to-pink-600 text-white px-10 py-5 rounded-2xl font-bold transition-all duration-500 hover:scale-110 shadow-xl hover:shadow-2xl group"
                >
                  <Zap className="h-6 w-6 mr-3 group-hover:rotate-12 transition-transform duration-300" />
                  List Your Property
                  <ArrowRight className="h-6 w-6 ml-3 group-hover:translate-x-1 transition-transform duration-300" />
                </Button>
              </HoverAnimation>
            </div>
          </SlideIn>
        </div>

        {/* Modern Floating elements */}
        <div className="absolute top-20 left-10 w-24 h-24 bg-gradient-to-br from-sea-green-400/20 to-ocean-blue-400/20 rounded-full animate-wave backdrop-blur-sm" style={{ animationDelay: '0s' }} />
        <div className="absolute top-40 right-20 w-20 h-20 bg-gradient-to-br from-ocean-blue-400/20 to-sunset-orange-400/20 rounded-full animate-wave backdrop-blur-sm" style={{ animationDelay: '1s' }} />
        <div className="absolute bottom-20 left-20 w-16 h-16 bg-gradient-to-br from-sunset-orange-400/20 to-sea-green-400/20 rounded-full animate-wave backdrop-blur-sm" style={{ animationDelay: '2s' }} />
        <div className="absolute top-1/3 right-1/4 w-12 h-12 bg-gradient-to-br from-sea-green-300/30 to-ocean-blue-300/30 rounded-full animate-wave backdrop-blur-sm" style={{ animationDelay: '3s' }} />
        <div className="absolute bottom-1/3 left-1/4 w-14 h-14 bg-gradient-to-br from-ocean-blue-300/30 to-sunset-orange-300/30 rounded-full animate-wave backdrop-blur-sm" style={{ animationDelay: '4s' }} />
      </div>

      {/* Quick Search Results Modal */}
      {showResults && (
        <SearchResults
          searchQuery={searchQuery}
          onClose={() => setShowResults(false)}
        />
      )}
    </PageTransition>
  );
};
