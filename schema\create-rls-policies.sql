-- ============================================================================
-- StayFinder Row Level Security (RLS) Policies
-- PostgreSQL/Supabase Compatible
-- Version: 1.0
-- Created: July 16, 2025
-- ============================================================================

-- ============================================================================
-- ENABLE ROW LEVEL SECURITY
-- ============================================================================

ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE properties ENABLE ROW LEVEL SECURITY;
ALTER TABLE property_amenities ENABLE ROW LEVEL SECURITY;
ALTER TABLE property_images ENABLE ROW LEVEL SECURITY;
ALTER TABLE bookings ENABLE ROW LEVEL SECURITY;
ALTER TABLE booking_payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE property_availability ENABLE ROW LEVEL SECURITY;

-- Amenities table is public (read-only for all users)
-- No RLS needed for amenities as it's reference data

-- ============================================================================
-- HELPER FUNCTIONS FOR RLS
-- ============================================================================

-- Function to check if user is admin
CREATE OR REPLACE FUNCTION public.is_admin() RETURNS BOOLEAN AS $$
  SELECT EXISTS (
    SELECT 1 FROM public.users
    WHERE auth_user_id = auth.uid()
    AND user_type = 'admin'
    AND is_active = TRUE
  );
$$ LANGUAGE sql STABLE SECURITY DEFINER;

-- Function to check if user is host
CREATE OR REPLACE FUNCTION public.is_host() RETURNS BOOLEAN AS $$
  SELECT EXISTS (
    SELECT 1 FROM public.users
    WHERE auth_user_id = auth.uid()
    AND user_type IN ('host', 'admin')
    AND is_active = TRUE
  );
$$ LANGUAGE sql STABLE SECURITY DEFINER;

-- Function to get current user's database ID
CREATE OR REPLACE FUNCTION public.current_user_id() RETURNS UUID AS $$
  SELECT id FROM public.users WHERE auth_user_id = auth.uid();
$$ LANGUAGE sql STABLE SECURITY DEFINER;

-- ============================================================================
-- USERS TABLE POLICIES
-- ============================================================================

-- Users can read their own profile
CREATE POLICY "Users can read own profile" ON users
  FOR SELECT USING (auth_user_id = auth.uid());

-- Users can update their own profile
CREATE POLICY "Users can update own profile" ON users
  FOR UPDATE USING (auth_user_id = auth.uid());

-- Users can insert their own profile (during registration)
CREATE POLICY "Users can insert own profile" ON users
  FOR INSERT WITH CHECK (auth_user_id = auth.uid());

-- Admins can read all users
CREATE POLICY "Admins can read all users" ON users
  FOR SELECT USING (public.is_admin());

-- Admins can update all users
CREATE POLICY "Admins can update all users" ON users
  FOR UPDATE USING (public.is_admin());

-- Public can read basic host information (for property listings)
CREATE POLICY "Public can read host info" ON users
  FOR SELECT USING (
    user_type = 'host' 
    AND is_active = TRUE 
    AND is_verified = TRUE
  );

-- ============================================================================
-- PROPERTIES TABLE POLICIES
-- ============================================================================

-- Anyone can read active properties
CREATE POLICY "Anyone can read active properties" ON properties
  FOR SELECT USING (status = 'active');

-- Hosts can read their own properties (all statuses)
CREATE POLICY "Hosts can read own properties" ON properties
  FOR SELECT USING (host_id = public.current_user_id());

-- Hosts can insert properties
CREATE POLICY "Hosts can insert properties" ON properties
  FOR INSERT WITH CHECK (
    public.is_host() AND host_id = public.current_user_id()
  );

-- Hosts can update their own properties
CREATE POLICY "Hosts can update own properties" ON properties
  FOR UPDATE USING (host_id = public.current_user_id());

-- Admins can read all properties
CREATE POLICY "Admins can read all properties" ON properties
  FOR SELECT USING (public.is_admin());

-- Admins can update all properties
CREATE POLICY "Admins can update all properties" ON properties
  FOR UPDATE USING (public.is_admin());

-- ============================================================================
-- PROPERTY AMENITIES POLICIES
-- ============================================================================

-- Anyone can read property amenities for active properties
CREATE POLICY "Anyone can read property amenities" ON property_amenities
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM properties p 
      WHERE p.id = property_id AND p.status = 'active'
    )
  );

-- Hosts can manage amenities for their properties
CREATE POLICY "Hosts can manage property amenities" ON property_amenities
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM properties p 
      WHERE p.id = property_id AND p.host_id = public.current_user_id()
    )
  );

-- ============================================================================
-- PROPERTY IMAGES POLICIES
-- ============================================================================

-- Anyone can read images for active properties
CREATE POLICY "Anyone can read property images" ON property_images
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM properties p 
      WHERE p.id = property_id AND p.status = 'active'
    )
  );

-- Hosts can manage images for their properties
CREATE POLICY "Hosts can manage property images" ON property_images
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM properties p 
      WHERE p.id = property_id AND p.host_id = public.current_user_id()
    )
  );

-- ============================================================================
-- BOOKINGS TABLE POLICIES
-- ============================================================================

-- Guests can read their own bookings
CREATE POLICY "Guests can read own bookings" ON bookings
  FOR SELECT USING (guest_id = public.current_user_id());

-- Hosts can read bookings for their properties
CREATE POLICY "Hosts can read property bookings" ON bookings
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM properties p 
      WHERE p.id = property_id AND p.host_id = public.current_user_id()
    )
  );

-- Guests can create bookings
CREATE POLICY "Guests can create bookings" ON bookings
  FOR INSERT WITH CHECK (guest_id = public.current_user_id());

-- Guests can update their own bookings (limited fields)
CREATE POLICY "Guests can update own bookings" ON bookings
  FOR UPDATE USING (guest_id = public.current_user_id());

-- Hosts can update bookings for their properties
CREATE POLICY "Hosts can update property bookings" ON bookings
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM properties p 
      WHERE p.id = property_id AND p.host_id = public.current_user_id()
    )
  );

-- Admins can read all bookings
CREATE POLICY "Admins can read all bookings" ON bookings
  FOR SELECT USING (public.is_admin());

-- ============================================================================
-- BOOKING PAYMENTS POLICIES
-- ============================================================================

-- Users can read payments for their bookings
CREATE POLICY "Users can read own booking payments" ON booking_payments
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM bookings b 
      WHERE b.id = booking_id 
      AND (b.guest_id = public.current_user_id() OR 
           EXISTS (SELECT 1 FROM properties p WHERE p.id = b.property_id AND p.host_id = public.current_user_id()))
    )
  );

-- System can insert payments (service role)
CREATE POLICY "System can insert payments" ON booking_payments
  FOR INSERT WITH CHECK (true);

-- System can update payments (service role)
CREATE POLICY "System can update payments" ON booking_payments
  FOR UPDATE USING (true);

-- ============================================================================
-- REVIEWS TABLE POLICIES
-- ============================================================================

-- Anyone can read published reviews
CREATE POLICY "Anyone can read published reviews" ON reviews
  FOR SELECT USING (status = 'published');

-- Guests can read their own reviews (all statuses)
CREATE POLICY "Guests can read own reviews" ON reviews
  FOR SELECT USING (guest_id = public.current_user_id());

-- Hosts can read reviews for their properties
CREATE POLICY "Hosts can read property reviews" ON reviews
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM properties p 
      WHERE p.id = property_id AND p.host_id = public.current_user_id()
    )
  );

-- Guests can create reviews for their completed bookings
CREATE POLICY "Guests can create reviews" ON reviews
  FOR INSERT WITH CHECK (
    guest_id = public.current_user_id() AND
    EXISTS (
      SELECT 1 FROM bookings b 
      WHERE b.id = booking_id 
      AND b.guest_id = public.current_user_id()
      AND b.status = 'completed'
    )
  );

-- Guests can update their own reviews
CREATE POLICY "Guests can update own reviews" ON reviews
  FOR UPDATE USING (guest_id = public.current_user_id());

-- Hosts can respond to reviews (update host_response field)
CREATE POLICY "Hosts can respond to reviews" ON reviews
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM properties p 
      WHERE p.id = property_id AND p.host_id = public.current_user_id()
    )
  );

-- ============================================================================
-- MESSAGES TABLE POLICIES
-- ============================================================================

-- Users can read messages they sent or received
CREATE POLICY "Users can read own messages" ON messages
  FOR SELECT USING (
    sender_id = public.current_user_id() OR recipient_id = public.current_user_id()
  );

-- Users can send messages
CREATE POLICY "Users can send messages" ON messages
  FOR INSERT WITH CHECK (sender_id = public.current_user_id());

-- Users can update messages they sent (mark as read, etc.)
CREATE POLICY "Users can update sent messages" ON messages
  FOR UPDATE USING (sender_id = public.current_user_id());

-- Users can update messages they received (mark as read)
CREATE POLICY "Users can update received messages" ON messages
  FOR UPDATE USING (recipient_id = public.current_user_id());

-- ============================================================================
-- NOTIFICATIONS TABLE POLICIES
-- ============================================================================

-- Users can read their own notifications
CREATE POLICY "Users can read own notifications" ON notifications
  FOR SELECT USING (user_id = public.current_user_id());

-- Users can update their own notifications (mark as read)
CREATE POLICY "Users can update own notifications" ON notifications
  FOR UPDATE USING (user_id = public.current_user_id());

-- System can insert notifications (service role)
CREATE POLICY "System can insert notifications" ON notifications
  FOR INSERT WITH CHECK (true);

-- ============================================================================
-- USER PREFERENCES POLICIES
-- ============================================================================

-- Users can read their own preferences
CREATE POLICY "Users can read own preferences" ON user_preferences
  FOR SELECT USING (user_id = public.current_user_id());

-- Users can insert their own preferences
CREATE POLICY "Users can insert own preferences" ON user_preferences
  FOR INSERT WITH CHECK (user_id = public.current_user_id());

-- Users can update their own preferences
CREATE POLICY "Users can update own preferences" ON user_preferences
  FOR UPDATE USING (user_id = public.current_user_id());

-- ============================================================================
-- PROPERTY AVAILABILITY POLICIES
-- ============================================================================

-- Anyone can read availability for active properties
CREATE POLICY "Anyone can read property availability" ON property_availability
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM properties p 
      WHERE p.id = property_id AND p.status = 'active'
    )
  );

-- Hosts can manage availability for their properties
CREATE POLICY "Hosts can manage property availability" ON property_availability
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM properties p 
      WHERE p.id = property_id AND p.host_id = public.current_user_id()
    )
  );

-- ============================================================================
-- AMENITIES TABLE POLICIES (Public Read-Only)
-- ============================================================================

-- Everyone can read amenities (no RLS needed, but adding for completeness)
-- This is reference data that should be publicly readable
ALTER TABLE amenities DISABLE ROW LEVEL SECURITY;
