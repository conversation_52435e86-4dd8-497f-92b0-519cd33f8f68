<?php
require_once 'config.php';

function getCurrentUser() {
    global $pdo;
    
    // Get the Authorization header
    $headers = getallheaders();
    $authHeader = $headers['Authorization'] ?? $headers['authorization'] ?? null;
    
    if (!$authHeader || !preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
        return null;
    }
    
    $token = $matches[1];
    
    try {
        // For now, we'll use a simple token validation
        // In production, you'd use JWT validation
        $stmt = $pdo->prepare("
            SELECT u.* FROM users u 
            WHERE u.id = ? AND u.email IS NOT NULL
        ");
        
        // Extract user ID from token (simplified for demo)
        // In real implementation, decode JWT token
        if (strlen($token) === 36) { // UUID length
            $stmt->execute([$token]);
            $user = $stmt->fetch();
            return $user ?: null;
        }
        
        // Try to find user by email (fallback for testing)
        // For testing, return the first user in the database
        $stmt = $pdo->prepare("
            SELECT * FROM users
            ORDER BY created_at ASC
            LIMIT 1
        ");
        $stmt->execute();
        $user = $stmt->fetch();
        if ($user) {
            error_log("Found test user: " . $user['email'] . " with ID: " . $user['id']);
        } else {
            error_log("No users found in database");
        }
        return $user ?: null;
        
    } catch (Exception $e) {
        error_log("Auth error: " . $e->getMessage());
        return null;
    }
}

function requireAuth() {
    $user = getCurrentUser();
    if (!$user) {
        http_response_code(401);
        echo json_encode(['error' => 'Authentication required']);
        exit;
    }
    return $user;
}
?>
