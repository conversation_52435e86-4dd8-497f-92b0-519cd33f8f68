#!/usr/bin/env node

/**
 * Supabase Email Service Test
 * Tests email functionality through Supabase Auth
 */

import { createClient } from '@supabase/supabase-js';
import { config } from 'dotenv';
config();

// ANSI color codes
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, colors.green);
}

function logError(message) {
  log(`❌ ${message}`, colors.red);
}

function logWarning(message) {
  log(`⚠️  ${message}`, colors.yellow);
}

function logInfo(message) {
  log(`ℹ️  ${message}`, colors.blue);
}

// Initialize Supabase clients
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_ANON_KEY
);

const supabaseAdmin = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

// Test results
const testResults = {
  authConfigTest: false,
  emailTemplateTest: false,
  passwordResetTest: false,
  emailVerificationTest: false
};

// Test 1: Auth Configuration Check
async function testAuthConfiguration() {
  log('\n📧 Testing Email/Auth Configuration...', colors.cyan);
  
  try {
    // Check environment variables
    const authEnabled = process.env.SUPABASE_AUTH_ENABLED === 'true';
    const autoConfirm = process.env.SUPABASE_AUTH_AUTO_CONFIRM === 'true';
    const redirectUrl = process.env.SUPABASE_AUTH_EMAIL_CONFIRM_REDIRECT;

    if (authEnabled) {
      logSuccess('Supabase Auth is enabled');
    } else {
      logWarning('Supabase Auth is disabled in environment');
    }

    if (autoConfirm) {
      logWarning('Auto-confirm is enabled (good for development, disable in production)');
    } else {
      logSuccess('Auto-confirm is disabled (email verification required)');
    }

    if (redirectUrl) {
      logSuccess(`Email confirmation redirect URL configured: ${redirectUrl}`);
    } else {
      logWarning('No email confirmation redirect URL configured');
    }

    // Test basic auth service availability
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error && !error.message.includes('Invalid JWT')) {
      logError(`Auth service error: ${error.message}`);
      return false;
    }

    logSuccess('Auth service is accessible');
    return true;
  } catch (error) {
    logError(`Auth configuration test error: ${error.message}`);
    return false;
  }
}

// Test 2: Password Reset Email
async function testPasswordResetEmail() {
  log('\n🔑 Testing Password Reset Email...', colors.cyan);
  
  const testEmail = '<EMAIL>';
  
  try {
    const { data, error } = await supabase.auth.resetPasswordForEmail(testEmail, {
      redirectTo: process.env.SUPABASE_AUTH_EMAIL_CONFIRM_REDIRECT || 'http://localhost:5173/auth/reset'
    });

    if (error) {
      if (error.message.includes('Email rate limit exceeded')) {
        logWarning('Email rate limit exceeded - service is working but rate limited');
        logInfo('This indicates the email service is functional');
        return true;
      } else if (error.message.includes('Invalid email')) {
        logWarning('Invalid email format - but service is accessible');
        return true;
      } else {
        logError(`Password reset email failed: ${error.message}`);
        return false;
      }
    }

    logSuccess('Password reset email request successful');
    logInfo('Note: No actual email will be sent to test email addresses');
    return true;
  } catch (error) {
    logError(`Password reset test error: ${error.message}`);
    return false;
  }
}

// Test 3: Email Verification Test
async function testEmailVerification() {
  log('\n✉️  Testing Email Verification...', colors.cyan);
  
  const testEmail = `test-verification-${Date.now()}@example.com`;
  const testPassword = 'TestPassword123!';
  
  try {
    // Attempt to sign up a user (this should trigger email verification if enabled)
    const { data, error } = await supabase.auth.signUp({
      email: testEmail,
      password: testPassword,
      options: {
        data: {
          first_name: 'Test',
          last_name: 'User'
        }
      }
    });

    if (error) {
      if (error.message.includes('Email rate limit exceeded')) {
        logWarning('Email rate limit exceeded - service is working but rate limited');
        return true;
      } else if (error.message.includes('User already registered')) {
        logInfo('User already exists - email service is accessible');
        return true;
      } else {
        logError(`Email verification test failed: ${error.message}`);
        return false;
      }
    }

    if (data.user) {
      if (data.user.email_confirmed_at) {
        logWarning('Email auto-confirmation is enabled (good for development)');
      } else {
        logSuccess('Email verification required - verification email should be sent');
      }

      // Cleanup: Delete test user
      try {
        await supabaseAdmin.auth.admin.deleteUser(data.user.id);
        logInfo('Test user cleaned up');
      } catch (cleanupError) {
        logWarning('Could not cleanup test user');
      }
    }

    logSuccess('Email verification test completed');
    return true;
  } catch (error) {
    logError(`Email verification test error: ${error.message}`);
    return false;
  }
}

// Test 4: Email Template Configuration Check
async function testEmailTemplateConfiguration() {
  log('\n📝 Testing Email Template Configuration...', colors.cyan);
  
  try {
    logInfo('Checking email template configuration...');
    
    // Note: Email template configuration is typically done in the Supabase dashboard
    // We can only test if the service responds appropriately
    
    logInfo('Email templates are configured in the Supabase dashboard under:');
    logInfo('Authentication > Settings > Email Templates');
    
    logSuccess('Email template configuration check completed');
    logInfo('To customize email templates:');
    logInfo('1. Go to your Supabase dashboard');
    logInfo('2. Navigate to Authentication > Settings');
    logInfo('3. Scroll down to Email Templates');
    logInfo('4. Customize the templates for your brand');
    
    return true;
  } catch (error) {
    logError(`Email template test error: ${error.message}`);
    return false;
  }
}

// Test 5: SMTP Configuration Check
async function testSMTPConfiguration() {
  log('\n📮 Testing SMTP Configuration...', colors.cyan);
  
  try {
    // Check if custom SMTP is configured in environment
    const smtpHost = process.env.SMTP_HOST;
    const smtpUser = process.env.SMTP_USER;
    const smtpFrom = process.env.SMTP_FROM;

    if (smtpHost && smtpUser) {
      logInfo('Custom SMTP configuration found in environment:');
      logInfo(`SMTP Host: ${smtpHost}`);
      logInfo(`SMTP User: ${smtpUser}`);
      logInfo(`From Address: ${smtpFrom || 'Not configured'}`);
      logWarning('Note: Supabase uses its own email service by default');
      logInfo('Custom SMTP would need to be configured in Supabase dashboard');
    } else {
      logInfo('Using Supabase default email service');
      logSuccess('This is the recommended configuration for most use cases');
    }

    logInfo('To configure custom SMTP in Supabase:');
    logInfo('1. Go to Authentication > Settings in your dashboard');
    logInfo('2. Scroll to SMTP Settings');
    logInfo('3. Enable custom SMTP and enter your credentials');

    return true;
  } catch (error) {
    logError(`SMTP configuration test error: ${error.message}`);
    return false;
  }
}

// Main test runner
async function runEmailTests() {
  log('📧 Starting Email Service Tests...', colors.bright);
  log('='.repeat(50), colors.cyan);

  const startTime = Date.now();

  // Run tests
  testResults.authConfigTest = await testAuthConfiguration();
  testResults.emailTemplateTest = await testEmailTemplateConfiguration();
  testResults.passwordResetTest = await testPasswordResetEmail();
  testResults.emailVerificationTest = await testEmailVerification();
  await testSMTPConfiguration();

  const endTime = Date.now();
  const duration = ((endTime - startTime) / 1000).toFixed(2);

  // Print summary
  log('\n' + '='.repeat(50), colors.cyan);
  log('📊 Email Service Test Summary', colors.bright);
  log('='.repeat(50), colors.cyan);
  
  const passed = Object.values(testResults).filter(Boolean).length;
  const total = Object.keys(testResults).length;
  
  if (testResults.authConfigTest) {
    logSuccess('Auth configuration: PASSED');
  } else {
    logError('Auth configuration: FAILED');
  }
  
  if (testResults.passwordResetTest) {
    logSuccess('Password reset emails: WORKING');
  } else {
    logError('Password reset emails: FAILED');
  }
  
  if (testResults.emailVerificationTest) {
    logSuccess('Email verification: WORKING');
  } else {
    logError('Email verification: FAILED');
  }

  logInfo(`Duration: ${duration}s`);
  logInfo(`Tests passed: ${passed}/${total}`);

  // Provide recommendations
  log('\n📋 Email Service Recommendations:', colors.yellow);
  
  if (process.env.SUPABASE_AUTH_AUTO_CONFIRM === 'true') {
    log('• Consider disabling auto-confirm in production for security');
  }
  
  log('• Customize email templates in Supabase dashboard for better branding');
  log('• Set up custom domain for emails if needed');
  log('• Monitor email delivery rates in Supabase dashboard');
  log('• Consider setting up email webhooks for delivery tracking');

  if (passed === total) {
    log('\n✅ Email service is working correctly!', colors.green);
  } else {
    log('\n⚠️  Some email features may need configuration.', colors.yellow);
  }
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runEmailTests().catch(error => {
    logError(`Email test error: ${error.message}`);
    console.error(error);
    process.exit(1);
  });
}

export {
  runEmailTests,
  testResults
};
