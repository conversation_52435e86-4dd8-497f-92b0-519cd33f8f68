// Search Suggestions API Service
const API_BASE_URL = 'http://localhost/stayfinder/api';

// Debug logging
const DEBUG = true;
const log = (...args: any[]) => {
  if (DEBUG) {
    console.log('[SearchSuggestions]', ...args);
  }
};

interface ApiSuggestion {
  id: string;
  text: string;
  subtitle: string;
  full_location: string;
  type: string;
  icon: string;
  property_count: number;
  price_range: {
    min: number;
    max: number;
    avg: number;
  };
}

interface ApiResponse {
  success: boolean;
  suggestions: ApiSuggestion[];
  query: string;
  total_found: number;
  error?: string;
}

interface FormattedSuggestion {
  id: string;
  text: string;
  subtitle: string;
  fullLocation: string;
  type: string;
  icon: string;
  propertyCount: number;
  priceRange: {
    min: number;
    max: number;
    avg: number;
  };
}

class SearchSuggestionsService {
  /**
   * Fetch search suggestions based on query
   */
  async fetchSuggestions(query: string, limit: number = 8): Promise<ApiResponse> {
    try {
      log('Fetching suggestions for:', query);
      
      if (!query || query.trim().length < 2) {
        log('Query too short, returning empty results');
        return {
          success: true,
          suggestions: [],
          query: query,
          total_found: 0
        };
      }

      const url = new URL(`${API_BASE_URL}/search-suggestions.php`);
      url.searchParams.append('q', query.trim());
      url.searchParams.append('limit', limit.toString());

      log('Making API call to:', url.toString());

      const response = await fetch(url.toString(), {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      log('Response status:', response.status);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      log('API response:', data);
      
      if (!data.success) {
        throw new Error(data.error || 'Failed to fetch suggestions');
      }

      return data;
    } catch (error) {
      console.error('Error fetching search suggestions:', error);
      log('Error details:', error);
      return {
        success: false,
        error: (error as Error).message,
        suggestions: [],
        query: query,
        total_found: 0
      };
    }
  }

  /**
   * Get popular destinations (locations with most properties)
   */
  async getPopularDestinations(limit: number = 6): Promise<FormattedSuggestion[]> {
    try {
      // Get all suggestions and filter for popular ones
      const response = await this.fetchSuggestions('', limit * 2);
      
      if (response.success && response.suggestions) {
        return response.suggestions
          .filter(suggestion => suggestion.type === 'location' || suggestion.type === 'city')
          .sort((a, b) => b.property_count - a.property_count)
          .slice(0, limit)
          .map(suggestion => this.formatSuggestion(suggestion));
      }
      
      return [];
    } catch (error) {
      console.error('Error fetching popular destinations:', error);
      return [];
    }
  }

  /**
   * Format suggestion for display
   */
  formatSuggestion(suggestion: ApiSuggestion): FormattedSuggestion {
    const icons: Record<string, string> = {
      'MapPin': '📍',
      'Building': '🏢',
      'Camera': '📸',
      'Map': '🗺️'
    };

    return {
      id: suggestion.id,
      text: suggestion.text,
      subtitle: suggestion.subtitle,
      fullLocation: suggestion.full_location,
      type: suggestion.type,
      icon: icons[suggestion.icon] || '📍',
      propertyCount: suggestion.property_count,
      priceRange: suggestion.price_range
    };
  }

  /**
   * Get search suggestions with proper formatting
   */
  async getFormattedSuggestions(query: string, limit: number = 8): Promise<FormattedSuggestion[]> {
    try {
      log('Getting formatted suggestions for:', query);
      const response = await this.fetchSuggestions(query, limit);
      
      if (response.success && response.suggestions) {
        const formatted = response.suggestions.map(suggestion => this.formatSuggestion(suggestion));
        log('Formatted suggestions:', formatted);
        return formatted;
      }
      
      log('No suggestions or API failed');
      return [];
    } catch (error) {
      console.error('Error getting formatted suggestions:', error);
      return [];
    }
  }

  /**
   * Check if a location has properties
   */
  async hasProperties(location: string): Promise<boolean> {
    try {
      const response = await this.fetchSuggestions(location, 1);
      return response.success && response.suggestions.length > 0;
    } catch (error) {
      console.error('Error checking if location has properties:', error);
      return false;
    }
  }

  /**
   * Get location statistics
   */
  async getLocationStats(location: string): Promise<{
    propertyCount: number;
    priceRange: { min: number; max: number; avg: number };
    location: string;
  } | null> {
    try {
      const response = await this.fetchSuggestions(location, 1);
      
      if (response.success && response.suggestions.length > 0) {
        const suggestion = response.suggestions[0];
        return {
          propertyCount: suggestion.property_count,
          priceRange: suggestion.price_range,
          location: suggestion.full_location
        };
      }
      
      return null;
    } catch (error) {
      console.error('Error getting location stats:', error);
      return null;
    }
  }
}

// Create and export a singleton instance
const searchSuggestionsService = new SearchSuggestionsService();
export default searchSuggestionsService;
