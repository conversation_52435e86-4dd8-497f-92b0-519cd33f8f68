import { useState, useEffect, useCallback } from 'react';
import { toast } from '@/components/ui/use-toast';

interface OfflineData {
  properties: any[];
  searchHistory: any[];
  wishlist: any[];
  userPreferences: any;
  lastSync: Date;
}

interface OfflineAction {
  id: string;
  type: 'wishlist_add' | 'wishlist_remove' | 'search' | 'view_property' | 'user_preference_update';
  data: any;
  timestamp: Date;
}

interface OfflineState {
  isOnline: boolean;
  hasOfflineData: boolean;
  pendingActions: OfflineAction[];
  lastSync: Date | null;
  syncInProgress: boolean;
}

export const useOfflineMode = () => {
  const [offlineState, setOfflineState] = useState<OfflineState>({
    isOnline: navigator.onLine,
    hasOfflineData: false,
    pendingActions: [],
    lastSync: null,
    syncInProgress: false
  });

  const [offlineData, setOfflineData] = useState<OfflineData | null>(null);

  // Monitor online/offline status
  useEffect(() => {
    const handleOnline = () => {
      setOfflineState(prev => ({ ...prev, isOnline: true }));
      toast({
        title: "Back online",
        description: "Connection restored. Syncing data...",
      });
      syncPendingActions();
    };

    const handleOffline = () => {
      setOfflineState(prev => ({ ...prev, isOnline: false }));
      toast({
        title: "You're offline",
        description: "You can still browse saved properties and use basic features.",
        variant: "destructive"
      });
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Load offline data on mount
  useEffect(() => {
    loadOfflineData();
  }, []);

  const loadOfflineData = useCallback(() => {
    try {
      const stored = localStorage.getItem('stayfinder_offline_data');
      const pendingActions = localStorage.getItem('stayfinder_pending_actions');
      
      if (stored) {
        const data = JSON.parse(stored);
        data.lastSync = new Date(data.lastSync);
        setOfflineData(data);
        setOfflineState(prev => ({ 
          ...prev, 
          hasOfflineData: true,
          lastSync: data.lastSync
        }));
      }

      if (pendingActions) {
        const actions = JSON.parse(pendingActions).map((action: any) => ({
          ...action,
          timestamp: new Date(action.timestamp)
        }));
        setOfflineState(prev => ({ ...prev, pendingActions: actions }));
      }
    } catch (error) {
      console.error('Failed to load offline data:', error);
    }
  }, []);

  const saveOfflineData = useCallback((data: Partial<OfflineData>) => {
    try {
      const currentData = offlineData || {
        properties: [],
        searchHistory: [],
        wishlist: [],
        userPreferences: {},
        lastSync: new Date()
      };

      const updatedData = {
        ...currentData,
        ...data,
        lastSync: new Date()
      };

      localStorage.setItem('stayfinder_offline_data', JSON.stringify(updatedData));
      setOfflineData(updatedData);
      setOfflineState(prev => ({ 
        ...prev, 
        hasOfflineData: true,
        lastSync: updatedData.lastSync
      }));
    } catch (error) {
      console.error('Failed to save offline data:', error);
    }
  }, [offlineData]);

  const addPendingAction = useCallback((action: Omit<OfflineAction, 'id' | 'timestamp'>) => {
    const newAction: OfflineAction = {
      ...action,
      id: `action_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date()
    };

    const updatedActions = [...offlineState.pendingActions, newAction];
    setOfflineState(prev => ({ ...prev, pendingActions: updatedActions }));
    
    try {
      localStorage.setItem('stayfinder_pending_actions', JSON.stringify(updatedActions));
    } catch (error) {
      console.error('Failed to save pending action:', error);
    }
  }, [offlineState.pendingActions]);

  const syncPendingActions = useCallback(async () => {
    if (!offlineState.isOnline || offlineState.pendingActions.length === 0) {
      return;
    }

    setOfflineState(prev => ({ ...prev, syncInProgress: true }));

    try {
      // Process each pending action
      for (const action of offlineState.pendingActions) {
        switch (action.type) {
          case 'wishlist_add':
            // Sync wishlist addition to server
            await syncWishlistAdd(action.data);
            break;
          case 'wishlist_remove':
            // Sync wishlist removal to server
            await syncWishlistRemove(action.data);
            break;
          case 'search':
            // Log search to analytics
            await syncSearch(action.data);
            break;
          case 'view_property':
            // Log property view to analytics
            await syncPropertyView(action.data);
            break;
          case 'user_preference_update':
            // Sync user preferences
            await syncUserPreferences(action.data);
            break;
        }
      }

      // Clear pending actions after successful sync
      setOfflineState(prev => ({ 
        ...prev, 
        pendingActions: [],
        syncInProgress: false
      }));
      localStorage.removeItem('stayfinder_pending_actions');

      toast({
        title: "Sync complete",
        description: "All offline changes have been synchronized.",
      });

    } catch (error) {
      console.error('Failed to sync pending actions:', error);
      setOfflineState(prev => ({ ...prev, syncInProgress: false }));
      
      toast({
        title: "Sync failed",
        description: "Some changes couldn't be synchronized. Will retry when connection improves.",
        variant: "destructive"
      });
    }
  }, [offlineState.isOnline, offlineState.pendingActions]);

  // Sync functions (these would make actual API calls in a real implementation)
  const syncWishlistAdd = async (data: any) => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 500));
    console.log('Syncing wishlist add:', data);
  };

  const syncWishlistRemove = async (data: any) => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 500));
    console.log('Syncing wishlist remove:', data);
  };

  const syncSearch = async (data: any) => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 200));
    console.log('Syncing search:', data);
  };

  const syncPropertyView = async (data: any) => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 200));
    console.log('Syncing property view:', data);
  };

  const syncUserPreferences = async (data: any) => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 300));
    console.log('Syncing user preferences:', data);
  };

  // Offline-specific actions
  const addToWishlistOffline = useCallback((propertyId: string, propertyData: any) => {
    if (!offlineData) return;

    // Update local wishlist
    const updatedWishlist = [...offlineData.wishlist, { id: propertyId, ...propertyData, addedAt: new Date() }];
    saveOfflineData({ wishlist: updatedWishlist });

    // Add to pending actions for sync
    addPendingAction({
      type: 'wishlist_add',
      data: { propertyId, propertyData }
    });

    toast({
      title: "Added to wishlist",
      description: offlineState.isOnline ? "Property saved to wishlist" : "Property saved offline. Will sync when online.",
    });
  }, [offlineData, saveOfflineData, addPendingAction, offlineState.isOnline]);

  const removeFromWishlistOffline = useCallback((propertyId: string) => {
    if (!offlineData) return;

    // Update local wishlist
    const updatedWishlist = offlineData.wishlist.filter(item => item.id !== propertyId);
    saveOfflineData({ wishlist: updatedWishlist });

    // Add to pending actions for sync
    addPendingAction({
      type: 'wishlist_remove',
      data: { propertyId }
    });

    toast({
      title: "Removed from wishlist",
      description: offlineState.isOnline ? "Property removed from wishlist" : "Property removed offline. Will sync when online.",
    });
  }, [offlineData, saveOfflineData, addPendingAction, offlineState.isOnline]);

  const logSearchOffline = useCallback((searchData: any) => {
    // Update search history
    if (offlineData) {
      const updatedHistory = [searchData, ...offlineData.searchHistory.slice(0, 19)];
      saveOfflineData({ searchHistory: updatedHistory });
    }

    // Add to pending actions for analytics
    addPendingAction({
      type: 'search',
      data: searchData
    });
  }, [offlineData, saveOfflineData, addPendingAction]);

  const logPropertyViewOffline = useCallback((propertyId: string, propertyData: any) => {
    // Add to pending actions for analytics
    addPendingAction({
      type: 'view_property',
      data: { propertyId, propertyData, viewedAt: new Date() }
    });
  }, [addPendingAction]);

  const updateUserPreferencesOffline = useCallback((preferences: any) => {
    if (!offlineData) return;

    // Update local preferences
    const updatedPreferences = { ...offlineData.userPreferences, ...preferences };
    saveOfflineData({ userPreferences: updatedPreferences });

    // Add to pending actions for sync
    addPendingAction({
      type: 'user_preference_update',
      data: preferences
    });
  }, [offlineData, saveOfflineData, addPendingAction]);

  const getOfflineProperties = useCallback(() => {
    return offlineData?.properties || [];
  }, [offlineData]);

  const getOfflineWishlist = useCallback(() => {
    return offlineData?.wishlist || [];
  }, [offlineData]);

  const getOfflineSearchHistory = useCallback(() => {
    return offlineData?.searchHistory || [];
  }, [offlineData]);

  const clearOfflineData = useCallback(() => {
    localStorage.removeItem('stayfinder_offline_data');
    localStorage.removeItem('stayfinder_pending_actions');
    setOfflineData(null);
    setOfflineState(prev => ({
      ...prev,
      hasOfflineData: false,
      pendingActions: [],
      lastSync: null
    }));

    toast({
      title: "Offline data cleared",
      description: "All offline data has been removed.",
    });
  }, []);

  const downloadForOffline = useCallback(async (properties: any[]) => {
    try {
      // In a real implementation, this would download and cache images and data
      const offlineProperties = properties.map(property => ({
        ...property,
        images: property.images?.slice(0, 3), // Limit images for storage
        cachedAt: new Date()
      }));

      saveOfflineData({ properties: offlineProperties });

      toast({
        title: "Downloaded for offline",
        description: `${properties.length} properties are now available offline.`,
      });
    } catch (error) {
      console.error('Failed to download for offline:', error);
      toast({
        title: "Download failed",
        description: "Failed to download properties for offline use.",
        variant: "destructive"
      });
    }
  }, [saveOfflineData]);

  return {
    // State
    ...offlineState,
    offlineData,

    // Actions
    addToWishlistOffline,
    removeFromWishlistOffline,
    logSearchOffline,
    logPropertyViewOffline,
    updateUserPreferencesOffline,
    
    // Data access
    getOfflineProperties,
    getOfflineWishlist,
    getOfflineSearchHistory,
    
    // Sync
    syncPendingActions,
    
    // Management
    downloadForOffline,
    clearOfflineData,
    
    // Utilities
    canUseFeature: (feature: string) => {
      // Define which features work offline
      const offlineFeatures = ['browse_saved', 'wishlist', 'search_history', 'user_preferences'];
      return offlineState.isOnline || offlineFeatures.includes(feature);
    }
  };
};
