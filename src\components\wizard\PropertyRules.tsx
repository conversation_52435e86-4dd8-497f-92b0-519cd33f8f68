import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { 
  Clock, 
  Shield, 
  AlertTriangle,
  Plus,
  X,
  CheckCircle,
  XCircle
} from 'lucide-react';

interface PropertyRulesProps {
  data: {
    checkInTime: string;
    checkOutTime: string;
    houseRules: string[];
    cancellationPolicy: string;
  };
  errors: Record<string, string>;
  onChange: (updates: any) => void;
}

const CANCELLATION_POLICIES = [
  {
    value: 'flexible',
    title: 'Flexible',
    description: 'Full refund 1 day prior to arrival, except fees.',
    details: 'Guests can cancel up to 24 hours before check-in for a full refund.'
  },
  {
    value: 'moderate',
    title: 'Moderate',
    description: 'Full refund 5 days prior to arrival, except fees.',
    details: 'Guests can cancel up to 5 days before check-in for a full refund.'
  },
  {
    value: 'strict',
    title: 'Strict',
    description: 'Full refund 14 days prior to arrival, except fees.',
    details: 'Guests can cancel up to 14 days before check-in for a full refund.'
  },
  {
    value: 'super_strict',
    title: 'Super Strict',
    description: 'Full refund 30 days prior to arrival, except fees.',
    details: 'Guests can cancel up to 30 days before check-in for a full refund.'
  }
];

const COMMON_HOUSE_RULES = [
  'No smoking inside the property',
  'No pets allowed',
  'No parties or events',
  'Quiet hours from 10 PM to 8 AM',
  'Maximum occupancy strictly enforced',
  'No shoes inside the house',
  'Clean up after yourself',
  'Respect the neighbors',
  'No loud music after 10 PM',
  'Check-out cleaning required'
];

export const PropertyRules: React.FC<PropertyRulesProps> = ({
  data,
  errors,
  onChange
}) => {
  const [newRule, setNewRule] = useState('');

  const addHouseRule = () => {
    if (newRule.trim()) {
      const updatedRules = [...(data.houseRules || []), newRule.trim()];
      onChange({ houseRules: updatedRules });
      setNewRule('');
    }
  };

  const removeHouseRule = (index: number) => {
    const updatedRules = (data.houseRules || []).filter((_, i) => i !== index);
    onChange({ houseRules: updatedRules });
  };

  const addCommonRule = (rule: string) => {
    if (!(data.houseRules || []).includes(rule)) {
      const updatedRules = [...(data.houseRules || []), rule];
      onChange({ houseRules: updatedRules });
    }
  };

  const generateTimeOptions = () => {
    const options = [];
    for (let hour = 0; hour < 24; hour++) {
      for (let minute = 0; minute < 60; minute += 30) {
        const timeString = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
        const displayTime = new Date(`2000-01-01T${timeString}`).toLocaleTimeString('en-US', {
          hour: 'numeric',
          minute: '2-digit',
          hour12: true
        });
        options.push({ value: timeString, label: displayTime });
      }
    }
    return options;
  };

  const timeOptions = generateTimeOptions();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          Set your house rules and policies
        </h2>
        <p className="text-gray-600">
          Clear rules help set expectations and ensure a great experience for everyone
        </p>
      </div>

      {/* Check-in/Check-out Times */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Check-in & Check-out Times
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="checkInTime">Check-in Time *</Label>
              <select
                id="checkInTime"
                value={data.checkInTime}
                onChange={(e) => onChange({ checkInTime: e.target.value })}
                className={`w-full px-3 py-2 border rounded-md ${
                  errors.checkInTime ? 'border-red-300' : 'border-gray-300'
                }`}
              >
                <option value="">Select check-in time</option>
                {timeOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
              {errors.checkInTime && (
                <p className="text-red-600 text-sm">{errors.checkInTime}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="checkOutTime">Check-out Time *</Label>
              <select
                id="checkOutTime"
                value={data.checkOutTime}
                onChange={(e) => onChange({ checkOutTime: e.target.value })}
                className={`w-full px-3 py-2 border rounded-md ${
                  errors.checkOutTime ? 'border-red-300' : 'border-gray-300'
                }`}
              >
                <option value="">Select check-out time</option>
                {timeOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
              {errors.checkOutTime && (
                <p className="text-red-600 text-sm">{errors.checkOutTime}</p>
              )}
            </div>
          </div>
          <p className="text-gray-500 text-sm mt-2">
            Standard check-in is 3:00 PM and check-out is 11:00 AM
          </p>
        </CardContent>
      </Card>

      {/* House Rules */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            House Rules
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Current Rules */}
          {(data.houseRules || []).length > 0 && (
            <div className="space-y-2">
              <h4 className="font-medium text-gray-900">Your Rules:</h4>
              <div className="space-y-2">
                {(data.houseRules || []).map((rule, index) => (
                  <div key={index} className="flex items-center gap-2 p-2 bg-gray-50 rounded-lg">
                    <CheckCircle className="h-4 w-4 text-green-600 flex-shrink-0" />
                    <span className="flex-1 text-sm">{rule}</span>
                    <button
                      type="button"
                      onClick={() => removeHouseRule(index)}
                      className="text-red-600 hover:text-red-800"
                    >
                      <X className="h-4 w-4" />
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Add Custom Rule */}
          <div className="space-y-2">
            <Label htmlFor="newRule">Add Custom Rule</Label>
            <div className="flex gap-2">
              <Input
                id="newRule"
                placeholder="e.g., No smoking in bedrooms"
                value={newRule}
                onChange={(e) => setNewRule(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && addHouseRule()}
              />
              <Button
                type="button"
                onClick={addHouseRule}
                disabled={!newRule.trim()}
                className="bg-sea-green-500 hover:bg-sea-green-600 text-white"
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Common Rules */}
          <div className="space-y-2">
            <h4 className="font-medium text-gray-900">Common Rules (click to add):</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
              {COMMON_HOUSE_RULES.map((rule) => {
                const isAdded = (data.houseRules || []).includes(rule);
                return (
                  <button
                    key={rule}
                    type="button"
                    onClick={() => !isAdded && addCommonRule(rule)}
                    disabled={isAdded}
                    className={`text-left p-2 rounded-lg border text-sm transition-colors ${
                      isAdded
                        ? 'bg-green-50 border-green-200 text-green-800 cursor-not-allowed'
                        : 'bg-white border-gray-200 hover:border-sea-green-300 hover:bg-sea-green-50'
                    }`}
                  >
                    <div className="flex items-center gap-2">
                      {isAdded ? (
                        <CheckCircle className="h-4 w-4 text-green-600" />
                      ) : (
                        <Plus className="h-4 w-4 text-gray-400" />
                      )}
                      <span>{rule}</span>
                    </div>
                  </button>
                );
              })}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Cancellation Policy */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5" />
            Cancellation Policy
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {CANCELLATION_POLICIES.map((policy) => (
              <div
                key={policy.value}
                onClick={() => onChange({ cancellationPolicy: policy.value })}
                className={`cursor-pointer p-4 rounded-lg border-2 transition-all ${
                  data.cancellationPolicy === policy.value
                    ? 'border-sea-green-500 bg-sea-green-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <div className="flex items-start gap-3">
                  <div className={`w-5 h-5 rounded-full border-2 flex items-center justify-center mt-0.5 ${
                    data.cancellationPolicy === policy.value
                      ? 'border-sea-green-500 bg-sea-green-500'
                      : 'border-gray-300'
                  }`}>
                    {data.cancellationPolicy === policy.value && (
                      <div className="w-2 h-2 bg-white rounded-full" />
                    )}
                  </div>
                  <div className="flex-1">
                    <h4 className={`font-medium ${
                      data.cancellationPolicy === policy.value ? 'text-sea-green-900' : 'text-gray-900'
                    }`}>
                      {policy.title}
                    </h4>
                    <p className={`text-sm ${
                      data.cancellationPolicy === policy.value ? 'text-sea-green-700' : 'text-gray-600'
                    }`}>
                      {policy.description}
                    </p>
                    <p className={`text-xs mt-1 ${
                      data.cancellationPolicy === policy.value ? 'text-sea-green-600' : 'text-gray-500'
                    }`}>
                      {policy.details}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
          {errors.cancellationPolicy && (
            <p className="text-red-600 text-sm mt-2">{errors.cancellationPolicy}</p>
          )}
        </CardContent>
      </Card>

      {/* Tips */}
      <Card className="bg-blue-50 border-blue-200">
        <CardContent className="p-4">
          <h4 className="font-medium text-blue-900 mb-2">💡 Rules & Policy Tips</h4>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• Clear rules help prevent misunderstandings and conflicts</li>
            <li>• Be specific about noise restrictions and quiet hours</li>
            <li>• Consider your neighborhood when setting rules</li>
            <li>• Flexible cancellation policies can attract more bookings</li>
            <li>• Stricter policies may reduce cancellations but could deter some guests</li>
          </ul>
        </CardContent>
      </Card>
    </div>
  );
};
