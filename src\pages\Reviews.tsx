import React, { useState, useEffect } from 'react';
import { Header } from '../components/Header';
import { ReviewCard } from '../components/ReviewCard';
import { ReviewForm } from '../components/ReviewForm';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useReviews } from '../contexts/ReviewsContext';
import { useAuth } from '../contexts/AuthContext';
import { 
  MessageSquare, 
  Star, 
  Edit,
  Loader2,
  AlertCircle,
  Filter
} from 'lucide-react';

export const Reviews: React.FC = () => {
  const [filter, setFilter] = useState<'all' | '5' | '4' | '3' | '2' | '1'>('all');
  const [editingReview, setEditingReview] = useState<any>(null);
  const [showEditForm, setShowEditForm] = useState(false);
  
  const { isAuthenticated, user } = useAuth();
  const { 
    userReviews, 
    getUserReviews, 
    loading, 
    error 
  } = useReviews();

  useEffect(() => {
    if (isAuthenticated) {
      getUserReviews();
    }
  }, [isAuthenticated, getUserReviews]);

  const handleEditReview = (review: any) => {
    setEditingReview(review);
    setShowEditForm(true);
  };

  const handleEditSuccess = () => {
    setShowEditForm(false);
    setEditingReview(null);
    getUserReviews(); // Refresh reviews
  };

  const handleCancelEdit = () => {
    setShowEditForm(false);
    setEditingReview(null);
  };

  const handleDeleteReview = () => {
    getUserReviews(); // Refresh reviews after deletion
  };

  const getFilteredReviews = () => {
    if (filter === 'all') {
      return userReviews;
    }
    return userReviews.filter(review => review.rating === parseInt(filter));
  };

  const getReviewStats = () => {
    const total = userReviews.length;
    const averageRating = total > 0 
      ? userReviews.reduce((sum, review) => sum + review.rating, 0) / total 
      : 0;
    
    const ratingCounts = {
      5: userReviews.filter(r => r.rating === 5).length,
      4: userReviews.filter(r => r.rating === 4).length,
      3: userReviews.filter(r => r.rating === 3).length,
      2: userReviews.filter(r => r.rating === 2).length,
      1: userReviews.filter(r => r.rating === 1).length,
    };

    return { total, averageRating, ratingCounts };
  };

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="container mx-auto px-4 py-8">
          <Card className="max-w-md mx-auto">
            <CardContent className="text-center py-12">
              <AlertCircle className="h-12 w-12 mx-auto mb-4 text-gray-400" />
              <h2 className="text-xl font-semibold text-gray-900 mb-2">Login Required</h2>
              <p className="text-gray-600 mb-4">
                Please log in to view your reviews.
              </p>
              <Button onClick={() => window.location.href = '/'}>
                Go to Homepage
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  const filteredReviews = getFilteredReviews();
  const stats = getReviewStats();

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            My Reviews
          </h1>
          <p className="text-gray-600">
            Manage your property reviews and ratings
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Reviews</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
                </div>
                <MessageSquare className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Average Rating</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {stats.averageRating > 0 ? stats.averageRating.toFixed(1) : '0.0'}
                  </p>
                </div>
                <Star className="h-8 w-8 text-yellow-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">5-Star Reviews</p>
                  <p className="text-2xl font-bold text-green-600">{stats.ratingCounts[5]}</p>
                </div>
                <div className="flex">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <Star key={star} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Recent Reviews</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {userReviews.filter(r => {
                      const reviewDate = new Date(r.createdAt);
                      const thirtyDaysAgo = new Date();
                      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
                      return reviewDate > thirtyDaysAgo;
                    }).length}
                  </p>
                </div>
                <Edit className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Edit Form */}
        {showEditForm && editingReview && (
          <div className="mb-8">
            <ReviewForm
              propertyId={editingReview.propertyId}
              bookingId={editingReview.bookingId}
              propertyTitle={editingReview.property?.title}
              existingReview={editingReview}
              onSuccess={handleEditSuccess}
              onCancel={handleCancelEdit}
            />
          </div>
        )}

        {/* Filters */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Filter Reviews
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {[
                { key: 'all', label: 'All Reviews', count: stats.total },
                { key: '5', label: '5 Stars', count: stats.ratingCounts[5] },
                { key: '4', label: '4 Stars', count: stats.ratingCounts[4] },
                { key: '3', label: '3 Stars', count: stats.ratingCounts[3] },
                { key: '2', label: '2 Stars', count: stats.ratingCounts[2] },
                { key: '1', label: '1 Star', count: stats.ratingCounts[1] }
              ].map((filterOption) => (
                <Button
                  key={filterOption.key}
                  variant={filter === filterOption.key ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setFilter(filterOption.key as any)}
                  className="flex items-center gap-2"
                >
                  {filterOption.label}
                  {filterOption.count > 0 && (
                    <Badge variant="secondary" className="ml-1">
                      {filterOption.count}
                    </Badge>
                  )}
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Loading State */}
        {loading && (
          <div className="text-center py-12">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-sea-green-600" />
            <p className="text-gray-600">Loading your reviews...</p>
          </div>
        )}

        {/* Error State */}
        {error && (
          <Card>
            <CardContent className="text-center py-12">
              <AlertCircle className="h-12 w-12 mx-auto mb-4 text-red-500" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading Reviews</h3>
              <p className="text-gray-600 mb-4">{error}</p>
              <Button onClick={() => getUserReviews()}>
                Try Again
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Reviews List */}
        {!loading && !error && (
          <>
            {filteredReviews.length === 0 ? (
              <Card>
                <CardContent className="text-center py-12">
                  <MessageSquare className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    {filter === 'all' ? 'No Reviews Found' : `No ${filter}-Star Reviews`}
                  </h3>
                  <p className="text-gray-600 mb-4">
                    {filter === 'all' 
                      ? "You haven't written any reviews yet. Start exploring properties and share your experiences!"
                      : `You don't have any ${filter}-star reviews.`
                    }
                  </p>
                  <Button onClick={() => window.location.href = '/'}>
                    Browse Properties
                  </Button>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-6">
                <div className="flex justify-between items-center">
                  <h2 className="text-xl font-semibold text-gray-900">
                    {filter === 'all' ? 'All Reviews' : `${filter}-Star Reviews`}
                  </h2>
                  <p className="text-gray-600">
                    {filteredReviews.length} review{filteredReviews.length !== 1 ? 's' : ''}
                  </p>
                </div>

                <div className="grid gap-6">
                  {filteredReviews.map((review) => (
                    <ReviewCard
                      key={review.id}
                      review={review}
                      showProperty={true}
                      showActions={true}
                      onEdit={handleEditReview}
                      onDelete={handleDeleteReview}
                    />
                  ))}
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default Reviews;
