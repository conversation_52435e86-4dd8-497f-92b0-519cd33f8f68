import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  MapPin, 
  Navigation, 
  Clock, 
  Car, 
  Walking, 
  Bike,
  Plane,
  Train,
  Bus,
  Target,
  Route,
  Calculator,
  Compass,
  Timer,
  Fuel,
  DollarSign,
  Waves,
  Mountain,
  Trees,
  Building,
  ShoppingBag,
  Utensils,
  Camera,
  Hospital,
  GraduationCap
} from 'lucide-react';
import { 
  HoverAnimation, 
  SlideIn, 
  StaggeredAnimation 
} from '@/components/ui/page-transitions';
import { QuickTooltip } from '@/components/ui/enhanced-tooltip';

interface Coordinates {
  latitude: number;
  longitude: number;
}

interface DistanceResult {
  distance: number; // in kilometers
  duration: number; // in minutes
  mode: 'driving' | 'walking' | 'cycling' | 'transit';
  route?: string;
  cost?: number;
}

interface PointOfInterest {
  id: string;
  name: string;
  type: 'beach' | 'restaurant' | 'shopping' | 'attraction' | 'hospital' | 'airport' | 'transport';
  coordinates: Coordinates;
  description: string;
  category: string;
}

interface DistanceCalculatorProps {
  propertyLocation: string;
  propertyCoordinates?: Coordinates;
  className?: string;
  showNearbyPlaces?: boolean;
  maxDistance?: number; // in kilometers
}

// Popular South African destinations and points of interest
const SOUTH_AFRICAN_POIS: PointOfInterest[] = [
  // Cape Town Area
  { id: 'table-mountain', name: 'Table Mountain', type: 'attraction', coordinates: { latitude: -33.9628, longitude: 18.4098 }, description: 'Iconic flat-topped mountain', category: 'Nature' },
  { id: 'va-waterfront', name: 'V&A Waterfront', type: 'shopping', coordinates: { latitude: -33.9017, longitude: 18.4197 }, description: 'Premier shopping destination', category: 'Shopping' },
  { id: 'camps-bay-beach', name: 'Camps Bay Beach', type: 'beach', coordinates: { latitude: -33.9553, longitude: 18.3756 }, description: 'Beautiful white sand beach', category: 'Beach' },
  { id: 'cape-town-airport', name: 'Cape Town International Airport', type: 'airport', coordinates: { latitude: -33.9648, longitude: 18.6017 }, description: 'Main airport', category: 'Transport' },
  { id: 'groote-schuur-hospital', name: 'Groote Schuur Hospital', type: 'hospital', coordinates: { latitude: -33.9391, longitude: 18.4653 }, description: 'Major hospital', category: 'Healthcare' },
  
  // Johannesburg Area
  { id: 'sandton-city', name: 'Sandton City', type: 'shopping', coordinates: { latitude: -26.1076, longitude: 28.0567 }, description: 'Luxury shopping mall', category: 'Shopping' },
  { id: 'or-tambo-airport', name: 'OR Tambo International Airport', type: 'airport', coordinates: { latitude: -26.1392, longitude: 28.2460 }, description: 'Main international airport', category: 'Transport' },
  { id: 'cradle-of-humankind', name: 'Cradle of Humankind', type: 'attraction', coordinates: { latitude: -25.9167, longitude: 27.7667 }, description: 'UNESCO World Heritage Site', category: 'Culture' },
  
  // Durban Area
  { id: 'golden-mile', name: 'Golden Mile Beach', type: 'beach', coordinates: { latitude: -29.8579, longitude: 31.0292 }, description: 'Famous beachfront', category: 'Beach' },
  { id: 'ushaka-marine', name: 'uShaka Marine World', type: 'attraction', coordinates: { latitude: -29.8711, longitude: 31.0394 }, description: 'Marine theme park', category: 'Entertainment' },
  { id: 'king-shaka-airport', name: 'King Shaka International Airport', type: 'airport', coordinates: { latitude: -29.6144, longitude: 31.1197 }, description: 'Durban airport', category: 'Transport' },
  
  // Garden Route
  { id: 'knysna-heads', name: 'Knysna Heads', type: 'attraction', coordinates: { latitude: -34.0527, longitude: 23.0471 }, description: 'Dramatic coastal cliffs', category: 'Nature' },
  { id: 'tsitsikamma', name: 'Tsitsikamma National Park', type: 'attraction', coordinates: { latitude: -34.0167, longitude: 23.9000 }, description: 'Coastal national park', category: 'Nature' },
  
  // Kruger Area
  { id: 'kruger-skukuza', name: 'Kruger National Park (Skukuza)', type: 'attraction', coordinates: { latitude: -24.9947, longitude: 31.5914 }, description: 'Main rest camp', category: 'Wildlife' },
  { id: 'blyde-river-canyon', name: 'Blyde River Canyon', type: 'attraction', coordinates: { latitude: -24.5500, longitude: 30.8000 }, description: 'Scenic canyon', category: 'Nature' }
];

export const DistanceCalculator: React.FC<DistanceCalculatorProps> = ({
  propertyLocation,
  propertyCoordinates,
  className,
  showNearbyPlaces = true,
  maxDistance = 100
}) => {
  const [selectedDestination, setSelectedDestination] = useState<string>('');
  const [customDestination, setCustomDestination] = useState<string>('');
  const [distanceResults, setDistanceResults] = useState<DistanceResult[]>([]);
  const [nearbyPlaces, setNearbyPlaces] = useState<PointOfInterest[]>([]);
  const [loading, setLoading] = useState(false);
  const [travelMode, setTravelMode] = useState<'driving' | 'walking' | 'cycling' | 'transit'>('driving');

  // Calculate distance between two coordinates using Haversine formula
  const calculateDistance = (coord1: Coordinates, coord2: Coordinates): number => {
    const R = 6371; // Earth's radius in kilometers
    const dLat = (coord2.latitude - coord1.latitude) * Math.PI / 180;
    const dLon = (coord2.longitude - coord1.longitude) * Math.PI / 180;
    const a = 
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(coord1.latitude * Math.PI / 180) * Math.cos(coord2.latitude * Math.PI / 180) * 
      Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  };

  // Estimate travel time based on distance and mode
  const estimateTravelTime = (distance: number, mode: string): number => {
    const speeds = {
      driving: 60, // km/h average in cities
      walking: 5,  // km/h
      cycling: 15, // km/h
      transit: 30  // km/h average with stops
    };
    return Math.round((distance / speeds[mode as keyof typeof speeds]) * 60); // minutes
  };

  // Estimate travel cost
  const estimateTravelCost = (distance: number, mode: string): number => {
    const costs = {
      driving: distance * 2.5, // R2.50 per km (fuel + wear)
      walking: 0,
      cycling: 0,
      transit: Math.max(15, distance * 1.2) // Minimum R15, then R1.20 per km
    };
    return Math.round(costs[mode as keyof typeof costs]);
  };

  // Get default coordinates for property location
  const getPropertyCoordinates = (): Coordinates => {
    if (propertyCoordinates) return propertyCoordinates;
    
    // Default coordinates for major cities
    const cityCoords: Record<string, Coordinates> = {
      'cape town': { latitude: -33.9249, longitude: 18.4241 },
      'johannesburg': { latitude: -26.2041, longitude: 28.0473 },
      'durban': { latitude: -29.8587, longitude: 31.0218 },
      'pretoria': { latitude: -25.7479, longitude: 28.2293 },
      'port elizabeth': { latitude: -33.9608, longitude: 25.6022 }
    };
    
    const cityKey = Object.keys(cityCoords).find(city => 
      propertyLocation.toLowerCase().includes(city)
    );
    
    return cityKey ? cityCoords[cityKey] : { latitude: -30.5, longitude: 24.5 };
  };

  // Calculate distances to nearby places
  useEffect(() => {
    const propCoords = getPropertyCoordinates();
    
    const nearby = SOUTH_AFRICAN_POIS
      .map(poi => ({
        ...poi,
        distance: calculateDistance(propCoords, poi.coordinates)
      }))
      .filter(poi => poi.distance <= maxDistance)
      .sort((a, b) => a.distance - b.distance)
      .slice(0, 12); // Limit to 12 nearest places
    
    setNearbyPlaces(nearby);
  }, [propertyLocation, propertyCoordinates, maxDistance]);

  const handleCalculateDistance = async (destination: PointOfInterest) => {
    setLoading(true);
    
    try {
      const propCoords = getPropertyCoordinates();
      const distance = calculateDistance(propCoords, destination.coordinates);
      
      // Calculate for all travel modes
      const modes: Array<'driving' | 'walking' | 'cycling' | 'transit'> = ['driving', 'walking', 'cycling', 'transit'];
      const results = modes.map(mode => ({
        distance,
        duration: estimateTravelTime(distance, mode),
        mode,
        cost: estimateTravelCost(distance, mode)
      }));
      
      setDistanceResults(results);
    } catch (error) {
      console.error('Error calculating distance:', error);
    } finally {
      setLoading(false);
    }
  };

  const getIconForPOIType = (type: string) => {
    switch (type) {
      case 'beach': return Waves;
      case 'restaurant': return Utensils;
      case 'shopping': return ShoppingBag;
      case 'attraction': return Camera;
      case 'hospital': return Hospital;
      case 'airport': return Plane;
      case 'transport': return Train;
      default: return MapPin;
    }
  };

  const getIconForTravelMode = (mode: string) => {
    switch (mode) {
      case 'driving': return Car;
      case 'walking': return Walking;
      case 'cycling': return Bike;
      case 'transit': return Bus;
      default: return Navigation;
    }
  };

  const formatTime = (minutes: number) => {
    if (minutes < 60) return `${minutes}min`;
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return mins > 0 ? `${hours}h ${mins}min` : `${hours}h`;
  };

  return (
    <SlideIn direction="up" delay={100}>
      <Card className={`border-0 shadow-lg ${className}`}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calculator className="h-5 w-5 text-sea-green-600" />
            Distance Calculator
          </CardTitle>
          <p className="text-gray-600">
            Calculate distances and travel times from {propertyLocation} to popular destinations
          </p>
        </CardHeader>

        <CardContent>
          {/* Custom Destination Input */}
          <div className="mb-6">
            <label className="text-sm font-medium text-gray-700 mb-2 block">
              Calculate distance to:
            </label>
            <div className="flex gap-2">
              <Input
                placeholder="Enter destination..."
                value={customDestination}
                onChange={(e) => setCustomDestination(e.target.value)}
                className="flex-1"
              />
              <Button 
                onClick={() => {/* Handle custom destination */}}
                disabled={!customDestination.trim()}
              >
                <Route className="h-4 w-4 mr-2" />
                Calculate
              </Button>
            </div>
          </div>

          {/* Distance Results */}
          {distanceResults.length > 0 && (
            <div className="mb-6 p-4 bg-gray-50 rounded-lg">
              <h4 className="font-semibold text-gray-900 mb-3">Travel Options</h4>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                {distanceResults.map((result, index) => {
                  const Icon = getIconForTravelMode(result.mode);
                  return (
                    <HoverAnimation key={index} type="lift">
                      <div className="text-center p-3 bg-white rounded-lg border">
                        <Icon className="h-6 w-6 mx-auto mb-2 text-sea-green-600" />
                        <div className="text-sm font-medium capitalize">{result.mode}</div>
                        <div className="text-xs text-gray-600">{result.distance.toFixed(1)}km</div>
                        <div className="text-xs text-gray-600">{formatTime(result.duration)}</div>
                        {result.cost && result.cost > 0 && (
                          <div className="text-xs text-green-600">R{result.cost}</div>
                        )}
                      </div>
                    </HoverAnimation>
                  );
                })}
              </div>
            </div>
          )}

          {/* Nearby Places */}
          {showNearbyPlaces && (
            <div>
              <h4 className="font-semibold text-gray-900 mb-4">Nearby Places</h4>
              
              {/* Category Filter */}
              <div className="flex flex-wrap gap-2 mb-4">
                {['All', 'Beach', 'Shopping', 'Nature', 'Transport', 'Healthcare'].map((category) => (
                  <Badge
                    key={category}
                    variant="outline"
                    className="cursor-pointer hover:bg-sea-green-50 hover:border-sea-green-300"
                  >
                    {category}
                  </Badge>
                ))}
              </div>

              <div className="space-y-3">
                <StaggeredAnimation delay={100}>
                  {nearbyPlaces.map((place, index) => {
                    const Icon = getIconForPOIType(place.type);
                    return (
                      <HoverAnimation key={place.id} type="lift">
                        <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                          <div className="flex items-center gap-3">
                            <Icon className="h-5 w-5 text-sea-green-600" />
                            <div>
                              <h5 className="font-medium text-gray-900">{place.name}</h5>
                              <p className="text-sm text-gray-600">{place.description}</p>
                              <Badge variant="secondary" className="text-xs mt-1">
                                {place.category}
                              </Badge>
                            </div>
                          </div>
                          
                          <div className="text-right">
                            <div className="text-lg font-bold text-sea-green-600">
                              {(place as any).distance?.toFixed(1)}km
                            </div>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleCalculateDistance(place)}
                              disabled={loading}
                            >
                              <Navigation className="h-3 w-3 mr-1" />
                              Directions
                            </Button>
                          </div>
                        </div>
                      </HoverAnimation>
                    );
                  })}
                </StaggeredAnimation>
              </div>
            </div>
          )}

          {/* Quick Distance Reference */}
          <div className="mt-6 p-4 bg-blue-50 rounded-lg">
            <h5 className="font-medium text-blue-900 mb-2">Distance Reference</h5>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-sm">
              <div className="flex items-center gap-2">
                <Walking className="h-4 w-4 text-blue-600" />
                <span>5 min walk ≈ 400m</span>
              </div>
              <div className="flex items-center gap-2">
                <Car className="h-4 w-4 text-blue-600" />
                <span>5 min drive ≈ 5km</span>
              </div>
              <div className="flex items-center gap-2">
                <Bike className="h-4 w-4 text-blue-600" />
                <span>5 min cycle ≈ 1.2km</span>
              </div>
              <div className="flex items-center gap-2">
                <Bus className="h-4 w-4 text-blue-600" />
                <span>5 min transit ≈ 2.5km</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </SlideIn>
  );
};
