import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Calendar, 
  Users, 
  MapPin, 
  DollarSign, 
  Clock, 
  Phone, 
  Mail,
  AlertCircle,
  CheckCircle,
  XCircle
} from 'lucide-react';
import { format, parseISO, isAfter, isBefore } from 'date-fns';
import { useBooking } from '../contexts/BookingContext';
import bookingsService from '../services/bookings';

interface BookingCardProps {
  booking: {
    id: string;
    propertyId: string;
    propertyTitle: string;
    propertyLocation: string;
    checkInDate: string;
    checkOutDate: string;
    guestCount: number;
    totalAmount: number;
    bookingStatus: string;
    paymentStatus: string;
    specialRequests?: string;
    guest?: {
      firstName: string;
      lastName: string;
      email: string;
      phone?: string;
    };
    owner?: {
      firstName: string;
      lastName: string;
      email: string;
      phone?: string;
    };
    isOwner?: boolean;
    isGuest?: boolean;
    costBreakdown?: {
      nights: number;
      accommodationCost: number;
      cleaningFee: number;
      totalAmount: number;
      pricePerNight: number;
    };
    createdAt: string;
  };
  onUpdate?: () => void;
}

export const BookingCard: React.FC<BookingCardProps> = ({ booking, onUpdate }) => {
  const { updateBookingStatus, submitting } = useBooking();

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'confirmed':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-600" />;
      case 'cancelled':
        return <XCircle className="h-4 w-4 text-red-600" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-blue-600" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    return bookingsService.getStatusColor(status);
  };

  const getPaymentStatusColor = (status: string) => {
    return bookingsService.getPaymentStatusColor(status);
  };

  const canCancel = () => {
    return bookingsService.canCancelBooking(booking);
  };

  const canModify = () => {
    return bookingsService.canModifyBooking(booking);
  };

  const getDaysUntilCheckIn = () => {
    return bookingsService.getDaysUntilCheckIn(booking.checkInDate);
  };

  const handleStatusUpdate = async (newStatus: string) => {
    try {
      await updateBookingStatus(booking.id, newStatus);
      if (onUpdate) {
        onUpdate();
      }
    } catch (error) {
      console.error('Failed to update booking status:', error);
    }
  };

  const checkInDate = parseISO(booking.checkInDate);
  const checkOutDate = parseISO(booking.checkOutDate);
  const daysUntilCheckIn = getDaysUntilCheckIn();
  const isUpcoming = daysUntilCheckIn > 0;
  const isActive = daysUntilCheckIn <= 0 && isAfter(new Date(), checkInDate) && isBefore(new Date(), checkOutDate);
  const isPast = isBefore(checkOutDate, new Date());

  return (
    <Card className="hover:shadow-lg transition-shadow duration-200">
      <CardHeader>
        <div className="flex justify-between items-start">
          <div className="flex-1">
            <CardTitle className="text-lg font-semibold text-gray-900 mb-1">
              {booking.propertyTitle}
            </CardTitle>
            <div className="flex items-center text-gray-600 mb-2">
              <MapPin className="h-4 w-4 mr-1" />
              <span className="text-sm">{booking.propertyLocation}</span>
            </div>
          </div>
          <div className="flex flex-col items-end gap-2">
            <Badge 
              variant="outline" 
              className={`bg-${getStatusColor(booking.bookingStatus)}-100 text-${getStatusColor(booking.bookingStatus)}-800 border-${getStatusColor(booking.bookingStatus)}-200`}
            >
              <span className="flex items-center gap-1">
                {getStatusIcon(booking.bookingStatus)}
                {bookingsService.formatBookingStatus(booking.bookingStatus)}
              </span>
            </Badge>
            <Badge 
              variant="outline"
              className={`bg-${getPaymentStatusColor(booking.paymentStatus)}-100 text-${getPaymentStatusColor(booking.paymentStatus)}-800 border-${getPaymentStatusColor(booking.paymentStatus)}-200`}
            >
              {bookingsService.formatPaymentStatus(booking.paymentStatus)}
            </Badge>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Booking Details */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-3">
            <div className="flex items-center text-gray-700">
              <Calendar className="h-4 w-4 mr-2 text-gray-500" />
              <div>
                <p className="font-medium">
                  {format(checkInDate, 'MMM dd')} - {format(checkOutDate, 'MMM dd, yyyy')}
                </p>
                <p className="text-sm text-gray-600">
                  {booking.costBreakdown?.nights || 1} night{(booking.costBreakdown?.nights || 1) > 1 ? 's' : ''}
                </p>
              </div>
            </div>

            <div className="flex items-center text-gray-700">
              <Users className="h-4 w-4 mr-2 text-gray-500" />
              <span>{booking.guestCount} guest{booking.guestCount > 1 ? 's' : ''}</span>
            </div>

            <div className="flex items-center text-gray-700">
              <DollarSign className="h-4 w-4 mr-2 text-gray-500" />
              <div>
                <span className="font-semibold text-lg">R{booking.totalAmount.toLocaleString()}</span>
                <span className="text-sm text-gray-600 ml-1">total</span>
              </div>
            </div>
          </div>

          <div className="space-y-3">
            {/* Time until check-in */}
            {isUpcoming && (
              <div className="bg-blue-50 p-3 rounded-lg">
                <p className="text-sm font-medium text-blue-900">
                  {daysUntilCheckIn === 1 ? 'Tomorrow' : `${daysUntilCheckIn} days`} until check-in
                </p>
              </div>
            )}

            {isActive && (
              <div className="bg-green-50 p-3 rounded-lg">
                <p className="text-sm font-medium text-green-900">
                  Currently staying
                </p>
              </div>
            )}

            {isPast && booking.bookingStatus === 'confirmed' && (
              <div className="bg-gray-50 p-3 rounded-lg">
                <p className="text-sm font-medium text-gray-900">
                  Stay completed
                </p>
              </div>
            )}

            {/* Contact Information */}
            {booking.isGuest && booking.owner && (
              <div className="text-sm">
                <p className="font-medium text-gray-900 mb-1">Host Contact:</p>
                <p className="text-gray-600">{booking.owner.firstName} {booking.owner.lastName}</p>
                {booking.owner.phone && (
                  <p className="text-gray-600">{booking.owner.phone}</p>
                )}
              </div>
            )}

            {booking.isOwner && booking.guest && (
              <div className="text-sm">
                <p className="font-medium text-gray-900 mb-1">Guest Contact:</p>
                <p className="text-gray-600">{booking.guest.firstName} {booking.guest.lastName}</p>
                {booking.guest.phone && (
                  <p className="text-gray-600">{booking.guest.phone}</p>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Special Requests */}
        {booking.specialRequests && (
          <div className="bg-gray-50 p-3 rounded-lg">
            <p className="text-sm font-medium text-gray-900 mb-1">Special Requests:</p>
            <p className="text-sm text-gray-700">{booking.specialRequests}</p>
          </div>
        )}

        {/* Cost Breakdown */}
        {booking.costBreakdown && (
          <div className="bg-gray-50 p-3 rounded-lg">
            <p className="text-sm font-medium text-gray-900 mb-2">Cost Breakdown:</p>
            <div className="space-y-1 text-sm text-gray-700">
              <div className="flex justify-between">
                <span>R{booking.costBreakdown.pricePerNight.toLocaleString()} × {booking.costBreakdown.nights} nights</span>
                <span>R{booking.costBreakdown.accommodationCost.toLocaleString()}</span>
              </div>
              {booking.costBreakdown.cleaningFee > 0 && (
                <div className="flex justify-between">
                  <span>Cleaning fee</span>
                  <span>R{booking.costBreakdown.cleaningFee.toLocaleString()}</span>
                </div>
              )}
              <div className="border-t pt-1 flex justify-between font-medium">
                <span>Total</span>
                <span>R{booking.costBreakdown.totalAmount.toLocaleString()}</span>
              </div>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex flex-wrap gap-2 pt-2">
          {booking.isOwner && booking.bookingStatus === 'pending' && (
            <>
              <Button
                size="sm"
                onClick={() => handleStatusUpdate('confirmed')}
                disabled={submitting}
                className="bg-green-600 hover:bg-green-700 text-white"
              >
                Confirm Booking
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleStatusUpdate('cancelled')}
                disabled={submitting}
                className="border-red-300 text-red-700 hover:bg-red-50"
              >
                Decline
              </Button>
            </>
          )}

          {canCancel() && (
            <Button
              size="sm"
              variant="outline"
              onClick={() => handleStatusUpdate('cancelled')}
              disabled={submitting}
              className="border-red-300 text-red-700 hover:bg-red-50"
            >
              Cancel Booking
            </Button>
          )}

          {/* Contact Buttons */}
          {((booking.isGuest && booking.owner) || (booking.isOwner && booking.guest)) && (
            <>
              <Button size="sm" variant="outline">
                <Phone className="h-4 w-4 mr-1" />
                Call
              </Button>
              <Button size="sm" variant="outline">
                <Mail className="h-4 w-4 mr-1" />
                Message
              </Button>
            </>
          )}
        </div>

        {/* Booking ID and Date */}
        <div className="text-xs text-gray-500 pt-2 border-t">
          <p>Booking ID: {booking.id}</p>
          <p>Booked on: {format(parseISO(booking.createdAt), 'MMM dd, yyyy')}</p>
        </div>
      </CardContent>
    </Card>
  );
};
