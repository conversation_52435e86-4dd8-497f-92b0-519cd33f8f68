import React, { useEffect, useState } from 'react';
import { cn } from '@/lib/utils';

interface SlideInProps {
  children: React.ReactNode;
  direction?: 'up' | 'down' | 'left' | 'right';
  delay?: number;
  duration?: number;
  className?: string;
}

export const SlideIn: React.FC<SlideInProps> = ({
  children,
  direction = 'up',
  delay = 0,
  duration = 500,
  className = ''
}) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, delay);

    return () => clearTimeout(timer);
  }, [delay]);

  const getTransformClasses = () => {
    const baseClasses = 'transition-all ease-out';
    
    if (!isVisible) {
      switch (direction) {
        case 'up':
          return `${baseClasses} translate-y-8 opacity-0`;
        case 'down':
          return `${baseClasses} -translate-y-8 opacity-0`;
        case 'left':
          return `${baseClasses} translate-x-8 opacity-0`;
        case 'right':
          return `${baseClasses} -translate-x-8 opacity-0`;
        default:
          return `${baseClasses} translate-y-8 opacity-0`;
      }
    }
    
    return `${baseClasses} translate-x-0 translate-y-0 opacity-100`;
  };

  return (
    <div
      className={cn(getTransformClasses(), className)}
      style={{
        transitionDuration: `${duration}ms`
      }}
    >
      {children}
    </div>
  );
};
