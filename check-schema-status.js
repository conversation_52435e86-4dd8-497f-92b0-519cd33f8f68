#!/usr/bin/env node

/**
 * StayFinder Schema Status Checker
 * Checks the current status of the database schema
 */

import { createClient } from '@supabase/supabase-js';
import { config } from 'dotenv';
config();

// ANSI color codes
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, colors.green);
}

function logError(message) {
  log(`❌ ${message}`, colors.red);
}

function logWarning(message) {
  log(`⚠️  ${message}`, colors.yellow);
}

function logInfo(message) {
  log(`ℹ️  ${message}`, colors.blue);
}

// Initialize Supabase admin client
const supabaseAdmin = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

// Expected schema components
const expectedTables = [
  'users', 'properties', 'amenities', 'property_amenities',
  'property_images', 'bookings', 'booking_payments', 'reviews',
  'messages', 'notifications', 'user_preferences', 'property_availability'
];

async function checkSchemaStatus() {
  log('🔍 CHECKING STAYFINDER SCHEMA STATUS', colors.bright);
  log('='.repeat(50), colors.cyan);
  
  const results = {
    tablesExist: false,
    tableCount: 0,
    missingTables: [],
    amenitiesPopulated: false,
    amenityCount: 0,
    rlsEnabled: false,
    schemaReady: false
  };
  
  try {
    // Check if we can connect
    logInfo('Testing Supabase connection...');
    
    // Try a simple query that should work
    const { data: testData, error: testError } = await supabaseAdmin
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .limit(1);
    
    if (testError) {
      logError(`Connection test failed: ${testError.message}`);
      return results;
    }
    
    logSuccess('Connected to Supabase successfully');
    
    // Check existing tables
    logInfo('Checking existing tables...');
    
    const { data: tables, error: tablesError } = await supabaseAdmin
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public');
    
    if (tablesError) {
      logError(`Failed to check tables: ${tablesError.message}`);
      return results;
    }
    
    const existingTables = tables.map(t => t.table_name);
    results.tableCount = existingTables.length;
    results.missingTables = expectedTables.filter(t => !existingTables.includes(t));
    results.tablesExist = results.missingTables.length === 0;
    
    if (results.tablesExist) {
      logSuccess(`All ${expectedTables.length} required tables exist`);
    } else {
      logWarning(`${existingTables.length} tables exist, ${results.missingTables.length} missing`);
      logInfo(`Missing tables: ${results.missingTables.join(', ')}`);
    }
    
    // Check if amenities are populated (if table exists)
    if (existingTables.includes('amenities')) {
      logInfo('Checking amenities data...');
      
      const { data: amenities, error: amenitiesError } = await supabaseAdmin
        .from('amenities')
        .select('id', { count: 'exact' });
      
      if (amenitiesError) {
        logWarning(`Could not check amenities: ${amenitiesError.message}`);
      } else {
        results.amenityCount = amenities.length;
        results.amenitiesPopulated = amenities.length > 0;
        
        if (results.amenitiesPopulated) {
          logSuccess(`${amenities.length} amenities loaded`);
        } else {
          logWarning('Amenities table is empty');
        }
      }
    }
    
    // Check if we can access a user table (indicates RLS is working)
    if (existingTables.includes('users')) {
      logInfo('Testing Row Level Security...');
      
      const { data: users, error: usersError } = await supabaseAdmin
        .from('users')
        .select('id')
        .limit(1);
      
      if (usersError) {
        if (usersError.message.includes('RLS')) {
          logSuccess('Row Level Security is enabled');
          results.rlsEnabled = true;
        } else {
          logWarning(`Users table access issue: ${usersError.message}`);
        }
      } else {
        logSuccess('Users table accessible (RLS configured)');
        results.rlsEnabled = true;
      }
    }
    
    // Overall schema status
    results.schemaReady = results.tablesExist && results.amenitiesPopulated && results.rlsEnabled;
    
  } catch (error) {
    logError(`Schema check failed: ${error.message}`);
  }
  
  return results;
}

async function generateStatusReport(results) {
  log('\n' + '='.repeat(50), colors.cyan);
  log('📊 SCHEMA STATUS REPORT', colors.bright);
  log('='.repeat(50), colors.cyan);
  
  // Database connection
  if (results.tableCount >= 0) {
    logSuccess('Database Connection: WORKING');
  } else {
    logError('Database Connection: FAILED');
  }
  
  // Tables status
  if (results.tablesExist) {
    logSuccess(`Tables: ALL PRESENT (${expectedTables.length}/${expectedTables.length})`);
  } else {
    logWarning(`Tables: INCOMPLETE (${expectedTables.length - results.missingTables.length}/${expectedTables.length})`);
  }
  
  // Amenities status
  if (results.amenitiesPopulated) {
    logSuccess(`Amenities: POPULATED (${results.amenityCount} items)`);
  } else {
    logWarning('Amenities: EMPTY');
  }
  
  // RLS status
  if (results.rlsEnabled) {
    logSuccess('Row Level Security: ENABLED');
  } else {
    logWarning('Row Level Security: NOT CONFIGURED');
  }
  
  // Overall status
  log('\n🎯 OVERALL STATUS:', colors.cyan);
  if (results.schemaReady) {
    log('✅ SCHEMA IS READY FOR USE!', colors.green);
    log('Your StayFinder database is fully configured and ready.', colors.green);
  } else {
    log('⚠️  SCHEMA NEEDS SETUP', colors.yellow);
    log('Please follow the manual deployment guide.', colors.yellow);
  }
  
  // Next steps
  log('\n📋 NEXT STEPS:', colors.blue);
  
  if (!results.tablesExist) {
    log('1. 🛠️  Deploy schema using MANUAL_SCHEMA_DEPLOYMENT.md', colors.blue);
    log('2. 📊 Execute create-schema.sql in Supabase SQL Editor', colors.blue);
  }
  
  if (!results.amenitiesPopulated) {
    log('3. 🌟 Load amenities data using seed-data.sql', colors.blue);
  }
  
  if (!results.rlsEnabled) {
    log('4. 🔒 Set up Row Level Security using create-rls-policies.sql', colors.blue);
  }
  
  if (results.schemaReady) {
    log('1. 🔧 Configure real-time subscriptions for required tables', colors.blue);
    log('2. 🧪 Test your application with the new schema', colors.blue);
    log('3. 📱 Update your frontend to use Supabase queries', colors.blue);
  }
  
  log('\n📚 RESOURCES:', colors.cyan);
  log('• Schema Documentation: schema.md', colors.blue);
  log('• Manual Deployment Guide: MANUAL_SCHEMA_DEPLOYMENT.md', colors.blue);
  log('• SQL Files: create-schema.sql, create-indexes-functions.sql, etc.', colors.blue);
}

// Main function
async function main() {
  const results = await checkSchemaStatus();
  await generateStatusReport(results);
  
  process.exit(results.schemaReady ? 0 : 1);
}

// Run the check
main().catch(error => {
  logError(`Status check error: ${error.message}`);
  console.error(error);
  process.exit(1);
});
