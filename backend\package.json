{"name": "stayfinder-backend", "version": "1.0.0", "description": "Backend API for KZN South Coast StayFinder", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["stayfinder", "holiday-rental", "api", "kzn"], "author": "StayFinder Team", "license": "MIT", "dependencies": {"axios": "^1.10.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "mysql2": "^3.6.5", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.0.2"}}