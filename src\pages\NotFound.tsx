import React, { useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  Home,
  Search,
  ArrowLeft,
  MapPin,
  Compass,
  Heart,
  Calendar
} from 'lucide-react';
import { Header } from '@/components/Header';
import {
  PageTransition,
  SlideIn,
  BounceIn,
  HoverAnimation
} from '@/components/ui/page-transitions';

const NotFound: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname
    );
  }, [location.pathname]);

  const quickLinks = [
    {
      title: 'Search Properties',
      description: 'Find your perfect holiday rental',
      icon: Search,
      path: '/search',
      color: 'sea-green'
    },
    {
      title: 'Popular Destinations',
      description: 'Explore trending locations',
      icon: MapPin,
      path: '/search?location=popular',
      color: 'ocean-blue'
    },
    {
      title: 'My Dashboard',
      description: 'View your bookings and saved properties',
      icon: Calendar,
      path: '/dashboard',
      color: 'sunset-orange'
    }
  ];

  return (
    <PageTransition>
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-sea-green-50">
        <Header />

        <div className="container mx-auto px-4 py-16">
          <div className="max-w-4xl mx-auto text-center">

            {/* 404 Animation */}
            <BounceIn delay={100}>
              <div className="mb-8">
                <div className="text-9xl font-bold text-transparent bg-gradient-to-r from-sea-green-500 via-ocean-blue-500 to-sea-green-600 bg-clip-text mb-4">
                  404
                </div>
                <div className="relative">
                  <Compass className="h-24 w-24 mx-auto text-sea-green-500 animate-pulse" />
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="w-8 h-8 bg-gradient-to-r from-sea-green-500 to-ocean-blue-500 rounded-full animate-pulse"></div>
                  </div>
                </div>
              </div>
            </BounceIn>

            {/* Error Message */}
            <SlideIn direction="up" delay={200}>
              <div className="mb-12">
                <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
                  Oops! Page Not Found
                </h1>
                <p className="text-xl text-gray-600 mb-6 max-w-2xl mx-auto">
                  It looks like you've wandered off the beaten path. The page you're looking for
                  doesn't exist, but don't worry - we'll help you find your way back to your
                  perfect holiday destination!
                </p>

                {/* Fun Message */}
                <div className="bg-gradient-to-r from-sea-green-100 to-ocean-blue-100 rounded-2xl p-6 mb-8 max-w-2xl mx-auto">
                  <p className="text-sea-green-800 font-medium">
                    🏖️ While you're here, did you know the KZN South Coast has over 100km of pristine beaches?
                    Let's get you back to exploring them!
                  </p>
                </div>
              </div>
            </SlideIn>

            {/* Quick Actions */}
            <SlideIn direction="up" delay={300}>
              <div className="mb-12">
                <div className="flex flex-wrap justify-center gap-4 mb-8">
                  <HoverAnimation type="lift">
                    <Button
                      onClick={() => navigate(-1)}
                      variant="outline"
                      size="lg"
                      className="border-2 hover:border-sea-green-300 hover:bg-sea-green-50"
                    >
                      <ArrowLeft className="h-5 w-5 mr-2" />
                      Go Back
                    </Button>
                  </HoverAnimation>

                  <HoverAnimation type="lift">
                    <Button
                      onClick={() => navigate('/')}
                      size="lg"
                      className="bg-gradient-to-r from-sea-green-500 to-ocean-blue-500 hover:from-sea-green-600 hover:to-ocean-blue-600 text-white shadow-lg"
                    >
                      <Home className="h-5 w-5 mr-2" />
                      Go Home
                    </Button>
                  </HoverAnimation>
                </div>
              </div>
            </SlideIn>

            {/* Quick Links Grid */}
            <SlideIn direction="up" delay={400}>
              <div className="mb-12">
                <h2 className="text-2xl font-bold text-gray-900 mb-6">
                  Or try one of these popular options:
                </h2>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
                  {quickLinks.map((link, index) => {
                    const Icon = link.icon;
                    return (
                      <HoverAnimation key={index} type="lift">
                        <Card
                          className="cursor-pointer border-0 shadow-lg hover:shadow-xl transition-all duration-300"
                          onClick={() => navigate(link.path)}
                        >
                          <CardContent className="p-6 text-center">
                            <div className={`w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-${link.color}-500 to-${link.color}-600 rounded-2xl flex items-center justify-center shadow-lg`}>
                              <Icon className="h-8 w-8 text-white" />
                            </div>
                            <h3 className="text-lg font-semibold text-gray-900 mb-2">
                              {link.title}
                            </h3>
                            <p className="text-gray-600 text-sm">
                              {link.description}
                            </p>
                          </CardContent>
                        </Card>
                      </HoverAnimation>
                    );
                  })}
                </div>
              </div>
            </SlideIn>

            {/* Help Section */}
            <SlideIn direction="up" delay={500}>
              <Card className="max-w-2xl mx-auto border-0 shadow-lg bg-gradient-to-r from-gray-50 to-white">
                <CardContent className="p-8">
                  <div className="flex items-center justify-center mb-4">
                    <Heart className="h-8 w-8 text-red-500 mr-3" />
                    <h3 className="text-xl font-semibold text-gray-900">
                      Need Help?
                    </h3>
                  </div>
                  <p className="text-gray-600 mb-6">
                    If you're looking for something specific or experiencing technical issues,
                    our support team is here to help you find the perfect accommodation.
                  </p>
                  <div className="flex flex-wrap justify-center gap-4">
                    <Button variant="outline" size="sm">
                      Contact Support
                    </Button>
                    <Button variant="outline" size="sm">
                      Browse FAQ
                    </Button>
                    <Button variant="outline" size="sm">
                      Live Chat
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </SlideIn>

            {/* Footer Message */}
            <SlideIn direction="up" delay={600}>
              <div className="mt-12 text-center">
                <p className="text-gray-500 text-sm">
                  Lost but not forgotten - every great adventure starts with a wrong turn! 🗺️
                </p>
              </div>
            </SlideIn>
          </div>
        </div>
      </div>
    </PageTransition>
  );
};

export default NotFound;
