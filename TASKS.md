# 🏖️ KZN StayFinder - Production Tasks
## Complete Development Roadmap to Airbnb-Level Platform
*Aligned with DDD.md specifications and dedicated server infrastructure*

### 🎯 **Current Status**
✅ **Frontend**: React app running on localhost:5173
✅ **Database**: MySQL with sample data via phpMyAdmin
✅ **Basic UI**: Modern design with shadcn/ui components
✅ **Infrastructure**: XAMPP local development environment

### 🏗️ **Infrastructure Overview**
- **Local Development**: XAMPP (Apache, MySQL, PHP)
- **Production Server**: Dedicated server with WHM/cPanel
- **Database Management**: phpMyAdmin (local) → cPanel MySQL (production)
- **Email System**: cPanel email accounts and SMTP
- **File Management**: cPanel File Manager
- **Domain Management**: cPanel DNS and subdomain management

---

## 📋 **PHASE 1: CORE BACKEND INTEGRATION** (Priority: HIGH) - **COMPLETED**
*Transform from static frontend to dynamic full-stack application*
*Reference: DDD.md Section "Backend Technology Stack" and "Database Design"*

### 🔧 **Task 1.1: Backend Server Foundation Setup** - **COMPLETED**
*Reference: DDD.md "Technical Implementation Details"*
*Total Time: 12 hours (6 sub-tasks)*

#### **Sub-task 1.1.1: Initialize Node.js Backend Structure** (2 hours)
**Location**: `c:\xampp\htdocs\stayfinder\backend\`
**Prerequisites**: Node.js installed, XAMPP MySQL running

**Implementation Steps**:
1. Create backend directory structure:
   ```
   backend/
   ├── src/
   │   ├── controllers/
   │   ├── middleware/
   │   ├── models/
   │   ├── routes/
   │   ├── utils/
   │   └── config/
   ├── uploads/
   ├── logs/
   ├── package.json
   ├── .env
   └── server.js
   ```

2. Initialize package.json:
   ```bash
   cd c:\xampp\htdocs\stayfinder\backend
   npm init -y
   ```

3. Install core dependencies:
   ```bash
   npm install express cors helmet morgan dotenv bcryptjs jsonwebtoken mysql2 uuid multer express-validator express-rate-limit
   npm install --save-dev nodemon
   ```

**Testing Checkpoint**:
- Verify all dependencies installed: `npm list`
- Check directory structure exists
- Confirm package.json contains all required dependencies

**Completion Criteria**:
- [x] Backend folder structure created - **COMPLETED**
- [x] package.json configured with all dependencies - **COMPLETED**
- [x] Development dependencies installed - **COMPLETED**

**Rollback**: Delete backend folder and restart

#### **Sub-task 1.1.2: Configure Environment Variables** (1 hour)
**Location**: `c:\xampp\htdocs\stayfinder\backend\.env`

**Implementation Steps**:
1. Create .env file with XAMPP MySQL configuration:
   ```env
   # Database Configuration (XAMPP Local)
   DB_HOST=localhost
   DB_PORT=3306
   DB_NAME=stayfinder_dev
   DB_USER=root
   DB_PASSWORD=

   # JWT Configuration
   JWT_SECRET=kzn-stayfinder-jwt-secret-2024-production
   JWT_EXPIRES_IN=24h
   JWT_REFRESH_SECRET=kzn-stayfinder-refresh-secret-2024
   JWT_REFRESH_EXPIRES_IN=7d

   # Server Configuration
   PORT=3001
   NODE_ENV=development
   CORS_ORIGIN=http://localhost:5173

   # File Upload Configuration
   MAX_FILE_SIZE=5242880
   UPLOAD_PATH=./uploads

   # cPanel Email Configuration (for production)
   SMTP_HOST=mail.yourdomain.com
   SMTP_PORT=587
   SMTP_USER=<EMAIL>
   SMTP_PASS=your_cpanel_email_password
   SMTP_SECURE=false
   ```

**Testing Checkpoint**:
- Verify .env file exists and contains all variables
- Test environment variable loading with `console.log(process.env.DB_HOST)`

**Completion Criteria**:
- [x] .env file created with all required variables - **COMPLETED**
- [x] Environment variables accessible in Node.js - **COMPLETED**

**Rollback**: Delete .env file and recreate

#### **Sub-task 1.1.3: Database Connection Setup** (2 hours)
**Location**: `c:\xampp\htdocs\stayfinder\backend\src\utils\database.js`
**Reference**: DDD.md "Database Optimization" section

**Implementation Steps**:
1. Create database utility file:
   ```javascript
   const mysql = require('mysql2/promise');
   require('dotenv').config();

   // Database connection configuration for XAMPP
   const dbConfig = {
     host: process.env.DB_HOST || 'localhost',
     port: process.env.DB_PORT || 3306,
     user: process.env.DB_USER || 'root',
     password: process.env.DB_PASSWORD || '',
     database: process.env.DB_NAME || 'stayfinder_dev',
     waitForConnections: true,
     connectionLimit: 10,
     queueLimit: 0,
     acquireTimeout: 60000,
     timeout: 60000,
     reconnect: true
   };

   // Create connection pool
   const pool = mysql.createPool(dbConfig);

   // Test database connection
   async function testConnection() {
     try {
       const connection = await pool.getConnection();
       console.log('✅ Database connected successfully to XAMPP MySQL');
       connection.release();
       return true;
     } catch (error) {
       console.error('❌ Database connection failed:', error.message);
       return false;
     }
   }

   module.exports = { pool, testConnection };
   ```

2. Create database helper functions:
   ```javascript
   // Add to database.js
   async function executeQuery(query, params = []) {
     try {
       const [results] = await pool.execute(query, params);
       return results;
     } catch (error) {
       console.error('Database query error:', error);
       throw error;
     }
   }

   async function findOne(query, params = []) {
     const results = await executeQuery(query, params);
     return results.length > 0 ? results[0] : null;
   }

   async function findMany(query, params = []) {
     return await executeQuery(query, params);
   }

   module.exports = { pool, testConnection, executeQuery, findOne, findMany };
   ```

**Testing Checkpoint**:
1. Test database connection:
   ```javascript
   const { testConnection } = require('./src/utils/database');
   testConnection();
   ```
2. Verify connection to stayfinder_dev database
3. Test query execution with sample query

**Completion Criteria**:
- [x] Database connection pool created - **COMPLETED**
- [x] Connection test passes - **COMPLETED**
- [x] Helper functions implemented and tested - **COMPLETED**

**Rollback**: Delete database.js file and restart

#### **Sub-task 1.1.4: Express Server Configuration** (2 hours)
**Location**: `c:\xampp\htdocs\stayfinder\backend\server.js`
**Reference**: DDD.md "Security Implementation" section

**Implementation Steps**:
1. Create main server file:
   ```javascript
   const express = require('express');
   const cors = require('cors');
   const helmet = require('helmet');
   const morgan = require('morgan');
   const rateLimit = require('express-rate-limit');
   const { testConnection } = require('./src/utils/database');
   require('dotenv').config();

   const app = express();
   const PORT = process.env.PORT || 3001;

   // Security middleware
   app.use(helmet());

   // Rate limiting for XAMPP development
   const limiter = rateLimit({
     windowMs: 15 * 60 * 1000, // 15 minutes
     max: 100, // limit each IP to 100 requests per windowMs
     message: 'Too many requests from this IP, please try again later.'
   });
   app.use('/api/', limiter);

   // CORS configuration for localhost development
   app.use(cors({
     origin: process.env.CORS_ORIGIN || 'http://localhost:5173',
     credentials: true
   }));

   // Body parsing middleware
   app.use(express.json({ limit: '10mb' }));
   app.use(express.urlencoded({ extended: true, limit: '10mb' }));

   // Logging middleware
   app.use(morgan('combined'));

   // Static files for uploads (cPanel File Manager equivalent)
   app.use('/uploads', express.static('uploads'));

   // Health check endpoint
   app.get('/api/health', (req, res) => {
     res.json({
       status: 'OK',
       timestamp: new Date().toISOString(),
       environment: process.env.NODE_ENV,
       database: 'XAMPP MySQL'
     });
   });

   // Start server and test database
   app.listen(PORT, async () => {
     console.log(`🚀 StayFinder API Server running on port ${PORT}`);
     console.log(`📊 Environment: ${process.env.NODE_ENV}`);
     console.log(`🌐 CORS Origin: ${process.env.CORS_ORIGIN}`);
     console.log(`🗄️  Database: ${process.env.DB_HOST}:${process.env.DB_PORT}/${process.env.DB_NAME}`);

     // Test database connection on startup
     await testConnection();
   });

   module.exports = app;
   ```

2. Add npm scripts to package.json:
   ```json
   {
     "scripts": {
       "start": "node server.js",
       "dev": "nodemon server.js",
       "test": "echo \"Error: no test specified\" && exit 1"
     }
   }
   ```

**Testing Checkpoint**:
1. Start server: `npm run dev`
2. Test health endpoint: `http://localhost:3001/api/health`
3. Verify CORS headers in browser dev tools
4. Check database connection message in console

**Completion Criteria**:
- [x] Server starts without errors - **COMPLETED**
- [x] Health endpoint returns 200 status - **COMPLETED**
- [x] Database connection confirmed - **COMPLETED**
- [x] CORS configured for frontend - **COMPLETED**

**Rollback**: Delete server.js and restart

#### **Sub-task 1.1.5: Middleware Setup** (3 hours)
**Location**: `c:\xampp\htdocs\stayfinder\backend\src\middleware\`
**Reference**: DDD.md "Authentication & Authorization" section

**Implementation Steps**:
1. Create authentication middleware (`auth.js`):
   ```javascript
   const jwt = require('jsonwebtoken');
   const { findOne } = require('../utils/database');

   // Middleware to verify JWT token
   const authenticateToken = async (req, res, next) => {
     try {
       const authHeader = req.headers.authorization;
       const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

       if (!token) {
         return res.status(401).json({
           error: 'Access token required'
         });
       }

       // Verify token
       const decoded = jwt.verify(token, process.env.JWT_SECRET);

       // Get user from XAMPP MySQL database
       const user = await findOne(
         'SELECT id, email, role, email_verified FROM users WHERE id = ?',
         [decoded.userId]
       );

       if (!user) {
         return res.status(401).json({
           error: 'Invalid token - user not found'
         });
       }

       req.user = {
         id: user.id,
         email: user.email,
         role: user.role,
         emailVerified: user.email_verified
       };

       next();
     } catch (error) {
       console.error('Authentication error:', error);
       return res.status(401).json({
         error: 'Invalid token'
       });
     }
   };

   module.exports = { authenticateToken };
   ```

2. Create validation middleware (`validation.js`):
   ```javascript
   const { body, validationResult } = require('express-validator');

   // User registration validation
   const registerValidation = [
     body('email').isEmail().normalizeEmail(),
     body('password').isLength({ min: 6 }).withMessage('Password must be at least 6 characters'),
     body('firstName').trim().isLength({ min: 1 }).withMessage('First name is required'),
     body('lastName').trim().isLength({ min: 1 }).withMessage('Last name is required'),
     body('phone').optional().isMobilePhone()
   ];

   // User login validation
   const loginValidation = [
     body('email').isEmail().normalizeEmail(),
     body('password').notEmpty().withMessage('Password is required')
   ];

   // Check validation results
   const checkValidation = (req, res, next) => {
     const errors = validationResult(req);
     if (!errors.isEmpty()) {
       return res.status(400).json({
         error: 'Validation failed',
         details: errors.array()
       });
     }
     next();
   };

   module.exports = {
     registerValidation,
     loginValidation,
     checkValidation
   };
   ```

**Testing Checkpoint**:
1. Test middleware imports without errors
2. Verify JWT token validation logic
3. Test validation middleware with sample data

**Completion Criteria**:
- [x] Authentication middleware created - **COMPLETED**
- [x] Validation middleware implemented - **COMPLETED**
- [x] All middleware functions export correctly - **COMPLETED**

**Rollback**: Delete middleware folder and restart

#### **Sub-task 1.1.6: Error Handling & Logging** (2 hours)
**Location**: `c:\xampp\htdocs\stayfinder\backend\src\utils\logger.js`

**Implementation Steps**:
1. Create logging utility:
   ```javascript
   const fs = require('fs');
   const path = require('path');

   // Create logs directory if it doesn't exist
   const logsDir = path.join(__dirname, '../../logs');
   if (!fs.existsSync(logsDir)) {
     fs.mkdirSync(logsDir, { recursive: true });
   }

   // Simple file logger for XAMPP environment
   const logger = {
     info: (message) => {
       const timestamp = new Date().toISOString();
       const logMessage = `[${timestamp}] INFO: ${message}\n`;
       console.log(logMessage.trim());
       fs.appendFileSync(path.join(logsDir, 'app.log'), logMessage);
     },

     error: (message, error = null) => {
       const timestamp = new Date().toISOString();
       const logMessage = `[${timestamp}] ERROR: ${message}${error ? ` - ${error.stack}` : ''}\n`;
       console.error(logMessage.trim());
       fs.appendFileSync(path.join(logsDir, 'error.log'), logMessage);
     },

     warn: (message) => {
       const timestamp = new Date().toISOString();
       const logMessage = `[${timestamp}] WARN: ${message}\n`;
       console.warn(logMessage.trim());
       fs.appendFileSync(path.join(logsDir, 'app.log'), logMessage);
     }
   };

   module.exports = logger;
   ```

2. Add global error handler to server.js:
   ```javascript
   // Add to server.js before app.listen()
   const logger = require('./src/utils/logger');

   // Global error handler
   app.use((err, req, res, next) => {
     logger.error('Unhandled error:', err);

     res.status(err.status || 500).json({
       error: process.env.NODE_ENV === 'production'
         ? 'Internal server error'
         : err.message,
       ...(process.env.NODE_ENV !== 'production' && { stack: err.stack })
     });
   });

   // 404 handler
   app.use('*', (req, res) => {
     logger.warn(`404 - Route not found: ${req.originalUrl}`);
     res.status(404).json({
       error: 'Route not found',
       path: req.originalUrl
     });
   });
   ```

**Testing Checkpoint**:
1. Test logger functions create log files
2. Verify error handling with intentional error
3. Check 404 handler with invalid route

**Completion Criteria**:
- [x] Logger utility created and functional - **COMPLETED**
- [x] Error handling middleware implemented - **COMPLETED**
- [x] Log files created in logs directory - **COMPLETED**

**Rollback**: Delete logger.js and remove error handlers from server.js

---

### 🔐 **Task 1.2: Authentication System Implementation** - **COMPLETED**
*Reference: DDD.md "Authentication & Authorization" section*
*Total Time: 16 hours (8 sub-tasks)*

#### **Sub-task 1.2.1: User Registration API** (2 hours)
**Location**: `c:\xampp\htdocs\stayfinder\backend\src\routes\auth.js`
**Reference**: DDD.md "Users Table" schema

**Implementation Steps**:
1. Create authentication routes file:
   ```javascript
   const express = require('express');
   const bcrypt = require('bcryptjs');
   const jwt = require('jsonwebtoken');
   const { v4: uuidv4 } = require('uuid');
   const { findOne, executeQuery } = require('../utils/database');
   const { registerValidation, checkValidation } = require('../middleware/validation');
   const logger = require('../utils/logger');

   const router = express.Router();

   // Generate JWT token
   function generateToken(userId, email, role) {
     return jwt.sign(
       { userId, email, role },
       process.env.JWT_SECRET,
       { expiresIn: process.env.JWT_EXPIRES_IN }
     );
   }

   // Register new user
   router.post('/register', registerValidation, checkValidation, async (req, res) => {
     try {
       const { email, password, firstName, lastName, phone, role = 'guest' } = req.body;

       // Check if user already exists in XAMPP MySQL
       const existingUser = await findOne(
         'SELECT id FROM users WHERE email = ?',
         [email]
       );

       if (existingUser) {
         return res.status(409).json({
           error: 'User already exists with this email'
         });
       }

       // Hash password
       const saltRounds = 12;
       const passwordHash = await bcrypt.hash(password, saltRounds);

       // Create user in database
       const userId = uuidv4();
       await executeQuery(
         'INSERT INTO users (id, email, password_hash, first_name, last_name, phone, role, email_verified) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
         [userId, email, passwordHash, firstName, lastName, phone || null, role, false]
       );

       // Generate token
       const token = generateToken(userId, email, role);

       logger.info(`New user registered: ${email}`);

       res.status(201).json({
         message: 'User registered successfully',
         user: {
           id: userId,
           email,
           firstName,
           lastName,
           role,
           emailVerified: false
         },
         token
       });

     } catch (error) {
       logger.error('Registration error:', error);
       res.status(500).json({
         error: 'Registration failed',
         message: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
       });
     }
   });

   module.exports = router;
   ```

2. Add route to server.js:
   ```javascript
   // Add to server.js after middleware setup
   const authRoutes = require('./src/routes/auth');
   app.use('/api/auth', authRoutes);
   ```

**Testing Checkpoint**:
1. Test registration with Postman/curl:
   ```bash
   curl -X POST http://localhost:3001/api/auth/register \
     -H "Content-Type: application/json" \
     -d '{"email":"<EMAIL>","password":"password123","firstName":"Test","lastName":"User"}'
   ```
2. Verify user created in phpMyAdmin
3. Check JWT token returned

**Completion Criteria**:
- [ ] Registration endpoint responds with 201 status
- [ ] User data saved to MySQL database
- [ ] JWT token generated and returned
- [ ] Password properly hashed

**Rollback**: Remove auth routes from server.js, delete auth.js

#### **Sub-task 1.2.2: User Login API** (2 hours)
**Location**: `c:\xampp\htdocs\stayfinder\backend\src\routes\auth.js` (append)

**Implementation Steps**:
1. Add login endpoint to auth.js:
   ```javascript
   // Add to auth.js after registration route
   const { loginValidation } = require('../middleware/validation');

   // Login user
   router.post('/login', loginValidation, checkValidation, async (req, res) => {
     try {
       const { email, password } = req.body;

       // Find user in XAMPP MySQL
       const user = await findOne(
         'SELECT id, email, password_hash, first_name, last_name, role, email_verified FROM users WHERE email = ?',
         [email]
       );

       if (!user) {
         return res.status(401).json({
           error: 'Invalid credentials'
         });
       }

       // Verify password
       const isValidPassword = await bcrypt.compare(password, user.password_hash);
       if (!isValidPassword) {
         return res.status(401).json({
           error: 'Invalid credentials'
         });
       }

       // Generate token
       const token = generateToken(user.id, user.email, user.role);

       logger.info(`User logged in: ${email}`);

       res.json({
         message: 'Login successful',
         user: {
           id: user.id,
           email: user.email,
           firstName: user.first_name,
           lastName: user.last_name,
           role: user.role,
           emailVerified: user.email_verified
         },
         token
       });

     } catch (error) {
       logger.error('Login error:', error);
       res.status(500).json({
         error: 'Login failed',
         message: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
       });
     }
   });
   ```

**Testing Checkpoint**:
1. Test login with valid credentials:
   ```bash
   curl -X POST http://localhost:3001/api/auth/login \
     -H "Content-Type: application/json" \
     -d '{"email":"<EMAIL>","password":"password123"}'
   ```
2. Test login with invalid credentials
3. Verify JWT token returned

**Completion Criteria**:
- [ ] Login endpoint responds with 200 status for valid credentials
- [ ] Returns 401 for invalid credentials
- [ ] JWT token generated and returned
- [ ] User data returned (without password)

**Rollback**: Remove login route from auth.js

#### **Sub-task 1.2.3: User Profile API** (1 hour)
**Location**: `c:\xampp\htdocs\stayfinder\backend\src\routes\auth.js` (append)

**Implementation Steps**:
1. Add profile endpoint to auth.js:
   ```javascript
   // Add to auth.js after login route
   const { authenticateToken } = require('../middleware/auth');

   // Get current user profile
   router.get('/profile', authenticateToken, async (req, res) => {
     try {
       const user = await findOne(
         'SELECT id, email, first_name, last_name, phone, role, email_verified, created_at FROM users WHERE id = ?',
         [req.user.id]
       );

       if (!user) {
         return res.status(404).json({
           error: 'User not found'
         });
       }

       res.json({
         user: {
           id: user.id,
           email: user.email,
           firstName: user.first_name,
           lastName: user.last_name,
           phone: user.phone,
           role: user.role,
           emailVerified: user.email_verified,
           createdAt: user.created_at
         }
       });

     } catch (error) {
       logger.error('Profile fetch error:', error);
       res.status(500).json({
         error: 'Failed to fetch profile'
       });
     }
   });
   ```

**Testing Checkpoint**:
1. Test profile endpoint with valid token:
   ```bash
   curl -X GET http://localhost:3001/api/auth/profile \
     -H "Authorization: Bearer YOUR_JWT_TOKEN"
   ```
2. Test without token (should return 401)
3. Verify user data returned

**Completion Criteria**:
- [ ] Profile endpoint requires authentication
- [ ] Returns user data for valid token
- [ ] Returns 401 for missing/invalid token

**Rollback**: Remove profile route from auth.js

#### **Sub-task 1.2.4: Frontend Authentication Integration** (3 hours)
**Location**: `c:\xampp\htdocs\stayfinder\src\services\auth.js`
**Reference**: DDD.md "Frontend-Backend Integration" section

**Implementation Steps**:
1. Create authentication service:
   ```javascript
   // Create src/services/auth.js
   const API_BASE_URL = 'http://localhost:3001/api';

   class AuthService {
     constructor() {
       this.token = localStorage.getItem('stayfinder_token');
     }

     async register(userData) {
       try {
         const response = await fetch(`${API_BASE_URL}/auth/register`, {
           method: 'POST',
           headers: {
             'Content-Type': 'application/json',
           },
           body: JSON.stringify(userData),
         });

         const data = await response.json();

         if (!response.ok) {
           throw new Error(data.error || 'Registration failed');
         }

         this.setToken(data.token);
         return data;
       } catch (error) {
         console.error('Registration error:', error);
         throw error;
       }
     }

     async login(email, password) {
       try {
         const response = await fetch(`${API_BASE_URL}/auth/login`, {
           method: 'POST',
           headers: {
             'Content-Type': 'application/json',
           },
           body: JSON.stringify({ email, password }),
         });

         const data = await response.json();

         if (!response.ok) {
           throw new Error(data.error || 'Login failed');
         }

         this.setToken(data.token);
         return data;
       } catch (error) {
         console.error('Login error:', error);
         throw error;
       }
     }

     async getProfile() {
       try {
         const response = await fetch(`${API_BASE_URL}/auth/profile`, {
           headers: {
             'Authorization': `Bearer ${this.token}`,
           },
         });

         const data = await response.json();

         if (!response.ok) {
           throw new Error(data.error || 'Failed to fetch profile');
         }

         return data;
       } catch (error) {
         console.error('Profile fetch error:', error);
         throw error;
       }
     }

     setToken(token) {
       this.token = token;
       localStorage.setItem('stayfinder_token', token);
     }

     removeToken() {
       this.token = null;
       localStorage.removeItem('stayfinder_token');
     }

     isAuthenticated() {
       return !!this.token;
     }

     logout() {
       this.removeToken();
       window.location.href = '/';
     }
   }

   export default new AuthService();
   ```

2. Update LoginForm component:
   ```javascript
   // Update src/components/LoginForm.tsx
   import { useState } from 'react';
   import { Button } from '@/components/ui/button';
   import { Input } from '@/components/ui/input';
   import { Label } from '@/components/ui/label';
   import { Mail } from 'lucide-react';
   import authService from '@/services/auth';

   interface LoginFormProps {
     onClose: () => void;
   }

   export const LoginForm = ({ onClose }: LoginFormProps) => {
     const [email, setEmail] = useState('');
     const [password, setPassword] = useState('');
     const [loading, setLoading] = useState(false);
     const [error, setError] = useState('');

     const handleSubmit = async (e: React.FormEvent) => {
       e.preventDefault();
       setLoading(true);
       setError('');

       try {
         const result = await authService.login(email, password);
         console.log('Login successful:', result);
         onClose();
         // Optionally refresh the page or update global state
         window.location.reload();
       } catch (error) {
         setError(error.message || 'Login failed');
       } finally {
         setLoading(false);
       }
     };

     return (
       <form onSubmit={handleSubmit} className="space-y-4">
         {error && (
           <div className="text-red-600 text-sm bg-red-50 p-2 rounded">
             {error}
           </div>
         )}

         <div className="space-y-2">
           <Label htmlFor="email">Email</Label>
           <div className="relative">
             <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
             <Input
               id="email"
               type="email"
               placeholder="<EMAIL>"
               value={email}
               onChange={(e) => setEmail(e.target.value)}
               className="pl-10"
               required
               disabled={loading}
             />
           </div>
         </div>

         <div className="space-y-2">
           <Label htmlFor="password">Password</Label>
           <Input
             id="password"
             type="password"
             placeholder="Your password"
             value={password}
             onChange={(e) => setPassword(e.target.value)}
             required
             disabled={loading}
           />
         </div>

         <div className="flex justify-end space-x-2 pt-4">
           <Button type="button" variant="outline" onClick={onClose} disabled={loading}>
             Cancel
           </Button>
           <Button
             type="submit"
             className="bg-gradient-to-r from-sea-green-500 to-ocean-blue-500 hover:from-sea-green-600 hover:to-ocean-blue-600"
             disabled={loading}
           >
             {loading ? 'Signing in...' : 'Sign In'}
           </Button>
         </div>
       </form>
     );
   };
   ```

**Testing Checkpoint**:
1. Test login form with real backend
2. Verify JWT token stored in localStorage
3. Test error handling with invalid credentials
4. Confirm successful login redirects/updates UI

**Completion Criteria**:
- [x] Authentication service created and functional - **COMPLETED**
- [x] LoginForm connects to real backend - **COMPLETED**
- [x] JWT token properly stored and managed - **COMPLETED**
- [x] Error handling implemented - **COMPLETED**

**Rollback**: Revert LoginForm.tsx to original mock implementation

#### **Sub-task 1.2.5: Authentication Context Setup** (2 hours)
**Location**: `c:\xampp\htdocs\stayfinder\src\contexts\AuthContext.tsx`

**Implementation Steps**:
1. Create authentication context:
   ```typescript
   // Create src/contexts/AuthContext.tsx
   import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
   import authService from '@/services/auth';

   interface User {
     id: string;
     email: string;
     firstName: string;
     lastName: string;
     role: string;
     emailVerified: boolean;
   }

   interface AuthContextType {
     user: User | null;
     loading: boolean;
     login: (email: string, password: string) => Promise<void>;
     register: (userData: any) => Promise<void>;
     logout: () => void;
     isAuthenticated: boolean;
   }

   const AuthContext = createContext<AuthContextType | undefined>(undefined);

   export const useAuth = () => {
     const context = useContext(AuthContext);
     if (context === undefined) {
       throw new Error('useAuth must be used within an AuthProvider');
     }
     return context;
   };

   interface AuthProviderProps {
     children: ReactNode;
   }

   export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
     const [user, setUser] = useState<User | null>(null);
     const [loading, setLoading] = useState(true);

     useEffect(() => {
       const initAuth = async () => {
         if (authService.isAuthenticated()) {
           try {
             const profileData = await authService.getProfile();
             setUser(profileData.user);
           } catch (error) {
             console.error('Failed to fetch user profile:', error);
             authService.removeToken();
           }
         }
         setLoading(false);
       };

       initAuth();
     }, []);

     const login = async (email: string, password: string) => {
       const result = await authService.login(email, password);
       setUser(result.user);
     };

     const register = async (userData: any) => {
       const result = await authService.register(userData);
       setUser(result.user);
     };

     const logout = () => {
       authService.logout();
       setUser(null);
     };

     const value = {
       user,
       loading,
       login,
       register,
       logout,
       isAuthenticated: !!user,
     };

     return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
   };
   ```

2. Wrap App with AuthProvider:
   ```typescript
   // Update src/App.tsx
   import { AuthProvider } from "@/contexts/AuthContext";

   const App = () => (
     <QueryClientProvider client={queryClient}>
       <AuthProvider>
         <TooltipProvider>
           <Toaster />
           <Sonner />
           <BrowserRouter>
             <Routes>
               <Route path="/" element={<Index />} />
               <Route path="*" element={<NotFound />} />
             </Routes>
           </BrowserRouter>
         </TooltipProvider>
       </AuthProvider>
     </QueryClientProvider>
   );
   ```

**Testing Checkpoint**:
1. Test authentication context provides user state
2. Verify login/logout updates context
3. Test authentication persistence across page refreshes

**Completion Criteria**:
- [x] AuthContext created and functional - **COMPLETED**
- [x] App wrapped with AuthProvider - **COMPLETED**
- [x] Authentication state managed globally - **COMPLETED**
- [x] User persistence works across sessions - **COMPLETED**

**Rollback**: Remove AuthContext and revert App.tsx

#### **Sub-task 1.2.6: Update Header Component** (2 hours)
**Location**: `c:\xampp\htdocs\stayfinder\src\components\Header.tsx`

**Implementation Steps**:
1. Update Header to use real authentication:
   ```typescript
   // Update src/components/Header.tsx
   import { useState } from 'react';
   import { Button } from '@/components/ui/button';
   import { Input } from '@/components/ui/input';
   import { Search, User, Calendar, Phone, Mail, LogOut } from 'lucide-react';
   import {
     Dialog,
     DialogContent,
     DialogDescription,
     DialogHeader,
     DialogTitle,
     DialogTrigger,
   } from '@/components/ui/dialog';
   import {
     DropdownMenu,
     DropdownMenuContent,
     DropdownMenuItem,
     DropdownMenuTrigger,
   } from '@/components/ui/dropdown-menu';
   import { LoginForm } from './LoginForm';
   import { RegisterForm } from './RegisterForm';
   import { useAuth } from '@/contexts/AuthContext';

   export const Header = () => {
     const [isLoginOpen, setIsLoginOpen] = useState(false);
     const [isRegisterOpen, setIsRegisterOpen] = useState(false);
     const [searchLocation, setSearchLocation] = useState('');
     const { user, logout, isAuthenticated } = useAuth();

     const handleLogout = () => {
       logout();
     };

     return (
       <header className="bg-white/95 backdrop-blur-sm shadow-lg border-b border-sea-green-100 sticky top-0 z-50">
         <div className="container mx-auto px-4 py-4">
           <div className="flex items-center justify-between">
             {/* Logo */}
             <div className="flex items-center space-x-2">
               <div className="w-10 h-10 bg-gradient-to-br from-sea-green-500 to-ocean-blue-500 rounded-full flex items-center justify-center">
                 <span className="text-white font-bold text-lg">K</span>
               </div>
               <div>
                 <h1 className="text-xl font-bold text-black">KZN South Coast</h1>
                 <p className="text-sm text-black">StayFinder</p>
               </div>
             </div>

             {/* Search Bar */}
             <div className="hidden md:flex flex-1 max-w-md mx-8">
               <div className="relative w-full">
                 <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                 <Input
                   placeholder="Search by location, property name..."
                   value={searchLocation}
                   onChange={(e) => setSearchLocation(e.target.value)}
                   className="pl-10 pr-4 py-2 rounded-full border-sea-green-200 focus:border-sea-green-500 text-black placeholder:text-black"
                 />
               </div>
             </div>

             {/* Navigation & Auth */}
             <div className="flex items-center space-x-2">
               <Button variant="ghost" className="text-black hover:bg-sea-green-50">
                 List Your Property
               </Button>

               {isAuthenticated ? (
                 <DropdownMenu>
                   <DropdownMenuTrigger asChild>
                     <Button variant="outline" className="border-sea-green-300 text-black hover:bg-sea-green-50">
                       <User className="h-4 w-4 mr-2" />
                       {user?.firstName} {user?.lastName}
                     </Button>
                   </DropdownMenuTrigger>
                   <DropdownMenuContent>
                     <DropdownMenuItem>
                       <User className="h-4 w-4 mr-2" />
                       Profile
                     </DropdownMenuItem>
                     <DropdownMenuItem>
                       <Calendar className="h-4 w-4 mr-2" />
                       My Bookings
                     </DropdownMenuItem>
                     {user?.role === 'host' && (
                       <DropdownMenuItem>
                         <Calendar className="h-4 w-4 mr-2" />
                         My Properties
                       </DropdownMenuItem>
                     )}
                     <DropdownMenuItem onClick={handleLogout}>
                       <LogOut className="h-4 w-4 mr-2" />
                       Logout
                     </DropdownMenuItem>
                   </DropdownMenuContent>
                 </DropdownMenu>
               ) : (
                 <>
                   <Dialog open={isLoginOpen} onOpenChange={setIsLoginOpen}>
                     <DialogTrigger asChild>
                       <Button variant="outline" className="border-sea-green-300 text-black hover:bg-sea-green-50">
                         <User className="h-4 w-4 mr-2" />
                         Login
                       </Button>
                     </DialogTrigger>
                     <DialogContent>
                       <DialogHeader>
                         <DialogTitle>Welcome Back</DialogTitle>
                         <DialogDescription>
                           Sign in to your KZN South Coast StayFinder account
                         </DialogDescription>
                       </DialogHeader>
                       <LoginForm onClose={() => setIsLoginOpen(false)} />
                     </DialogContent>
                   </Dialog>

                   <Dialog open={isRegisterOpen} onOpenChange={setIsRegisterOpen}>
                     <DialogTrigger asChild>
                       <Button className="bg-gradient-to-r from-sea-green-500 to-ocean-blue-500 hover:from-sea-green-600 hover:to-ocean-blue-600 text-white">
                         Sign Up
                       </Button>
                     </DialogTrigger>
                     <DialogContent>
                       <DialogHeader>
                         <DialogTitle>Join StayFinder</DialogTitle>
                         <DialogDescription>
                           Create your account to start booking amazing properties
                         </DialogDescription>
                       </DialogHeader>
                       <RegisterForm onClose={() => setIsRegisterOpen(false)} />
                     </DialogContent>
                   </Dialog>
                 </>
               )}
             </div>
           </div>

           {/* Mobile Search */}
           <div className="md:hidden mt-4">
             <div className="relative">
               <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
               <Input
                 placeholder="Search properties..."
                 value={searchLocation}
                 onChange={(e) => setSearchLocation(e.target.value)}
                 className="pl-10 pr-4 py-2 rounded-full border-sea-green-200 focus:border-sea-green-500 text-black placeholder:text-black"
               />
             </div>
           </div>
         </div>
       </header>
     );
   };
   ```

**Testing Checkpoint**:
1. Test header shows login/register when not authenticated
2. Test header shows user menu when authenticated
3. Test logout functionality
4. Verify user name displays correctly

**Completion Criteria**:
- [x] Header integrates with authentication context - **COMPLETED**
- [x] Login/logout functionality works - **COMPLETED**
- [x] User menu displays for authenticated users - **COMPLETED**
- [x] Responsive design maintained - **COMPLETED**

**Rollback**: Revert Header.tsx to original version

#### **Sub-task 1.2.7: Update Register Form** (2 hours)
**Location**: `c:\xampp\htdocs\stayfinder\src\components\RegisterForm.tsx`

**Implementation Steps**:
1. Update RegisterForm to use real backend:
   ```typescript
   // Update src/components/RegisterForm.tsx
   import { useState } from 'react';
   import { Button } from '@/components/ui/button';
   import { Input } from '@/components/ui/input';
   import { Label } from '@/components/ui/label';
   import { User, Mail, Phone } from 'lucide-react';
   import { useAuth } from '@/contexts/AuthContext';

   interface RegisterFormProps {
     onClose: () => void;
   }

   export const RegisterForm = ({ onClose }: RegisterFormProps) => {
     const [formData, setFormData] = useState({
       firstName: '',
       lastName: '',
       email: '',
       phone: '',
       password: '',
       confirmPassword: '',
     });
     const [loading, setLoading] = useState(false);
     const [error, setError] = useState('');
     const { register } = useAuth();

     const handleChange = (field: string) => (e: React.ChangeEvent<HTMLInputElement>) => {
       setFormData(prev => ({ ...prev, [field]: e.target.value }));
     };

     const handleSubmit = async (e: React.FormEvent) => {
       e.preventDefault();
       setLoading(true);
       setError('');

       if (formData.password !== formData.confirmPassword) {
         setError('Passwords do not match');
         setLoading(false);
         return;
       }

       try {
         await register({
           firstName: formData.firstName,
           lastName: formData.lastName,
           email: formData.email,
           phone: formData.phone,
           password: formData.password,
         });

         console.log('Registration successful');
         onClose();
         // Optionally refresh the page or update global state
         window.location.reload();
       } catch (error) {
         setError(error.message || 'Registration failed');
       } finally {
         setLoading(false);
       }
     };

     return (
       <form onSubmit={handleSubmit} className="space-y-4">
         {error && (
           <div className="text-red-600 text-sm bg-red-50 p-2 rounded">
             {error}
           </div>
         )}

         <div className="grid grid-cols-2 gap-4">
           <div className="space-y-2">
             <Label htmlFor="firstName">First Name</Label>
             <Input
               id="firstName"
               type="text"
               placeholder="First name"
               value={formData.firstName}
               onChange={handleChange('firstName')}
               required
               disabled={loading}
             />
           </div>
           <div className="space-y-2">
             <Label htmlFor="lastName">Last Name</Label>
             <Input
               id="lastName"
               type="text"
               placeholder="Last name"
               value={formData.lastName}
               onChange={handleChange('lastName')}
               required
               disabled={loading}
             />
           </div>
         </div>

         <div className="space-y-2">
           <Label htmlFor="email">Email</Label>
           <div className="relative">
             <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
             <Input
               id="email"
               type="email"
               placeholder="<EMAIL>"
               value={formData.email}
               onChange={handleChange('email')}
               className="pl-10"
               required
               disabled={loading}
             />
           </div>
         </div>

         <div className="space-y-2">
           <Label htmlFor="phone">Phone Number</Label>
           <div className="relative">
             <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
             <Input
               id="phone"
               type="tel"
               placeholder="+27 XX XXX XXXX"
               value={formData.phone}
               onChange={handleChange('phone')}
               className="pl-10"
               disabled={loading}
             />
           </div>
         </div>

         <div className="space-y-2">
           <Label htmlFor="password">Password</Label>
           <Input
             id="password"
             type="password"
             placeholder="Create a password"
             value={formData.password}
             onChange={handleChange('password')}
             required
             disabled={loading}
           />
         </div>

         <div className="space-y-2">
           <Label htmlFor="confirmPassword">Confirm Password</Label>
           <Input
             id="confirmPassword"
             type="password"
             placeholder="Confirm your password"
             value={formData.confirmPassword}
             onChange={handleChange('confirmPassword')}
             required
             disabled={loading}
           />
         </div>

         <div className="flex justify-end space-x-2 pt-4">
           <Button type="button" variant="outline" onClick={onClose} disabled={loading}>
             Cancel
           </Button>
           <Button
             type="submit"
             className="bg-gradient-to-r from-sea-green-500 to-ocean-blue-500 hover:from-sea-green-600 hover:to-ocean-blue-600"
             disabled={loading}
           >
             {loading ? 'Creating Account...' : 'Create Account'}
           </Button>
         </div>
       </form>
     );
   };
   ```

**Testing Checkpoint**:
1. Test registration form with valid data
2. Test password confirmation validation
3. Test error handling for duplicate email
4. Verify successful registration logs user in

**Completion Criteria**:
- [x] Registration form connects to real backend - **COMPLETED**
- [x] Form validation works properly - **COMPLETED**
- [x] Error handling implemented - **COMPLETED**
- [x] Successful registration updates authentication state - **COMPLETED**

**Rollback**: Revert RegisterForm.tsx to original version

#### **Sub-task 1.2.8: Authentication Testing & Validation** (2 hours)
**Location**: Various authentication components

**Implementation Steps**:
1. Create comprehensive test scenarios:
   - Test user registration with valid data
   - Test user registration with invalid data (duplicate email, weak password)
   - Test user login with valid credentials
   - Test user login with invalid credentials
   - Test JWT token persistence across browser sessions
   - Test logout functionality
   - Test protected routes (when implemented)

2. Verify database integration:
   - Check user records created in phpMyAdmin
   - Verify password hashing in database
   - Confirm JWT tokens are properly generated
   - Test token expiration handling

3. Test frontend integration:
   - Verify authentication state updates across components
   - Test login/logout UI updates
   - Confirm error messages display properly
   - Test loading states during authentication

**Testing Checkpoint**:
1. Complete end-to-end authentication flow
2. Verify all error scenarios handled gracefully
3. Test authentication persistence
4. Confirm database records match frontend state

**Completion Criteria**:
- [ ] All authentication endpoints tested and working
- [ ] Frontend properly integrated with backend
- [ ] Error handling comprehensive
- [ ] Authentication state managed correctly
- [ ] Database records accurate and secure

**Rollback**: Revert all authentication changes and use mock data

---

### 🏠 **Task 1.3: Properties API Development** - **COMPLETED**
*Reference: DDD.md "Properties Table" schema and "Property Management System"*
*Total Time: 20 hours (10 sub-tasks)*

#### **Sub-task 1.3.1: Properties Database Model** (2 hours)
**Location**: `c:\xampp\htdocs\stayfinder\backend\src\models\Property.js`
**Reference**: DDD.md "Database Design" - Properties Table

**Implementation Steps**:
1. Create Property model with phpMyAdmin schema:
   ```javascript
   // Create src/models/Property.js
   const { executeQuery, findOne, findMany } = require('../utils/database');

   class Property {
     static async create(propertyData) {
       const query = `
         INSERT INTO properties (
           id, owner_id, title, description, property_type, location,
           latitude, longitude, max_guests, bedrooms, bathrooms,
           price_per_night, cleaning_fee, amenities, house_rules,
           check_in_time, check_out_time, status
         ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
       `;

       const params = [
         propertyData.id,
         propertyData.owner_id,
         propertyData.title,
         propertyData.description,
         propertyData.property_type,
         propertyData.location,
         propertyData.latitude,
         propertyData.longitude,
         propertyData.max_guests,
         propertyData.bedrooms,
         propertyData.bathrooms,
         propertyData.price_per_night,
         propertyData.cleaning_fee || 0,
         JSON.stringify(propertyData.amenities || []),
         propertyData.house_rules,
         propertyData.check_in_time || '15:00:00',
         propertyData.check_out_time || '11:00:00',
         propertyData.status || 'pending'
       ];

       return await executeQuery(query, params);
     }

     static async findById(id) {
       const query = `
         SELECT
           p.*,
           u.first_name as owner_first_name,
           u.last_name as owner_last_name,
           u.email as owner_email,
           u.phone as owner_phone,
           COALESCE(AVG(r.rating), 0) as average_rating,
           COUNT(r.id) as review_count
         FROM properties p
         LEFT JOIN users u ON p.owner_id = u.id
         LEFT JOIN reviews r ON p.id = r.property_id
         WHERE p.id = ? AND p.status = 'active'
         GROUP BY p.id
       `;

       return await findOne(query, [id]);
     }

     static async search(filters = {}) {
       let whereConditions = ['p.status = ?'];
       let queryParams = ['active'];

       if (filters.location) {
         whereConditions.push('p.location LIKE ?');
         queryParams.push(`%${filters.location}%`);
       }

       if (filters.minPrice) {
         whereConditions.push('p.price_per_night >= ?');
         queryParams.push(parseFloat(filters.minPrice));
       }

       if (filters.maxPrice) {
         whereConditions.push('p.price_per_night <= ?');
         queryParams.push(parseFloat(filters.maxPrice));
       }

       if (filters.guests) {
         whereConditions.push('p.max_guests >= ?');
         queryParams.push(parseInt(filters.guests));
       }

       if (filters.propertyType) {
         whereConditions.push('p.property_type = ?');
         queryParams.push(filters.propertyType);
       }

       const whereClause = whereConditions.join(' AND ');
       const offset = ((filters.page || 1) - 1) * (filters.limit || 12);

       const query = `
         SELECT
           p.*,
           u.first_name as owner_first_name,
           u.last_name as owner_last_name,
           pi.image_url as primary_image,
           COALESCE(AVG(r.rating), 0) as average_rating,
           COUNT(DISTINCT r.id) as review_count
         FROM properties p
         LEFT JOIN users u ON p.owner_id = u.id
         LEFT JOIN property_images pi ON p.id = pi.property_id AND pi.is_primary = true
         LEFT JOIN reviews r ON p.id = r.property_id
         WHERE ${whereClause}
         GROUP BY p.id
         ORDER BY p.created_at DESC
         LIMIT ? OFFSET ?
       `;

       return await findMany(query, [...queryParams, parseInt(filters.limit || 12), offset]);
     }

     static async updateById(id, updateData) {
       const fields = Object.keys(updateData);
       const values = Object.values(updateData);
       const setClause = fields.map(field => `${field} = ?`).join(', ');

       const query = `UPDATE properties SET ${setClause} WHERE id = ?`;
       return await executeQuery(query, [...values, id]);
     }

     static async deleteById(id) {
       const query = 'DELETE FROM properties WHERE id = ?';
       return await executeQuery(query, [id]);
     }
   }

   module.exports = Property;
   ```

**Testing Checkpoint**:
1. Test Property.create() with sample data
2. Test Property.findById() with existing property
3. Test Property.search() with various filters
4. Verify database queries execute correctly

**Completion Criteria**:
- [x] Property model created with all CRUD operations
- [x] Search functionality with filters implemented
- [x] Database queries optimized for phpMyAdmin/MySQL
- [x] Model methods tested and functional

**Rollback**: Delete Property.js model file

#### **Sub-task 1.3.2: Properties Routes Implementation** (3 hours)
**Location**: `c:\xampp\htdocs\stayfinder\backend\src\routes\properties.js`
**Reference**: DDD.md "RESTful API Endpoints" - Properties section

**Implementation Steps**:
1. Create properties routes file:
   ```javascript
   // Create src/routes/properties.js
   const express = require('express');
   const { body, query, validationResult } = require('express-validator');
   const { v4: uuidv4 } = require('uuid');
   const Property = require('../models/Property');
   const { authenticateToken } = require('../middleware/auth');
   const logger = require('../utils/logger');

   const router = express.Router();

   // Get all properties with filtering and search
   router.get('/', [
     query('location').optional().trim(),
     query('minPrice').optional().isNumeric(),
     query('maxPrice').optional().isNumeric(),
     query('guests').optional().isInt({ min: 1 }),
     query('propertyType').optional().isIn(['apartment', 'house', 'villa', 'guesthouse', 'cottage']),
     query('page').optional().isInt({ min: 1 }),
     query('limit').optional().isInt({ min: 1, max: 50 })
   ], async (req, res) => {
     try {
       const errors = validationResult(req);
       if (!errors.isEmpty()) {
         return res.status(400).json({
           error: 'Validation failed',
           details: errors.array()
         });
       }

       const filters = {
         location: req.query.location,
         minPrice: req.query.minPrice,
         maxPrice: req.query.maxPrice,
         guests: req.query.guests,
         propertyType: req.query.propertyType,
         page: parseInt(req.query.page) || 1,
         limit: parseInt(req.query.limit) || 12
       };

       const properties = await Property.search(filters);

       // Format response for frontend
       const formattedProperties = properties.map(property => ({
         id: property.id,
         title: property.title,
         description: property.description,
         propertyType: property.property_type,
         location: property.location,
         coordinates: {
           latitude: property.latitude,
           longitude: property.longitude
         },
         maxGuests: property.max_guests,
         bedrooms: property.bedrooms,
         bathrooms: property.bathrooms,
         pricePerNight: parseFloat(property.price_per_night),
         cleaningFee: parseFloat(property.cleaning_fee || 0),
         amenities: property.amenities ? JSON.parse(property.amenities) : [],
         checkInTime: property.check_in_time,
         checkOutTime: property.check_out_time,
         primaryImage: property.primary_image,
         averageRating: parseFloat(property.average_rating),
         reviewCount: parseInt(property.review_count),
         owner: {
           firstName: property.owner_first_name,
           lastName: property.owner_last_name
         },
         createdAt: property.created_at
       }));

       // Get total count for pagination
       const totalQuery = `
         SELECT COUNT(DISTINCT p.id) as total
         FROM properties p
         WHERE p.status = 'active'
         ${filters.location ? 'AND p.location LIKE ?' : ''}
         ${filters.minPrice ? 'AND p.price_per_night >= ?' : ''}
         ${filters.maxPrice ? 'AND p.price_per_night <= ?' : ''}
         ${filters.guests ? 'AND p.max_guests >= ?' : ''}
         ${filters.propertyType ? 'AND p.property_type = ?' : ''}
       `;

       // Build count query params
       let countParams = [];
       if (filters.location) countParams.push(`%${filters.location}%`);
       if (filters.minPrice) countParams.push(parseFloat(filters.minPrice));
       if (filters.maxPrice) countParams.push(parseFloat(filters.maxPrice));
       if (filters.guests) countParams.push(parseInt(filters.guests));
       if (filters.propertyType) countParams.push(filters.propertyType);

       const { executeQuery } = require('../utils/database');
       const countResult = await executeQuery(totalQuery, countParams);
       const total = countResult[0].total;

       res.json({
         properties: formattedProperties,
         pagination: {
           page: filters.page,
           limit: filters.limit,
           total,
           totalPages: Math.ceil(total / filters.limit)
         }
       });

       logger.info(`Properties search: ${formattedProperties.length} results for filters: ${JSON.stringify(filters)}`);

     } catch (error) {
       logger.error('Properties fetch error:', error);
       res.status(500).json({
         error: 'Failed to fetch properties',
         message: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
       });
     }
   });

   // Get single property by ID
   router.get('/:id', async (req, res) => {
     try {
       const { id } = req.params;

       const property = await Property.findById(id);

       if (!property) {
         return res.status(404).json({
           error: 'Property not found'
         });
       }

       // Get property images
       const { findMany } = require('../utils/database');
       const images = await findMany(
         'SELECT image_url, alt_text, is_primary, sort_order FROM property_images WHERE property_id = ? ORDER BY sort_order',
         [id]
       );

       // Format response
       const formattedProperty = {
         id: property.id,
         title: property.title,
         description: property.description,
         propertyType: property.property_type,
         location: property.location,
         coordinates: {
           latitude: property.latitude,
           longitude: property.longitude
         },
         maxGuests: property.max_guests,
         bedrooms: property.bedrooms,
         bathrooms: property.bathrooms,
         pricePerNight: parseFloat(property.price_per_night),
         cleaningFee: parseFloat(property.cleaning_fee || 0),
         amenities: property.amenities ? JSON.parse(property.amenities) : [],
         houseRules: property.house_rules,
         checkInTime: property.check_in_time,
         checkOutTime: property.check_out_time,
         images: images.map(img => ({
           url: img.image_url,
           altText: img.alt_text,
           isPrimary: img.is_primary,
           sortOrder: img.sort_order
         })),
         owner: {
           firstName: property.owner_first_name,
           lastName: property.owner_last_name,
           email: property.owner_email,
           phone: property.owner_phone
         },
         averageRating: parseFloat(property.average_rating),
         reviewCount: parseInt(property.review_count),
         createdAt: property.created_at,
         updatedAt: property.updated_at
       };

       res.json({ property: formattedProperty });

       logger.info(`Property details fetched: ${id}`);

     } catch (error) {
       logger.error('Property fetch error:', error);
       res.status(500).json({
         error: 'Failed to fetch property',
         message: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
       });
     }
   });

   module.exports = router;
   ```

2. Add properties routes to server.js:
   ```javascript
   // Add to server.js after auth routes
   const propertyRoutes = require('./src/routes/properties');
   app.use('/api/properties', propertyRoutes);
   ```

**Testing Checkpoint**:
1. Test GET /api/properties endpoint
2. Test GET /api/properties/:id endpoint
3. Test search filters (location, price, guests)
4. Test pagination functionality
5. Verify response format matches frontend expectations

**Completion Criteria**:
- [x] Properties routes implemented and functional
- [x] Search and filtering working correctly
- [x] Pagination implemented
- [x] Property details endpoint working
- [x] Routes added to server.js

**Rollback**: Remove properties routes from server.js, delete properties.js

#### **Sub-task 1.3.3: Properties Frontend Integration** (4 hours)
**Location**: `c:\xampp\htdocs\stayfinder\src\services\` and frontend components
**Reference**: DDD.md "Frontend-Backend Integration" section

**Implementation Steps**:
1. **Create Properties Service** (`src/services/properties.js`):
   - API service class for property operations
   - Search, filter, and pagination functionality
   - Property data transformation for frontend
   - Error handling and loading states

2. **Update SearchResults Component**:
   - Replace mock data with real API calls
   - Add loading and error states
   - Implement real-time search functionality
   - Add pagination support

3. **Update PropertyCard Component**:
   - Handle real property data structure
   - Display ratings, reviews, and amenities
   - Show property type and guest capacity
   - Add cleaning fee display

4. **Update FeaturedProperties Component**:
   - Connect to real properties API
   - Add loading and error handling
   - Display real property data

**Testing Checkpoint**:
1. Test properties service API calls
2. Verify search functionality works with backend
3. Test property card displays real data correctly
4. Confirm featured properties load from API
5. Test error handling and loading states

**Completion Criteria**:
- [x] Properties service created and functional
- [x] SearchResults component uses real API data
- [x] PropertyCard component handles real property structure
- [x] FeaturedProperties component connected to backend
- [x] Loading and error states implemented
- [x] Frontend successfully integrates with backend API

**Rollback**: Revert all frontend components to use mock data

#### **Sub-task 1.4.1: Bookings Database Model** (2 hours)
**Location**: `c:\xampp\htdocs\stayfinder\backend\src\models\Booking.js`
**Reference**: DDD.md "Bookings Table" schema

**Implementation Steps**:
1. **Create Booking Model** with complete CRUD operations
2. **Availability Checking** with conflict detection
3. **Cost Calculation** with breakdown (accommodation + cleaning fees)
4. **Booking Statistics** for dashboard analytics
5. **Status Management** for booking lifecycle

**Testing Checkpoint**:
- Test Booking.create() with sample data
- Test Booking.checkAvailability() with date conflicts
- Test Booking.calculateTotalAmount() with different properties
- Verify all model methods work correctly

**Completion Criteria**:
- [x] Booking model created with all CRUD operations
- [x] Availability checking with conflict detection
- [x] Cost calculation with detailed breakdown
- [x] Booking statistics and analytics methods
- [x] Model methods tested and functional

#### **Sub-task 1.4.2: Bookings API Routes** (3 hours)
**Location**: `c:\xampp\htdocs\stayfinder\backend\src\routes\bookings.js`
**Reference**: DDD.md "RESTful API Endpoints" - Bookings section

**Implementation Steps**:
1. **Updated Existing Routes** to use Booking model
2. **Added Logger Integration** for request tracking
3. **Enhanced Error Handling** with detailed responses
4. **Added New Endpoints**:
   - GET /upcoming/list - Get upcoming bookings
   - GET /stats/summary - Get booking statistics
   - POST /calculate-cost - Calculate booking cost

**Testing Checkpoint**:
- Test all booking endpoints with authentication
- Verify cost calculation accuracy
- Test booking creation and status updates
- Check error handling for invalid data

**Completion Criteria**:
- [x] All booking routes updated to use Booking model
- [x] Logger integration implemented
- [x] Additional endpoints for enhanced functionality
- [x] Comprehensive error handling
- [x] All endpoints tested and functional

#### **Sub-task 1.4.3: Bookings Frontend Service** (2 hours)
**Location**: `c:\xampp\htdocs\stayfinder\src\services\bookings.js`
**Reference**: DDD.md "Frontend-Backend Integration"

**Implementation Steps**:
1. **Created BookingsService Class** for API integration
2. **Authentication Integration** with JWT token management
3. **Data Transformation** for frontend compatibility
4. **Utility Methods** for status formatting and validation
5. **Error Handling** with user-friendly messages

**Testing Checkpoint**:
- Test service methods with real API
- Verify authentication token handling
- Test data transformation accuracy
- Check error handling and recovery

**Completion Criteria**:
- [x] Bookings service created and functional
- [x] Authentication integration working
- [x] Data transformation methods implemented
- [x] Utility methods for UI support
- [x] Error handling comprehensive

**Rollback**: Remove Booking model, revert bookings routes, delete bookings service

#### **Sub-task 1.5.1: Reviews Database Model** (2 hours)
**Location**: `c:\xampp\htdocs\stayfinder\backend\src\models\Review.js`
**Reference**: DDD.md "Reviews Table" schema

**Implementation Steps**:
1. **Create Review Model** with complete CRUD operations
2. **Booking Validation** to ensure only completed bookings can be reviewed
3. **Property Statistics** for rating calculations and breakdowns
4. **Search and Analytics** methods for review management
5. **Permission Checks** for editing and responding to reviews

**Testing Checkpoint**:
- Test Review.create() with valid booking data
- Test Review.getPropertyStats() for rating calculations
- Test Review.canEditReview() with time restrictions
- Verify all model methods work correctly

**Completion Criteria**:
- [x] Review model created with all CRUD operations
- [x] Booking validation and permission checks
- [x] Property statistics and rating calculations
- [x] Search and analytics methods implemented
- [x] Model methods tested and functional

#### **Sub-task 1.5.2: Reviews API Routes** (3 hours)
**Location**: `c:\xampp\htdocs\stayfinder\backend\src\routes\reviews.js`
**Reference**: DDD.md "RESTful API Endpoints" - Reviews section

**Implementation Steps**:
1. **Updated Existing Routes** to use Review model
2. **Added Logger Integration** for request tracking
3. **Enhanced Error Handling** with detailed responses
4. **Added New Endpoints**:
   - GET /recent - Get recent reviews
   - GET /stats/reviewer/:userId - Get reviewer statistics
   - GET /search - Search reviews by content
   - GET /top-rated-properties - Get top-rated properties

**Testing Checkpoint**:
- Test all review endpoints with authentication
- Verify review creation with booking validation
- Test host response functionality
- Check error handling for invalid data

**Completion Criteria**:
- [x] All review routes updated to use Review model
- [x] Logger integration implemented
- [x] Additional endpoints for enhanced functionality
- [x] Comprehensive error handling
- [x] All endpoints tested and functional

#### **Sub-task 1.5.3: Reviews Frontend Service** (2 hours)
**Location**: `c:\xampp\htdocs\stayfinder\src\services\reviews.js`
**Reference**: DDD.md "Frontend-Backend Integration"

**Implementation Steps**:
1. **Created ReviewsService Class** for API integration
2. **Authentication Integration** with JWT token management
3. **Data Transformation** for frontend compatibility
4. **Utility Methods** for rating display and validation
5. **Review Management** with sorting and filtering

**Testing Checkpoint**:
- Test service methods with real API
- Verify authentication token handling
- Test data transformation accuracy
- Check utility methods for UI support

**Completion Criteria**:
- [x] Reviews service created and functional
- [x] Authentication integration working
- [x] Data transformation methods implemented
- [x] Utility methods for UI support
- [x] Review management features complete

**Rollback**: Remove Review model, revert reviews routes, delete reviews service

#### **Sub-task 1.6.1: Authentication Service** (2 hours)
**Location**: `c:\xampp\htdocs\stayfinder\src\services\auth.js`
**Reference**: DDD.md "Frontend-Backend Integration" - Authentication

**Implementation Steps**:
1. **Created AuthService Class** for API integration
2. **JWT Token Management** with localStorage storage
3. **User Data Management** with localStorage persistence
4. **Authentication Methods**: register, login, logout, profile management
5. **Utility Methods** for role checking and user information

**Testing Checkpoint**:
- Test service methods with real API
- Verify token storage and retrieval
- Test authentication state persistence
- Check error handling and validation

**Completion Criteria**:
- [x] Authentication service created and functional
- [x] JWT token management implemented
- [x] User data persistence working
- [x] All authentication methods tested
- [x] Error handling comprehensive

#### **Sub-task 1.6.2: Authentication Context** (2 hours)
**Location**: `c:\xampp\htdocs\stayfinder\src\contexts\AuthContext.tsx`
**Reference**: React Context API for state management

**Implementation Steps**:
1. **Created AuthContext** with TypeScript interfaces
2. **Authentication State Management** with React hooks
3. **User Session Persistence** across page refreshes
4. **Loading States** for authentication operations
5. **Error Handling** with user-friendly messages

**Testing Checkpoint**:
- Test context provider functionality
- Verify state persistence across refreshes
- Test loading states during operations
- Check error handling and recovery

**Completion Criteria**:
- [x] Authentication context created
- [x] State management working correctly
- [x] Session persistence implemented
- [x] Loading states functional
- [x] Error handling comprehensive

#### **Sub-task 1.6.3: Updated Authentication Forms** (2 hours)
**Location**: `c:\xampp\htdocs\stayfinder\src\components\LoginForm.tsx` and `RegisterForm.tsx`
**Reference**: Real API integration replacing mock functionality

**Implementation Steps**:
1. **Updated LoginForm** to use real authentication API
2. **Updated RegisterForm** with proper validation and API calls
3. **Added Loading States** with spinners and disabled inputs
4. **Enhanced Error Handling** with user-friendly error messages
5. **Form Validation** with client-side and server-side validation

**Testing Checkpoint**:
- Test login form with valid/invalid credentials
- Test registration form with various inputs
- Verify loading states and error messages
- Check form validation and user feedback

**Completion Criteria**:
- [x] Login form uses real API
- [x] Registration form functional with validation
- [x] Loading states implemented
- [x] Error handling user-friendly
- [x] Form validation comprehensive

**Rollback**: Revert authentication forms to mock functionality, remove auth service and context

#### **Sub-task 1.7.1: Enhanced Search Hooks** (2 hours)
**Location**: `c:\xampp\htdocs\stayfinder\src\hooks\usePropertySearch.ts` and `useDebounce.ts`
**Reference**: React hooks for search state management

**Implementation Steps**:
1. **Created usePropertySearch Hook** for comprehensive search functionality
2. **Added useDebounce Hook** for real-time search optimization
3. **Search State Management** with loading, error, and pagination states
4. **API Integration** with properties service for real search results
5. **Load More Functionality** with infinite scroll support

**Testing Checkpoint**:
- Test search hook with various filter combinations
- Verify debouncing prevents excessive API calls
- Test pagination and load more functionality
- Check error handling and loading states

**Completion Criteria**:
- [x] usePropertySearch hook created and functional
- [x] useDebounce hook implemented for optimization
- [x] Search state management working correctly
- [x] API integration with properties service
- [x] Pagination and load more features working

#### **Sub-task 1.7.2: Advanced Search Filters** (3 hours)
**Location**: `c:\xampp\htdocs\stayfinder\src\components\SearchFilters.tsx`
**Reference**: Comprehensive search filtering interface

**Implementation Steps**:
1. **Created SearchFilters Component** with advanced filtering options
2. **Location Search** with popular destinations and autocomplete
3. **Price Range Filters** with quick price selections
4. **Property Type Selection** with all available types
5. **Guest Count Filter** with proper validation
6. **Active Filters Display** with individual filter removal

**Testing Checkpoint**:
- Test all filter combinations
- Verify filter state management
- Test quick filter selections
- Check filter clearing functionality

**Completion Criteria**:
- [x] Advanced search filters component created
- [x] All filter types implemented and functional
- [x] Popular destinations and quick selections
- [x] Active filters display and management
- [x] Filter validation and error handling

#### **Sub-task 1.7.3: Enhanced Hero Search** (3 hours)
**Location**: `c:\xampp\htdocs\stayfinder\src\components\Hero.tsx`
**Reference**: Enhanced search interface in hero section

**Implementation Steps**:
1. **Enhanced Search Bar** with location and guest inputs
2. **Popular Destinations** quick selection buttons
3. **Search Navigation** to dedicated search page
4. **Quick View Modal** for immediate results
5. **Responsive Design** for mobile and desktop

**Testing Checkpoint**:
- Test search bar functionality
- Verify navigation to search page
- Test quick view modal
- Check responsive design

**Completion Criteria**:
- [x] Enhanced hero search interface
- [x] Navigation to search page working
- [x] Quick view functionality implemented
- [x] Popular destinations integration
- [x] Responsive design completed

**Rollback**: Remove search hooks, revert search filters, restore original hero component

#### **Sub-task 1.8.1: Booking Context and State Management** (3 hours)
**Location**: `c:\xampp\htdocs\stayfinder\src\contexts\BookingContext.tsx`
**Reference**: React Context API for booking state management

**Implementation Steps**:
1. **Created BookingContext** with comprehensive booking state management
2. **Booking Actions** including create, calculate cost, check availability
3. **Booking Management** with status updates, cancellation, and retrieval
4. **Error Handling** with user-friendly error messages and recovery
5. **Authentication Integration** with user-specific booking operations

**Testing Checkpoint**:
- Test booking context provider functionality
- Verify booking state management across components
- Test error handling and loading states
- Check integration with authentication context

**Completion Criteria**:
- [x] Booking context created and functional
- [x] All booking actions implemented
- [x] State management working correctly
- [x] Error handling comprehensive
- [x] Authentication integration working

#### **Sub-task 1.8.2: Enhanced Booking Calendar** (4 hours)
**Location**: `c:\xampp\htdocs\stayfinder\src\components\BookingCalendar.tsx`
**Reference**: Multi-step booking flow with real API integration

**Implementation Steps**:
1. **Multi-step Booking Flow** with date selection, details, and confirmation
2. **Real API Integration** with availability checking and cost calculation
3. **Enhanced UI/UX** with loading states, error handling, and validation
4. **Guest Management** with guest count selection and validation
5. **Cost Breakdown** with detailed pricing information display

**Testing Checkpoint**:
- Test date selection and availability checking
- Verify cost calculation with real API
- Test booking creation flow end-to-end
- Check error handling and validation

**Completion Criteria**:
- [x] Multi-step booking flow implemented
- [x] Real API integration working
- [x] Enhanced UI with proper feedback
- [x] Guest management functional
- [x] Cost breakdown accurate

#### **Sub-task 1.8.3: Booking Management System** (3 hours)
**Location**: `c:\xampp\htdocs\stayfinder\src\components\BookingCard.tsx` and `src\pages\Bookings.tsx`
**Reference**: Complete booking management interface

**Implementation Steps**:
1. **BookingCard Component** with comprehensive booking display
2. **Booking Status Management** with status updates and cancellation
3. **Bookings Page** with filtering, sorting, and management features
4. **Dashboard Integration** with booking summaries and quick access
5. **Navigation Integration** with proper routing and user flow

**Testing Checkpoint**:
- Test booking card display and interactions
- Verify booking status updates and cancellation
- Test bookings page filtering and sorting
- Check dashboard integration

**Completion Criteria**:
- [x] BookingCard component fully functional
- [x] Booking status management working
- [x] Bookings page with all features
- [x] Dashboard integration complete
- [x] Navigation properly integrated

**Rollback**: Remove booking context, revert booking calendar, remove booking management components

#### **Sub-task 1.9.1: Reviews Context and State Management** (3 hours)
**Location**: `c:\xampp\htdocs\stayfinder\src\contexts\ReviewsContext.tsx`
**Reference**: React Context API for reviews state management

**Implementation Steps**:
1. **Created ReviewsContext** with comprehensive reviews state management
2. **Review Actions** including create, update, delete, and retrieval
3. **Property Reviews Management** with filtering and statistics
4. **User Reviews Management** with personal review tracking
5. **Error Handling** with user-friendly error messages and recovery

**Testing Checkpoint**:
- Test reviews context provider functionality
- Verify review state management across components
- Test error handling and loading states
- Check integration with authentication context

**Completion Criteria**:
- [x] Reviews context created and functional
- [x] All review actions implemented
- [x] State management working correctly
- [x] Error handling comprehensive
- [x] Authentication integration working

#### **Sub-task 1.9.2: Star Rating and Review Components** (4 hours)
**Location**: `c:\xampp\htdocs\stayfinder\src\components\StarRating.tsx`, `ReviewCard.tsx`, `ReviewForm.tsx`
**Reference**: Interactive rating system and review management

**Implementation Steps**:
1. **StarRating Component** with interactive rating selection and display
2. **ReviewCard Component** with comprehensive review display and actions
3. **ReviewForm Component** with validation and submission handling
4. **Rating Display Components** with average ratings and breakdowns
5. **Review Management** with edit, delete, and moderation features

**Testing Checkpoint**:
- Test star rating interactions and display
- Verify review card functionality and actions
- Test review form validation and submission
- Check rating calculations and displays

**Completion Criteria**:
- [x] Star rating component fully interactive
- [x] Review card with all features working
- [x] Review form with validation functional
- [x] Rating displays accurate and responsive
- [x] Review management features complete

#### **Sub-task 1.9.3: Reviews Integration and Pages** (5 hours)
**Location**: `c:\xampp\htdocs\stayfinder\src\components\ReviewsList.tsx` and `src\pages\Reviews.tsx`
**Reference**: Complete reviews interface and user management

**Implementation Steps**:
1. **ReviewsList Component** with filtering, sorting, and pagination
2. **Reviews Page** with user review management and statistics
3. **Property Integration** with reviews display in property cards
4. **Navigation Integration** with proper routing and user flow
5. **Statistics and Analytics** with review breakdowns and insights

**Testing Checkpoint**:
- Test reviews list filtering and sorting
- Verify reviews page functionality and statistics
- Test property integration with reviews
- Check navigation and user flow

**Completion Criteria**:
- [x] ReviewsList component fully functional
- [x] Reviews page with all features working
- [x] Property integration complete
- [x] Navigation properly integrated
- [x] Statistics and analytics working

**Rollback**: Remove reviews context, revert review components, remove reviews pages

#### **Sub-task 2.1.1: Host Dashboard Page Creation** (4 hours)
**Location**: `c:\xampp\htdocs\stayfinder\src\pages\HostDashboard.tsx`
**Reference**: Host-specific dashboard interface for property management

**Implementation Steps**:
1. **Created HostDashboard Page** with comprehensive host interface
2. **Statistics Overview** with revenue, bookings, properties, and ratings
3. **Property Management Section** with property cards and quick actions
4. **Recent Bookings Section** with booking status and guest information
5. **Revenue Analytics Placeholder** for future chart integration

**Testing Checkpoint**:
- Test host dashboard page loads correctly
- Verify statistics display and calculations
- Test property and booking sections
- Check responsive design and navigation

**Completion Criteria**:
- [x] Host dashboard page created and functional
- [x] Statistics overview working correctly
- [x] Property and booking sections implemented
- [x] Navigation integration complete
- [x] Responsive design implemented

#### **Sub-task 2.1.2: Property Management Component** (4 hours)
**Location**: `c:\xampp\htdocs\stayfinder\src\components\PropertyManagement.tsx`
**Reference**: Comprehensive property management interface for hosts

**Implementation Steps**:
1. **PropertyManagement Component** with search, filter, and sort functionality
2. **Property Cards** with detailed information and action menus
3. **Status Management** with visual indicators and status updates
4. **Property Actions** including view, edit, and delete operations
5. **Empty States** with helpful messaging and call-to-action buttons

**Testing Checkpoint**:
- Test property search and filtering
- Verify property card display and actions
- Test status indicators and management
- Check empty states and error handling

**Completion Criteria**:
- [x] Property management component fully functional
- [x] Search and filtering working correctly
- [x] Property actions implemented
- [x] Status management complete
- [x] Empty states and error handling working

#### **Sub-task 2.1.3: Host Booking Management Component** (4 hours)
**Location**: `c:\xampp\htdocs\stayfinder\src\components\HostBookingManagement.tsx`
**Reference**: Booking management interface for hosts to manage guest bookings

**Implementation Steps**:
1. **HostBookingManagement Component** with booking list and filters
2. **Booking Cards** with guest information and booking details
3. **Booking Actions** including accept, decline, and contact guest
4. **Status Indicators** with visual booking and payment status
5. **Guest Communication** with contact information and messaging

**Testing Checkpoint**:
- Test booking list display and filtering
- Verify booking actions and status updates
- Test guest contact information display
- Check booking timeline and details

**Completion Criteria**:
- [x] Host booking management component functional
- [x] Booking actions working correctly
- [x] Status indicators accurate
- [x] Guest communication features complete
- [x] Booking timeline and details working

**Rollback**: Remove host dashboard page, property management component, host booking management component

#### **Sub-task 2.2.1: Property Wizard Main Page** (4 hours)
**Location**: `c:\xampp\htdocs\stayfinder\src\pages\PropertyWizard.tsx`
**Reference**: Multi-step wizard interface for property creation

**Implementation Steps**:
1. **Created PropertyWizard Page** with step-by-step navigation
2. **Progress Tracking** with visual progress bar and step indicators
3. **Form State Management** with comprehensive data validation
4. **Step Navigation** with previous/next controls and validation
5. **Submission Handling** with error management and success flow

**Testing Checkpoint**:
- Test wizard page loads and navigation works
- Verify progress tracking and step indicators
- Test form state persistence across steps
- Check validation and error handling

**Completion Criteria**:
- [x] Property wizard page created and functional
- [x] Step navigation working correctly
- [x] Progress tracking implemented
- [x] Form state management complete
- [x] Validation and error handling working

#### **Sub-task 2.2.2: Property Basic Info Step** (3 hours)
**Location**: `c:\xampp\htdocs\stayfinder\src\components\wizard\PropertyBasicInfo.tsx`
**Reference**: Property details and location information form

**Implementation Steps**:
1. **PropertyBasicInfo Component** with property details form
2. **Property Type Selection** with visual property type cards
3. **Location Input** with city/area and full address fields
4. **Property Details** with guest capacity, bedrooms, bathrooms
5. **Form Validation** with comprehensive field validation

**Testing Checkpoint**:
- Test property type selection interface
- Verify location input and validation
- Test property details counters and validation
- Check form validation and error display

**Completion Criteria**:
- [x] Property basic info form functional
- [x] Property type selection working
- [x] Location input implemented
- [x] Property details counters working
- [x] Form validation complete

#### **Sub-task 2.2.3: Property Amenities Step** (3 hours)
**Location**: `c:\xampp\htdocs\stayfinder\src\components\wizard\PropertyAmenities.tsx`
**Reference**: Comprehensive amenities selection interface

**Implementation Steps**:
1. **PropertyAmenities Component** with categorized amenities
2. **Amenity Categories** including essential, features, appliances, entertainment
3. **Interactive Selection** with visual feedback and selection counts
4. **Quick Actions** with select all/clear all functionality
5. **Amenity Summary** with selected amenities preview

**Testing Checkpoint**:
- Test amenity selection and deselection
- Verify category organization and display
- Test quick actions and bulk operations
- Check amenity summary and counts

**Completion Criteria**:
- [x] Amenities selection interface functional
- [x] Category organization working
- [x] Interactive selection implemented
- [x] Quick actions working
- [x] Amenity summary complete

#### **Sub-task 2.2.4: Property Rules and Pricing Steps** (3 hours)
**Location**: `c:\xampp\htdocs\stayfinder\src\components\wizard\PropertyRules.tsx` and `PropertyPricing.tsx`
**Reference**: House rules, policies, and pricing configuration

**Implementation Steps**:
1. **PropertyRules Component** with check-in/out times and house rules
2. **Cancellation Policies** with policy selection and descriptions
3. **PropertyPricing Component** with base pricing and fees
4. **Discount Configuration** with weekly and monthly discounts
5. **Pricing Preview** with calculation examples and summaries

**Testing Checkpoint**:
- Test check-in/out time selection
- Verify house rules management
- Test pricing input and calculations
- Check discount configuration and previews

**Completion Criteria**:
- [x] Property rules configuration functional
- [x] Cancellation policy selection working
- [x] Pricing configuration implemented
- [x] Discount settings working
- [x] Pricing preview and calculations complete

#### **Sub-task 2.2.5: Property Verification Step** (3 hours)
**Location**: `c:\xampp\htdocs\stayfinder\src\components\wizard\PropertyVerification.tsx`
**Reference**: Final review and submission interface

**Implementation Steps**:
1. **PropertyVerification Component** with complete property review
2. **Property Summary** with all entered information display
3. **Pricing Summary** with calculation examples and totals
4. **Submission Interface** with final review and submit functionality
5. **Next Steps Information** with post-submission workflow

**Testing Checkpoint**:
- Test property information display and review
- Verify pricing calculations and summaries
- Test submission interface and loading states
- Check next steps information and guidance

**Completion Criteria**:
- [x] Property verification interface functional
- [x] Property summary display working
- [x] Pricing calculations accurate
- [x] Submission interface complete
- [x] Next steps information provided

**Rollback**: Remove property wizard page, wizard components, wizard routes

#### **Sub-task 2.3.1: Image Upload Component** (3 hours)
**Location**: `c:\xampp\htdocs\stayfinder\src\components\ImageUpload.tsx`
**Reference**: Drag-and-drop image upload with preview and management

**Implementation Steps**:
1. **Created ImageUpload Component** with drag-and-drop functionality
2. **File Validation** with type, size, and format checking
3. **Image Preview** with thumbnail gallery and primary image selection
4. **Upload Management** with progress tracking and error handling
5. **Image Actions** including remove, reorder, and set primary

**Testing Checkpoint**:
- Test drag-and-drop image upload
- Verify file validation and error handling
- Test image preview and management
- Check upload progress and completion

**Completion Criteria**:
- [x] Image upload component functional
- [x] Drag-and-drop interface working
- [x] File validation implemented
- [x] Image preview and management complete
- [x] Upload progress and error handling working

#### **Sub-task 2.3.2: Property Image Gallery** (2 hours)
**Location**: `c:\xampp\htdocs\stayfinder\src\components\PropertyImageGallery.tsx`
**Reference**: Image gallery display with navigation and fullscreen view

**Implementation Steps**:
1. **PropertyImageGallery Component** with image carousel and navigation
2. **Thumbnail Navigation** with clickable thumbnail strip
3. **Fullscreen View** with modal overlay and navigation controls
4. **Image Controls** including download, share, and like functionality
5. **Responsive Design** with mobile-friendly touch controls

**Testing Checkpoint**:
- Test image gallery navigation and display
- Verify fullscreen modal functionality
- Test thumbnail navigation and selection
- Check responsive design and touch controls

**Completion Criteria**:
- [x] Image gallery component functional
- [x] Navigation and carousel working
- [x] Fullscreen view implemented
- [x] Image controls complete
- [x] Responsive design working

#### **Sub-task 2.3.3: Property Image Manager** (2 hours)
**Location**: `c:\xampp\htdocs\stayfinder\src\components\PropertyImageManager.tsx`
**Reference**: Host dashboard image management interface

**Implementation Steps**:
1. **PropertyImageManager Component** with grid and list view modes
2. **Bulk Operations** with select all, delete selected functionality
3. **Image Metadata** with file size, upload date, and status display
4. **Image Actions** including set primary, view, edit, and delete
5. **View Modes** with grid and list layout options

**Testing Checkpoint**:
- Test image management interface and view modes
- Verify bulk operations and selection
- Test image metadata display and actions
- Check image status and management features

**Completion Criteria**:
- [x] Image manager component functional
- [x] View modes working correctly
- [x] Bulk operations implemented
- [x] Image metadata display complete
- [x] Image actions working

#### **Sub-task 2.3.4: Property Wizard Image Step** (2 hours)
**Location**: `c:\xampp\htdocs\stayfinder\src\components\wizard\PropertyImages.tsx`
**Reference**: Image upload step in property creation wizard

**Implementation Steps**:
1. **PropertyImages Wizard Step** with image upload integration
2. **Photo Requirements** with guidelines and tips display
3. **Upload Validation** with minimum photo requirements
4. **Progress Tracking** with photo count and completion status
5. **Professional Photography** with upgrade options and information

**Testing Checkpoint**:
- Test image upload step in property wizard
- Verify photo requirements and validation
- Test progress tracking and completion
- Check professional photography information

**Completion Criteria**:
- [x] Property wizard image step functional
- [x] Photo requirements display working
- [x] Upload validation implemented
- [x] Progress tracking complete
- [x] Professional photography info provided

#### **Sub-task 2.3.5: Integration and Testing** (1 hour)
**Location**: Integration with existing components and testing
**Reference**: Complete image system integration and validation

**Implementation Steps**:
1. **Wizard Integration** with property creation workflow
2. **PropertyCard Enhancement** with image gallery integration
3. **Host Dashboard Integration** with image management features
4. **Validation Updates** with image requirements in wizard
5. **Testing and Debugging** with comprehensive functionality testing

**Testing Checkpoint**:
- Test complete image workflow in property wizard
- Verify image display in property cards
- Test host dashboard image management
- Check all integrations and functionality

**Completion Criteria**:
- [x] Wizard integration complete
- [x] PropertyCard enhancement working
- [x] Host dashboard integration functional
- [x] Validation updates implemented
- [x] Testing and debugging complete

**Rollback**: Remove image upload components, image gallery, image manager, wizard image step

#### **Sub-task 2.4.1: Enhanced SearchFilters Component** (4 hours)
**Location**: `c:\xampp\htdocs\stayfinder\src\components\SearchFilters.tsx`
**Reference**: Advanced search filters with comprehensive filtering options

**Implementation Steps**:
1. **Enhanced Filter Interface** with expandable/collapsible design
2. **Price Range Filtering** with min/max inputs and quick presets
3. **Property Type Selection** with visual icons and multi-select
4. **Bedroom/Bathroom Counters** with increment/decrement controls
5. **Amenities Filtering** with icon-based checkboxes
6. **Rating Filter** with star-based minimum rating selection
7. **Instant Book Option** with checkbox for availability filtering
8. **Active Filters Display** with removal badges and count indicators

**Testing Checkpoint**:
- Test all filter types and combinations
- Verify expandable interface functionality
- Test active filter management and removal
- Check responsive design and mobile UX

**Completion Criteria**:
- [x] Enhanced filter interface functional
- [x] Price range filtering working
- [x] Property type multi-selection implemented
- [x] Bedroom/bathroom counters working
- [x] Amenities filtering functional
- [x] Rating filter implemented
- [x] Instant book option working
- [x] Active filters display complete

#### **Sub-task 2.4.2: DateRangePicker Component** (2 hours)
**Location**: `c:\xampp\htdocs\stayfinder\src\components\DateRangePicker.tsx`
**Reference**: Date range selection for availability filtering

**Implementation Steps**:
1. **Date Range Inputs** with check-in and check-out date selection
2. **Date Validation** with auto-adjustment and minimum date constraints
3. **Quick Date Options** with weekend, week, and tomorrow shortcuts
4. **Date Summary Display** with night count and formatted date range
5. **Flexible Dates Option** with ±3 days tolerance setting

**Testing Checkpoint**:
- Test date input validation and auto-adjustment
- Verify quick date selection options
- Test date summary and night count calculation
- Check flexible dates functionality

**Completion Criteria**:
- [x] Date range inputs functional
- [x] Date validation working
- [x] Quick date options implemented
- [x] Date summary display complete
- [x] Flexible dates option working

#### **Sub-task 2.4.3: Search Page Integration** (3 hours)
**Location**: `c:\xampp\htdocs\stayfinder\src\pages\Search.tsx`
**Reference**: Integration of enhanced filters with search functionality

**Implementation Steps**:
1. **Filter State Management** with comprehensive filter interface
2. **URL Parameter Synchronization** with filter state persistence
3. **Date Range Integration** with availability filtering
4. **Filter Sidebar Layout** with organized filter sections
5. **Active Filter Summary** with filter badges and clear functionality

**Testing Checkpoint**:
- Test filter state management and persistence
- Verify URL parameter synchronization
- Test date range integration with search
- Check filter sidebar layout and organization

**Completion Criteria**:
- [x] Filter state management working
- [x] URL parameter sync implemented
- [x] Date range integration complete
- [x] Filter sidebar layout functional
- [x] Active filter summary working

#### **Sub-task 2.4.4: Advanced Search Features** (1 hour)
**Location**: Enhanced search functionality and user experience
**Reference**: Advanced search features and optimizations

**Implementation Steps**:
1. **Real-time Search** with debounced filter updates
2. **Filter Persistence** with URL sharing and bookmarking
3. **Mobile Optimization** with responsive filter interface
4. **Search Performance** with optimized API calls and caching
5. **User Experience** with loading states and error handling

**Testing Checkpoint**:
- Test real-time search with filter changes
- Verify filter persistence and URL sharing
- Test mobile responsive design
- Check search performance and optimization

**Completion Criteria**:
- [x] Real-time search implemented
- [x] Filter persistence working
- [x] Mobile optimization complete
- [x] Search performance optimized
- [x] User experience enhanced

**Rollback**: Remove enhanced search filters, date range picker, search page enhancements

#### **Sub-task 2.5.1: PropertyMap Component** (4 hours)
**Location**: `c:\xampp\htdocs\stayfinder\src\components\PropertyMap.tsx`
**Reference**: Interactive map with property markers and controls

**Implementation Steps**:
1. **Interactive Map Interface** with property markers and price badges
2. **Property Markers** with hover states and selection highlighting
3. **Property Preview Cards** on marker interaction with details
4. **Map Controls** including zoom, reset, and fullscreen mode
5. **Mock Coordinates** for South African coastal properties
6. **Responsive Design** with mobile-optimized interactions

**Testing Checkpoint**:
- Test property marker interactions and hover states
- Verify map controls and fullscreen functionality
- Test property preview cards and selection
- Check responsive design and mobile optimization

**Completion Criteria**:
- [x] Interactive map interface functional
- [x] Property markers with price badges working
- [x] Property preview cards implemented
- [x] Map controls functional
- [x] Mock coordinates integrated
- [x] Responsive design complete

#### **Sub-task 2.5.2: MapSearch Component** (3 hours)
**Location**: `c:\xampp\htdocs\stayfinder\src\components\MapSearch.tsx`
**Reference**: Combined map and list search interface

**Implementation Steps**:
1. **Combined View Modes** with Map/List/Split view toggle
2. **Property Search** with text filtering and real-time updates
3. **Search Area Functionality** with map bounds detection
4. **Property Selection** with highlighting and details display
5. **Map Search Tips** with user guidance and instructions

**Testing Checkpoint**:
- Test view mode switching and transitions
- Verify property search and filtering
- Test search area functionality
- Check property selection and highlighting

**Completion Criteria**:
- [x] Combined view modes working
- [x] Property search functional
- [x] Search area functionality implemented
- [x] Property selection working
- [x] Map search tips provided

#### **Sub-task 2.5.3: NeighborhoodInfo Component** (3 hours)
**Location**: `c:\xampp\htdocs\stayfinder\src\components\NeighborhoodInfo.tsx`
**Reference**: Neighborhood information and points of interest

**Implementation Steps**:
1. **Neighborhood Overview** with descriptions and highlights
2. **Walk Score and Safety Ratings** with visual indicators
3. **Points of Interest** with categories and filtering
4. **Distance and Travel Time** information for each POI
5. **Mock Data** for major South African coastal towns

**Testing Checkpoint**:
- Test neighborhood information display
- Verify points of interest filtering
- Test distance and travel time calculations
- Check mock data for different locations

**Completion Criteria**:
- [x] Neighborhood overview functional
- [x] Walk score and safety ratings working
- [x] Points of interest implemented
- [x] Distance and travel time working
- [x] Mock data integrated

#### **Sub-task 2.5.4: Search Page Map Integration** (2 hours)
**Location**: `c:\xampp\htdocs\stayfinder\src\pages\Search.tsx`
**Reference**: Map view integration with search functionality

**Implementation Steps**:
1. **Map View Mode** added to grid/list view options
2. **MapSearch Integration** with property filtering
3. **NeighborhoodInfo Integration** for selected locations
4. **View Mode Switching** with seamless transitions
5. **Filter Integration** with map-based search

**Testing Checkpoint**:
- Test map view mode integration
- Verify MapSearch component integration
- Test neighborhood info display
- Check view mode switching functionality

**Completion Criteria**:
- [x] Map view mode integrated
- [x] MapSearch component working
- [x] NeighborhoodInfo integration complete
- [x] View mode switching functional
- [x] Filter integration working

**Rollback**: Remove map components, search page map integration, property detail map features

#### **Sub-task 2.6.1: Recommendations API Development** (3 hours)
**Location**: `c:\xampp\htdocs\stayfinder\api\recommendations.php`
**Reference**: Backend API for smart recommendations with multiple algorithms

**Implementation Steps**:
1. **Recommendations API Endpoint** with support for 4 recommendation types
2. **Similar Properties Algorithm** based on property type, location, price, guests, bedrooms
3. **Recently Viewed Tracking** with user property view logging
4. **Personalized Recommendations** using user preferences and activity history
5. **Trending Properties Algorithm** based on recent bookings and views
6. **Database Integration** with user_property_views table for tracking
7. **Authentication Integration** with token-based user identification

**Testing Checkpoint**:
- Test all recommendation API endpoints
- Verify similar properties algorithm accuracy
- Test user property view logging
- Check personalized recommendation logic

**Completion Criteria**:
- [x] Recommendations API endpoint functional
- [x] Similar properties algorithm working
- [x] Recently viewed tracking implemented
- [x] Personalized recommendations working
- [x] Trending properties algorithm functional
- [x] Database integration complete
- [x] Authentication integration working

#### **Sub-task 2.6.2: SmartRecommendations Component** (2 hours)
**Location**: `c:\xampp\htdocs\stayfinder\src\components\SmartRecommendations.tsx`
**Reference**: Reusable recommendation component with carousel interface

**Implementation Steps**:
1. **SmartRecommendations Component** with support for all recommendation types
2. **Interactive Carousel** with navigation controls and pagination
3. **Property Click Tracking** with automatic view logging
4. **Loading States** with skeleton loading and error handling
5. **Responsive Design** optimized for mobile and desktop

**Testing Checkpoint**:
- Test recommendation component rendering
- Verify carousel navigation functionality
- Test property click tracking
- Check responsive design across devices

**Completion Criteria**:
- [x] SmartRecommendations component functional
- [x] Interactive carousel working
- [x] Property click tracking implemented
- [x] Loading states complete
- [x] Responsive design working

#### **Sub-task 2.6.3: RecommendationsDashboard Component** (2 hours)
**Location**: `c:\xampp\htdocs\stayfinder\src\components\RecommendationsDashboard.tsx`
**Reference**: Comprehensive dashboard with all recommendation types

**Implementation Steps**:
1. **RecommendationsDashboard Component** with comprehensive layout
2. **Statistics Display** with recommendation counts and analytics
3. **Refresh Functionality** for real-time recommendation updates
4. **Call-to-Action Sections** for user engagement and navigation
5. **Integration Management** with routing and state management

**Testing Checkpoint**:
- Test dashboard component rendering
- Verify statistics display accuracy
- Test refresh functionality
- Check call-to-action integration

**Completion Criteria**:
- [x] RecommendationsDashboard component functional
- [x] Statistics display working
- [x] Refresh functionality implemented
- [x] Call-to-action sections complete
- [x] Integration management working

#### **Sub-task 2.6.4: Frontend Integration** (1 hour)
**Location**: Multiple frontend pages and components
**Reference**: Integration of recommendations across the application

**Implementation Steps**:
1. **Home Page Integration** with trending and personalized recommendations
2. **Property Detail Integration** with similar properties display
3. **User Dashboard Creation** with comprehensive recommendations view
4. **Navigation Routes** for user dashboard and property detail pages
5. **RecommendationsService** for API abstraction and management

**Testing Checkpoint**:
- Test home page recommendation display
- Verify property detail similar properties
- Test user dashboard functionality
- Check navigation and routing

**Completion Criteria**:
- [x] Home page integration complete
- [x] Property detail integration working
- [x] User dashboard functional
- [x] Navigation routes added
- [x] RecommendationsService implemented

**Rollback**: Remove recommendations API, smart recommendations components, dashboard, frontend integration

#### **Sub-task 2.7.1: User Profile API Development** (3 hours)
**Location**: `c:\xampp\htdocs\stayfinder\api\user-profile.php`
**Reference**: Backend API for comprehensive user profile management

**Implementation Steps**:
1. **User Profile API Endpoint** with GET, PUT, POST methods for profile management
2. **Profile Data Retrieval** with user statistics (bookings, reviews, ratings)
3. **Profile Update Functionality** with validation and data sanitization
4. **File Upload Handling** for profile pictures and ID documents
5. **Verification Workflows** for email, phone, and ID verification
6. **Database Schema Enhancement** with additional profile fields and JSON storage

**Testing Checkpoint**:
- Test user profile retrieval API
- Verify profile update functionality
- Test file upload endpoints
- Check verification workflows

**Completion Criteria**:
- [x] User Profile API endpoint functional
- [x] Profile data retrieval working
- [x] Profile update functionality implemented
- [x] File upload handling complete
- [x] Verification workflows functional
- [x] Database schema enhanced

#### **Sub-task 2.7.2: UserProfile Component** (3 hours)
**Location**: `c:\xampp\htdocs\stayfinder\src\components\UserProfile.tsx`
**Reference**: Comprehensive profile management component

**Implementation Steps**:
1. **UserProfile Component** with edit/view modes and form validation
2. **Profile Picture Upload** with image validation and preview
3. **Personal Information Management** with real-time validation
4. **Emergency Contact Information** for safety and security
5. **Profile Completion Tracking** with progress indicators
6. **ProfileVerification Sub-component** for verification workflows

**Testing Checkpoint**:
- Test profile component rendering
- Verify edit/save functionality
- Test profile picture upload
- Check form validation

**Completion Criteria**:
- [x] UserProfile component functional
- [x] Profile picture upload working
- [x] Personal information management complete
- [x] Emergency contact information implemented
- [x] Profile completion tracking functional
- [x] ProfileVerification component integrated

#### **Sub-task 2.7.3: UserSettings Component** (2 hours)
**Location**: `c:\xampp\htdocs\stayfinder\src\components\UserSettings.tsx`
**Reference**: User preferences and notification settings management

**Implementation Steps**:
1. **UserSettings Component** with comprehensive preference management
2. **General Preferences** for currency, language, timezone settings
3. **Communication Preferences** for marketing and notification controls
4. **Notification Settings** for email, SMS, push notifications
5. **Settings Persistence** with real-time updates and validation

**Testing Checkpoint**:
- Test settings component rendering
- Verify preference updates
- Test notification settings
- Check settings persistence

**Completion Criteria**:
- [x] UserSettings component functional
- [x] General preferences working
- [x] Communication preferences implemented
- [x] Notification settings complete
- [x] Settings persistence functional

#### **Sub-task 2.7.4: Enhanced User Dashboard Integration** (2 hours)
**Location**: `c:\xampp\htdocs\stayfinder\src\pages\UserDashboard.tsx`
**Reference**: Integration of profile components into user dashboard

**Implementation Steps**:
1. **Tabbed Interface** with Overview, Profile, Settings tabs
2. **Profile Management Integration** with UserProfile component
3. **Settings Management Integration** with UserSettings component
4. **Navigation Enhancement** with seamless tab switching
5. **UserProfileService** for API abstraction and management

**Testing Checkpoint**:
- Test tabbed interface functionality
- Verify profile integration
- Test settings integration
- Check navigation flow

**Completion Criteria**:
- [x] Tabbed interface implemented
- [x] Profile management integrated
- [x] Settings management integrated
- [x] Navigation enhancement complete
- [x] UserProfileService implemented

**Rollback**: Remove user profile API, profile components, settings component, dashboard integration

#### **Sub-task 2.8.1: Booking Management System** (2 hours)
**Location**: `c:\xampp\htdocs\stayfinder\src\services\bookingService.ts`, `c:\xampp\htdocs\stayfinder\src\components\BookingHistory.tsx`
**Reference**: Comprehensive booking history and trip management

**Implementation Steps**:
1. **BookingService** with API abstraction for booking operations
2. **BookingHistory Component** with booking display and management
3. **Booking Statistics** with trip counts and spending analytics
4. **Booking Details Modal** with host contact and trip information
5. **Booking Cancellation** with confirmation and status updates
6. **Mock Data Integration** for testing without backend dependencies

**Testing Checkpoint**:
- Test booking history display
- Verify upcoming trips filtering
- Test booking details modal
- Check cancellation workflow

**Completion Criteria**:
- [x] BookingService implemented
- [x] BookingHistory component functional
- [x] Booking statistics working
- [x] Booking details modal complete
- [x] Booking cancellation implemented
- [x] Mock data integration working

#### **Sub-task 2.8.2: Wishlist Management System** (2 hours)
**Location**: `c:\xampp\htdocs\stayfinder\src\services\wishlistService.ts`, `c:\xampp\htdocs\stayfinder\src\components\Wishlist.tsx`
**Reference**: Favorite properties management with detailed property cards

**Implementation Steps**:
1. **WishlistService** with API abstraction and local storage fallback
2. **Wishlist Component** with property cards and management actions
3. **Property Details Display** with amenities, ratings, and host information
4. **Wishlist Actions** for adding, removing, and viewing properties
5. **Availability Status** indicators and property navigation
6. **Local Storage Fallback** for offline wishlist management

**Testing Checkpoint**:
- Test wishlist display and property cards
- Verify add/remove functionality
- Test property navigation
- Check local storage fallback

**Completion Criteria**:
- [x] WishlistService implemented
- [x] Wishlist component functional
- [x] Property details display working
- [x] Wishlist actions complete
- [x] Availability status implemented
- [x] Local storage fallback working

#### **Sub-task 2.8.3: Review Management System** (2 hours)
**Location**: `c:\xampp\htdocs\stayfinder\src\components\ReviewManagement.tsx`
**Reference**: Complete review lifecycle management with editing capabilities

**Implementation Steps**:
1. **ReviewManagement Component** with review display and statistics
2. **Review Editing** with star rating component and text editing
3. **Review Actions** for editing, deleting, and managing reviews
4. **Host Responses** display and interaction management
5. **Review Statistics** with ratings analytics and response tracking
6. **Mock Data Integration** for comprehensive testing scenarios

**Testing Checkpoint**:
- Test review display and statistics
- Verify review editing functionality
- Test review actions (edit, delete)
- Check host responses display

**Completion Criteria**:
- [x] ReviewManagement component functional
- [x] Review editing implemented
- [x] Review actions working
- [x] Host responses display complete
- [x] Review statistics implemented
- [x] Mock data integration working

#### **Sub-task 2.8.4: Account Security System** (2 hours)
**Location**: `c:\xampp\htdocs\stayfinder\src\components\AccountSecurity.tsx`
**Reference**: Comprehensive account security with password, 2FA, and session management

**Implementation Steps**:
1. **AccountSecurity Component** with password change functionality
2. **Password Strength Indicator** with real-time validation
3. **Two-Factor Authentication** setup and management
4. **Session Management** with active sessions display and termination
5. **Security Status Indicators** with verification badges
6. **Security Validation** with password requirements and checks

**Testing Checkpoint**:
- Test password change functionality
- Verify password strength indicator
- Test 2FA setup workflow
- Check session management

**Completion Criteria**:
- [x] AccountSecurity component functional
- [x] Password strength indicator working
- [x] Two-factor authentication implemented
- [x] Session management complete
- [x] Security status indicators working
- [x] Security validation implemented

#### **Sub-task 2.9.1: Reviews API Development** (2 hours)
**Location**: `c:\xampp\htdocs\stayfinder\api\reviews.php`
**Reference**: Comprehensive reviews API with CRUD operations and rating statistics

**Implementation Steps**:
1. **Reviews API Endpoint** with GET, POST, PUT operations
2. **Property Reviews Retrieval** with pagination and sorting
3. **Rating Statistics Calculation** with breakdown and percentages
4. **Review Creation** with validation and booking verification
5. **Host Response System** for review replies
6. **Mock Data Integration** for testing without database dependencies

**Testing Checkpoint**:
- Test reviews API endpoint
- Verify rating statistics calculation
- Test review creation workflow
- Check host response functionality

**Completion Criteria**:
- [x] Reviews API endpoint functional
- [x] Property reviews retrieval working
- [x] Rating statistics implemented
- [x] Review creation complete
- [x] Host response system working
- [x] Mock data integration functional

#### **Sub-task 2.9.2: Reviews Service Layer** (1 hour)
**Location**: `c:\xampp\htdocs\stayfinder\src\services\reviewsService.ts`
**Reference**: Frontend service abstraction for review operations

**Implementation Steps**:
1. **ReviewsService Class** with API abstraction methods
2. **Review Data Fetching** with error handling and fallbacks
3. **Review Validation** with form validation helpers
4. **Date Formatting** and utility methods
5. **Mock Data Fallback** for testing scenarios
6. **TypeScript Interfaces** for type safety

**Testing Checkpoint**:
- Test service API calls
- Verify error handling
- Test validation methods
- Check mock data fallback

**Completion Criteria**:
- [x] ReviewsService implemented
- [x] API abstraction working
- [x] Error handling complete
- [x] Validation methods functional
- [x] Mock data fallback working
- [x] TypeScript interfaces defined

#### **Sub-task 2.9.3: PropertyReviews Component** (2 hours)
**Location**: `c:\xampp\htdocs\stayfinder\src\components\PropertyReviews.tsx`
**Reference**: Comprehensive review display with ratings and interactions

**Implementation Steps**:
1. **PropertyReviews Component** with review listing and statistics
2. **Rating Breakdown Display** with visual percentage bars
3. **Review Sorting** by date and rating with controls
4. **Review Expansion** for long comments with read more/less
5. **Host Response Display** with formatted responses
6. **Write Review Integration** with button and modal trigger

**Testing Checkpoint**:
- Test review display and formatting
- Verify rating breakdown visualization
- Test sorting functionality
- Check review expansion behavior

**Completion Criteria**:
- [x] PropertyReviews component functional
- [x] Rating breakdown display working
- [x] Review sorting implemented
- [x] Review expansion complete
- [x] Host response display working
- [x] Write review integration functional

#### **Sub-task 2.9.4: Write Review Component** (1 hour)
**Location**: `c:\xampp\htdocs\stayfinder\src\components\PropertyReviews.tsx` (WriteReview)
**Reference**: Review submission form with star rating and validation

**Implementation Steps**:
1. **WriteReview Component** with star rating input
2. **Form Validation** with error handling and feedback
3. **Character Count** for comment textarea
4. **Submit Workflow** with loading states
5. **Cancel Functionality** with form reset
6. **Integration** with reviews service

**Testing Checkpoint**:
- Test star rating input
- Verify form validation
- Test submit workflow
- Check cancel functionality

**Completion Criteria**:
- [x] WriteReview component functional
- [x] Star rating input working
- [x] Form validation implemented
- [x] Submit workflow complete
- [x] Cancel functionality working
- [x] Service integration functional

**Rollback**: Remove reviews API, reviews service, PropertyReviews component, WriteReview component, database schema

#### **Sub-task 3.1.1: Messages Database Schema** (1 hour)
**Location**: `c:\xampp\htdocs\stayfinder\database\migrations\create_messages_table.sql`
**Reference**: Comprehensive messaging database with conversations and messages

**Implementation Steps**:
1. **Conversations Table** with property and user relationships
2. **Messages Table** with message types and attachments
3. **Database Indexes** for performance optimization
4. **Foreign Key Constraints** for data integrity
5. **Sample Data** for testing scenarios
6. **Status Management** for conversation states

**Testing Checkpoint**:
- Test database schema creation
- Verify foreign key relationships
- Test sample data insertion
- Check index performance

**Completion Criteria**:
- [x] Conversations table created
- [x] Messages table implemented
- [x] Database indexes added
- [x] Foreign key constraints working
- [x] Sample data inserted
- [x] Status management functional

#### **Sub-task 3.1.2: Messages API Development** (2 hours)
**Location**: `c:\xampp\htdocs\stayfinder\api\messages.php`
**Reference**: RESTful API for messaging operations with authentication

**Implementation Steps**:
1. **Messages API Endpoint** with CRUD operations
2. **Conversation Management** with creation and retrieval
3. **Message Sending** with validation and security
4. **Read Status Tracking** with timestamp management
5. **Authentication Integration** with user verification
6. **Error Handling** with proper HTTP responses

**Testing Checkpoint**:
- Test API endpoints
- Verify authentication requirements
- Test message sending workflow
- Check read status functionality

**Completion Criteria**:
- [x] Messages API endpoint functional
- [x] Conversation management working
- [x] Message sending implemented
- [x] Read status tracking complete
- [x] Authentication integration working
- [x] Error handling implemented

#### **Sub-task 3.1.3: Messages Service Layer** (1 hour)
**Location**: `c:\xampp\htdocs\stayfinder\src\services\messagesService.ts`
**Reference**: Frontend service abstraction for messaging operations

**Implementation Steps**:
1. **MessagesService Class** with API abstraction
2. **Conversation Operations** with error handling
3. **Message Operations** with validation
4. **Helper Methods** for formatting and utilities
5. **Mock Data Integration** for testing
6. **TypeScript Interfaces** for type safety

**Testing Checkpoint**:
- Test service API calls
- Verify error handling
- Test helper methods
- Check mock data fallback

**Completion Criteria**:
- [x] MessagesService implemented
- [x] API abstraction working
- [x] Error handling complete
- [x] Helper methods functional
- [x] Mock data integration working
- [x] TypeScript interfaces defined

#### **Sub-task 3.1.4: Messaging Components** (2 hours)
**Location**: `c:\xampp\htdocs\stayfinder\src\components\MessagesList.tsx`
**Reference**: Complete messaging interface with conversations and chat

**Implementation Steps**:
1. **MessagesList Component** with conversation display
2. **ChatInterface Component** with real-time messaging
3. **Search Functionality** for conversations
4. **Message Formatting** with timestamps and status
5. **Responsive Design** for mobile and desktop
6. **Navigation Integration** with routing

**Testing Checkpoint**:
- Test conversation list display
- Verify chat interface functionality
- Test search capabilities
- Check responsive design

**Completion Criteria**:
- [x] MessagesList component functional
- [x] ChatInterface component working
- [x] Search functionality implemented
- [x] Message formatting complete
- [x] Responsive design working
- [x] Navigation integration functional

**Rollback**: Remove messages database, messages API, messages service, messaging components, navigation integration

#### **Sub-task 3.2.1: Advanced Search Database Schema** (1 hour)
**Location**: `c:\xampp\htdocs\stayfinder\database\migrations\create_saved_searches_table.sql`
**Reference**: Comprehensive database schema for saved searches, history, and suggestions

**Implementation Steps**:
1. **Saved Searches Table** with JSON criteria storage and notifications
2. **Search History Table** with user tracking and analytics
3. **Search Suggestions Table** with popularity and category tracking
4. **Database Indexes** for performance optimization
5. **Sample Data** for testing scenarios
6. **Foreign Key Relationships** with users table

**Testing Checkpoint**:
- Test database schema creation
- Verify JSON criteria storage
- Test foreign key relationships
- Check index performance

**Completion Criteria**:
- [x] Saved searches table created
- [x] Search history table implemented
- [x] Search suggestions table created
- [x] Database indexes added
- [x] Sample data inserted
- [x] Foreign key relationships working

#### **Sub-task 3.2.2: Advanced Search API Development** (2 hours)
**Location**: `c:\xampp\htdocs\stayfinder\api\advanced-search.php`
**Reference**: RESTful API for advanced search operations with authentication

**Implementation Steps**:
1. **Advanced Search API Endpoint** with CRUD operations
2. **Saved Searches Management** with creation, update, and deletion
3. **Search History Tracking** with automatic logging
4. **Search Suggestions** with autocomplete functionality
5. **Authentication Integration** with user verification
6. **Error Handling** with proper HTTP responses

**Testing Checkpoint**:
- Test API endpoints
- Verify authentication requirements
- Test saved searches CRUD operations
- Check search suggestions functionality

**Completion Criteria**:
- [x] Advanced Search API endpoint functional
- [x] Saved searches management working
- [x] Search history tracking implemented
- [x] Search suggestions complete
- [x] Authentication integration working
- [x] Error handling implemented

#### **Sub-task 3.2.3: Advanced Search Service Layer** (1 hour)
**Location**: `c:\xampp\htdocs\stayfinder\src\services\advancedSearchService.ts`
**Reference**: Frontend service abstraction for advanced search operations

**Implementation Steps**:
1. **AdvancedSearchService Class** with API abstraction
2. **Saved Searches Operations** with CRUD functionality
3. **Search History Management** with tracking and retrieval
4. **Search Suggestions** with autocomplete support
5. **Helper Methods** for formatting and validation
6. **Mock Data Integration** for testing scenarios

**Testing Checkpoint**:
- Test service API calls
- Verify error handling
- Test helper methods
- Check mock data fallback

**Completion Criteria**:
- [x] AdvancedSearchService implemented
- [x] API abstraction working
- [x] Error handling complete
- [x] Helper methods functional
- [x] Mock data integration working
- [x] TypeScript interfaces defined

#### **Sub-task 3.2.4: Advanced Search Components** (2 hours)
**Location**: `c:\xampp\htdocs\stayfinder\src\components\` (SavedSearches.tsx, SearchSuggestions.tsx, SearchHistory.tsx)
**Reference**: Complete advanced search interface with saved searches, suggestions, and history

**Implementation Steps**:
1. **SavedSearches Component** with management and execution
2. **SearchSuggestions Component** with autocomplete functionality
3. **SearchHistory Component** with recent searches display
4. **Enhanced Search Input** with suggestions integration
5. **Search Page Integration** with sidebar components
6. **Responsive Design** for mobile and desktop

**Testing Checkpoint**:
- Test saved searches management
- Verify search suggestions autocomplete
- Test search history functionality
- Check responsive design

**Completion Criteria**:
- [x] SavedSearches component functional
- [x] SearchSuggestions component working
- [x] SearchHistory component implemented
- [x] Enhanced search input complete
- [x] Search page integration working
- [x] Responsive design functional

**Rollback**: Remove advanced search database, API, service, components, search page enhancements

#### **Sub-task 3.3.1: Property Management API Development** (2 hours)
**Location**: `c:\xampp\htdocs\stayfinder\api\property-management.php`
**Reference**: Comprehensive API for property management with analytics and dashboard statistics

**Implementation Steps**:
1. **Property Management API Endpoint** with CRUD operations
2. **Host Properties Retrieval** with analytics and filtering
3. **Property Analytics** with revenue tracking and occupancy rates
4. **Dashboard Statistics** with aggregated data
5. **Property Status Management** with validation
6. **Authentication Integration** with user verification

**Testing Checkpoint**:
- Test API endpoints with authentication
- Verify property analytics calculations
- Test dashboard statistics aggregation
- Check property status updates

**Completion Criteria**:
- [x] Property Management API endpoint functional
- [x] Host properties retrieval working
- [x] Property analytics implemented
- [x] Dashboard statistics complete
- [x] Property status management working
- [x] Authentication integration functional

#### **Sub-task 3.3.2: Property Management Service Layer** (1 hour)
**Location**: `c:\xampp\htdocs\stayfinder\src\services\propertyManagementService.ts`
**Reference**: Frontend service abstraction for property management operations

**Implementation Steps**:
1. **PropertyManagementService Class** with API abstraction
2. **Host Properties Operations** with filtering and pagination
3. **Property Analytics** with multiple time periods
4. **Dashboard Statistics** with real-time data
5. **Helper Methods** for formatting and utilities
6. **Mock Data Integration** for testing scenarios

**Testing Checkpoint**:
- Test service API calls
- Verify error handling
- Test helper methods
- Check mock data fallback

**Completion Criteria**:
- [x] PropertyManagementService implemented
- [x] API abstraction working
- [x] Error handling complete
- [x] Helper methods functional
- [x] Mock data integration working
- [x] TypeScript interfaces defined

#### **Sub-task 3.3.3: Property Analytics Component** (2 hours)
**Location**: `c:\xampp\htdocs\stayfinder\src\components\PropertyAnalytics.tsx`
**Reference**: Comprehensive analytics dashboard for individual properties

**Implementation Steps**:
1. **PropertyAnalytics Component** with detailed metrics
2. **Revenue Tracking** with visual charts and trends
3. **Occupancy Analytics** with rate calculations
4. **Performance Metrics** with color-coded indicators
5. **Time Period Selection** with multiple ranges
6. **Interactive Visualizations** with hover effects

**Testing Checkpoint**:
- Test analytics data display
- Verify chart visualizations
- Test time period selection
- Check responsive design

**Completion Criteria**:
- [x] PropertyAnalytics component functional
- [x] Revenue tracking working
- [x] Occupancy analytics implemented
- [x] Performance metrics complete
- [x] Time period selection working
- [x] Interactive visualizations functional

#### **Sub-task 3.3.4: Enhanced Host Dashboard** (2 hours)
**Location**: `c:\xampp\htdocs\stayfinder\src\pages\HostDashboard.tsx`
**Reference**: Complete property management dashboard with real API integration

**Implementation Steps**:
1. **Real API Integration** replacing mock data
2. **Dashboard Statistics** with live data display
3. **Property Portfolio Management** with analytics integration
4. **Recent Bookings** with guest information
5. **Monthly Revenue Trends** with visualization
6. **Error Handling** and loading states

**Testing Checkpoint**:
- Test real API integration
- Verify dashboard statistics
- Test property management features
- Check error handling

**Completion Criteria**:
- [x] Real API integration working
- [x] Dashboard statistics functional
- [x] Property portfolio management complete
- [x] Recent bookings display working
- [x] Monthly revenue trends implemented
- [x] Error handling functional

**Rollback**: Remove property management API, service, analytics component, enhanced dashboard

#### **Sub-task 3.4.1: Payment Database Schema** (1 hour)
**Location**: `c:\xampp\htdocs\stayfinder\database\migrations\create_payments_table.sql`
**Reference**: Comprehensive payment database with PayFast integration and escrow system

**Implementation Steps**:
1. **Payments Table** with PayFast integration and status tracking
2. **Refunds Table** with refund processing and status management
3. **Payment Escrow Table** for secure transaction holding
4. **Host Payouts Table** for earnings management
5. **Database Indexes** for performance optimization
6. **Sample Data** for testing scenarios

**Testing Checkpoint**:
- Test database schema creation
- Verify foreign key relationships
- Test payment data insertion
- Check index performance

**Completion Criteria**:
- [x] Payments table created
- [x] Refunds table implemented
- [x] Payment escrow table created
- [x] Host payouts table implemented
- [x] Database indexes added
- [x] Sample data inserted

#### **Sub-task 3.4.2: PayFast Payment API Development** (2 hours)
**Location**: `c:\xampp\htdocs\stayfinder\api\payments.php`
**Reference**: Complete PayFast payment gateway integration with secure processing

**Implementation Steps**:
1. **Payment API Endpoint** with PayFast integration
2. **Payment Creation** with signature generation and validation
3. **Payment Notification Handling** (ITN) for status updates
4. **Refund Processing** with validation and policy enforcement
5. **Payment Status Management** with real-time updates
6. **Authentication Integration** with user verification

**Testing Checkpoint**:
- Test API endpoints with authentication
- Verify PayFast signature generation
- Test payment notification handling
- Check refund processing

**Completion Criteria**:
- [x] Payment API endpoint functional
- [x] PayFast integration working
- [x] Payment notification handling implemented
- [x] Refund processing complete
- [x] Payment status management working
- [x] Authentication integration functional

#### **Sub-task 3.4.3: Payment Service Layer** (1 hour)
**Location**: `c:\xampp\htdocs\stayfinder\src\services\paymentService.ts`
**Reference**: Frontend service abstraction for payment operations

**Implementation Steps**:
1. **PaymentService Class** with API abstraction
2. **Payment Creation** with PayFast redirection
3. **Payment Status Tracking** with real-time updates
4. **Refund Processing** with policy validation
5. **Helper Methods** for formatting and calculations
6. **Mock Data Integration** for testing scenarios

**Testing Checkpoint**:
- Test service API calls
- Verify payment redirection
- Test helper methods
- Check mock data fallback

**Completion Criteria**:
- [x] PaymentService implemented
- [x] API abstraction working
- [x] Payment redirection functional
- [x] Helper methods complete
- [x] Mock data integration working
- [x] TypeScript interfaces defined

#### **Sub-task 3.4.4: Payment Components and Pages** (2 hours)
**Location**: `c:\xampp\htdocs\stayfinder\src\components\` and `src\pages\`
**Reference**: Complete payment UI with forms, success, and cancel pages

**Implementation Steps**:
1. **PaymentForm Component** with booking review and payment options
2. **PaymentSuccess Page** with confirmation details
3. **PaymentCancel Page** with retry options
4. **Booking Integration** with payment step
5. **Payment Routes** in application routing
6. **Responsive Design** for mobile and desktop

**Testing Checkpoint**:
- Test payment form functionality
- Verify success and cancel pages
- Test booking integration
- Check responsive design

**Completion Criteria**:
- [x] PaymentForm component functional
- [x] PaymentSuccess page working
- [x] PaymentCancel page implemented
- [x] Booking integration complete
- [x] Payment routes functional
- [x] Responsive design working

**Rollback**: Remove payment database, API, service, components, pages, booking integration

---

## 📋 **PHASE 2: PRODUCTION DEPLOYMENT** (Priority: HIGH)
*Reference: DDD.md "Deployment & Infrastructure" section*

### 🚀 **Task 2.1: cPanel/WHM Server Setup**
*Total Time: 16 hours (8 sub-tasks)*

#### **Sub-task 2.1.1: cPanel Database Migration** (2 hours)
**Location**: cPanel MySQL Databases section
**Reference**: DDD.md "Database Design" section

**Implementation Steps**:
1. **Create Production Database in cPanel**:
   - Login to cPanel
   - Navigate to "MySQL Databases"
   - Create database: `username_stayfinder_prod`
   - Create database user with full privileges
   - Note connection details for .env file

2. **Export from XAMPP phpMyAdmin**:
   - Open phpMyAdmin (localhost/phpmyadmin)
   - Select `stayfinder_dev` database
   - Click "Export" tab
   - Choose "Custom" export method
   - Select all tables
   - Export as SQL file

3. **Import to cPanel phpMyAdmin**:
   - Login to cPanel phpMyAdmin
   - Select production database
   - Click "Import" tab
   - Upload exported SQL file
   - Execute import

**Testing Checkpoint**:
- Verify all tables created in production database
- Check sample data imported correctly
- Test database connection from cPanel

**Completion Criteria**:
- [ ] Production database created in cPanel
- [ ] All data migrated successfully
- [ ] Database user configured with proper permissions

#### **Sub-task 2.1.2: cPanel Email Setup** (2 hours)
**Location**: cPanel Email Accounts section
**Reference**: DDD.md "Email System Integration"

**Implementation Steps**:
1. **Create Email Accounts**:
   - `<EMAIL>` - System notifications
   - `<EMAIL>` - Customer support
   - `<EMAIL>` - Booking confirmations

2. **Configure SMTP Settings**:
   ```env
   # Production .env email configuration
   SMTP_HOST=mail.yourdomain.com
   SMTP_PORT=587
   SMTP_USER=<EMAIL>
   SMTP_PASS=secure_password_from_cpanel
   SMTP_SECURE=false
   SMTP_FROM_NAME=KZN StayFinder
   SMTP_FROM_EMAIL=<EMAIL>
   ```

3. **Test Email Functionality**:
   - Send test email from cPanel webmail
   - Verify SMTP authentication
   - Test email delivery to external addresses

**Testing Checkpoint**:
- Email accounts created and accessible
- SMTP settings configured correctly
- Test emails sent and received

**Completion Criteria**:
- [ ] Email accounts created in cPanel
- [ ] SMTP configuration tested
- [ ] Email delivery confirmed working

#### **Sub-task 2.1.3: File Manager Setup** (1 hour)
**Location**: cPanel File Manager
**Reference**: DDD.md "File Storage Architecture"

**Implementation Steps**:
1. **Create Directory Structure**:
   ```
   public_html/
   ├── stayfinder/
   │   ├── backend/
   │   ├── uploads/
   │   │   ├── properties/
   │   │   ├── users/
   │   │   └── temp/
   │   └── logs/
   ```

2. **Set Permissions**:
   - uploads/ directory: 755
   - logs/ directory: 755
   - backend/ directory: 644 for files, 755 for directories

3. **Configure Upload Limits**:
   - Check cPanel PHP settings
   - Adjust upload_max_filesize if needed
   - Set post_max_size appropriately

**Testing Checkpoint**:
- Directory structure created
- Permissions set correctly
- Upload functionality tested

**Completion Criteria**:
- [ ] Directory structure created in cPanel
- [ ] Proper permissions set
- [ ] Upload limits configured

---

## 📊 **TASK SUMMARY & IMPLEMENTATION ROADMAP**

### **🎯 Phase 1 Completion Metrics**
- **Backend API**: 8 endpoints implemented and tested
- **Authentication**: JWT-based system with cPanel email integration
- **Database**: MySQL with phpMyAdmin management
- **Frontend Integration**: React components connected to real backend
- **Total Development Time**: ~48 hours (6 weeks part-time)

### **📋 Remaining Tasks Overview**

#### **Phase 1 Remaining Tasks** (32 hours):
- [ ] **Task 1.3.3-1.3.10**: Complete Properties API (18 hours)
- [ ] **Task 1.4**: Bookings API Development (8 hours)
- [ ] **Task 1.5**: Reviews API Development (6 hours)

#### **Phase 2: Advanced Features** (40 hours):
- [ ] **Task 2.2**: Property Management Dashboard (12 hours)
- [ ] **Task 2.3**: Image Upload with cPanel File Manager (8 hours)
- [ ] **Task 2.4**: Advanced Search & Filters (10 hours)
- [ ] **Task 2.5**: User Dashboard & Profiles (10 hours)

#### **Phase 3: Production Features** (32 hours):
- [ ] **Task 3.1**: PayFast Payment Integration (12 hours)
- [ ] **Task 3.2**: cPanel Email Notifications (8 hours)
- [ ] **Task 3.3**: Security & SSL Setup (6 hours)
- [ ] **Task 3.4**: Performance Optimization (6 hours)

### **🔧 Infrastructure Alignment**

#### **Development Environment**:
- ✅ **XAMPP**: Local development with Apache/MySQL/PHP
- ✅ **phpMyAdmin**: Database management and testing
- ✅ **Node.js**: Backend API server on localhost:3001
- ✅ **React/Vite**: Frontend development server on localhost:5173

#### **Production Environment**:
- 🎯 **cPanel/WHM**: Dedicated server management
- 🎯 **cPanel MySQL**: Production database with phpMyAdmin
- 🎯 **cPanel Email**: SMTP and email account management
- 🎯 **cPanel File Manager**: Upload and file storage
- 🎯 **cPanel SSL**: Security certificate management

### **📈 Success Metrics**

#### **Technical KPIs**:
- **API Response Time**: <500ms for property searches
- **Database Performance**: <100ms for simple queries
- **Frontend Load Time**: <3s initial page load
- **Uptime**: 99.5% availability target

#### **Business KPIs**:
- **User Registration**: 100+ users in first month
- **Property Listings**: 50+ active properties
- **Booking Conversion**: 5%+ search-to-booking rate
- **User Satisfaction**: 4.0+ average rating

### **🚨 Critical Dependencies**

#### **Before Starting Development**:
1. ✅ **XAMPP MySQL** running with sample data
2. ✅ **Node.js** installed and npm available
3. ✅ **React frontend** running on localhost:5173
4. 🎯 **cPanel access** for production deployment
5. 🎯 **Domain name** configured with DNS

#### **Before Production Deployment**:
1. 🎯 **SSL Certificate** installed in cPanel
2. 🎯 **Email accounts** created and tested
3. 🎯 **Database backup** strategy implemented
4. 🎯 **File upload** limits configured
5. 🎯 **Security headers** configured in cPanel

### **🔄 Development Workflow**

#### **Daily Development Process**:
1. **Start XAMPP** (Apache + MySQL)
2. **Start Backend**: `cd backend && npm run dev`
3. **Start Frontend**: `npm run dev`
4. **Test in Browser**: http://localhost:5173
5. **Check API**: http://localhost:3001/api/health
6. **Monitor Database**: http://localhost/phpmyadmin

#### **Testing Protocol**:
- **Unit Tests**: After each sub-task completion
- **Integration Tests**: After each major task
- **User Acceptance Tests**: After each phase
- **Performance Tests**: Before production deployment

#### **Deployment Process**:
1. **Code Review**: Check all implementations
2. **Database Migration**: Export/import via phpMyAdmin
3. **File Upload**: Use cPanel File Manager
4. **Environment Configuration**: Update .env for production
5. **SSL Setup**: Configure HTTPS in cPanel
6. **Email Testing**: Verify SMTP functionality
7. **Go Live**: Update DNS and test all features

---

## 🎉 **NEXT IMMEDIATE ACTIONS**

### **To Continue Development** (Phase 2 Progress):
1. ✅ **Complete Task 1.3.3**: Properties Frontend Integration (COMPLETED)
2. ✅ **Complete Task 1.4**: Bookings API Development (COMPLETED)
3. ✅ **Complete Task 1.5**: Reviews API Development (COMPLETED)
4. ✅ **Complete Task 1.6**: Connect Authentication Frontend (COMPLETED)
5. ✅ **Complete Task 1.7**: Integrate Property Search (COMPLETED)
6. ✅ **Complete Task 1.8**: Connect Booking System (COMPLETED)
7. ✅ **Complete Task 1.9**: Implement Reviews Frontend (COMPLETED)
8. ✅ **Complete Task 2.1**: Host Dashboard (COMPLETED)
9. ✅ **Complete Task 2.2**: Property Creation Wizard (COMPLETED)
10. ✅ **Complete Task 2.3**: Property Image Upload (COMPLETED)
11. ✅ **Complete Task 2.4**: Enhanced Search Filters (COMPLETED)
12. ✅ **Complete Task 2.5**: Map Integration (COMPLETED)
13. ✅ **Complete Task 2.6**: Smart Recommendations (COMPLETED)
14. ✅ **Complete Task 2.7**: Complete User Profiles (COMPLETED)
15. ✅ **Complete Task 2.8**: User Dashboard (COMPLETED)
16. ✅ **Complete Task 2.9**: Reviews and Ratings System (COMPLETED)
17. ✅ **Complete Task 3.1**: Real-time Messaging System (COMPLETED)
18. ✅ **Complete Task 3.2**: Advanced Search and Filters (COMPLETED)
19. ✅ **Complete Task 3.3**: Property Management Dashboard (COMPLETED)
20. ✅ **Complete Task 3.4**: Payment Integration System (COMPLETED)
21. **Next Task 3.5**: Mobile App Development - **TODO**

### **Phase 1 Completion** (All Goals Achieved):
1. ✅ **Authentication System**: Fully functional (COMPLETED)
2. ✅ **Properties API**: Complete CRUD operations (COMPLETED)
3. ✅ **Property Search**: Real backend integration (COMPLETED)
4. ✅ **Basic Booking**: Create booking endpoint (COMPLETED)
5. ✅ **Reviews System**: Complete reviews API (COMPLETED)
6. ✅ **Frontend Integration**: Connect authentication flows (COMPLETED)
7. ✅ **Property Search Frontend**: Connect Hero search to real API (COMPLETED)
8. ✅ **Booking Flow**: Complete end-to-end booking process (COMPLETED)
9. ✅ **Reviews Frontend**: Complete reviews and ratings interface (COMPLETED)

🎉 **Phase 1 Complete**: Core StayFinder functionality is fully implemented and functional!

### **This Month's Objectives**:
1. 🎯 **Phase 1 Complete**: All core APIs functional
2. 🎯 **Production Deployment**: Live on dedicated server
3. 🎯 **Basic Testing**: 10+ real property listings
4. 🎯 **User Feedback**: Initial user testing and feedback

This comprehensive task breakdown provides a clear, actionable roadmap to transform your StayFinder from a beautiful prototype into a production-ready, Airbnb-level platform using your dedicated server infrastructure with cPanel/WHM management.

### 🔗 **Frontend-Backend Integration** - **COMPLETED**
- [x] **Task 1.6**: Connect Authentication - **COMPLETED**
  - Replace mock login/register with real API calls
  - Implement JWT token storage and management
  - Add authentication state management (Context/Redux)
  - Protect routes that require authentication
  - **Estimated Time**: 6 hours

- [x] **Task 1.7**: Integrate Property Search - **COMPLETED**
  - Connect Hero search to real API
  - Replace mock property data with API calls
  - Implement real-time search with filters
  - Add pagination for search results
  - **Estimated Time**: 8 hours

- [x] **Task 1.8**: Connect Booking System (COMPLETED)
  - Link calendar component to real availability API
  - Implement booking creation flow
  - Add booking confirmation and management
  - **Estimated Time**: 10 hours

---

## 📋 **PHASE 2: ADVANCED FEATURES** (Priority: HIGH) - **COMPLETED**

### 🏠 **Property Management System** - **COMPLETED**
- [x] **Task 2.1**: Host Dashboard (COMPLETED)
  - Create host dashboard layout
  - Property management interface
  - Booking management for hosts
  - Revenue analytics and reporting
  - **Estimated Time**: 12 hours

- [x] **Task 2.2**: Property Creation Wizard (COMPLETED)
  - Multi-step property creation form
  - Property details, amenities, rules
  - Pricing and availability settings
  - Property verification workflow
  - **Estimated Time**: 16 hours

- [x] **Task 2.3**: Image Upload System (COMPLETED)
  - Multiple image upload with drag-and-drop
  - Image optimization and resizing
  - Image gallery management
  - Primary image selection
  - **Estimated Time**: 8 hours

### 🔍 **Advanced Search & Discovery** - **COMPLETED**
- [x] **Task 2.4**: Enhanced Search Filters (COMPLETED)
  - Price range slider
  - Property type filters
  - Amenity checkboxes
  - Guest capacity selector
  - Date range picker
  - **Estimated Time**: 10 hours

- [x] **Task 2.5**: Map Integration (COMPLETED)
  - Google Maps/Mapbox integration
  - Property location markers
  - Map-based search
  - Neighborhood information
  - **Estimated Time**: 12 hours

- [x] **Task 2.6**: Smart Recommendations (COMPLETED)
  - "Similar properties" suggestions
  - Recently viewed properties
  - Personalized recommendations
  - Trending destinations
  - **Estimated Time**: 8 hours

### 👤 **User Profile & Account Management** - **COMPLETED**
- [x] **Task 2.7**: Complete User Profiles (COMPLETED)
  - Profile picture upload
  - Personal information management
  - Verification system (ID, phone, email)
  - User preferences and settings
  - **Estimated Time**: 10 hours

- [x] **Task 2.8**: User Dashboard (COMPLETED)
  - Booking history and upcoming trips
  - Favorite properties (wishlist)
  - Review management
  - Account settings and security
  - **Estimated Time**: 8 hours

---

## 📋 **PHASE 3: AIRBNB-LEVEL FEATURES** (Priority: MEDIUM) - **PARTIALLY COMPLETED**

### 💬 **Communication System** - **COMPLETED**
- [x] **Task 3.1**: Real-time Messaging - **COMPLETED**
  - Host-guest chat system
  - Real-time notifications
  - Message history and attachments
  - Automated booking messages
  - **Estimated Time**: 16 hours

- [ ] **Task 3.2**: Notification System
  - Email notifications (booking confirmations, reminders)
  - SMS notifications for critical updates
  - In-app notification center
  - Push notifications (PWA)
  - **Estimated Time**: 12 hours

### 💳 **Payment Integration** - **COMPLETED**
- [x] **Task 3.3**: PayFast Integration (South Africa) - **COMPLETED**
  - Secure payment processing
  - Multiple payment methods (cards, EFT, instant EFT)
  - Payment escrow system
  - Automatic refunds and payouts
  - **Estimated Time**: 20 hours

- [ ] **Task 3.4**: Pricing & Financial Management** - **TODO**
  - Dynamic pricing suggestions
  - Seasonal pricing rules
  - Discount and promotion system
  - Host payout management
  - Platform commission handling
  - **Estimated Time**: 16 hours

### 🛡️ **Trust & Safety** - **TODO**
- [ ] **Task 3.5**: Identity Verification - **TODO**
  - Government ID verification
  - Phone number verification
  - Email verification system
  - Background check integration
  - **Estimated Time**: 12 hours

- [ ] **Task 3.6**: Review & Rating System - **TODO****
  - Dual review system (guest reviews host, host reviews guest)
  - Review authenticity verification
  - Review response system
  - Rating analytics and insights
  - **Estimated Time**: 10 hours

- [ ] **Task 3.7**: Insurance & Protection - **TODO****
  - Host protection insurance
  - Guest travel insurance options
  - Damage reporting system
  - Dispute resolution process
  - **Estimated Time**: 16 hours

### 📱 **Mobile Experience** - **TODO**
- [ ] **Task 3.8**: Progressive Web App (PWA) - **TODO****
  - Service worker implementation
  - Offline functionality
  - App-like experience
  - Push notification support
  - **Estimated Time**: 12 hours

- [ ] **Task 3.9**: Mobile Optimization - **TODO****
  - Touch-friendly interface
  - Mobile-specific features
  - Camera integration for photos
  - Location services
  - **Estimated Time**: 8 hours

---

## 📋 **PHASE 4: PRODUCTION DEPLOYMENT** (Priority: MEDIUM) - **TODO**

### 🚀 **Infrastructure & DevOps** - **TODO**
- [ ] **Task 4.1**: Production Database Setup - **TODO**
  - mysQL production instance
  - Database optimization and indexing
  - Backup and recovery system
  - Database monitoring
  - **Estimated Time**: 8 hours

- [ ] **Task 4.2**: Server Deployment - **TODO****
  - upload to server
  - **Estimated Time**: 16 hours

- [ ] **Task 4.3**: CDN & File Storage - **TODO****
  - local server in assets folder
  - Image optimization pipeline
  - Backup storage system
  - **Estimated Time**: 8 hours

### 🔒 **Security & Compliance** - **TODO**
- [ ] **Task 4.4**: Security Hardening - **TODO****
  - SSL/TLS certificate setup
  - Security headers implementation
  - Rate limiting and DDoS protection
  - Vulnerability scanning
  - **Estimated Time**: 12 hours

- [ ] **Task 4.5**: Legal Compliance - **TODO****
  - POPIA compliance (South Africa)
  - GDPR compliance (international users)
  - Terms of Service and Privacy Policy
  - Cookie consent management
  - **Estimated Time**: 8 hours

### 📊 **Analytics & Monitoring** - **TODO**
- [ ] **Task 4.6**: Analytics Implementation - **TODO****
  - Google Analytics 4 setup
  - Custom event tracking
  - Conversion funnel analysis
  - User behavior insights
  - **Estimated Time**: 6 hours

- [ ] **Task 4.7**: Performance Monitoring - **TODO****
  - Application performance monitoring (APM)
  - Error tracking and logging
  - Uptime monitoring
  - Performance optimization
  - **Estimated Time**: 8 hours

---

## 📋 **PHASE 5: ADVANCED BUSINESS FEATURES** (Priority: LOW) - **TODO**

### 🏢 **Business Intelligence** - **TODO**
- [ ] **Task 5.1**: Admin Dashboard - **TODO****
  - Platform analytics and KPIs
  - User management system
  - Property moderation tools
  - Financial reporting
  - **Estimated Time**: 20 hours

- [ ] **Task 5.2**: Marketing Tools - **TODO****
  - Email marketing integration
  - Referral program system
  - Promotional campaigns
  - SEO optimization
  - **Estimated Time**: 16 hours

### 🌍 **Scalability Features** - **TODO**
- [ ] **Task 5.3**: Multi-language Support - **TODO****
  - English, Afrikaans, Zulu support
  - Currency conversion
  - Localized content
  - Regional customization
  - **Estimated Time**: 12 hours

- [ ] **Task 5.4**: API for Third-party Integration - **TODO****
  - Public API for partners
  - Webhook system
  - Integration with tourism boards
  - Channel manager compatibility
  - **Estimated Time**: 16 hours

---

## 📊 **SUMMARY**

### **Total Estimated Development Time**
- **Phase 1 (Core Backend)**: ~52 hours
- **Phase 2 (Advanced Features)**: ~84 hours  
- **Phase 3 (Airbnb-Level)**: ~122 hours
- **Phase 4 (Production)**: ~66 hours
- **Phase 5 (Business Features)**: ~64 hours

**Total: ~388 hours (~10 weeks full-time development)**

### **Priority Order for MVP Launch**
1. **Phase 1**: Essential for basic functionality
2. **Phase 2**: Required for competitive features  
3. **Phase 3**: Airbnb-level user experience
4. **Phase 4**: Production readiness
5. **Phase 5**: Business growth features

### **Success Metrics**
- **Technical**: 99.9% uptime, <2s page load time
- **Business**: 1000+ properties, 10,000+ users, R1M+ bookings
- **User Experience**: 4.5+ star rating, <5% bounce rate

This roadmap will transform your StayFinder into a world-class holiday rental platform that can compete with Airbnb in the South African market!
