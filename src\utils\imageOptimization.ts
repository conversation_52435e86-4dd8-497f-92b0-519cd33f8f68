// Image optimization utilities for StayFinder
import { toast } from '@/components/ui/use-toast';

export interface ImageOptimizationOptions {
  maxWidth?: number;
  maxHeight?: number;
  quality?: number;
  format?: 'webp' | 'jpeg' | 'png';
  enableProgressive?: boolean;
}

export interface OptimizedImage {
  blob: Blob;
  url: string;
  originalSize: number;
  optimizedSize: number;
  compressionRatio: number;
  width: number;
  height: number;
  format: string;
}

/**
 * Optimize an image file for web usage
 */
export const optimizeImage = async (
  file: File,
  options: ImageOptimizationOptions = {}
): Promise<OptimizedImage> => {
  const {
    maxWidth = 1920,
    maxHeight = 1080,
    quality = 0.85,
    format = 'webp',
    enableProgressive = true
  } = options;

  return new Promise((resolve, reject) => {
    const img = new Image();
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');

    if (!ctx) {
      reject(new Error('Canvas context not available'));
      return;
    }

    img.onload = () => {
      // Calculate new dimensions while maintaining aspect ratio
      let { width, height } = img;
      
      if (width > maxWidth) {
        height = (height * maxWidth) / width;
        width = maxWidth;
      }
      
      if (height > maxHeight) {
        width = (width * maxHeight) / height;
        height = maxHeight;
      }

      // Set canvas dimensions
      canvas.width = width;
      canvas.height = height;

      // Enable image smoothing for better quality
      ctx.imageSmoothingEnabled = true;
      ctx.imageSmoothingQuality = 'high';

      // Draw and compress image
      ctx.drawImage(img, 0, 0, width, height);

      canvas.toBlob(
        (blob) => {
          if (!blob) {
            reject(new Error('Failed to optimize image'));
            return;
          }

          const optimizedSize = blob.size;
          const originalSize = file.size;
          const compressionRatio = ((originalSize - optimizedSize) / originalSize) * 100;

          resolve({
            blob,
            url: URL.createObjectURL(blob),
            originalSize,
            optimizedSize,
            compressionRatio,
            width,
            height,
            format
          });
        },
        `image/${format}`,
        quality
      );
    };

    img.onerror = () => {
      reject(new Error('Failed to load image'));
    };

    img.src = URL.createObjectURL(file);
  });
};

/**
 * Generate responsive image sizes
 */
export const generateResponsiveImages = async (
  file: File,
  sizes: number[] = [320, 640, 960, 1280, 1920]
): Promise<OptimizedImage[]> => {
  const images: OptimizedImage[] = [];

  for (const size of sizes) {
    try {
      const optimized = await optimizeImage(file, {
        maxWidth: size,
        maxHeight: size,
        quality: 0.85,
        format: 'webp'
      });
      images.push(optimized);
    } catch (error) {
      console.warn(`Failed to generate ${size}px image:`, error);
    }
  }

  return images;
};

/**
 * Convert image to WebP format
 */
export const convertToWebP = async (file: File, quality: number = 0.85): Promise<OptimizedImage> => {
  return optimizeImage(file, { format: 'webp', quality });
};

/**
 * Batch optimize multiple images
 */
export const batchOptimizeImages = async (
  files: File[],
  options: ImageOptimizationOptions = {},
  onProgress?: (completed: number, total: number) => void
): Promise<OptimizedImage[]> => {
  const results: OptimizedImage[] = [];
  
  for (let i = 0; i < files.length; i++) {
    try {
      const optimized = await optimizeImage(files[i], options);
      results.push(optimized);
      onProgress?.(i + 1, files.length);
    } catch (error) {
      console.error(`Failed to optimize image ${files[i].name}:`, error);
    }
  }

  return results;
};

/**
 * Get image metadata
 */
export const getImageMetadata = (file: File): Promise<{
  width: number;
  height: number;
  aspectRatio: number;
  size: number;
  type: string;
}> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    
    img.onload = () => {
      resolve({
        width: img.naturalWidth,
        height: img.naturalHeight,
        aspectRatio: img.naturalWidth / img.naturalHeight,
        size: file.size,
        type: file.type
      });
    };

    img.onerror = () => {
      reject(new Error('Failed to load image metadata'));
    };

    img.src = URL.createObjectURL(file);
  });
};

/**
 * Validate image file
 */
export const validateImage = async (file: File): Promise<{
  isValid: boolean;
  errors: string[];
  metadata?: any;
}> => {
  const errors: string[] = [];
  
  // Check file type
  if (!file.type.startsWith('image/')) {
    errors.push('File must be an image');
  }

  // Check file size (max 10MB)
  const maxSize = 10 * 1024 * 1024;
  if (file.size > maxSize) {
    errors.push('Image must be smaller than 10MB');
  }

  // Check image dimensions
  try {
    const metadata = await getImageMetadata(file);
    
    if (metadata.width < 100 || metadata.height < 100) {
      errors.push('Image must be at least 100x100 pixels');
    }

    if (metadata.width > 8000 || metadata.height > 8000) {
      errors.push('Image must be smaller than 8000x8000 pixels');
    }

    return {
      isValid: errors.length === 0,
      errors,
      metadata
    };
  } catch (error) {
    errors.push('Invalid image file');
    return {
      isValid: false,
      errors
    };
  }
};

/**
 * Create image thumbnail
 */
export const createThumbnail = async (
  file: File,
  size: number = 150
): Promise<OptimizedImage> => {
  return optimizeImage(file, {
    maxWidth: size,
    maxHeight: size,
    quality: 0.8,
    format: 'webp'
  });
};

/**
 * Lazy load image with intersection observer
 */
export const createLazyImageLoader = () => {
  const imageObserver = new IntersectionObserver((entries, observer) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const img = entry.target as HTMLImageElement;
        const src = img.dataset.src;
        
        if (src) {
          img.src = src;
          img.classList.remove('lazy');
          observer.unobserve(img);
        }
      }
    });
  });

  return {
    observe: (img: HTMLImageElement) => imageObserver.observe(img),
    unobserve: (img: HTMLImageElement) => imageObserver.unobserve(img),
    disconnect: () => imageObserver.disconnect()
  };
};

/**
 * Preload critical images
 */
export const preloadImages = (urls: string[]): Promise<void[]> => {
  return Promise.all(
    urls.map(url => 
      new Promise<void>((resolve, reject) => {
        const img = new Image();
        img.onload = () => resolve();
        img.onerror = () => reject(new Error(`Failed to preload ${url}`));
        img.src = url;
      })
    )
  );
};

/**
 * Format file size for display
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * Calculate compression savings
 */
export const calculateSavings = (originalSize: number, optimizedSize: number) => {
  const savings = originalSize - optimizedSize;
  const percentage = ((savings / originalSize) * 100).toFixed(1);
  
  return {
    bytes: savings,
    percentage: parseFloat(percentage),
    formatted: `${formatFileSize(savings)} (${percentage}%)`
  };
};

/**
 * Image optimization service worker registration
 */
export const registerImageOptimizationWorker = async () => {
  if ('serviceWorker' in navigator) {
    try {
      const registration = await navigator.serviceWorker.register('/sw-image-optimization.js');
      console.log('Image optimization service worker registered:', registration);
      return registration;
    } catch (error) {
      console.error('Failed to register image optimization service worker:', error);
    }
  }
};

/**
 * Progressive image loading component helper
 */
export const createProgressiveImageLoader = (
  lowQualityUrl: string,
  highQualityUrl: string,
  onLoad?: () => void
) => {
  const container = document.createElement('div');
  container.className = 'progressive-image-container';
  
  const lowQualityImg = document.createElement('img');
  lowQualityImg.src = lowQualityUrl;
  lowQualityImg.className = 'progressive-image-low';
  
  const highQualityImg = document.createElement('img');
  highQualityImg.className = 'progressive-image-high';
  
  lowQualityImg.onload = () => {
    container.appendChild(lowQualityImg);
    
    highQualityImg.onload = () => {
      container.appendChild(highQualityImg);
      setTimeout(() => {
        lowQualityImg.remove();
        onLoad?.();
      }, 300);
    };
    
    highQualityImg.src = highQualityUrl;
  };
  
  return container;
};
