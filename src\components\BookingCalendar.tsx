
import { useState, useEffect } from 'react';
import { Calendar } from '@/components/ui/calendar';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { format, isAfter, isBefore, isEqual, parseISO } from 'date-fns';
import { useBooking } from '../contexts/BookingContext';
import { useAuth } from '../contexts/AuthContext';
import { PaymentForm } from './PaymentForm';
import { Loader2, Users, DollarSign, Calendar as CalendarIcon, AlertCircle } from 'lucide-react';

interface BookingCalendarProps {
  propertyId: string;
  propertyTitle?: string;
  propertyPrice?: number;
  maxGuests?: number;
  cleaningFee?: number;
  bookedDates?: string[];
  pendingBookings?: Array<{
    checkIn: string;
    checkOut: string;
    expiresAt: string;
  }>;
  onBookingComplete?: (booking: any) => void;
  onClose?: () => void;
}

export const BookingCalendar = ({
  propertyId,
  propertyTitle,
  propertyPrice,
  maxGuests = 1,
  cleaningFee = 0,
  bookedDates = [],
  pendingBookings = [],
  onBookingComplete,
  onClose
}: BookingCalendarProps) => {
  const [checkInDate, setCheckInDate] = useState<Date>();
  const [checkOutDate, setCheckOutDate] = useState<Date>();
  const [guestCount, setGuestCount] = useState(1);
  const [specialRequests, setSpecialRequests] = useState('');
  const [costBreakdown, setCostBreakdown] = useState<any>(null);
  const [step, setStep] = useState<'dates' | 'details' | 'payment' | 'confirmation'>('dates');
  const [currentBooking, setCurrentBooking] = useState<any>(null);

  const { isAuthenticated } = useAuth();
  const {
    createBooking,
    calculateCost,
    checkAvailability,
    loading,
    submitting,
    error,
    clearError
  } = useBooking();

  // Calculate cost when dates change
  useEffect(() => {
    if (checkInDate && checkOutDate) {
      const fetchCost = async () => {
        try {
          const cost = await calculateCost(
            propertyId,
            format(checkInDate, 'yyyy-MM-dd'),
            format(checkOutDate, 'yyyy-MM-dd')
          );
          setCostBreakdown(cost);
        } catch (err) {
          console.error('Failed to calculate cost:', err);
        }
      };
      fetchCost();
    } else {
      setCostBreakdown(null);
    }
  }, [checkInDate, checkOutDate, propertyId, calculateCost]);

  const isDateBooked = (date: Date) => {
    const dateStr = format(date, 'yyyy-MM-dd');
    return bookedDates.includes(dateStr);
  };

  const isDatePending = (date: Date) => {
    const currentTime = new Date();
    return pendingBookings.some(booking => {
      const checkIn = parseISO(booking.checkIn);
      const checkOut = parseISO(booking.checkOut);
      const expiresAt = parseISO(booking.expiresAt);

      // Only consider pending if not expired
      if (isAfter(currentTime, expiresAt)) return false;

      return (isAfter(date, checkIn) || isEqual(date, checkIn)) &&
             (isBefore(date, checkOut) || isEqual(date, checkOut));
    });
  };

  const getDateStyle = (date: Date) => {
    if (isDateBooked(date)) {
      return 'bg-red-500 text-white hover:bg-red-600';
    }
    if (isDatePending(date)) {
      return 'bg-yellow-500 text-white hover:bg-yellow-600';
    }
    return 'bg-ocean-blue-100 text-black hover:bg-ocean-blue-200';
  };

  const isDateDisabled = (date: Date) => {
    return isBefore(date, new Date()) || isDateBooked(date) || isDatePending(date);
  };

  const handleCheckInSelect = (date: Date | undefined) => {
    setCheckInDate(date);
    setCheckOutDate(undefined); // Reset checkout when checkin changes
  };

  const handleCheckOutSelect = (date: Date | undefined) => {
    if (date && checkInDate && isAfter(date, checkInDate)) {
      setCheckOutDate(date);
    }
  };

  const handleDateSelection = () => {
    if (checkInDate && checkOutDate && costBreakdown) {
      setStep('details');
    }
  };

  const handleBookingSubmit = async () => {
    if (!isAuthenticated) {
      alert('Please log in to make a booking');
      return;
    }

    if (!checkInDate || !checkOutDate) {
      alert('Please select check-in and check-out dates');
      return;
    }

    if (guestCount > maxGuests) {
      alert(`Maximum ${maxGuests} guests allowed for this property`);
      return;
    }

    try {
      clearError();

      // Check availability one more time before booking
      const available = await checkAvailability(
        propertyId,
        format(checkInDate, 'yyyy-MM-dd'),
        format(checkOutDate, 'yyyy-MM-dd')
      );

      if (!available) {
        alert('Sorry, this property is no longer available for the selected dates');
        return;
      }

      const booking = await createBooking({
        propertyId,
        checkInDate: format(checkInDate, 'yyyy-MM-dd'),
        checkOutDate: format(checkOutDate, 'yyyy-MM-dd'),
        guestCount,
        specialRequests: specialRequests.trim() || undefined
      });

      setCurrentBooking(booking);
      setStep('payment');
    } catch (err: any) {
      console.error('Booking failed:', err);
      alert(err.message || 'Failed to create booking. Please try again.');
    }
  };

  const handleBackToSearch = () => {
    setStep('dates');
    setCheckInDate(undefined);
    setCheckOutDate(undefined);
    setGuestCount(1);
    setSpecialRequests('');
    setCostBreakdown(null);
    clearError();
  };

  // Step 1: Date Selection
  if (step === 'dates') {
    return (
      <Card className="w-full max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CalendarIcon className="h-5 w-5" />
            Select Your Dates
          </CardTitle>
          {propertyTitle && (
            <p className="text-gray-600">{propertyTitle}</p>
          )}
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Error Display */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-red-600" />
              <p className="text-red-700">{error}</p>
            </div>
          )}

          {/* Legend */}
          <div className="flex flex-wrap gap-2">
            <Badge className="bg-red-500 text-white">Booked</Badge>
            <Badge className="bg-yellow-500 text-white">Pending (24h)</Badge>
            <Badge className="bg-ocean-blue-100 text-black">Available</Badge>
          </div>

          {/* Calendar Grid */}
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold mb-3 text-gray-900">Check-in Date</h4>
              <Calendar
                mode="single"
                selected={checkInDate}
                onSelect={handleCheckInSelect}
                disabled={isDateDisabled}
                className="rounded-md border"
                modifiers={{
                  booked: (date) => isDateBooked(date),
                  pending: (date) => isDatePending(date),
                  available: (date) => !isDateBooked(date) && !isDatePending(date) && !isBefore(date, new Date())
                }}
                modifiersStyles={{
                  booked: { backgroundColor: '#ef4444', color: 'white' },
                  pending: { backgroundColor: '#eab308', color: 'white' },
                  available: { backgroundColor: '#dbeafe', color: 'black' }
                }}
              />
            </div>

            <div>
              <h4 className="font-semibold mb-3 text-gray-900">Check-out Date</h4>
              <Calendar
                mode="single"
                selected={checkOutDate}
                onSelect={handleCheckOutSelect}
                disabled={(date) => {
                  if (!checkInDate) return true;
                  return isDateDisabled(date) || !isAfter(date, checkInDate);
                }}
                className="rounded-md border"
                modifiers={{
                  booked: (date) => isDateBooked(date),
                  pending: (date) => isDatePending(date),
                  available: (date) => checkInDate && isAfter(date, checkInDate) && !isDateBooked(date) && !isDatePending(date)
                }}
                modifiersStyles={{
                  booked: { backgroundColor: '#ef4444', color: 'white' },
                  pending: { backgroundColor: '#eab308', color: 'white' },
                  available: { backgroundColor: '#dbeafe', color: 'black' }
                }}
              />
            </div>
          </div>

          {/* Date Summary and Cost */}
          {checkInDate && checkOutDate && (
            <div className="bg-ocean-blue-50 p-6 rounded-lg space-y-4">
              <div className="flex justify-between items-center">
                <div>
                  <p className="font-medium text-gray-900">
                    {format(checkInDate, 'MMM dd, yyyy')} - {format(checkOutDate, 'MMM dd, yyyy')}
                  </p>
                  {costBreakdown && (
                    <p className="text-sm text-gray-600">
                      {costBreakdown.nights} night{costBreakdown.nights > 1 ? 's' : ''}
                    </p>
                  )}
                </div>
                {costBreakdown && (
                  <div className="text-right">
                    <p className="text-2xl font-bold text-ocean-blue-600">
                      R{costBreakdown.totalAmount.toLocaleString()}
                    </p>
                    <p className="text-sm text-gray-600">Total</p>
                  </div>
                )}
              </div>

              {loading && (
                <div className="flex items-center justify-center py-4">
                  <Loader2 className="h-5 w-5 animate-spin mr-2" />
                  <span>Calculating cost...</span>
                </div>
              )}

              {costBreakdown && (
                <div className="space-y-2 text-sm text-gray-600">
                  <div className="flex justify-between">
                    <span>R{costBreakdown.pricePerNight.toLocaleString()} × {costBreakdown.nights} nights</span>
                    <span>R{costBreakdown.accommodationCost.toLocaleString()}</span>
                  </div>
                  {costBreakdown.cleaningFee > 0 && (
                    <div className="flex justify-between">
                      <span>Cleaning fee</span>
                      <span>R{costBreakdown.cleaningFee.toLocaleString()}</span>
                    </div>
                  )}
                  <div className="border-t pt-2 flex justify-between font-medium text-gray-900">
                    <span>Total</span>
                    <span>R{costBreakdown.totalAmount.toLocaleString()}</span>
                  </div>
                </div>
              )}

              <Button
                onClick={handleDateSelection}
                disabled={!costBreakdown || loading}
                className="w-full bg-gradient-to-r from-sea-green-500 to-ocean-blue-500 hover:from-sea-green-600 hover:to-ocean-blue-600 text-white font-semibold"
              >
                Continue to Booking Details
              </Button>
            </div>
          )}

          {/* Close Button */}
          {onClose && (
            <div className="flex justify-center">
              <Button variant="outline" onClick={onClose}>
                Close
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    );
  }

  // Step 2: Booking Details
  if (step === 'details') {
    return (
      <Card className="w-full max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Booking Details
          </CardTitle>
          <p className="text-gray-600">
            {format(checkInDate!, 'MMM dd')} - {format(checkOutDate!, 'MMM dd, yyyy')}
          </p>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Error Display */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-red-600" />
              <p className="text-red-700">{error}</p>
            </div>
          )}

          {/* Guest Count */}
          <div className="space-y-2">
            <Label htmlFor="guests" className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              Number of Guests
            </Label>
            <select
              id="guests"
              value={guestCount}
              onChange={(e) => setGuestCount(parseInt(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-sea-green-500"
            >
              {Array.from({ length: maxGuests }, (_, i) => i + 1).map((num) => (
                <option key={num} value={num}>
                  {num} {num === 1 ? 'guest' : 'guests'}
                </option>
              ))}
            </select>
            <p className="text-sm text-gray-600">
              Maximum {maxGuests} guests allowed
            </p>
          </div>

          {/* Special Requests */}
          <div className="space-y-2">
            <Label htmlFor="requests">Special Requests (Optional)</Label>
            <Textarea
              id="requests"
              placeholder="Any special requests or requirements..."
              value={specialRequests}
              onChange={(e) => setSpecialRequests(e.target.value)}
              rows={3}
              className="resize-none"
            />
            <p className="text-sm text-gray-600">
              Let the host know about any special requirements
            </p>
          </div>

          {/* Cost Summary */}
          {costBreakdown && (
            <div className="bg-gray-50 p-4 rounded-lg space-y-3">
              <h4 className="font-medium text-gray-900 flex items-center gap-2">
                <DollarSign className="h-4 w-4" />
                Cost Breakdown
              </h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>R{costBreakdown.pricePerNight.toLocaleString()} × {costBreakdown.nights} nights</span>
                  <span>R{costBreakdown.accommodationCost.toLocaleString()}</span>
                </div>
                {costBreakdown.cleaningFee > 0 && (
                  <div className="flex justify-between">
                    <span>Cleaning fee</span>
                    <span>R{costBreakdown.cleaningFee.toLocaleString()}</span>
                  </div>
                )}
                <div className="border-t pt-2 flex justify-between font-medium text-lg">
                  <span>Total</span>
                  <span>R{costBreakdown.totalAmount.toLocaleString()}</span>
                </div>
              </div>
            </div>
          )}

          {/* Authentication Check */}
          {!isAuthenticated && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <p className="text-yellow-800">
                Please log in to complete your booking.
              </p>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={handleBackToSearch}
              className="flex-1"
            >
              Back to Dates
            </Button>
            <Button
              onClick={handleBookingSubmit}
              disabled={submitting || !isAuthenticated}
              className="flex-1 bg-gradient-to-r from-sea-green-500 to-ocean-blue-500 hover:from-sea-green-600 hover:to-ocean-blue-600 text-white font-semibold"
            >
              {submitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating Booking...
                </>
              ) : (
                'Confirm Booking'
              )}
            </Button>
          </div>

          <p className="text-xs text-gray-600 text-center">
            * Payment required within 24 hours to confirm booking
          </p>
        </CardContent>
      </Card>
    );
  }

  // Step 3: Payment
  if (step === 'payment' && currentBooking) {
    const bookingDetails = {
      property_title: propertyTitle || 'Property Booking',
      check_in_date: format(checkInDate!, 'yyyy-MM-dd'),
      check_out_date: format(checkOutDate!, 'yyyy-MM-dd'),
      guest_count: guestCount,
      total_amount: costBreakdown?.totalAmount || 0,
      cleaning_fee: costBreakdown?.cleaningFee || 0,
      property_location: 'Location TBD' // This should come from property data
    };

    return (
      <div className="w-full max-w-2xl mx-auto">
        <PaymentForm
          bookingId={currentBooking.id}
          bookingDetails={bookingDetails}
          onPaymentSuccess={(paymentData) => {
            setStep('confirmation');
            if (onBookingComplete) {
              onBookingComplete({ ...currentBooking, payment: paymentData });
            }
          }}
          onPaymentCancel={() => {
            setStep('details');
          }}
        />
      </div>
    );
  }

  // Step 4: Confirmation
  if (step === 'confirmation') {
    return (
      <Card className="w-full max-w-2xl mx-auto">
        <CardHeader className="text-center">
          <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
            <CalendarIcon className="h-8 w-8 text-green-600" />
          </div>
          <CardTitle className="text-2xl text-green-600">Booking Confirmed!</CardTitle>
          <p className="text-gray-600">
            Your booking request has been submitted successfully
          </p>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Booking Summary */}
          <div className="bg-green-50 p-6 rounded-lg space-y-4">
            <h4 className="font-medium text-gray-900">Booking Summary</h4>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Property:</span>
                <span className="font-medium">{propertyTitle}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Dates:</span>
                <span className="font-medium">
                  {format(checkInDate!, 'MMM dd')} - {format(checkOutDate!, 'MMM dd, yyyy')}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Guests:</span>
                <span className="font-medium">{guestCount}</span>
              </div>
              {costBreakdown && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Total Amount:</span>
                  <span className="font-medium text-lg">R{costBreakdown.totalAmount.toLocaleString()}</span>
                </div>
              )}
            </div>
          </div>

          {/* Next Steps */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-2">Next Steps</h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• You will receive a confirmation email shortly</li>
              <li>• Payment is required within 24 hours</li>
              <li>• The host will review and confirm your booking</li>
              <li>• Check your dashboard for booking updates</li>
            </ul>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={handleBackToSearch}
              className="flex-1"
            >
              Book Another Property
            </Button>
            <Button
              onClick={() => window.location.href = '/dashboard'}
              className="flex-1 bg-sea-green-500 hover:bg-sea-green-600 text-white"
            >
              View My Bookings
            </Button>
          </div>

          {onClose && (
            <div className="flex justify-center">
              <Button variant="ghost" onClick={onClose}>
                Close
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    );
  }

  return null;
};
