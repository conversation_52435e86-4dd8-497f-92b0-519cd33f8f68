#!/usr/bin/env node

/**
 * Quick Real-time Connection Test
 * Tests basic real-time connectivity without requiring database tables
 */

import { createClient } from '@supabase/supabase-js';
import { config } from 'dotenv';
config();

// ANSI color codes
const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, colors.green);
}

function logError(message) {
  log(`❌ ${message}`, colors.red);
}

function logInfo(message) {
  log(`ℹ️  ${message}`, colors.blue);
}

async function testRealtimeConnection() {
  log('🔄 Testing Real-time Connection...', colors.cyan);
  
  const supabase = createClient(
    process.env.SUPABASE_URL,
    process.env.SUPABASE_ANON_KEY
  );
  
  return new Promise((resolve) => {
    const timeout = setTimeout(() => {
      logError('Real-time connection timeout (10s)');
      channel.unsubscribe();
      resolve(false);
    }, 10000);

    const channel = supabase.channel('test-connection');
    
    channel
      .on('presence', { event: 'sync' }, () => {
        logSuccess('Real-time presence sync successful');
        clearTimeout(timeout);
        channel.unsubscribe();
        resolve(true);
      })
      .on('presence', { event: 'join' }, () => {
        logInfo('Presence join event received');
      })
      .subscribe((status) => {
        logInfo(`Real-time status: ${status}`);
        
        if (status === 'SUBSCRIBED') {
          logSuccess('Real-time channel subscribed');
          // Track presence to trigger sync
          channel.track({ 
            user: 'test-user', 
            timestamp: Date.now() 
          });
        } else if (status === 'CHANNEL_ERROR') {
          logError('Real-time channel error');
          clearTimeout(timeout);
          resolve(false);
        }
      });
  });
}

async function runRealtimeTest() {
  log('🔄 REAL-TIME CONNECTIVITY TEST', colors.cyan);
  log('='.repeat(40), colors.cyan);
  
  const startTime = Date.now();
  const result = await testRealtimeConnection();
  const duration = ((Date.now() - startTime) / 1000).toFixed(2);
  
  log('\n📊 Real-time Test Result', colors.cyan);
  log('='.repeat(40), colors.cyan);
  
  if (result) {
    logSuccess('Real-time connection: WORKING');
    logInfo('✅ WebSocket connections are functional');
    logInfo('✅ Presence tracking is working');
    logInfo('✅ Real-time subscriptions are ready');
  } else {
    logError('Real-time connection: FAILED');
    logInfo('❌ WebSocket connection issues detected');
  }
  
  logInfo(`Duration: ${duration}s`);
  
  if (result) {
    log('\n🎉 Real-time features are ready!', colors.green);
    log('Next steps for real-time setup:', colors.blue);
    log('1. Enable real-time for your database tables', colors.blue);
    log('2. Set up database change subscriptions', colors.blue);
    log('3. Implement real-time UI updates', colors.blue);
  } else {
    log('\n⚠️  Real-time connection needs attention', colors.yellow);
    log('Check your network connection and Supabase configuration', colors.yellow);
  }
  
  process.exit(result ? 0 : 1);
}

// Run the test
runRealtimeTest().catch(error => {
  logError(`Real-time test error: ${error.message}`);
  console.error(error);
  process.exit(1);
});
