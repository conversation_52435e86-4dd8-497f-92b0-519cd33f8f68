import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { SmartRecommendations } from './SmartRecommendations';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { recommendationsService, Property } from '../services/recommendationsService';
import { 
  Sparkles, 
  TrendingUp, 
  Clock, 
  Heart,
  Star,
  MapPin,
  Users,
  RefreshCw,
  ArrowRight
} from 'lucide-react';

interface RecommendationsDashboardProps {
  currentPropertyId?: string;
  showSimilar?: boolean;
  showRecentlyViewed?: boolean;
  showPersonalized?: boolean;
  showTrending?: boolean;
  className?: string;
}

export const RecommendationsDashboard: React.FC<RecommendationsDashboardProps> = ({
  currentPropertyId,
  showSimilar = true,
  showRecentlyViewed = true,
  showPersonalized = true,
  showTrending = true,
  className
}) => {
  const navigate = useNavigate();
  const [recommendations, setRecommendations] = useState({
    similar: [] as Property[],
    recentlyViewed: [] as Property[],
    personalized: [] as Property[],
    trending: [] as Property[]
  });
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const fetchAllRecommendations = async () => {
    try {
      setLoading(true);
      const data = await recommendationsService.getAllRecommendations(currentPropertyId);
      setRecommendations(data);
    } catch (error) {
      console.error('Error fetching recommendations:', error);
    } finally {
      setLoading(false);
    }
  };

  const refreshRecommendations = async () => {
    try {
      setRefreshing(true);
      const data = await recommendationsService.getAllRecommendations(currentPropertyId);
      setRecommendations(data);
    } catch (error) {
      console.error('Error refreshing recommendations:', error);
    } finally {
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchAllRecommendations();
  }, [currentPropertyId]);

  const handlePropertyClick = (property: Property) => {
    // Log the property view
    recommendationsService.logPropertyView(property.id);
    
    // Navigate to property detail page
    navigate(`/property/${property.id}`);
  };

  const getTotalRecommendations = () => {
    return recommendations.similar.length + 
           recommendations.recentlyViewed.length + 
           recommendations.personalized.length + 
           recommendations.trending.length;
  };

  const hasAnyRecommendations = () => {
    return getTotalRecommendations() > 0;
  };

  if (loading) {
    return (
      <div className={`space-y-8 ${className}`}>
        <div className="flex items-center justify-between">
          <div>
            <div className="h-8 bg-gray-200 rounded w-64 animate-pulse mb-2"></div>
            <div className="h-4 bg-gray-200 rounded w-96 animate-pulse"></div>
          </div>
          <div className="h-10 bg-gray-200 rounded w-32 animate-pulse"></div>
        </div>
        
        {[...Array(3)].map((_, index) => (
          <Card key={index}>
            <CardHeader>
              <div className="h-6 bg-gray-200 rounded w-48 animate-pulse"></div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {[...Array(3)].map((_, i) => (
                  <div key={i} className="space-y-3">
                    <div className="h-48 bg-gray-200 rounded-lg animate-pulse"></div>
                    <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                    <div className="h-3 bg-gray-200 rounded w-2/3 animate-pulse"></div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (!hasAnyRecommendations()) {
    return (
      <Card className={className}>
        <CardContent className="text-center py-12">
          <Sparkles className="h-16 w-16 mx-auto mb-4 text-gray-400" />
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            No Recommendations Yet
          </h3>
          <p className="text-gray-600 mb-6">
            Start exploring properties to get personalized recommendations
          </p>
          <Button
            onClick={() => navigate('/search')}
            className="bg-sea-green-500 hover:bg-sea-green-600"
          >
            Explore Properties
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`space-y-8 ${className}`}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
            <Sparkles className="h-6 w-6 text-sea-green-500" />
            Smart Recommendations
            <Badge variant="secondary">{getTotalRecommendations()}</Badge>
          </h2>
          <p className="text-gray-600 mt-1">
            Discover properties tailored to your preferences and interests
          </p>
        </div>
        <Button
          variant="outline"
          onClick={refreshRecommendations}
          disabled={refreshing}
          className="flex items-center gap-2"
        >
          <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* Recommendations Grid */}
      <div className="space-y-8">
        {/* Similar Properties */}
        {showSimilar && currentPropertyId && recommendations.similar.length > 0 && (
          <SmartRecommendations
            type="similar"
            propertyId={currentPropertyId}
            onPropertyClick={handlePropertyClick}
          />
        )}

        {/* Recently Viewed */}
        {showRecentlyViewed && recommendations.recentlyViewed.length > 0 && (
          <SmartRecommendations
            type="recently_viewed"
            onPropertyClick={handlePropertyClick}
          />
        )}

        {/* Personalized Recommendations */}
        {showPersonalized && recommendations.personalized.length > 0 && (
          <SmartRecommendations
            type="personalized"
            onPropertyClick={handlePropertyClick}
          />
        )}

        {/* Trending Properties */}
        {showTrending && recommendations.trending.length > 0 && (
          <SmartRecommendations
            type="trending"
            onPropertyClick={handlePropertyClick}
          />
        )}
      </div>

      {/* Recommendation Stats */}
      <Card className="bg-gradient-to-r from-sea-green-50 to-ocean-blue-50 border-sea-green-200">
        <CardContent className="p-6">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-sea-green-600">
                {recommendations.similar.length}
              </div>
              <div className="text-sm text-gray-600">Similar Properties</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-ocean-blue-600">
                {recommendations.recentlyViewed.length}
              </div>
              <div className="text-sm text-gray-600">Recently Viewed</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {recommendations.personalized.length}
              </div>
              <div className="text-sm text-gray-600">Personalized</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">
                {recommendations.trending.length}
              </div>
              <div className="text-sm text-gray-600">Trending</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Call to Action */}
      <Card className="bg-gradient-to-r from-sea-green-500 to-ocean-blue-500 text-white">
        <CardContent className="p-6 text-center">
          <h3 className="text-xl font-semibold mb-2">
            Looking for something specific?
          </h3>
          <p className="mb-4 opacity-90">
            Use our advanced search filters to find the perfect property for your needs
          </p>
          <Button
            variant="secondary"
            onClick={() => navigate('/search')}
            className="bg-white text-sea-green-600 hover:bg-gray-100"
          >
            Advanced Search
            <ArrowRight className="h-4 w-4 ml-2" />
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};
