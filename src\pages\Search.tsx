import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { Header } from '../components/Header';
import { SearchFilters } from '../components/SearchFilters';
import { DateRangePicker } from '../components/DateRangePicker';
import { MapSearch } from '../components/MapSearch';
import { NeighborhoodInfo } from '../components/NeighborhoodInfo';
import { SavedSearches } from '../components/SavedSearches';
import { SearchHistoryComponent } from '../components/SearchHistory';
import { SearchInputWithSuggestions } from '../components/SearchSuggestions';
import { PropertyCard } from '../components/PropertyCard';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { usePropertySearch } from '../hooks/usePropertySearch';
import { useDebounce } from '../hooks/useDebounce';
import { advancedSearchService, SearchCriteria } from '../services/advancedSearchService';
import {
  Loader2,
  Search as SearchIcon,
  MapPin,
  Filter,
  Grid,
  List,
  Map,
  SortAsc,
  SortDesc
} from 'lucide-react';

interface SearchFilters {
  location?: string;
  minPrice?: number;
  maxPrice?: number;
  guests?: number;
  propertyType?: string;
  bedrooms?: number;
  bathrooms?: number;
  amenities?: string[];
  rating?: number;
  instantBook?: boolean;
  checkIn?: string;
  checkOut?: string;
}

export const Search: React.FC = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [filters, setFilters] = useState<SearchFilters>({});
  const [viewMode, setViewMode] = useState<'grid' | 'list' | 'map'>('grid');
  const [sortBy, setSortBy] = useState<'price' | 'rating' | 'newest'>('rating');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [showFilters, setShowFilters] = useState(true);
  const [showAdvancedSearch, setShowAdvancedSearch] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  // Initialize filters from URL parameters
  useEffect(() => {
    const initialFilters: SearchFilters = {};

    const location = searchParams.get('location');
    const guests = searchParams.get('guests');
    const minPrice = searchParams.get('minPrice');
    const maxPrice = searchParams.get('maxPrice');
    const propertyType = searchParams.get('propertyType');
    const bedrooms = searchParams.get('bedrooms');
    const bathrooms = searchParams.get('bathrooms');
    const amenities = searchParams.get('amenities');
    const rating = searchParams.get('rating');
    const instantBook = searchParams.get('instantBook');
    const checkIn = searchParams.get('checkIn');
    const checkOut = searchParams.get('checkOut');

    if (location) initialFilters.location = location;
    if (guests) initialFilters.guests = parseInt(guests);
    if (minPrice) initialFilters.minPrice = parseInt(minPrice);
    if (maxPrice) initialFilters.maxPrice = parseInt(maxPrice);
    if (propertyType) initialFilters.propertyType = propertyType;
    if (bedrooms) initialFilters.bedrooms = parseInt(bedrooms);
    if (bathrooms) initialFilters.bathrooms = parseInt(bathrooms);
    if (amenities) initialFilters.amenities = amenities.split(',');
    if (rating) initialFilters.rating = parseFloat(rating);
    if (instantBook) initialFilters.instantBook = instantBook === 'true';
    if (checkIn) initialFilters.checkIn = checkIn;
    if (checkOut) initialFilters.checkOut = checkOut;

    setFilters(initialFilters);
  }, [searchParams]);

  // Debounce filters to avoid too many API calls
  const debouncedFilters = useDebounce(filters, 500);

  const {
    properties,
    pagination,
    loading,
    error,
    searchProperties,
    clearSearch,
    loadMore,
    hasMore
  } = usePropertySearch();

  // Search when debounced filters change
  useEffect(() => {
    if (Object.keys(debouncedFilters).length > 0) {
      searchProperties({
        ...debouncedFilters,
        limit: 12
      });
    }
  }, [debouncedFilters, searchProperties]);

  const handleFiltersChange = (newFilters: SearchFilters) => {
    setFilters(newFilters);

    // Update URL parameters
    const newSearchParams = new URLSearchParams();
    Object.entries(newFilters).forEach(([key, value]) => {
      if (value !== undefined && value !== '' && value !== false &&
          !(Array.isArray(value) && value.length === 0)) {
        if (Array.isArray(value)) {
          newSearchParams.set(key, value.join(','));
        } else {
          newSearchParams.set(key, value.toString());
        }
      }
    });
    setSearchParams(newSearchParams);
  };

  const handleSearch = () => {
    searchProperties({
      ...filters,
      limit: 12
    });
  };

  const handleClearFilters = () => {
    setFilters({});
    setSearchParams({});
    clearSearch();
  };

  const handleSavedSearchSelect = (criteria: SearchCriteria) => {
    setFilters(criteria);
    handleFiltersChange(criteria);
  };

  const handleSaveCurrentSearch = async () => {
    if (Object.keys(filters).length === 0) {
      alert('Please set some search criteria before saving');
      return;
    }

    const name = prompt('Enter a name for this search:');
    if (!name) return;

    try {
      await advancedSearchService.createSavedSearch({
        name: name.trim(),
        search_criteria: filters,
        notification_enabled: false
      });
      alert('Search saved successfully!');
    } catch (error) {
      console.error('Error saving search:', error);
      alert('Failed to save search. Please try again.');
    }
  };

  const handleSearchWithSuggestions = (query: string) => {
    setSearchQuery(query);
    handleFiltersChange({
      ...filters,
      location: query
    });
  };

  // Add to search history when search is performed
  useEffect(() => {
    if (Object.keys(debouncedFilters).length > 0 && properties.length > 0) {
      advancedSearchService.addToSearchHistory(debouncedFilters, properties.length);
    }
  }, [debouncedFilters, properties.length]);

  const getResultsText = () => {
    if (loading && properties.length === 0) return 'Searching...';
    if (error) return 'Search failed';
    if (properties.length === 0) return 'No properties found';
    
    const { total } = pagination;
    const location = filters.location;
    
    if (location) {
      return `${total} ${total === 1 ? 'property' : 'properties'} in ${location}`;
    }
    
    return `${total} ${total === 1 ? 'property' : 'properties'} found`;
  };

  const sortedProperties = [...properties].sort((a, b) => {
    let comparison = 0;
    
    switch (sortBy) {
      case 'price':
        comparison = a.price - b.price;
        break;
      case 'rating':
        comparison = (a.averageRating || 0) - (b.averageRating || 0);
        break;
      case 'newest':
        comparison = new Date(a.createdAt || 0).getTime() - new Date(b.createdAt || 0).getTime();
        break;
    }
    
    return sortOrder === 'desc' ? -comparison : comparison;
  });

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="container mx-auto px-4 py-6">
        {/* Search Header */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
                <SearchIcon className="h-6 w-6" />
                Search Results
              </h1>
              <p className="text-gray-600 mt-1">{getResultsText()}</p>
            </div>
            
            <div className="flex items-center gap-2">
              {/* View Mode Toggle */}
              <div className="flex border rounded-lg overflow-hidden">
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                  className="rounded-none"
                >
                  <Grid className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                  className="rounded-none"
                >
                  <List className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === 'map' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('map')}
                  className="rounded-none"
                >
                  <Map className="h-4 w-4" />
                </Button>
              </div>

              {/* Sort Controls */}
              <select
                value={`${sortBy}-${sortOrder}`}
                onChange={(e) => {
                  const [newSortBy, newSortOrder] = e.target.value.split('-') as [typeof sortBy, typeof sortOrder];
                  setSortBy(newSortBy);
                  setSortOrder(newSortOrder);
                }}
                className="px-3 py-2 border border-gray-300 rounded-lg text-sm"
              >
                <option value="rating-desc">Highest Rated</option>
                <option value="rating-asc">Lowest Rated</option>
                <option value="price-asc">Price: Low to High</option>
                <option value="price-desc">Price: High to Low</option>
                <option value="newest-desc">Newest First</option>
              </select>

              {/* Filters Toggle */}
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowFilters(!showFilters)}
                className="flex items-center gap-2"
              >
                <Filter className="h-4 w-4" />
                {showFilters ? 'Hide' : 'Show'} Filters
              </Button>
            </div>
          </div>

          {/* Active Filters Summary */}
          {Object.keys(filters).length > 0 && (
            <div className="flex flex-wrap items-center gap-2">
              <span className="text-sm text-gray-600">Active filters:</span>
              {filters.location && (
                <Badge variant="secondary" className="flex items-center gap-1">
                  <MapPin className="h-3 w-3" />
                  {filters.location}
                </Badge>
              )}
              {filters.guests && (
                <Badge variant="secondary">
                  {filters.guests} guests
                </Badge>
              )}
              {filters.propertyType && (
                <Badge variant="secondary">
                  {filters.propertyType}
                </Badge>
              )}
              {(filters.minPrice || filters.maxPrice) && (
                <Badge variant="secondary">
                  R{filters.minPrice || 0} - R{filters.maxPrice || '∞'}
                </Badge>
              )}
            </div>
          )}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Filters Sidebar */}
          {showFilters && (
            <div className="lg:col-span-1 space-y-6">
              {/* Advanced Search Input */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <SearchIcon className="h-5 w-5" />
                    Search
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <SearchInputWithSuggestions
                    value={searchQuery}
                    onChange={setSearchQuery}
                    onSearch={handleSearchWithSuggestions}
                    category="location"
                    placeholder="Search locations, property types..."
                  />
                </CardContent>
              </Card>

              {/* Saved Searches */}
              <SavedSearches
                onSearchSelect={handleSavedSearchSelect}
                onCreateNew={handleSaveCurrentSearch}
              />

              {/* Search History */}
              <SearchHistoryComponent
                onSearchSelect={handleSavedSearchSelect}
                maxItems={5}
              />

              {/* Date Range Picker */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">When</CardTitle>
                </CardHeader>
                <CardContent>
                  <DateRangePicker
                    value={{
                      checkIn: filters.checkIn,
                      checkOut: filters.checkOut
                    }}
                    onChange={(dateRange) => {
                      handleFiltersChange({
                        ...filters,
                        checkIn: dateRange.checkIn,
                        checkOut: dateRange.checkOut
                      });
                    }}
                  />
                </CardContent>
              </Card>

              {/* Other Filters */}
              <SearchFilters
                filters={filters}
                onFiltersChange={handleFiltersChange}
                onSearch={handleSearch}
                onClear={handleClearFilters}
                loading={loading}
              />
            </div>
          )}

          {/* Results */}
          <div className={showFilters ? 'lg:col-span-3' : 'lg:col-span-4'}>
            {/* Loading State */}
            {loading && properties.length === 0 && (
              <div className="text-center py-12">
                <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-sea-green-600" />
                <p className="text-gray-600">Searching for properties...</p>
              </div>
            )}

            {/* Error State */}
            {error && (
              <Card>
                <CardContent className="text-center py-12">
                  <p className="text-red-600 mb-4">{error}</p>
                  <Button onClick={handleSearch} variant="outline">
                    Try Again
                  </Button>
                </CardContent>
              </Card>
            )}

            {/* No Results */}
            {!loading && !error && properties.length === 0 && Object.keys(filters).length > 0 && (
              <Card>
                <CardContent className="text-center py-12">
                  <SearchIcon className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No properties found</h3>
                  <p className="text-gray-600 mb-4">
                    Try adjusting your search criteria or browse all properties.
                  </p>
                  <Button onClick={handleClearFilters} variant="outline">
                    Clear Filters
                  </Button>
                </CardContent>
              </Card>
            )}

            {/* Properties Grid/List/Map */}
            {!loading && !error && sortedProperties.length > 0 && (
              <>
                {viewMode === 'map' ? (
                  <div className="space-y-6">
                    <MapSearch
                      properties={sortedProperties}
                      onPropertySelect={(property) => {
                        // Handle property selection
                        console.log('Selected property:', property);
                      }}
                      onSearchArea={(bounds) => {
                        // Handle map area search
                        console.log('Search area:', bounds);
                      }}
                    />
                    {filters.location && (
                      <NeighborhoodInfo location={filters.location} />
                    )}
                  </div>
                ) : (
                  <div className={
                    viewMode === 'grid'
                      ? 'grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6'
                      : 'space-y-4'
                  }>
                    {sortedProperties.map((property) => (
                      <PropertyCard
                        key={property.id}
                        property={property}
                        variant={viewMode}
                      />
                    ))}
                  </div>
                )}

                {/* Load More Button */}
                {hasMore && (
                  <div className="text-center mt-8">
                    <Button
                      onClick={loadMore}
                      disabled={loading}
                      variant="outline"
                      size="lg"
                    >
                      {loading ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Loading...
                        </>
                      ) : (
                        'Load More Properties'
                      )}
                    </Button>
                  </div>
                )}

                {/* Pagination Info */}
                <div className="text-center mt-6 text-sm text-gray-600">
                  Showing {properties.length} of {pagination.total} properties
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Search;
