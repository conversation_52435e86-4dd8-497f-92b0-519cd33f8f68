import React, { useEffect, useState } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { Header } from '../components/Header';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { paymentService } from '../services/paymentService';
import { 
  CheckCircle, 
  Calendar,
  MapPin,
  Users,
  DollarSign,
  Mail,
  Phone,
  Download,
  ArrowRight,
  Home
} from 'lucide-react';

export const PaymentSuccess: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [paymentDetails, setPaymentDetails] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPaymentDetails = async () => {
      try {
        const paymentId = searchParams.get('payment_id');
        const paymentRef = searchParams.get('pf_payment_id');
        
        if (paymentId) {
          const payment = await paymentService.getPayment(paymentId);
          setPaymentDetails(payment);
        } else {
          // Use mock data for demonstration
          setPaymentDetails({
            id: 'payment-success',
            booking_id: 'booking-1',
            payment_reference: 'SF-TEST-' + Date.now(),
            amount: 1200.00,
            platform_fee: 60.00,
            host_amount: 1140.00,
            payment_status: 'completed',
            property_title: 'Beachfront Villa in Margate',
            property_location: 'Margate, KZN',
            check_in_date: '2024-06-25',
            check_out_date: '2024-06-28',
            guest_count: 4,
            created_at: new Date().toISOString()
          });
        }
      } catch (err: any) {
        console.error('Error fetching payment details:', err);
        setError('Failed to load payment details');
      } finally {
        setLoading(false);
      }
    };

    fetchPaymentDetails();
  }, [searchParams]);

  const formatCurrency = (amount: number) => {
    return paymentService.formatCurrency(amount);
  };

  const formatDate = (dateString: string) => {
    return paymentService.formatDate(dateString);
  };

  const handleDownloadReceipt = () => {
    // TODO: Implement receipt download
    alert('Receipt download will be implemented');
  };

  const handleViewBooking = () => {
    navigate('/bookings');
  };

  const handleBackToHome = () => {
    navigate('/');
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-2xl mx-auto">
            <Card>
              <CardContent className="p-8 text-center">
                <div className="animate-pulse space-y-4">
                  <div className="h-12 w-12 bg-gray-200 rounded-full mx-auto"></div>
                  <div className="h-6 bg-gray-200 rounded w-3/4 mx-auto"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/2 mx-auto"></div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-2xl mx-auto">
            <Card className="border-red-200 bg-red-50">
              <CardContent className="p-8 text-center">
                <div className="text-red-600 mb-4">
                  <CheckCircle className="h-12 w-12 mx-auto" />
                </div>
                <h2 className="text-xl font-semibold text-red-800 mb-2">
                  Error Loading Payment Details
                </h2>
                <p className="text-red-700 mb-6">{error}</p>
                <Button onClick={handleBackToHome}>
                  Return to Home
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto space-y-6">
          {/* Success Header */}
          <Card className="border-green-200 bg-green-50">
            <CardContent className="p-8 text-center">
              <div className="text-green-600 mb-4">
                <CheckCircle className="h-16 w-16 mx-auto" />
              </div>
              <h1 className="text-2xl font-bold text-green-800 mb-2">
                Payment Successful!
              </h1>
              <p className="text-green-700 mb-4">
                Your booking has been confirmed and payment processed successfully.
              </p>
              <Badge className="bg-green-100 text-green-800 px-4 py-2">
                Booking Confirmed
              </Badge>
            </CardContent>
          </Card>

          {/* Booking Details */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MapPin className="h-5 w-5" />
                Booking Confirmation
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="font-semibold text-lg text-gray-900">
                  {paymentDetails.property_title}
                </h3>
                {paymentDetails.property_location && (
                  <p className="text-gray-600">{paymentDetails.property_location}</p>
                )}
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-gray-600">Check-in</p>
                    <p className="font-medium">{formatDate(paymentDetails.check_in_date)}</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-gray-600">Check-out</p>
                    <p className="font-medium">{formatDate(paymentDetails.check_out_date)}</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-gray-600">Guests</p>
                    <p className="font-medium">{paymentDetails.guest_count}</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Payment Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="h-5 w-5" />
                Payment Summary
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Payment Reference</span>
                <span className="font-mono text-sm">{paymentDetails.payment_reference}</span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-gray-600">Total Amount</span>
                <span className="font-medium">{formatCurrency(paymentDetails.amount)}</span>
              </div>
              
              <div className="flex justify-between text-sm text-gray-500">
                <span>Platform Fee</span>
                <span>{formatCurrency(paymentDetails.platform_fee)}</span>
              </div>
              
              <div className="flex justify-between text-sm text-gray-500">
                <span>Host Amount</span>
                <span>{formatCurrency(paymentDetails.host_amount)}</span>
              </div>
              
              <div className="border-t pt-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">Payment Status</span>
                  <Badge className="bg-green-100 text-green-800">
                    {paymentService.getPaymentStatusIcon(paymentDetails.payment_status)} Completed
                  </Badge>
                </div>
              </div>
              
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Payment Date</span>
                <span>{paymentService.formatDateTime(paymentDetails.created_at)}</span>
              </div>
            </CardContent>
          </Card>

          {/* Next Steps */}
          <Card>
            <CardHeader>
              <CardTitle>What's Next?</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex items-start gap-3">
                  <Mail className="h-5 w-5 text-blue-600 mt-0.5" />
                  <div>
                    <h4 className="font-medium">Confirmation Email</h4>
                    <p className="text-sm text-gray-600">
                      You'll receive a booking confirmation email with all details shortly.
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start gap-3">
                  <Phone className="h-5 w-5 text-green-600 mt-0.5" />
                  <div>
                    <h4 className="font-medium">Host Contact</h4>
                    <p className="text-sm text-gray-600">
                      Your host will contact you 24-48 hours before check-in with details.
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start gap-3">
                  <CheckCircle className="h-5 w-5 text-purple-600 mt-0.5" />
                  <div>
                    <h4 className="font-medium">Payment Security</h4>
                    <p className="text-sm text-gray-600">
                      Your payment is held securely and released to the host after check-in.
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button
              variant="outline"
              onClick={handleDownloadReceipt}
              className="flex items-center justify-center gap-2"
            >
              <Download className="h-4 w-4" />
              Download Receipt
            </Button>
            
            <Button
              onClick={handleViewBooking}
              className="flex items-center justify-center gap-2 bg-sea-green-500 hover:bg-sea-green-600"
            >
              <Calendar className="h-4 w-4" />
              View Booking
              <ArrowRight className="h-4 w-4" />
            </Button>
            
            <Button
              variant="outline"
              onClick={handleBackToHome}
              className="flex items-center justify-center gap-2"
            >
              <Home className="h-4 w-4" />
              Back to Home
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};
