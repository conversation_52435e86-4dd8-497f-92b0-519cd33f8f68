import { useEffect, useCallback } from 'react';
import { AnalyticsService } from '@/services/analyticsService';
import { useAuth } from '@/contexts/AuthContext';

export const usePropertyTracking = () => {
  const { user } = useAuth();

  /**
   * Track when a property is viewed
   */
  const trackPropertyView = useCallback(async (
    propertyId: string,
    additionalData?: {
      referrer?: string;
      sessionId?: string;
    }
  ) => {
    try {
      await AnalyticsService.trackPropertyView(
        propertyId,
        user?.id,
        additionalData
      );
    } catch (error) {
      // Analytics errors should not break the user experience
      console.warn('Failed to track property view:', error);
    }
  }, [user?.id]);

  /**
   * Track a search query
   */
  const trackSearch = useCallback(async (
    searchQuery: string,
    filters: Record<string, any> = {},
    resultsCount: number = 0
  ) => {
    try {
      await AnalyticsService.trackSearch(
        searchQuery,
        filters,
        resultsCount,
        user?.id
      );
    } catch (error) {
      // Analytics errors should not break the user experience
      console.warn('Failed to track search:', error);
    }
  }, [user?.id]);

  /**
   * Auto-track property view when component mounts
   */
  const useAutoTrackPropertyView = (propertyId: string, enabled: boolean = true) => {
    useEffect(() => {
      if (enabled && propertyId) {
        // Small delay to ensure the page has loaded
        const timer = setTimeout(() => {
          trackPropertyView(propertyId, {
            referrer: document.referrer
          });
        }, 1000);

        return () => clearTimeout(timer);
      }
    }, [propertyId, enabled, trackPropertyView]);
  };

  return {
    trackPropertyView,
    trackSearch,
    useAutoTrackPropertyView,
  };
};
