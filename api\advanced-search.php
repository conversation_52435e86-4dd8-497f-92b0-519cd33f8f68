<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: http://localhost:8081');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Credentials: true');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once 'config.php';
require_once 'auth.php';

function getSavedSearches($userId, $limit = 20, $offset = 0) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            SELECT 
                id, name, search_criteria, notification_enabled, 
                created_at, updated_at
            FROM saved_searches 
            WHERE user_id = ?
            ORDER BY updated_at DESC
            LIMIT ? OFFSET ?
        ");
        
        $stmt->execute([$userId, $limit, $offset]);
        $searches = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Decode JSON criteria
        foreach ($searches as &$search) {
            $search['search_criteria'] = json_decode($search['search_criteria'], true);
        }
        
        return $searches;
        
    } catch (Exception $e) {
        error_log("Error getting saved searches: " . $e->getMessage());
        return [];
    }
}

function createSavedSearch($data, $userId) {
    global $pdo;
    
    try {
        // Validate required fields
        if (!isset($data['name']) || !isset($data['search_criteria'])) {
            throw new Exception("Name and search criteria are required");
        }
        
        $searchId = 'search-' . uniqid();
        $stmt = $pdo->prepare("
            INSERT INTO saved_searches (
                id, user_id, name, search_criteria, notification_enabled, 
                created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, NOW(), NOW())
        ");
        
        $stmt->execute([
            $searchId,
            $userId,
            $data['name'],
            json_encode($data['search_criteria']),
            $data['notification_enabled'] ?? false
        ]);
        
        return $searchId;
        
    } catch (Exception $e) {
        error_log("Error creating saved search: " . $e->getMessage());
        throw $e;
    }
}

function updateSavedSearch($searchId, $data, $userId) {
    global $pdo;
    
    try {
        // Verify ownership
        $stmt = $pdo->prepare("SELECT user_id FROM saved_searches WHERE id = ?");
        $stmt->execute([$searchId]);
        $search = $stmt->fetch();
        
        if (!$search || $search['user_id'] !== $userId) {
            throw new Exception("Search not found or access denied");
        }
        
        $updateFields = [];
        $values = [];
        
        if (isset($data['name'])) {
            $updateFields[] = "name = ?";
            $values[] = $data['name'];
        }
        
        if (isset($data['search_criteria'])) {
            $updateFields[] = "search_criteria = ?";
            $values[] = json_encode($data['search_criteria']);
        }
        
        if (isset($data['notification_enabled'])) {
            $updateFields[] = "notification_enabled = ?";
            $values[] = $data['notification_enabled'];
        }
        
        if (empty($updateFields)) {
            throw new Exception("No fields to update");
        }
        
        $updateFields[] = "updated_at = NOW()";
        $values[] = $searchId;
        
        $sql = "UPDATE saved_searches SET " . implode(', ', $updateFields) . " WHERE id = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute($values);
        
        return true;
        
    } catch (Exception $e) {
        error_log("Error updating saved search: " . $e->getMessage());
        throw $e;
    }
}

function deleteSavedSearch($searchId, $userId) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            DELETE FROM saved_searches 
            WHERE id = ? AND user_id = ?
        ");
        $stmt->execute([$searchId, $userId]);
        
        return $stmt->rowCount() > 0;
        
    } catch (Exception $e) {
        error_log("Error deleting saved search: " . $e->getMessage());
        throw $e;
    }
}

function getSearchHistory($userId, $limit = 10) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            SELECT 
                id, search_criteria, results_count, search_timestamp
            FROM search_history 
            WHERE user_id = ?
            ORDER BY search_timestamp DESC
            LIMIT ?
        ");
        
        $stmt->execute([$userId, $limit]);
        $history = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Decode JSON criteria
        foreach ($history as &$item) {
            $item['search_criteria'] = json_decode($item['search_criteria'], true);
        }
        
        return $history;
        
    } catch (Exception $e) {
        error_log("Error getting search history: " . $e->getMessage());
        return [];
    }
}

function addToSearchHistory($searchCriteria, $resultsCount, $userId = null) {
    global $pdo;
    
    try {
        $historyId = 'history-' . uniqid();
        $stmt = $pdo->prepare("
            INSERT INTO search_history (
                id, user_id, search_criteria, results_count, 
                search_timestamp, ip_address, user_agent
            ) VALUES (?, ?, ?, ?, NOW(), ?, ?)
        ");
        
        $stmt->execute([
            $historyId,
            $userId,
            json_encode($searchCriteria),
            $resultsCount,
            $_SERVER['REMOTE_ADDR'] ?? null,
            $_SERVER['HTTP_USER_AGENT'] ?? null
        ]);
        
        return $historyId;
        
    } catch (Exception $e) {
        error_log("Error adding to search history: " . $e->getMessage());
        return null;
    }
}

function getSearchSuggestions($query, $category = null, $limit = 10) {
    global $pdo;
    
    try {
        $sql = "
            SELECT term, category, search_count
            FROM search_suggestions 
            WHERE term LIKE ?
        ";
        $params = ["%$query%"];
        
        if ($category) {
            $sql .= " AND category = ?";
            $params[] = $category;
        }
        
        $sql .= " ORDER BY search_count DESC, term ASC LIMIT ?";
        $params[] = $limit;
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
        
    } catch (Exception $e) {
        error_log("Error getting search suggestions: " . $e->getMessage());
        return [];
    }
}

function updateSearchSuggestion($term, $category = 'general') {
    global $pdo;
    
    try {
        // Try to update existing suggestion
        $stmt = $pdo->prepare("
            UPDATE search_suggestions 
            SET search_count = search_count + 1, last_searched = NOW()
            WHERE term = ? AND category = ?
        ");
        $stmt->execute([$term, $category]);
        
        // If no rows affected, insert new suggestion
        if ($stmt->rowCount() === 0) {
            $suggestionId = 'suggest-' . uniqid();
            $stmt = $pdo->prepare("
                INSERT INTO search_suggestions (
                    id, term, category, search_count, last_searched, created_at
                ) VALUES (?, ?, ?, 1, NOW(), NOW())
            ");
            $stmt->execute([$suggestionId, $term, $category]);
        }
        
        return true;
        
    } catch (Exception $e) {
        error_log("Error updating search suggestion: " . $e->getMessage());
        return false;
    }
}

// Handle the request
$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['action'] ?? '';

try {
    switch ($method) {
        case 'GET':
            switch ($action) {
                case 'saved_searches':
                    $user = getCurrentUser();
                    if (!$user) {
                        http_response_code(401);
                        echo json_encode(['error' => 'Authentication required']);
                        exit;
                    }
                    
                    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 20;
                    $offset = isset($_GET['offset']) ? (int)$_GET['offset'] : 0;
                    
                    $searches = getSavedSearches($user['id'], $limit, $offset);
                    
                    echo json_encode([
                        'success' => true,
                        'data' => $searches
                    ]);
                    break;
                    
                case 'search_history':
                    $user = getCurrentUser();
                    if (!$user) {
                        http_response_code(401);
                        echo json_encode(['error' => 'Authentication required']);
                        exit;
                    }
                    
                    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;
                    $history = getSearchHistory($user['id'], $limit);
                    
                    echo json_encode([
                        'success' => true,
                        'data' => $history
                    ]);
                    break;
                    
                case 'suggestions':
                    $query = $_GET['query'] ?? '';
                    $category = $_GET['category'] ?? null;
                    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;
                    
                    if (empty($query)) {
                        echo json_encode([
                            'success' => true,
                            'data' => []
                        ]);
                        break;
                    }
                    
                    $suggestions = getSearchSuggestions($query, $category, $limit);
                    
                    echo json_encode([
                        'success' => true,
                        'data' => $suggestions
                    ]);
                    break;
                    
                default:
                    http_response_code(400);
                    echo json_encode(['error' => 'Invalid action']);
                    break;
            }
            break;
            
        case 'POST':
            $input = json_decode(file_get_contents('php://input'), true);
            
            switch ($action) {
                case 'save_search':
                    $user = getCurrentUser();
                    if (!$user) {
                        http_response_code(401);
                        echo json_encode(['error' => 'Authentication required']);
                        exit;
                    }
                    
                    $searchId = createSavedSearch($input, $user['id']);
                    
                    echo json_encode([
                        'success' => true,
                        'message' => 'Search saved successfully',
                        'search_id' => $searchId
                    ]);
                    break;
                    
                case 'add_to_history':
                    $user = getCurrentUser();
                    $userId = $user ? $user['id'] : null;
                    
                    $historyId = addToSearchHistory(
                        $input['search_criteria'] ?? [],
                        $input['results_count'] ?? 0,
                        $userId
                    );
                    
                    echo json_encode([
                        'success' => true,
                        'history_id' => $historyId
                    ]);
                    break;
                    
                case 'update_suggestion':
                    updateSearchSuggestion(
                        $input['term'] ?? '',
                        $input['category'] ?? 'general'
                    );
                    
                    echo json_encode([
                        'success' => true,
                        'message' => 'Suggestion updated'
                    ]);
                    break;
                    
                default:
                    http_response_code(400);
                    echo json_encode(['error' => 'Invalid action']);
                    break;
            }
            break;
            
        case 'PUT':
            $user = getCurrentUser();
            if (!$user) {
                http_response_code(401);
                echo json_encode(['error' => 'Authentication required']);
                exit;
            }
            
            $searchId = $_GET['search_id'] ?? '';
            if (empty($searchId)) {
                http_response_code(400);
                echo json_encode(['error' => 'Search ID is required']);
                exit;
            }
            
            $input = json_decode(file_get_contents('php://input'), true);
            
            $success = updateSavedSearch($searchId, $input, $user['id']);
            
            if ($success) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Search updated successfully'
                ]);
            } else {
                http_response_code(500);
                echo json_encode(['error' => 'Failed to update search']);
            }
            break;
            
        case 'DELETE':
            $user = getCurrentUser();
            if (!$user) {
                http_response_code(401);
                echo json_encode(['error' => 'Authentication required']);
                exit;
            }
            
            $searchId = $_GET['search_id'] ?? '';
            if (empty($searchId)) {
                http_response_code(400);
                echo json_encode(['error' => 'Search ID is required']);
                exit;
            }
            
            $success = deleteSavedSearch($searchId, $user['id']);
            
            if ($success) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Search deleted successfully'
                ]);
            } else {
                http_response_code(404);
                echo json_encode(['error' => 'Search not found']);
            }
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            break;
    }
    
} catch (Exception $e) {
    error_log("Advanced Search API error: " . $e->getMessage());
    http_response_code(400);
    echo json_encode(['error' => $e->getMessage()]);
}
?>
