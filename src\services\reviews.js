// Reviews API Service
const API_BASE_URL = 'http://localhost/stayfinder/api';

class ReviewsService {
  // Get authentication token from localStorage
  getAuthToken() {
    return localStorage.getItem('stayfinder_token');
  }

  // Get authorization headers
  getAuthHeaders() {
    const token = this.getAuthToken();
    return {
      'Content-Type': 'application/json',
      ...(token && { 'Authorization': `Bearer ${token}` })
    };
  }

  // Create a new review
  async createReview(reviewData) {
    try {
      const response = await fetch(`${API_BASE_URL}/reviews`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(reviewData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error creating review:', error);
      throw error;
    }
  }

  // Get reviews for a property
  async getPropertyReviews(propertyId, page = 1, limit = 10) {
    try {
      const response = await fetch(`${API_BASE_URL}/reviews/property/${propertyId}?page=${page}&limit=${limit}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error fetching property reviews:', error);
      throw error;
    }
  }

  // Get user's reviews
  async getUserReviews() {
    try {
      const response = await fetch(`${API_BASE_URL}/reviews/my-reviews`, {
        headers: this.getAuthHeaders()
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error fetching user reviews:', error);
      throw error;
    }
  }

  // Update a review
  async updateReview(reviewId, reviewData) {
    try {
      const response = await fetch(`${API_BASE_URL}/reviews/${reviewId}`, {
        method: 'PUT',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(reviewData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error updating review:', error);
      throw error;
    }
  }

  // Add host response to review
  async addHostResponse(reviewId, response) {
    try {
      const responseData = await fetch(`${API_BASE_URL}/reviews/${reviewId}/response`, {
        method: 'PATCH',
        headers: this.getAuthHeaders(),
        body: JSON.stringify({ response })
      });

      if (!responseData.ok) {
        const errorData = await responseData.json();
        throw new Error(errorData.error || `HTTP error! status: ${responseData.status}`);
      }

      const data = await responseData.json();
      return data;
    } catch (error) {
      console.error('Error adding host response:', error);
      throw error;
    }
  }

  // Get recent reviews
  async getRecentReviews(limit = 10) {
    try {
      const response = await fetch(`${API_BASE_URL}/reviews/recent?limit=${limit}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error fetching recent reviews:', error);
      throw error;
    }
  }

  // Get reviewer statistics
  async getReviewerStats(userId) {
    try {
      const response = await fetch(`${API_BASE_URL}/reviews/stats/reviewer/${userId}`, {
        headers: this.getAuthHeaders()
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error fetching reviewer stats:', error);
      throw error;
    }
  }

  // Search reviews
  async searchReviews(searchTerm, limit = 20) {
    try {
      const response = await fetch(`${API_BASE_URL}/reviews/search?q=${encodeURIComponent(searchTerm)}&limit=${limit}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error searching reviews:', error);
      throw error;
    }
  }

  // Get top-rated properties
  async getTopRatedProperties(limit = 10) {
    try {
      const response = await fetch(`${API_BASE_URL}/reviews/top-rated-properties?limit=${limit}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error fetching top-rated properties:', error);
      throw error;
    }
  }

  // Transform review data for frontend
  transformReview(backendReview) {
    return {
      id: backendReview.id,
      propertyId: backendReview.propertyId,
      propertyTitle: backendReview.propertyTitle,
      propertyLocation: backendReview.propertyLocation,
      rating: backendReview.rating,
      comment: backendReview.comment,
      response: backendReview.response,
      reviewer: backendReview.reviewer,
      createdAt: backendReview.createdAt,
      updatedAt: backendReview.updatedAt
    };
  }

  // Generate star rating display
  generateStarRating(rating) {
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 >= 0.5;
    const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);

    return {
      fullStars,
      hasHalfStar,
      emptyStars,
      rating
    };
  }

  // Format rating for display
  formatRating(rating) {
    return parseFloat(rating).toFixed(1);
  }

  // Get rating color for UI
  getRatingColor(rating) {
    if (rating >= 4.5) return 'green';
    if (rating >= 4.0) return 'lime';
    if (rating >= 3.5) return 'yellow';
    if (rating >= 3.0) return 'orange';
    return 'red';
  }

  // Get rating text description
  getRatingText(rating) {
    if (rating >= 4.5) return 'Excellent';
    if (rating >= 4.0) return 'Very Good';
    if (rating >= 3.5) return 'Good';
    if (rating >= 3.0) return 'Average';
    if (rating >= 2.0) return 'Poor';
    return 'Very Poor';
  }

  // Check if review can be edited (within 24 hours)
  canEditReview(reviewCreatedAt) {
    const reviewDate = new Date(reviewCreatedAt);
    const now = new Date();
    const hoursDiff = (now - reviewDate) / (1000 * 60 * 60);
    return hoursDiff <= 24;
  }

  // Format review date for display
  formatReviewDate(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now - date);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return 'Yesterday';
    if (diffDays <= 7) return `${diffDays} days ago`;
    if (diffDays <= 30) return `${Math.ceil(diffDays / 7)} weeks ago`;
    if (diffDays <= 365) return `${Math.ceil(diffDays / 30)} months ago`;
    return `${Math.ceil(diffDays / 365)} years ago`;
  }

  // Validate review data
  validateReviewData(reviewData) {
    const errors = [];

    if (!reviewData.bookingId) {
      errors.push('Booking ID is required');
    }

    if (!reviewData.rating || reviewData.rating < 1 || reviewData.rating > 5) {
      errors.push('Rating must be between 1 and 5');
    }

    if (!reviewData.comment || reviewData.comment.trim().length < 10) {
      errors.push('Comment must be at least 10 characters long');
    }

    if (reviewData.comment && reviewData.comment.length > 1000) {
      errors.push('Comment must be less than 1000 characters');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // Calculate review statistics
  calculateReviewStats(reviews) {
    if (!reviews || reviews.length === 0) {
      return {
        totalReviews: 0,
        averageRating: 0,
        ratingBreakdown: {
          fiveStar: 0,
          fourStar: 0,
          threeStar: 0,
          twoStar: 0,
          oneStar: 0
        }
      };
    }

    const totalReviews = reviews.length;
    const totalRating = reviews.reduce((sum, review) => sum + review.rating, 0);
    const averageRating = totalRating / totalReviews;

    const ratingBreakdown = {
      fiveStar: reviews.filter(r => r.rating === 5).length,
      fourStar: reviews.filter(r => r.rating === 4).length,
      threeStar: reviews.filter(r => r.rating === 3).length,
      twoStar: reviews.filter(r => r.rating === 2).length,
      oneStar: reviews.filter(r => r.rating === 1).length
    };

    return {
      totalReviews,
      averageRating,
      ratingBreakdown
    };
  }

  // Sort reviews by different criteria
  sortReviews(reviews, sortBy = 'newest') {
    const sortedReviews = [...reviews];

    switch (sortBy) {
      case 'newest':
        return sortedReviews.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
      case 'oldest':
        return sortedReviews.sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt));
      case 'highest':
        return sortedReviews.sort((a, b) => b.rating - a.rating);
      case 'lowest':
        return sortedReviews.sort((a, b) => a.rating - b.rating);
      default:
        return sortedReviews;
    }
  }
}

export default new ReviewsService();
