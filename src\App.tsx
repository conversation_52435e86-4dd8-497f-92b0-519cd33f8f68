import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "./contexts/AuthContext";
import { BookingProvider } from "./contexts/BookingContext";
import { ReviewsProvider } from "./contexts/ReviewsContext";
import { ThemeProvider } from "./contexts/ThemeContext";
import ProtectedRoute from "./components/ProtectedRoute";
import { DebugSystemProvider, DebugToggleButton } from "./components/DebugSystem/DebugSystemProvider";
import { performanceService } from "./services/performanceService";
import { initializePerformanceOptimizations } from "./utils/codeSplitting";
import {
  withCodeSplitting,
  createLazyRoute,
  lazyWithRetry
} from "./utils/codeSplitting";
import { useEffect } from "react";

// Immediate load components (critical path)
import Index from "./pages/Index";

// Lazy load non-critical components with code splitting
const Dashboard = createLazyRoute(() => import("./pages/Dashboard"), "Loading Dashboard...");
const Search = createLazyRoute(() => import("./pages/Search"), "Loading Search...");
const Bookings = createLazyRoute(() => import("./pages/Bookings"), "Loading Bookings...");
const Reviews = createLazyRoute(() => import("./pages/Reviews"), "Loading Reviews...");
const HostDashboard = createLazyRoute(() => import("./pages/HostDashboard"), "Loading Host Dashboard...");
const PropertyWizard = createLazyRoute(() => import("./pages/PropertyWizard"), "Loading Property Wizard...");
const UserDashboard = createLazyRoute(() => import("./pages/UserDashboard"), "Loading User Dashboard...");
const PropertyDetail = createLazyRoute(() => import("./pages/PropertyDetail"), "Loading Property Details...");
const Messages = createLazyRoute(() => import("./pages/Messages"), "Loading Messages...");
const PaymentSuccess = createLazyRoute(() => import("./pages/PaymentSuccess"), "Loading Payment Success...");
const PaymentCancel = createLazyRoute(() => import("./pages/PaymentCancel"), "Loading Payment Cancel...");
const NotFound = createLazyRoute(() => import("./pages/NotFound"), "Loading Page...");

// Enhanced QueryClient with performance optimizations
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
      retry: 2,
      refetchOnWindowFocus: false,
    },
  },
});

// Performance-optimized App component
const App = () => {
  useEffect(() => {
    // Initialize performance optimizations
    initializePerformanceOptimizations();

    // Send performance data to analytics after page load
    const sendPerformanceData = () => {
      setTimeout(() => {
        performanceService.sendToAnalytics();

        // Log performance metrics in development
        if (process.env.NODE_ENV === 'development') {
          console.log('Performance Metrics:', performanceService.getMetrics());
          console.log('Performance Score:', performanceService.getPerformanceScore());
          console.log('Recommendations:', performanceService.getRecommendations());
        }
      }, 2000);
    };

    if (document.readyState === 'complete') {
      sendPerformanceData();
    } else {
      window.addEventListener('load', sendPerformanceData);
    }

    return () => {
      window.removeEventListener('load', sendPerformanceData);
    };
  }, []);

  return (
    <ThemeProvider defaultTheme="system" storageKey="stayfinder-theme">
      <QueryClientProvider client={queryClient}>
        <TooltipProvider>
          <AuthProvider>
            <DebugSystemProvider userRole="admin" autoStart={true}>
              <BookingProvider>
                <ReviewsProvider>
                <Toaster />
                <Sonner />
                <DebugToggleButton position="bottom-right" />
                <BrowserRouter>
                <Routes>
                  <Route path="/" element={<Index />} />
                  <Route path="/search" element={<Search />} />
                  <Route
                    path="/dashboard"
                    element={
                      <ProtectedRoute>
                        <Dashboard />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/bookings"
                    element={
                      <ProtectedRoute>
                        <Bookings />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/reviews"
                    element={
                      <ProtectedRoute>
                        <Reviews />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/host"
                    element={
                      <ProtectedRoute>
                        <HostDashboard />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/host/property/new"
                    element={
                      <ProtectedRoute>
                        <PropertyWizard />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/user/dashboard"
                    element={
                      <ProtectedRoute>
                        <UserDashboard />
                      </ProtectedRoute>
                    }
                  />
                  <Route path="/property/:id" element={<PropertyDetail />} />
                  <Route
                    path="/messages"
                    element={
                      <ProtectedRoute>
                        <Messages />
                      </ProtectedRoute>
                    }
                  />
                  <Route path="/payment/success" element={<PaymentSuccess />} />
                  <Route path="/payment/cancel" element={<PaymentCancel />} />
                  {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
                  <Route path="*" element={<NotFound />} />
                </Routes>
              </BrowserRouter>
              </ReviewsProvider>
              </BookingProvider>
            </DebugSystemProvider>
          </AuthProvider>
        </TooltipProvider>
      </QueryClientProvider>
    </ThemeProvider>
  );
};

export default App;
