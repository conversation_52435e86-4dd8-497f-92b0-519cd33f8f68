import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { InteractivePropertyMap } from './InteractivePropertyMap';
import { PropertyCard } from './PropertyCard';
import { DistanceCalculator } from './DistanceCalculator';
import { NeighborhoodInfo } from './NeighborhoodInfo';
import {
  Map,
  List,
  Search,
  MapPin,
  Filter,
  RotateCcw,
  Maximize2,
  Navigation,
  Target,
  Layers,
  Route,
  Clock,
  DollarSign,
  Users,
  Bed,
  Bath,
  Wifi,
  Car,
  Coffee,
  Zap,
  Grid,
  SlidersHorizontal
} from 'lucide-react';
import {
  HoverAnimation,
  SlideIn,
  StaggeredAnimation
} from '@/components/ui/page-transitions';
import { QuickTooltip } from '@/components/ui/enhanced-tooltip';

interface Property {
  id: string;
  title: string;
  location: string;
  price: number;
  images: string[];
  coordinates?: {
    latitude: number;
    longitude: number;
  };
  averageRating?: number;
  reviewCount?: number;
  propertyType?: string;
  maxGuests?: number;
  bedrooms?: number;
  bathrooms?: number;
  amenities?: string[];
  available?: boolean;
}

interface MapSearchProps {
  properties: Property[];
  onPropertySelect?: (property: Property) => void;
  onSearchArea?: (bounds: any) => void;
  className?: string;
}

export const MapSearch: React.FC<MapSearchProps> = ({
  properties,
  onPropertySelect,
  onSearchArea,
  className
}) => {
  const [viewMode, setViewMode] = useState<'map' | 'list' | 'split' | 'neighborhood'>('split');
  const [selectedProperty, setSelectedProperty] = useState<Property | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredProperties, setFilteredProperties] = useState(properties);
  const [showFilters, setShowFilters] = useState(false);
  const [showNeighborhood, setShowNeighborhood] = useState(false);
  const [showDistanceCalculator, setShowDistanceCalculator] = useState(false);
  const [priceRange, setPriceRange] = useState({ min: 0, max: 10000 });
  const [selectedAmenities, setSelectedAmenities] = useState<string[]>([]);
  const [sortBy, setSortBy] = useState<'price' | 'rating' | 'distance'>('price');

  useEffect(() => {
    let filtered = properties;

    // Text search filter
    if (searchQuery.trim()) {
      filtered = filtered.filter(property =>
        property.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        property.location.toLowerCase().includes(searchQuery.toLowerCase()) ||
        property.propertyType?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Price range filter
    filtered = filtered.filter(property =>
      property.price >= priceRange.min && property.price <= priceRange.max
    );

    // Amenities filter
    if (selectedAmenities.length > 0) {
      filtered = filtered.filter(property =>
        selectedAmenities.every(amenity =>
          property.amenities?.includes(amenity)
        )
      );
    }

    // Sort results
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'price':
          return a.price - b.price;
        case 'rating':
          return (b.averageRating || 0) - (a.averageRating || 0);
        case 'distance':
          // For now, sort by price as distance calculation would need coordinates
          return a.price - b.price;
        default:
          return 0;
      }
    });

    setFilteredProperties(filtered);
  }, [searchQuery, properties, priceRange, selectedAmenities, sortBy]);

  const handlePropertySelect = (property: Property) => {
    setSelectedProperty(property);
    if (onPropertySelect) {
      onPropertySelect(property);
    }
  };

  const handleSearchArea = () => {
    if (onSearchArea) {
      // Mock bounds for the current map view
      const bounds = {
        north: -30.0,
        south: -31.0,
        east: 31.0,
        west: 30.0
      };
      onSearchArea(bounds);
    }
  };

  const getViewModeIcon = (mode: string) => {
    switch (mode) {
      case 'map': return <Map className="h-4 w-4" />;
      case 'list': return <List className="h-4 w-4" />;
      case 'split': return <Grid className="h-4 w-4" />;
      case 'neighborhood': return <MapPin className="h-4 w-4" />;
      default: return <Map className="h-4 w-4" />;
    }
  };

  const toggleAmenity = (amenity: string) => {
    setSelectedAmenities(prev =>
      prev.includes(amenity)
        ? prev.filter(a => a !== amenity)
        : [...prev, amenity]
    );
  };

  return (
    <SlideIn direction="up" delay={100}>
      <div className={`space-y-6 ${className}`}>
        {/* Enhanced Search Controls */}
        <Card className="border-0 shadow-lg">
          <CardHeader className="pb-4">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
              <CardTitle className="flex items-center gap-2">
                <Navigation className="h-5 w-5 text-sea-green-600" />
                Interactive Map Search
                <Badge variant="secondary" className="bg-sea-green-100 text-sea-green-700">
                  {filteredProperties.length} properties
                </Badge>
              </CardTitle>

              <div className="flex flex-wrap items-center gap-3">
                {/* Search Input */}
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search properties on map..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10 w-64 border-gray-200 focus:border-sea-green-500 focus:ring-sea-green-500"
                  />
                </div>

                {/* Sort Dropdown */}
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value as any)}
                  className="px-3 py-2 border border-gray-200 rounded-lg text-sm focus:border-sea-green-500 focus:ring-sea-green-500"
                >
                  <option value="price">Sort by Price</option>
                  <option value="rating">Sort by Rating</option>
                  <option value="distance">Sort by Distance</option>
                </select>

                {/* Filters Button */}
                <QuickTooltip text="Advanced Filters">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowFilters(!showFilters)}
                    className={`${showFilters ? 'bg-sea-green-50 border-sea-green-300' : ''}`}
                  >
                    <Filter className="h-4 w-4 mr-2" />
                    Filters
                  </Button>
                </QuickTooltip>

                {/* View Mode Toggle */}
                <div className="flex border rounded-lg overflow-hidden">
                  {[
                    { mode: 'map', label: 'Map', icon: Map },
                    { mode: 'list', label: 'List', icon: List },
                    { mode: 'split', label: 'Split', icon: Grid },
                    { mode: 'neighborhood', label: 'Area', icon: MapPin }
                  ].map(({ mode, label, icon: Icon }) => (
                    <QuickTooltip key={mode} text={label}>
                      <Button
                        variant={viewMode === mode ? 'default' : 'ghost'}
                        size="sm"
                        onClick={() => setViewMode(mode as any)}
                        className="rounded-none"
                      >
                        <Icon className="h-4 w-4" />
                      </Button>
                    </QuickTooltip>
                  ))}
                </div>

                {/* Search Area Button */}
                <QuickTooltip text="Search This Area">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleSearchArea}
                    className="flex items-center gap-2"
                  >
                    <Target className="h-4 w-4" />
                    Search Area
                  </Button>
                </QuickTooltip>
              </div>
            </div>

            {/* Advanced Filters Panel */}
            {showFilters && (
              <SlideIn direction="down" delay={100}>
                <div className="mt-6 p-6 bg-gray-50 rounded-lg space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    {/* Price Range */}
                    <div>
                      <label className="text-sm font-medium text-gray-700 mb-3 block">
                        Price Range (per night)
                      </label>
                      <div className="flex items-center gap-3">
                        <Input
                          type="number"
                          placeholder="Min"
                          value={priceRange.min}
                          onChange={(e) => setPriceRange(prev => ({ ...prev, min: Number(e.target.value) }))}
                          className="w-24"
                        />
                        <span className="text-gray-500">-</span>
                        <Input
                          type="number"
                          placeholder="Max"
                          value={priceRange.max}
                          onChange={(e) => setPriceRange(prev => ({ ...prev, max: Number(e.target.value) }))}
                          className="w-24"
                        />
                      </div>
                    </div>

                    {/* Property Type */}
                    <div>
                      <label className="text-sm font-medium text-gray-700 mb-3 block">
                        Property Type
                      </label>
                      <select className="w-full p-2 border border-gray-200 rounded-lg">
                        <option value="">All Types</option>
                        <option value="apartment">Apartment</option>
                        <option value="house">House</option>
                        <option value="villa">Villa</option>
                        <option value="cottage">Cottage</option>
                      </select>
                    </div>

                    {/* Quick Amenities */}
                    <div>
                      <label className="text-sm font-medium text-gray-700 mb-3 block">
                        Amenities
                      </label>
                      <div className="flex flex-wrap gap-2">
                        {['WiFi', 'Pool', 'Parking', 'Kitchen', 'Air Con', 'Pet Friendly'].map((amenity) => (
                          <Badge
                            key={amenity}
                            variant={selectedAmenities.includes(amenity) ? 'default' : 'outline'}
                            className={`cursor-pointer transition-colors ${
                              selectedAmenities.includes(amenity)
                                ? 'bg-sea-green-500 text-white'
                                : 'hover:bg-sea-green-50 hover:border-sea-green-300'
                            }`}
                            onClick={() => toggleAmenity(amenity)}
                          >
                            {amenity}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>

                  {/* Additional Tools */}
                  <div className="flex flex-wrap gap-3 pt-4 border-t border-gray-200">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setShowNeighborhood(!showNeighborhood)}
                      className="flex items-center gap-2"
                    >
                      <MapPin className="h-4 w-4" />
                      Neighborhood Info
                    </Button>

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setShowDistanceCalculator(!showDistanceCalculator)}
                      className="flex items-center gap-2"
                    >
                      <Route className="h-4 w-4" />
                      Distance Calculator
                    </Button>
                  </div>
                </div>
              </SlideIn>
            )}
          </CardHeader>
        </Card>
                  <Button
                    key={mode}
                    variant={viewMode === mode ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode(mode as any)}
                    className="rounded-none"
                    title={label}
                  >
                    {getViewModeIcon(mode)}
                  </Button>
                ))}
              </div>

              {/* Search Area Button */}
              <Button
                variant="outline"
                size="sm"
                onClick={handleSearchArea}
                className="flex items-center gap-2"
              >
                <Search className="h-4 w-4" />
                Search This Area
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

        {/* Enhanced Map and List Views */}
        <div className="space-y-6">
          {/* Neighborhood Info Panel */}
          {showNeighborhood && selectedProperty && (
            <SlideIn direction="down" delay={100}>
              <NeighborhoodInfo
                location={selectedProperty.location}
                coordinates={selectedProperty.coordinates}
              />
            </SlideIn>
          )}

          {/* Distance Calculator Panel */}
          {showDistanceCalculator && selectedProperty && (
            <SlideIn direction="down" delay={200}>
              <DistanceCalculator
                propertyLocation={selectedProperty.location}
                propertyCoordinates={selectedProperty.coordinates}
                showNearbyPlaces={true}
                maxDistance={50}
              />
            </SlideIn>
          )}

          {/* Main Content Grid */}
          <div className={`grid gap-6 ${
            viewMode === 'map' ? 'grid-cols-1' :
            viewMode === 'list' ? 'grid-cols-1' :
            viewMode === 'neighborhood' ? 'grid-cols-1' :
            'grid-cols-1 lg:grid-cols-2'
          }`}>
            {/* Enhanced Map View */}
            {(viewMode === 'map' || viewMode === 'split') && (
              <div className={viewMode === 'map' ? 'col-span-full' : ''}>
                <InteractivePropertyMap
                  properties={filteredProperties}
                  selectedProperty={selectedProperty}
                  onPropertySelect={handlePropertySelect}
                  onMapBoundsChange={onSearchArea}
                  height={viewMode === 'map' ? '700px' : '500px'}
                  showControls={true}
                  showSearch={false}
                  showNeighborhood={true}
                  enableClustering={true}
                />
              </div>
            )}

            {/* Enhanced List View */}
            {(viewMode === 'list' || viewMode === 'split') && (
              <div className={viewMode === 'list' ? 'col-span-full' : ''}>
                <Card className="border-0 shadow-lg">
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <List className="h-5 w-5 text-sea-green-600" />
                        Properties List
                        <Badge variant="secondary" className="bg-sea-green-100 text-sea-green-700">
                          {filteredProperties.length} found
                        </Badge>
                      </div>
                      <div className="flex items-center gap-2">
                        <QuickTooltip text="Reset Filters">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setSearchQuery('');
                              setPriceRange({ min: 0, max: 10000 });
                              setSelectedAmenities([]);
                            }}
                          >
                            <RotateCcw className="h-4 w-4" />
                          </Button>
                        </QuickTooltip>
                      </div>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className={`space-y-4 ${viewMode === 'split' ? 'max-h-[500px]' : 'max-h-[600px]'} overflow-y-auto custom-scrollbar`}>
                      {filteredProperties.length > 0 ? (
                        <StaggeredAnimation delay={100}>
                          {filteredProperties.map((property, index) => (
                            <HoverAnimation key={property.id} type="lift">
                              <div
                                className={`cursor-pointer transition-all duration-300 rounded-lg ${
                                  selectedProperty?.id === property.id
                                    ? 'ring-2 ring-sea-green-500 shadow-lg scale-[1.02]'
                                    : 'hover:shadow-md'
                                }`}
                                onClick={() => handlePropertySelect(property)}
                              >
                                <PropertyCard
                                  property={property}
                                  variant="list"
                                  compact={viewMode === 'split'}
                                  showDistance={true}
                                  showQuickActions={true}
                                />
                              </div>
                            </HoverAnimation>
                          ))}
                        </StaggeredAnimation>
                      ) : (
                        <div className="text-center py-12 text-gray-500">
                          <MapPin className="h-16 w-16 mx-auto mb-4 opacity-30" />
                          <h3 className="text-lg font-medium text-gray-900 mb-2">No properties found</h3>
                          <p className="text-gray-600 mb-4">
                            {searchQuery
                              ? `No results for "${searchQuery}". Try adjusting your search terms or filters.`
                              : 'Try adjusting your filters to see more properties.'
                            }
                          </p>
                          <Button
                            variant="outline"
                            onClick={() => {
                              setSearchQuery('');
                              setPriceRange({ min: 0, max: 10000 });
                              setSelectedAmenities([]);
                            }}
                          >
                            <RotateCcw className="h-4 w-4 mr-2" />
                            Reset Filters
                          </Button>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}

            {/* Neighborhood View */}
            {viewMode === 'neighborhood' && selectedProperty && (
              <div className="col-span-full space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <NeighborhoodInfo
                    location={selectedProperty.location}
                    coordinates={selectedProperty.coordinates}
                  />
                  <DistanceCalculator
                    propertyLocation={selectedProperty.location}
                    propertyCoordinates={selectedProperty.coordinates}
                    showNearbyPlaces={true}
                    maxDistance={25}
                  />
                </div>

                <InteractivePropertyMap
                  properties={[selectedProperty]}
                  selectedProperty={selectedProperty}
                  onPropertySelect={handlePropertySelect}
                  height="400px"
                  showControls={true}
                  showSearch={false}
                  showNeighborhood={true}
                  enableClustering={false}
                />
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Selected Property Details */}
      {selectedProperty && (
        <Card className="border-sea-green-200 bg-sea-green-50">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Selected Property</span>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSelectedProperty(null)}
              >
                ×
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="md:col-span-2">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {selectedProperty.title}
                </h3>
                <div className="flex items-center gap-4 text-sm text-gray-600 mb-3">
                  <div className="flex items-center gap-1">
                    <MapPin className="h-4 w-4" />
                    {selectedProperty.location}
                  </div>
                  {selectedProperty.maxGuests && (
                    <span>{selectedProperty.maxGuests} guests</span>
                  )}
                  {selectedProperty.bedrooms && (
                    <span>{selectedProperty.bedrooms} bedrooms</span>
                  )}
                  {selectedProperty.bathrooms && (
                    <span>{selectedProperty.bathrooms} bathrooms</span>
                  )}
                </div>
                <div className="flex items-center justify-between">
                  <div className="text-2xl font-bold text-sea-green-600">
                    R{selectedProperty.price.toLocaleString()}
                    <span className="text-sm font-normal text-gray-600 ml-1">/ night</span>
                  </div>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm">
                      View Details
                    </Button>
                    <Button size="sm" className="bg-sea-green-500 hover:bg-sea-green-600">
                      Book Now
                    </Button>
                  </div>
                </div>
              </div>
              <div>
                <img
                  src={selectedProperty.images[0] || "https://images.unsplash.com/photo-1721322800607-8c38375eef04?w=300&h=200&fit=crop"}
                  alt={selectedProperty.title}
                  className="w-full h-32 object-cover rounded-lg"
                />
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Map Search Tips */}
      <Card className="bg-blue-50 border-blue-200">
        <CardContent className="p-4">
          <h4 className="font-medium text-blue-900 mb-2">💡 Map Search Tips</h4>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• Click on property markers to see details</li>
            <li>• Use "Search This Area" to find properties in the current map view</li>
            <li>• Switch between Map, List, and Split views using the toggle buttons</li>
            <li>• Zoom in/out to explore different areas along the coast</li>
          </ul>
        </CardContent>
      </Card>
    </div>
    </SlideIn>
  );
};
