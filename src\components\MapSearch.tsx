import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Navigation, Search } from 'lucide-react';
import { SlideIn } from '@/components/ui/slide-in';

interface MapSearchProps {
  className?: string;
}

export const MapSearch: React.FC<MapSearchProps> = ({ className = '' }) => {
  const handleSearchArea = () => {
    console.log('Search area clicked');
  };

  const getViewModeIcon = (mode: string) => {
    return <Navigation className="h-4 w-4" />;
  };

  return (
    <SlideIn direction="up" delay={100}>
      <div className={`space-y-6 ${className}`}>
        <Card className="border-0 shadow-lg">
          <CardHeader className="pb-4">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
              <CardTitle className="flex items-center gap-2">
                <Navigation className="h-5 w-5 text-sea-green-600" />
                Interactive Map Search
              </CardTitle>
              
              <div className="flex items-center gap-2">
                {['map', 'list', 'split'].map((mode) => (
                  <Button
                    key={mode}
                    variant="outline"
                    size="sm"
                    className="flex items-center gap-1"
                  >
                    {getViewModeIcon(mode)}
                  </Button>
                ))}
              </div>

              <Button
                variant="outline"
                size="sm"
                onClick={handleSearchArea}
                className="flex items-center gap-2"
              >
                <Search className="h-4 w-4" />
                Search This Area
              </Button>
            </div>
          </CardHeader>
        </Card>

        <Card className="border-0 shadow-lg">
          <CardHeader>
            <CardTitle>Map Instructions</CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2 text-sm text-gray-600">
              <li>• Use the search controls above to filter properties</li>
              <li>• Switch between Map, List, and Split views using the toggle buttons</li>
              <li>• Zoom in/out to explore different areas along the coast</li>
            </ul>
          </CardContent>
        </Card>
      </div>
    </SlideIn>
  );
};