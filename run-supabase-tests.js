#!/usr/bin/env node

/**
 * Comprehensive Supabase Test Runner
 * Runs all Supabase integration tests and generates a complete report
 */

import fs from 'fs';
import path from 'path';
import { config } from 'dotenv';
config();

// Import test modules
import { runAllTests as runConnectionTests } from './test-supabase-connection.js';
import { runRealtimeTests } from './test-realtime-features.js';
import { runEmailTests } from './test-email-service.js';

// ANSI color codes
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, colors.green);
}

function logError(message) {
  log(`❌ ${message}`, colors.red);
}

function logWarning(message) {
  log(`⚠️  ${message}`, colors.yellow);
}

function logInfo(message) {
  log(`ℹ️  ${message}`, colors.blue);
}

// Test suite results
const suiteResults = {
  connection: { status: 'pending', duration: 0, details: null },
  realtime: { status: 'pending', duration: 0, details: null },
  email: { status: 'pending', duration: 0, details: null }
};

// Environment validation
function validateEnvironment() {
  log('\n🔍 Validating Environment...', colors.cyan);
  
  const requiredVars = [
    'SUPABASE_URL',
    'SUPABASE_ANON_KEY',
    'SUPABASE_SERVICE_ROLE_KEY'
  ];

  const missing = requiredVars.filter(varName => !process.env[varName]);
  
  if (missing.length > 0) {
    logError(`Missing required environment variables: ${missing.join(', ')}`);
    logError('Please check your .env file and ensure all Supabase credentials are set');
    return false;
  }

  logSuccess('All required environment variables are present');
  return true;
}

// Run connection tests
async function runConnectionTestSuite() {
  log('\n🔗 Running Connection Test Suite...', colors.magenta);
  const startTime = Date.now();
  
  try {
    await runConnectionTests();
    const duration = Date.now() - startTime;
    suiteResults.connection = {
      status: 'passed',
      duration,
      details: 'All connection tests passed'
    };
    logSuccess(`Connection tests completed in ${(duration / 1000).toFixed(2)}s`);
  } catch (error) {
    const duration = Date.now() - startTime;
    suiteResults.connection = {
      status: 'failed',
      duration,
      details: error.message
    };
    logError(`Connection tests failed: ${error.message}`);
  }
}

// Run real-time tests
async function runRealtimeTestSuite() {
  log('\n🔄 Running Real-time Test Suite...', colors.magenta);
  const startTime = Date.now();
  
  try {
    await runRealtimeTests();
    const duration = Date.now() - startTime;
    suiteResults.realtime = {
      status: 'passed',
      duration,
      details: 'Real-time tests completed'
    };
    logSuccess(`Real-time tests completed in ${(duration / 1000).toFixed(2)}s`);
  } catch (error) {
    const duration = Date.now() - startTime;
    suiteResults.realtime = {
      status: 'failed',
      duration,
      details: error.message
    };
    logError(`Real-time tests failed: ${error.message}`);
  }
}

// Run email tests
async function runEmailTestSuite() {
  log('\n📧 Running Email Test Suite...', colors.magenta);
  const startTime = Date.now();
  
  try {
    await runEmailTests();
    const duration = Date.now() - startTime;
    suiteResults.email = {
      status: 'passed',
      duration,
      details: 'Email tests completed'
    };
    logSuccess(`Email tests completed in ${(duration / 1000).toFixed(2)}s`);
  } catch (error) {
    const duration = Date.now() - startTime;
    suiteResults.email = {
      status: 'failed',
      duration,
      details: error.message
    };
    logError(`Email tests failed: ${error.message}`);
  }
}

// Generate comprehensive report
function generateReport() {
  const timestamp = new Date().toISOString();
  const totalDuration = Object.values(suiteResults).reduce((sum, result) => sum + result.duration, 0);
  
  const report = {
    timestamp,
    totalDuration: `${(totalDuration / 1000).toFixed(2)}s`,
    environment: {
      supabaseUrl: process.env.SUPABASE_URL,
      nodeVersion: process.version,
      platform: process.platform,
      cwd: process.cwd()
    },
    testSuites: suiteResults,
    summary: {
      total: Object.keys(suiteResults).length,
      passed: Object.values(suiteResults).filter(r => r.status === 'passed').length,
      failed: Object.values(suiteResults).filter(r => r.status === 'failed').length,
      pending: Object.values(suiteResults).filter(r => r.status === 'pending').length
    },
    recommendations: []
  };

  // Add recommendations based on results
  if (suiteResults.connection.status === 'failed') {
    report.recommendations.push('Fix database connection issues before proceeding with migration');
  }
  
  if (suiteResults.realtime.status === 'failed') {
    report.recommendations.push('Configure real-time features in Supabase dashboard');
    report.recommendations.push('Enable real-time for required tables');
  }
  
  if (suiteResults.email.status === 'failed') {
    report.recommendations.push('Configure email templates in Supabase dashboard');
    report.recommendations.push('Verify SMTP settings if using custom email service');
  }

  // Save report
  const reportPath = path.join(__dirname, 'supabase-integration-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  
  return { report, reportPath };
}

// Print final summary
function printSummary(report, reportPath) {
  log('\n' + '='.repeat(60), colors.cyan);
  log('🏁 SUPABASE INTEGRATION TEST SUMMARY', colors.bright);
  log('='.repeat(60), colors.cyan);
  
  // Test suite results
  Object.entries(suiteResults).forEach(([suite, result]) => {
    const icon = result.status === 'passed' ? '✅' : result.status === 'failed' ? '❌' : '⏳';
    const color = result.status === 'passed' ? colors.green : result.status === 'failed' ? colors.red : colors.yellow;
    log(`${icon} ${suite.toUpperCase()} Tests: ${result.status.toUpperCase()}`, color);
  });

  log('', colors.reset);
  
  // Overall statistics
  logInfo(`Total Duration: ${report.totalDuration}`);
  logInfo(`Test Suites: ${report.summary.total}`);
  logSuccess(`Passed: ${report.summary.passed}`);
  logError(`Failed: ${report.summary.failed}`);
  
  if (report.summary.pending > 0) {
    logWarning(`Pending: ${report.summary.pending}`);
  }

  // Recommendations
  if (report.recommendations.length > 0) {
    log('\n📋 RECOMMENDATIONS:', colors.yellow);
    report.recommendations.forEach(rec => {
      log(`• ${rec}`, colors.yellow);
    });
  }

  // Next steps
  log('\n🚀 NEXT STEPS:', colors.cyan);
  
  if (report.summary.failed === 0) {
    log('• Your Supabase integration is ready!', colors.green);
    log('• You can proceed with the migration from MySQL to Supabase', colors.green);
    log('• Follow the migration guide in supabase-upgrade.md', colors.blue);
  } else {
    log('• Fix the failed tests before proceeding', colors.red);
    log('• Check the detailed report for specific issues', colors.yellow);
    log('• Refer to the Supabase documentation for configuration help', colors.blue);
  }

  log(`\n📄 Detailed report saved to: ${reportPath}`, colors.blue);
  
  // Configuration check
  log('\n⚙️  CONFIGURATION STATUS:', colors.cyan);
  log(`• Database: ${suiteResults.connection.status === 'passed' ? 'READY' : 'NEEDS SETUP'}`, 
      suiteResults.connection.status === 'passed' ? colors.green : colors.red);
  log(`• Real-time: ${suiteResults.realtime.status === 'passed' ? 'READY' : 'NEEDS SETUP'}`, 
      suiteResults.realtime.status === 'passed' ? colors.green : colors.yellow);
  log(`• Email: ${suiteResults.email.status === 'passed' ? 'READY' : 'NEEDS SETUP'}`, 
      suiteResults.email.status === 'passed' ? colors.green : colors.yellow);
}

// Main test runner
async function runAllSupabaseTests() {
  log('🚀 SUPABASE INTEGRATION TEST SUITE', colors.bright);
  log('='.repeat(60), colors.cyan);
  log('Testing all Supabase services and configurations...', colors.blue);
  
  const overallStartTime = Date.now();

  // Validate environment first
  if (!validateEnvironment()) {
    process.exit(1);
  }

  // Run all test suites
  await runConnectionTestSuite();
  await runRealtimeTestSuite();
  await runEmailTestSuite();

  const overallDuration = Date.now() - overallStartTime;
  
  // Generate and save report
  const { report, reportPath } = generateReport();
  report.overallDuration = `${(overallDuration / 1000).toFixed(2)}s`;
  
  // Print summary
  printSummary(report, reportPath);

  // Exit with appropriate code
  const hasFailures = report.summary.failed > 0;
  process.exit(hasFailures ? 1 : 0);
}

// Handle uncaught errors
process.on('unhandledRejection', (reason, promise) => {
  logError(`Unhandled Rejection at: ${promise}, reason: ${reason}`);
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  logError(`Uncaught Exception: ${error.message}`);
  console.error(error);
  process.exit(1);
});

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runAllSupabaseTests().catch(error => {
    logError(`Test runner error: ${error.message}`);
    console.error(error);
    process.exit(1);
  });
}

export {
  runAllSupabaseTests,
  suiteResults
};
