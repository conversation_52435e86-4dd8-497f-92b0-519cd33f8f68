import React, { Component, ReactNode, ErrorInfo } from 'react';
import { CapturedError } from '@/hooks/useErrorCapture';

interface ErrorCaptureProps {
  children: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  fallback?: (error: Error, errorInfo: ErrorInfo, retry: () => void) => ReactNode;
  isolate?: boolean;
}

interface ErrorCaptureState {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string | null;
}

export class ErrorCapture extends Component<ErrorCaptureProps, ErrorCaptureState> {
  private retryTimeoutId: number | null = null;

  constructor(props: ErrorCaptureProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null
    };
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorCaptureState> {
    return {
      hasError: true,
      error,
      errorId: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({ errorInfo });

    // Call the onError callback if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Log error to console for debugging
    console.error('ErrorCapture caught an error:', error);
    console.error('Error Info:', errorInfo);

    // Dispatch custom event for debug system
    const capturedError: Omit<CapturedError, 'id' | 'timestamp' | 'userAgent' | 'url'> = {
      type: 'error',
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      source: 'react'
    };

    window.dispatchEvent(new CustomEvent('debug-react-error', {
      detail: { error, errorInfo, capturedError }
    }));
  }

  componentDidUpdate(prevProps: ErrorCaptureProps, prevState: ErrorCaptureState) {
    // If we had an error and now we don't, clear any retry timeout
    if (prevState.hasError && !this.state.hasError) {
      if (this.retryTimeoutId) {
        clearTimeout(this.retryTimeoutId);
        this.retryTimeoutId = null;
      }
    }
  }

  componentWillUnmount() {
    if (this.retryTimeoutId) {
      clearTimeout(this.retryTimeoutId);
    }
  }

  retry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null
    });
  };

  autoRetry = (delay: number = 5000) => {
    if (this.retryTimeoutId) {
      clearTimeout(this.retryTimeoutId);
    }

    this.retryTimeoutId = window.setTimeout(() => {
      this.retry();
    }, delay);
  };

  render() {
    if (this.state.hasError && this.state.error && this.state.errorInfo) {
      // If a custom fallback is provided, use it
      if (this.props.fallback) {
        return this.props.fallback(this.state.error, this.state.errorInfo, this.retry);
      }

      // Default error fallback UI
      return (
        <div className="error-boundary-fallback p-6 bg-red-50 border border-red-200 rounded-lg m-4">
          <div className="flex items-start gap-4">
            <div className="flex-shrink-0">
              <div className="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
            </div>
            
            <div className="flex-1 min-w-0">
              <h3 className="text-lg font-semibold text-red-800 mb-2">
                Something went wrong
              </h3>
              
              <p className="text-red-700 mb-4">
                An error occurred while rendering this component. The error has been logged for debugging.
              </p>

              <details className="mb-4">
                <summary className="cursor-pointer text-sm font-medium text-red-800 hover:text-red-900">
                  Error Details
                </summary>
                <div className="mt-2 p-3 bg-red-100 rounded border text-sm font-mono">
                  <div className="mb-2">
                    <strong>Error:</strong> {this.state.error.message}
                  </div>
                  {this.state.error.stack && (
                    <div className="mb-2">
                      <strong>Stack Trace:</strong>
                      <pre className="whitespace-pre-wrap text-xs mt-1 overflow-x-auto">
                        {this.state.error.stack}
                      </pre>
                    </div>
                  )}
                  {this.state.errorInfo.componentStack && (
                    <div>
                      <strong>Component Stack:</strong>
                      <pre className="whitespace-pre-wrap text-xs mt-1 overflow-x-auto">
                        {this.state.errorInfo.componentStack}
                      </pre>
                    </div>
                  )}
                </div>
              </details>

              <div className="flex gap-3">
                <button
                  onClick={this.retry}
                  className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors"
                >
                  Try Again
                </button>
                
                <button
                  onClick={() => this.autoRetry(3000)}
                  className="px-4 py-2 bg-red-100 text-red-700 rounded hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors"
                >
                  Auto Retry in 3s
                </button>

                <button
                  onClick={() => window.location.reload()}
                  className="px-4 py-2 bg-gray-100 text-gray-700 rounded hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors"
                >
                  Reload Page
                </button>
              </div>

              {process.env.NODE_ENV === 'development' && (
                <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded">
                  <p className="text-sm text-yellow-800">
                    <strong>Development Mode:</strong> This error boundary is active. 
                    In production, users would see a more user-friendly error message.
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// Higher-order component for wrapping components with error boundaries
export const withErrorCapture = <P extends object>(
  WrappedComponent: React.ComponentType<P>,
  options?: {
    fallback?: (error: Error, errorInfo: ErrorInfo, retry: () => void) => ReactNode;
    onError?: (error: Error, errorInfo: ErrorInfo) => void;
    isolate?: boolean;
  }
) => {
  const WithErrorCaptureComponent = (props: P) => {
    return (
      <ErrorCapture
        fallback={options?.fallback}
        onError={options?.onError}
        isolate={options?.isolate}
      >
        <WrappedComponent {...props} />
      </ErrorCapture>
    );
  };

  WithErrorCaptureComponent.displayName = `withErrorCapture(${WrappedComponent.displayName || WrappedComponent.name})`;
  
  return WithErrorCaptureComponent;
};

// Hook for manual error reporting
export const useErrorReporting = () => {
  const reportError = React.useCallback((error: Error, context?: string) => {
    console.error('Manual error report:', error);
    
    const capturedError: Omit<CapturedError, 'id' | 'timestamp' | 'userAgent' | 'url'> = {
      type: 'error',
      message: error.message,
      stack: error.stack,
      source: context || 'manual'
    };

    window.dispatchEvent(new CustomEvent('debug-manual-error', {
      detail: { error, capturedError, context }
    }));
  }, []);

  const reportWarning = React.useCallback((message: string, context?: string) => {
    console.warn('Manual warning report:', message);
    
    const capturedError: Omit<CapturedError, 'id' | 'timestamp' | 'userAgent' | 'url'> = {
      type: 'warning',
      message,
      source: context || 'manual'
    };

    window.dispatchEvent(new CustomEvent('debug-manual-warning', {
      detail: { message, capturedError, context }
    }));
  }, []);

  const reportInfo = React.useCallback((message: string, context?: string) => {
    console.info('Manual info report:', message);
    
    const capturedError: Omit<CapturedError, 'id' | 'timestamp' | 'userAgent' | 'url'> = {
      type: 'info',
      message,
      source: context || 'manual'
    };

    window.dispatchEvent(new CustomEvent('debug-manual-info', {
      detail: { message, capturedError, context }
    }));
  }, []);

  return {
    reportError,
    reportWarning,
    reportInfo
  };
};
