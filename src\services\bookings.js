// Bookings API Service
const API_BASE_URL = 'http://localhost:3001/api';

class BookingsService {
  // Get authentication token from localStorage
  getAuthToken() {
    return localStorage.getItem('stayfinder_token');
  }

  // Get authorization headers
  getAuthHeaders() {
    const token = this.getAuthToken();
    return {
      'Content-Type': 'application/json',
      ...(token && { 'Authorization': `Bearer ${token}` })
    };
  }

  // Create a new booking
  async createBooking(bookingData) {
    try {
      const response = await fetch(`${API_BASE_URL}/bookings`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(bookingData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error creating booking:', error);
      throw error;
    }
  }

  // Get user's bookings
  async getUserBookings() {
    try {
      const response = await fetch(`${API_BASE_URL}/bookings`, {
        headers: this.getAuthHeaders()
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error fetching user bookings:', error);
      throw error;
    }
  }

  // Get single booking by ID
  async getBookingById(id) {
    try {
      const response = await fetch(`${API_BASE_URL}/bookings/${id}`, {
        headers: this.getAuthHeaders()
      });

      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('Booking not found');
        }
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error fetching booking:', error);
      throw error;
    }
  }

  // Update booking status
  async updateBookingStatus(id, status) {
    try {
      const response = await fetch(`${API_BASE_URL}/bookings/${id}/status`, {
        method: 'PATCH',
        headers: this.getAuthHeaders(),
        body: JSON.stringify({ status })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error updating booking status:', error);
      throw error;
    }
  }

  // Get upcoming bookings
  async getUpcomingBookings() {
    try {
      const response = await fetch(`${API_BASE_URL}/bookings/upcoming/list`, {
        headers: this.getAuthHeaders()
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error fetching upcoming bookings:', error);
      throw error;
    }
  }

  // Get booking statistics
  async getBookingStats() {
    try {
      const response = await fetch(`${API_BASE_URL}/bookings/stats/summary`, {
        headers: this.getAuthHeaders()
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error fetching booking stats:', error);
      throw error;
    }
  }

  // Calculate booking cost
  async calculateBookingCost(propertyId, checkInDate, checkOutDate) {
    try {
      const response = await fetch(`${API_BASE_URL}/bookings/calculate-cost`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify({
          propertyId,
          checkInDate,
          checkOutDate
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error calculating booking cost:', error);
      throw error;
    }
  }

  // Transform booking data for frontend
  transformBooking(backendBooking) {
    return {
      id: backendBooking.id,
      propertyId: backendBooking.propertyId,
      propertyTitle: backendBooking.propertyTitle,
      propertyLocation: backendBooking.propertyLocation,
      checkInDate: backendBooking.checkInDate,
      checkOutDate: backendBooking.checkOutDate,
      guestCount: backendBooking.guestCount,
      totalAmount: backendBooking.totalAmount,
      bookingStatus: backendBooking.bookingStatus,
      paymentStatus: backendBooking.paymentStatus,
      specialRequests: backendBooking.specialRequests,
      guest: backendBooking.guest,
      owner: backendBooking.owner,
      isOwner: backendBooking.isOwner,
      isGuest: backendBooking.isGuest,
      costBreakdown: backendBooking.costBreakdown,
      createdAt: backendBooking.createdAt,
      updatedAt: backendBooking.updatedAt
    };
  }

  // Get booking status color for UI
  getStatusColor(status) {
    switch (status) {
      case 'confirmed':
        return 'green';
      case 'pending':
        return 'yellow';
      case 'cancelled':
        return 'red';
      case 'completed':
        return 'blue';
      default:
        return 'gray';
    }
  }

  // Get payment status color for UI
  getPaymentStatusColor(status) {
    switch (status) {
      case 'paid':
        return 'green';
      case 'pending':
        return 'yellow';
      case 'refunded':
        return 'blue';
      default:
        return 'gray';
    }
  }

  // Format booking status for display
  formatBookingStatus(status) {
    switch (status) {
      case 'confirmed':
        return 'Confirmed';
      case 'pending':
        return 'Pending';
      case 'cancelled':
        return 'Cancelled';
      case 'completed':
        return 'Completed';
      default:
        return status;
    }
  }

  // Format payment status for display
  formatPaymentStatus(status) {
    switch (status) {
      case 'paid':
        return 'Paid';
      case 'pending':
        return 'Pending Payment';
      case 'refunded':
        return 'Refunded';
      default:
        return status;
    }
  }

  // Check if booking can be cancelled
  canCancelBooking(booking) {
    const checkInDate = new Date(booking.checkInDate);
    const now = new Date();
    const hoursUntilCheckIn = (checkInDate - now) / (1000 * 60 * 60);
    
    return booking.bookingStatus === 'pending' || 
           (booking.bookingStatus === 'confirmed' && hoursUntilCheckIn > 24);
  }

  // Check if booking can be modified
  canModifyBooking(booking) {
    const checkInDate = new Date(booking.checkInDate);
    const now = new Date();
    const hoursUntilCheckIn = (checkInDate - now) / (1000 * 60 * 60);
    
    return booking.bookingStatus === 'pending' && hoursUntilCheckIn > 48;
  }

  // Calculate days until check-in
  getDaysUntilCheckIn(checkInDate) {
    const checkIn = new Date(checkInDate);
    const now = new Date();
    const diffTime = checkIn - now;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  }
}

export default new BookingsService();
