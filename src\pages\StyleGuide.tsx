import React from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  Home, 
  Search, 
  Heart, 
  Star, 
  MapPin, 
  Calendar,
  Settings,
  User,
  Plus,
  Check
} from 'lucide-react';
import { Header } from '@/components/Header';
import { 
  PageTransition, 
  SlideIn, 
  StaggeredAnimation 
} from '@/components/ui/page-transitions';
import { EnhancedInput, EnhancedTextarea } from '@/components/ui/enhanced-form';
import { QuickTooltip } from '@/components/ui/enhanced-tooltip';
import { EnhancedBreadcrumb } from '@/components/ui/enhanced-breadcrumb';

export const StyleGuide: React.FC = () => {
  const colors = [
    { name: 'Sea Green', class: 'bg-sea-green-500', hex: '#14b8a6' },
    { name: 'Ocean Blue', class: 'bg-ocean-blue-500', hex: '#3b82f6' },
    { name: 'Sunset Orange', class: 'bg-sunset-orange-500', hex: '#f97316' },
    { name: 'Gray', class: 'bg-gray-500', hex: '#6b7280' },
  ];

  const typography = [
    { name: 'Heading 1', class: 'text-4xl font-bold', text: 'The quick brown fox' },
    { name: 'Heading 2', class: 'text-3xl font-bold', text: 'The quick brown fox' },
    { name: 'Heading 3', class: 'text-2xl font-semibold', text: 'The quick brown fox' },
    { name: 'Heading 4', class: 'text-xl font-semibold', text: 'The quick brown fox' },
    { name: 'Body Large', class: 'text-lg', text: 'The quick brown fox jumps over the lazy dog' },
    { name: 'Body', class: 'text-base', text: 'The quick brown fox jumps over the lazy dog' },
    { name: 'Body Small', class: 'text-sm', text: 'The quick brown fox jumps over the lazy dog' },
    { name: 'Caption', class: 'text-xs text-gray-600', text: 'The quick brown fox jumps over the lazy dog' },
  ];

  return (
    <PageTransition>
      <div className="min-h-screen bg-gray-50">
        <Header />
        
        <div className="container mx-auto px-4 py-8">
          <SlideIn direction="up" delay={100}>
            <div className="mb-8">
              <EnhancedBreadcrumb 
                items={[
                  { label: 'Home', href: '/', icon: <Home className="h-4 w-4" /> },
                  { label: 'Style Guide', current: true }
                ]}
              />
              <h1 className="text-4xl font-bold text-gray-900 mt-4 mb-2">
                StayFinder Style Guide
              </h1>
              <p className="text-lg text-gray-600">
                Design system and component library for the KZN South Coast StayFinder platform
              </p>
            </div>
          </SlideIn>

          <div className="space-y-12">
            {/* Colors */}
            <SlideIn direction="up" delay={200}>
              <Card>
                <CardHeader>
                  <CardTitle>Color Palette</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                    {colors.map((color, index) => (
                      <div key={index} className="text-center">
                        <div className={`w-20 h-20 rounded-xl ${color.class} mx-auto mb-3 shadow-lg`}></div>
                        <h3 className="font-semibold text-gray-900">{color.name}</h3>
                        <p className="text-sm text-gray-600">{color.hex}</p>
                      </div>
                    ))}
                  </div>
                  
                  <div className="mt-8">
                    <h3 className="text-lg font-semibold mb-4">Gradient Examples</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="h-16 bg-gradient-to-r from-sea-green-500 to-ocean-blue-500 rounded-lg flex items-center justify-center text-white font-semibold">
                        Primary Gradient
                      </div>
                      <div className="h-16 bg-gradient-to-r from-sunset-orange-500 to-red-500 rounded-lg flex items-center justify-center text-white font-semibold">
                        Accent Gradient
                      </div>
                      <div className="h-16 bg-gradient-to-r from-gray-100 to-white rounded-lg flex items-center justify-center text-gray-700 font-semibold border">
                        Subtle Gradient
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </SlideIn>

            {/* Typography */}
            <SlideIn direction="up" delay={300}>
              <Card>
                <CardHeader>
                  <CardTitle>Typography</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {typography.map((type, index) => (
                      <div key={index} className="flex items-center justify-between border-b border-gray-100 pb-4">
                        <div className="flex-1">
                          <p className={type.class}>{type.text}</p>
                        </div>
                        <div className="text-right text-sm text-gray-500 ml-4">
                          <p className="font-medium">{type.name}</p>
                          <p className="text-xs">{type.class}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </SlideIn>

            {/* Buttons */}
            <SlideIn direction="up" delay={400}>
              <Card>
                <CardHeader>
                  <CardTitle>Buttons</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-8">
                    <div>
                      <h3 className="text-lg font-semibold mb-4">Button Variants</h3>
                      <div className="flex flex-wrap gap-4">
                        <Button>Default</Button>
                        <Button variant="outline">Outline</Button>
                        <Button variant="secondary">Secondary</Button>
                        <Button variant="ghost">Ghost</Button>
                        <Button variant="success">Success</Button>
                        <Button variant="warning">Warning</Button>
                        <Button variant="info">Info</Button>
                        <Button variant="premium">Premium</Button>
                      </div>
                    </div>
                    
                    <div>
                      <h3 className="text-lg font-semibold mb-4">Button Sizes</h3>
                      <div className="flex flex-wrap items-center gap-4">
                        <Button size="xs">Extra Small</Button>
                        <Button size="sm">Small</Button>
                        <Button size="default">Default</Button>
                        <Button size="lg">Large</Button>
                        <Button size="xl">Extra Large</Button>
                      </div>
                    </div>
                    
                    <div>
                      <h3 className="text-lg font-semibold mb-4">Icon Buttons</h3>
                      <div className="flex flex-wrap gap-4">
                        <Button size="icon"><Heart className="h-4 w-4" /></Button>
                        <Button size="icon-sm" variant="outline"><Star className="h-4 w-4" /></Button>
                        <Button size="icon-lg" variant="success"><Check className="h-4 w-4" /></Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </SlideIn>

            {/* Form Components */}
            <SlideIn direction="up" delay={500}>
              <Card>
                <CardHeader>
                  <CardTitle>Form Components</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div className="space-y-4">
                      <EnhancedInput
                        label="Email Address"
                        type="email"
                        placeholder="Enter your email"
                        validation={{ required: true, email: true }}
                        icon={<User className="h-4 w-4" />}
                        hint="We'll never share your email"
                      />
                      
                      <EnhancedInput
                        label="Password"
                        type="password"
                        placeholder="Enter your password"
                        validation={{ required: true, minLength: 8 }}
                      />
                      
                      <EnhancedInput
                        label="Phone Number"
                        type="tel"
                        placeholder="+27 123 456 789"
                        validation={{ phone: true }}
                        success="Phone number verified"
                      />
                    </div>
                    
                    <div>
                      <EnhancedTextarea
                        label="Message"
                        placeholder="Tell us about your stay..."
                        validation={{ required: true, minLength: 10 }}
                        showCharCount
                        maxLength={500}
                        rows={6}
                        hint="Describe your experience in detail"
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </SlideIn>

            {/* Components */}
            <SlideIn direction="up" delay={600}>
              <Card>
                <CardHeader>
                  <CardTitle>UI Components</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-8">
                    <div>
                      <h3 className="text-lg font-semibold mb-4">Badges</h3>
                      <div className="flex flex-wrap gap-2">
                        <Badge>Default</Badge>
                        <Badge variant="secondary">Secondary</Badge>
                        <Badge variant="destructive">Destructive</Badge>
                        <Badge variant="outline">Outline</Badge>
                      </div>
                    </div>
                    
                    <div>
                      <h3 className="text-lg font-semibold mb-4">Avatars</h3>
                      <div className="flex items-center gap-4">
                        <Avatar className="h-8 w-8">
                          <AvatarFallback>JD</AvatarFallback>
                        </Avatar>
                        <Avatar className="h-12 w-12">
                          <AvatarFallback className="bg-sea-green-500 text-white">SF</AvatarFallback>
                        </Avatar>
                        <Avatar className="h-16 w-16">
                          <AvatarFallback className="bg-gradient-to-br from-sea-green-500 to-ocean-blue-500 text-white text-xl">KZN</AvatarFallback>
                        </Avatar>
                      </div>
                    </div>
                    
                    <div>
                      <h3 className="text-lg font-semibold mb-4">Tooltips</h3>
                      <div className="flex gap-4">
                        <QuickTooltip text="This is a helpful tooltip">
                          <Button variant="outline">Hover me</Button>
                        </QuickTooltip>
                        <QuickTooltip text="Another tooltip example" position="bottom">
                          <Button variant="outline">Bottom tooltip</Button>
                        </QuickTooltip>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </SlideIn>

            {/* Icons */}
            <SlideIn direction="up" delay={700}>
              <Card>
                <CardHeader>
                  <CardTitle>Icons</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-4 md:grid-cols-8 gap-6">
                    {[Home, Search, Heart, Star, MapPin, Calendar, Settings, User, Plus].map((Icon, index) => (
                      <div key={index} className="text-center">
                        <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                          <Icon className="h-6 w-6 text-gray-600" />
                        </div>
                        <p className="text-xs text-gray-600">{Icon.name}</p>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </SlideIn>
          </div>
        </div>
      </div>
    </PageTransition>
  );
};
