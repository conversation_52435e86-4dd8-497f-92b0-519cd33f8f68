import { useState, useEffect, useCallback } from 'react';

interface GeolocationState {
  latitude: number | null;
  longitude: number | null;
  accuracy: number | null;
  error: string | null;
  loading: boolean;
  supported: boolean;
}

interface GeolocationOptions {
  enableHighAccuracy?: boolean;
  timeout?: number;
  maximumAge?: number;
  watch?: boolean;
}

interface LocationInfo {
  coordinates: [number, number];
  city?: string;
  province?: string;
  country?: string;
  address?: string;
  accuracy: number;
}

export const useGeolocation = (options: GeolocationOptions = {}) => {
  const {
    enableHighAccuracy = true,
    timeout = 10000,
    maximumAge = 300000, // 5 minutes
    watch = false
  } = options;

  const [state, setState] = useState<GeolocationState>({
    latitude: null,
    longitude: null,
    accuracy: null,
    error: null,
    loading: false,
    supported: 'geolocation' in navigator
  });

  const [watchId, setWatchId] = useState<number | null>(null);

  const onSuccess = useCallback((position: GeolocationPosition) => {
    setState({
      latitude: position.coords.latitude,
      longitude: position.coords.longitude,
      accuracy: position.coords.accuracy,
      error: null,
      loading: false,
      supported: true
    });
  }, []);

  const onError = useCallback((error: GeolocationPositionError) => {
    let errorMessage = 'Unknown error occurred';
    
    switch (error.code) {
      case error.PERMISSION_DENIED:
        errorMessage = 'Location access denied by user';
        break;
      case error.POSITION_UNAVAILABLE:
        errorMessage = 'Location information unavailable';
        break;
      case error.TIMEOUT:
        errorMessage = 'Location request timed out';
        break;
    }

    setState(prev => ({
      ...prev,
      error: errorMessage,
      loading: false
    }));
  }, []);

  const getCurrentPosition = useCallback(() => {
    if (!state.supported) {
      setState(prev => ({
        ...prev,
        error: 'Geolocation is not supported by this browser',
        loading: false
      }));
      return;
    }

    setState(prev => ({ ...prev, loading: true, error: null }));

    navigator.geolocation.getCurrentPosition(
      onSuccess,
      onError,
      {
        enableHighAccuracy,
        timeout,
        maximumAge
      }
    );
  }, [state.supported, onSuccess, onError, enableHighAccuracy, timeout, maximumAge]);

  const startWatching = useCallback(() => {
    if (!state.supported || watchId !== null) return;

    setState(prev => ({ ...prev, loading: true, error: null }));

    const id = navigator.geolocation.watchPosition(
      onSuccess,
      onError,
      {
        enableHighAccuracy,
        timeout,
        maximumAge
      }
    );

    setWatchId(id);
  }, [state.supported, watchId, onSuccess, onError, enableHighAccuracy, timeout, maximumAge]);

  const stopWatching = useCallback(() => {
    if (watchId !== null) {
      navigator.geolocation.clearWatch(watchId);
      setWatchId(null);
    }
  }, [watchId]);

  useEffect(() => {
    if (watch) {
      startWatching();
    }

    return () => {
      stopWatching();
    };
  }, [watch, startWatching, stopWatching]);

  return {
    ...state,
    getCurrentPosition,
    startWatching,
    stopWatching,
    isWatching: watchId !== null
  };
};

// Hook for reverse geocoding (coordinates to address)
export const useReverseGeocode = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const reverseGeocode = useCallback(async (latitude: number, longitude: number): Promise<LocationInfo | null> => {
    setLoading(true);
    setError(null);

    try {
      // Using OpenStreetMap Nominatim API for reverse geocoding
      const response = await fetch(
        `https://nominatim.openstreetmap.org/reverse?format=json&lat=${latitude}&lon=${longitude}&zoom=10&addressdetails=1`,
        {
          headers: {
            'User-Agent': 'StayFinder/1.0'
          }
        }
      );

      if (!response.ok) {
        throw new Error('Failed to fetch location information');
      }

      const data = await response.json();

      const locationInfo: LocationInfo = {
        coordinates: [longitude, latitude],
        accuracy: 100, // Approximate accuracy for reverse geocoding
        address: data.display_name,
        city: data.address?.city || data.address?.town || data.address?.village,
        province: data.address?.state || data.address?.province,
        country: data.address?.country
      };

      return locationInfo;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to get location information';
      setError(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    reverseGeocode,
    loading,
    error
  };
};

// Hook for calculating distance between two points
export const useDistanceCalculator = () => {
  const calculateDistance = useCallback((
    lat1: number,
    lon1: number,
    lat2: number,
    lon2: number,
    unit: 'km' | 'miles' = 'km'
  ): number => {
    const R = unit === 'km' ? 6371 : 3959; // Earth's radius in km or miles
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLon = (lon2 - lon1) * Math.PI / 180;
    
    const a = 
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
      Math.sin(dLon / 2) * Math.sin(dLon / 2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c;
    
    return Math.round(distance * 100) / 100; // Round to 2 decimal places
  }, []);

  const findNearestProperties = useCallback((
    userLat: number,
    userLon: number,
    properties: Array<{ id: string; latitude: number; longitude: number; [key: string]: any }>,
    maxDistance?: number,
    unit: 'km' | 'miles' = 'km'
  ) => {
    return properties
      .map(property => ({
        ...property,
        distance: calculateDistance(userLat, userLon, property.latitude, property.longitude, unit)
      }))
      .filter(property => maxDistance ? property.distance <= maxDistance : true)
      .sort((a, b) => a.distance - b.distance);
  }, [calculateDistance]);

  return {
    calculateDistance,
    findNearestProperties
  };
};

// Hook for location-based search
export const useLocationSearch = () => {
  const { getCurrentPosition, latitude, longitude, loading: geoLoading, error: geoError } = useGeolocation();
  const { reverseGeocode, loading: reverseLoading } = useReverseGeocode();
  const { findNearestProperties } = useDistanceCalculator();

  const [currentLocation, setCurrentLocation] = useState<LocationInfo | null>(null);
  const [searchRadius, setSearchRadius] = useState(25); // Default 25km radius

  const searchNearby = useCallback(async (
    properties: Array<{ id: string; latitude: number; longitude: number; [key: string]: any }>,
    radius?: number
  ) => {
    if (!latitude || !longitude) {
      throw new Error('Location not available');
    }

    const searchRadiusKm = radius || searchRadius;
    const nearbyProperties = findNearestProperties(
      latitude,
      longitude,
      properties,
      searchRadiusKm
    );

    return nearbyProperties;
  }, [latitude, longitude, searchRadius, findNearestProperties]);

  const getCurrentLocationInfo = useCallback(async () => {
    if (!latitude || !longitude) {
      throw new Error('Location coordinates not available');
    }

    const locationInfo = await reverseGeocode(latitude, longitude);
    if (locationInfo) {
      setCurrentLocation(locationInfo);
    }
    return locationInfo;
  }, [latitude, longitude, reverseGeocode]);

  const requestLocation = useCallback(async () => {
    getCurrentPosition();
  }, [getCurrentPosition]);

  return {
    // Location state
    latitude,
    longitude,
    currentLocation,
    
    // Loading states
    loading: geoLoading || reverseLoading,
    geoLoading,
    reverseLoading,
    
    // Errors
    error: geoError,
    
    // Search configuration
    searchRadius,
    setSearchRadius,
    
    // Actions
    requestLocation,
    getCurrentLocationInfo,
    searchNearby,
    
    // Utilities
    hasLocation: !!(latitude && longitude)
  };
};

// South African cities with coordinates for fallback
export const southAfricanCities = [
  { name: 'Cape Town', province: 'Western Cape', latitude: -33.9249, longitude: 18.4241 },
  { name: 'Johannesburg', province: 'Gauteng', latitude: -26.2041, longitude: 28.0473 },
  { name: 'Durban', province: 'KwaZulu-Natal', latitude: -29.8587, longitude: 31.0218 },
  { name: 'Pretoria', province: 'Gauteng', latitude: -25.7479, longitude: 28.2293 },
  { name: 'Port Elizabeth', province: 'Eastern Cape', latitude: -33.9608, longitude: 25.6022 },
  { name: 'Bloemfontein', province: 'Free State', latitude: -29.0852, longitude: 26.1596 },
  { name: 'East London', province: 'Eastern Cape', latitude: -33.0153, longitude: 27.9116 },
  { name: 'Nelspruit', province: 'Mpumalanga', latitude: -25.4753, longitude: 30.9694 },
  { name: 'Polokwane', province: 'Limpopo', latitude: -23.9045, longitude: 29.4689 },
  { name: 'Kimberley', province: 'Northern Cape', latitude: -28.7282, longitude: 24.7499 },
  { name: 'Mafikeng', province: 'North West', latitude: -25.8075, longitude: 25.6447 },
  { name: 'Stellenbosch', province: 'Western Cape', latitude: -33.9321, longitude: 18.8602 },
  { name: 'Hermanus', province: 'Western Cape', latitude: -34.4187, longitude: 19.2345 },
  { name: 'Knysna', province: 'Western Cape', latitude: -34.0361, longitude: 23.0471 },
  { name: 'Plettenberg Bay', province: 'Western Cape', latitude: -34.0527, longitude: 23.3716 }
];

// Utility function to get nearest city
export const getNearestCity = (latitude: number, longitude: number) => {
  let nearestCity = southAfricanCities[0];
  let minDistance = Infinity;

  southAfricanCities.forEach(city => {
    const distance = Math.sqrt(
      Math.pow(city.latitude - latitude, 2) + Math.pow(city.longitude - longitude, 2)
    );
    
    if (distance < minDistance) {
      minDistance = distance;
      nearestCity = city;
    }
  });

  return nearestCity;
};
