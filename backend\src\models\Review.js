const { executeQuery, findOne, findMany } = require('../utils/database');

class Review {
  static async create(reviewData) {
    const query = `
      INSERT INTO reviews (
        id, booking_id, reviewer_id, property_id, rating, comment
      ) VALUES (?, ?, ?, ?, ?, ?)
    `;
    
    const params = [
      reviewData.id,
      reviewData.booking_id,
      reviewData.reviewer_id,
      reviewData.property_id,
      reviewData.rating,
      reviewData.comment
    ];
    
    return await executeQuery(query, params);
  }

  static async findById(id) {
    const query = `
      SELECT 
        r.*,
        u.first_name as reviewer_first_name,
        u.last_name as reviewer_last_name,
        p.title as property_title,
        p.location as property_location
      FROM reviews r
      LEFT JOIN users u ON r.reviewer_id = u.id
      LEFT JOIN properties p ON r.property_id = p.id
      WHERE r.id = ?
    `;
    
    return await findOne(query, [id]);
  }

  static async findByPropertyId(propertyId, page = 1, limit = 10) {
    const offset = (page - 1) * limit;
    
    const query = `
      SELECT 
        r.*,
        u.first_name as reviewer_first_name,
        u.last_name as reviewer_last_name
      FROM reviews r
      LEFT JOIN users u ON r.reviewer_id = u.id
      WHERE r.property_id = ?
      ORDER BY r.created_at DESC
      LIMIT ? OFFSET ?
    `;
    
    return await findMany(query, [propertyId, parseInt(limit), offset]);
  }

  static async findByReviewerId(reviewerId) {
    const query = `
      SELECT 
        r.*,
        p.title as property_title,
        p.location as property_location
      FROM reviews r
      LEFT JOIN properties p ON r.property_id = p.id
      WHERE r.reviewer_id = ?
      ORDER BY r.created_at DESC
    `;
    
    return await findMany(query, [reviewerId]);
  }

  static async findByBookingId(bookingId) {
    const query = `
      SELECT 
        r.*,
        u.first_name as reviewer_first_name,
        u.last_name as reviewer_last_name,
        p.title as property_title
      FROM reviews r
      LEFT JOIN users u ON r.reviewer_id = u.id
      LEFT JOIN properties p ON r.property_id = p.id
      WHERE r.booking_id = ?
    `;
    
    return await findOne(query, [bookingId]);
  }

  static async updateById(id, updateData) {
    const fields = Object.keys(updateData).map(key => `${key} = ?`).join(', ');
    const values = Object.values(updateData);
    
    const query = `UPDATE reviews SET ${fields} WHERE id = ?`;
    return await executeQuery(query, [...values, id]);
  }

  static async addResponse(id, response) {
    const query = 'UPDATE reviews SET response = ? WHERE id = ?';
    return await executeQuery(query, [response, id]);
  }

  static async deleteById(id) {
    const query = 'DELETE FROM reviews WHERE id = ?';
    return await executeQuery(query, [id]);
  }

  static async getPropertyStats(propertyId) {
    const query = `
      SELECT 
        COUNT(*) as total_reviews,
        AVG(rating) as average_rating,
        COUNT(CASE WHEN rating = 5 THEN 1 END) as five_star,
        COUNT(CASE WHEN rating = 4 THEN 1 END) as four_star,
        COUNT(CASE WHEN rating = 3 THEN 1 END) as three_star,
        COUNT(CASE WHEN rating = 2 THEN 1 END) as two_star,
        COUNT(CASE WHEN rating = 1 THEN 1 END) as one_star
      FROM reviews 
      WHERE property_id = ?
    `;
    
    const result = await findOne(query, [propertyId]);
    return result || {
      total_reviews: 0,
      average_rating: 0,
      five_star: 0,
      four_star: 0,
      three_star: 0,
      two_star: 0,
      one_star: 0
    };
  }

  static async getRecentReviews(limit = 10) {
    const query = `
      SELECT 
        r.*,
        u.first_name as reviewer_first_name,
        u.last_name as reviewer_last_name,
        p.title as property_title,
        p.location as property_location
      FROM reviews r
      LEFT JOIN users u ON r.reviewer_id = u.id
      LEFT JOIN properties p ON r.property_id = p.id
      ORDER BY r.created_at DESC
      LIMIT ?
    `;
    
    return await findMany(query, [limit]);
  }

  static async validateBookingForReview(bookingId, reviewerId) {
    const query = `
      SELECT 
        b.id,
        b.property_id,
        b.guest_id,
        b.booking_status,
        b.check_out_date,
        p.title as property_title
      FROM bookings b
      LEFT JOIN properties p ON b.property_id = p.id
      WHERE b.id = ? AND b.guest_id = ?
    `;
    
    return await findOne(query, [bookingId, reviewerId]);
  }

  static async checkExistingReview(bookingId) {
    const query = 'SELECT id FROM reviews WHERE booking_id = ?';
    return await findOne(query, [bookingId]);
  }

  static async getPropertyOwner(reviewId) {
    const query = `
      SELECT r.id, p.owner_id
      FROM reviews r
      LEFT JOIN properties p ON r.property_id = p.id
      WHERE r.id = ?
    `;
    
    return await findOne(query, [reviewId]);
  }

  static async getReviewerStats(reviewerId) {
    const query = `
      SELECT 
        COUNT(*) as total_reviews,
        AVG(rating) as average_rating_given,
        COUNT(CASE WHEN rating = 5 THEN 1 END) as five_star_given,
        COUNT(CASE WHEN rating = 4 THEN 1 END) as four_star_given,
        COUNT(CASE WHEN rating = 3 THEN 1 END) as three_star_given,
        COUNT(CASE WHEN rating = 2 THEN 1 END) as two_star_given,
        COUNT(CASE WHEN rating = 1 THEN 1 END) as one_star_given
      FROM reviews 
      WHERE reviewer_id = ?
    `;
    
    const result = await findOne(query, [reviewerId]);
    return result || {
      total_reviews: 0,
      average_rating_given: 0,
      five_star_given: 0,
      four_star_given: 0,
      three_star_given: 0,
      two_star_given: 0,
      one_star_given: 0
    };
  }

  static async searchReviews(searchTerm, limit = 20) {
    const query = `
      SELECT 
        r.*,
        u.first_name as reviewer_first_name,
        u.last_name as reviewer_last_name,
        p.title as property_title,
        p.location as property_location
      FROM reviews r
      LEFT JOIN users u ON r.reviewer_id = u.id
      LEFT JOIN properties p ON r.property_id = p.id
      WHERE r.comment LIKE ? OR p.title LIKE ? OR p.location LIKE ?
      ORDER BY r.created_at DESC
      LIMIT ?
    `;
    
    const searchPattern = `%${searchTerm}%`;
    return await findMany(query, [searchPattern, searchPattern, searchPattern, limit]);
  }

  static async getTopRatedProperties(limit = 10) {
    const query = `
      SELECT 
        p.id,
        p.title,
        p.location,
        COUNT(r.id) as review_count,
        AVG(r.rating) as average_rating
      FROM properties p
      LEFT JOIN reviews r ON p.id = r.property_id
      GROUP BY p.id, p.title, p.location
      HAVING review_count >= 3
      ORDER BY average_rating DESC, review_count DESC
      LIMIT ?
    `;
    
    return await findMany(query, [limit]);
  }

  static async canEditReview(reviewId, reviewerId) {
    const query = `
      SELECT reviewer_id, created_at 
      FROM reviews 
      WHERE id = ? AND reviewer_id = ?
    `;
    
    const review = await findOne(query, [reviewId, reviewerId]);
    
    if (!review) {
      return { canEdit: false, reason: 'Review not found or not owned by user' };
    }

    // Check if review is within 24 hours
    const reviewDate = new Date(review.created_at);
    const now = new Date();
    const hoursDiff = (now - reviewDate) / (1000 * 60 * 60);

    if (hoursDiff > 24) {
      return { canEdit: false, reason: 'Reviews can only be edited within 24 hours of creation' };
    }

    return { canEdit: true };
  }

  static async getReviewsWithResponses(propertyId) {
    const query = `
      SELECT 
        r.*,
        u.first_name as reviewer_first_name,
        u.last_name as reviewer_last_name
      FROM reviews r
      LEFT JOIN users u ON r.reviewer_id = u.id
      WHERE r.property_id = ? AND r.response IS NOT NULL
      ORDER BY r.created_at DESC
    `;
    
    return await findMany(query, [propertyId]);
  }
}

module.exports = Review;
