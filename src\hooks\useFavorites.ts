import { useState, useEffect, useCallback } from 'react';
import { FavoritesService } from '@/services/favoritesService';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';

export const useFavorites = () => {
  const [favoriteIds, setFavoriteIds] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [initialized, setInitialized] = useState(false);
  const { user } = useAuth();
  const { toast } = useToast();

  // Load user's favorites on mount
  useEffect(() => {
    if (user?.id && !initialized) {
      loadFavorites();
    }
  }, [user?.id, initialized]);

  const loadFavorites = async () => {
    if (!user?.id) return;

    try {
      setLoading(true);
      const ids = await FavoritesService.getFavoritePropertyIds(user.id);
      setFavoriteIds(ids);
      setInitialized(true);
    } catch (error) {
      console.error('Failed to load favorites:', error);
      toast({
        title: "Error",
        description: "Failed to load your favorites",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const isFavorited = useCallback((propertyId: string): boolean => {
    return favoriteIds.includes(propertyId);
  }, [favoriteIds]);

  const toggleFavorite = async (propertyId: string): Promise<boolean> => {
    if (!user?.id) {
      toast({
        title: "Sign in required",
        description: "Please sign in to save favorites",
        variant: "destructive",
      });
      return false;
    }

    try {
      setLoading(true);
      const newStatus = await FavoritesService.toggleFavorite(user.id, propertyId);
      
      // Update local state
      if (newStatus) {
        setFavoriteIds(prev => [...prev, propertyId]);
        toast({
          title: "Added to favorites",
          description: "Property saved to your favorites",
        });
      } else {
        setFavoriteIds(prev => prev.filter(id => id !== propertyId));
        toast({
          title: "Removed from favorites",
          description: "Property removed from your favorites",
        });
      }

      return newStatus;
    } catch (error) {
      console.error('Failed to toggle favorite:', error);
      toast({
        title: "Error",
        description: "Failed to update favorites",
        variant: "destructive",
      });
      return isFavorited(propertyId);
    } finally {
      setLoading(false);
    }
  };

  const addFavorite = async (propertyId: string): Promise<boolean> => {
    if (!user?.id) {
      toast({
        title: "Sign in required",
        description: "Please sign in to save favorites",
        variant: "destructive",
      });
      return false;
    }

    if (isFavorited(propertyId)) {
      return true; // Already favorited
    }

    try {
      setLoading(true);
      await FavoritesService.addFavorite(user.id, propertyId);
      setFavoriteIds(prev => [...prev, propertyId]);
      
      toast({
        title: "Added to favorites",
        description: "Property saved to your favorites",
      });
      
      return true;
    } catch (error) {
      console.error('Failed to add favorite:', error);
      toast({
        title: "Error",
        description: "Failed to add to favorites",
        variant: "destructive",
      });
      return false;
    } finally {
      setLoading(false);
    }
  };

  const removeFavorite = async (propertyId: string): Promise<boolean> => {
    if (!user?.id) return false;

    if (!isFavorited(propertyId)) {
      return true; // Already not favorited
    }

    try {
      setLoading(true);
      await FavoritesService.removeFavorite(user.id, propertyId);
      setFavoriteIds(prev => prev.filter(id => id !== propertyId));
      
      toast({
        title: "Removed from favorites",
        description: "Property removed from your favorites",
      });
      
      return true;
    } catch (error) {
      console.error('Failed to remove favorite:', error);
      toast({
        title: "Error",
        description: "Failed to remove from favorites",
        variant: "destructive",
      });
      return false;
    } finally {
      setLoading(false);
    }
  };

  const getFavoriteProperties = async () => {
    if (!user?.id) return [];

    try {
      return await FavoritesService.getFavoriteProperties(user.id);
    } catch (error) {
      console.error('Failed to get favorite properties:', error);
      toast({
        title: "Error",
        description: "Failed to load favorite properties",
        variant: "destructive",
      });
      return [];
    }
  };

  return {
    favoriteIds,
    loading,
    initialized,
    isFavorited,
    toggleFavorite,
    addFavorite,
    removeFavorite,
    getFavoriteProperties,
    refreshFavorites: loadFavorites,
  };
};
