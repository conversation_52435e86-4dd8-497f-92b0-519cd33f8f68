-- Create payments and refunds tables for payment processing
CREATE TABLE IF NOT EXISTS payments (
    id VARCHAR(36) PRIMARY KEY,
    booking_id VARCHAR(36) NOT NULL,
    payment_reference VARCHAR(255) UNIQUE NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    platform_fee DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    host_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    refund_amount DECIMAL(10,2) DEFAULT 0.00,
    payment_method ENUM('payfast', 'card', 'eft', 'bank_transfer') DEFAULT 'payfast',
    payment_status ENUM('pending', 'completed', 'failed', 'refunded', 'cancelled') DEFAULT 'pending',
    payfast_payment_id VARCHAR(255) NULL,
    payment_data JSON NULL,
    notification_data JSON NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (booking_id) REFERENCES bookings(id) ON DELETE CASCADE
);

CREATE TABLE IF NOT EXISTS refunds (
    id VARCHAR(36) PRIMARY KEY,
    payment_id VARCHAR(36) NOT NULL,
    booking_id VARCHAR(36) NOT NULL,
    refund_amount DECIMAL(10,2) NOT NULL,
    refund_status ENUM('pending', 'completed', 'failed', 'cancelled') DEFAULT 'pending',
    refund_reason TEXT NULL,
    refund_reference VARCHAR(255) NULL,
    processed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (payment_id) REFERENCES payments(id) ON DELETE CASCADE,
    FOREIGN KEY (booking_id) REFERENCES bookings(id) ON DELETE CASCADE
);

CREATE TABLE IF NOT EXISTS payment_escrow (
    id VARCHAR(36) PRIMARY KEY,
    payment_id VARCHAR(36) NOT NULL,
    booking_id VARCHAR(36) NOT NULL,
    host_id VARCHAR(36) NOT NULL,
    escrow_amount DECIMAL(10,2) NOT NULL,
    platform_fee DECIMAL(10,2) NOT NULL,
    host_payout_amount DECIMAL(10,2) NOT NULL,
    escrow_status ENUM('held', 'released', 'refunded') DEFAULT 'held',
    hold_until_date DATE NOT NULL,
    release_date TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (payment_id) REFERENCES payments(id) ON DELETE CASCADE,
    FOREIGN KEY (booking_id) REFERENCES bookings(id) ON DELETE CASCADE,
    FOREIGN KEY (host_id) REFERENCES users(id) ON DELETE CASCADE
);

CREATE TABLE IF NOT EXISTS host_payouts (
    id VARCHAR(36) PRIMARY KEY,
    host_id VARCHAR(36) NOT NULL,
    payment_id VARCHAR(36) NOT NULL,
    booking_id VARCHAR(36) NOT NULL,
    payout_amount DECIMAL(10,2) NOT NULL,
    payout_method ENUM('bank_transfer', 'payfast', 'manual') DEFAULT 'bank_transfer',
    payout_status ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending',
    bank_details JSON NULL,
    payout_reference VARCHAR(255) NULL,
    processed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (host_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (payment_id) REFERENCES payments(id) ON DELETE CASCADE,
    FOREIGN KEY (booking_id) REFERENCES bookings(id) ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_payments_booking_id ON payments(booking_id);
CREATE INDEX IF NOT EXISTS idx_payments_reference ON payments(payment_reference);
CREATE INDEX IF NOT EXISTS idx_payments_status ON payments(payment_status);
CREATE INDEX IF NOT EXISTS idx_payments_created_at ON payments(created_at);

CREATE INDEX IF NOT EXISTS idx_refunds_payment_id ON refunds(payment_id);
CREATE INDEX IF NOT EXISTS idx_refunds_booking_id ON refunds(booking_id);
CREATE INDEX IF NOT EXISTS idx_refunds_status ON refunds(refund_status);

CREATE INDEX IF NOT EXISTS idx_escrow_payment_id ON payment_escrow(payment_id);
CREATE INDEX IF NOT EXISTS idx_escrow_booking_id ON payment_escrow(booking_id);
CREATE INDEX IF NOT EXISTS idx_escrow_host_id ON payment_escrow(host_id);
CREATE INDEX IF NOT EXISTS idx_escrow_status ON payment_escrow(escrow_status);
CREATE INDEX IF NOT EXISTS idx_escrow_hold_until ON payment_escrow(hold_until_date);

CREATE INDEX IF NOT EXISTS idx_payouts_host_id ON host_payouts(host_id);
CREATE INDEX IF NOT EXISTS idx_payouts_payment_id ON host_payouts(payment_id);
CREATE INDEX IF NOT EXISTS idx_payouts_status ON host_payouts(payout_status);

-- Insert sample payment data for testing
INSERT IGNORE INTO payments (
    id, booking_id, payment_reference, amount, platform_fee, host_amount,
    payment_method, payment_status, created_at
) VALUES 
(
    'payment-1',
    (SELECT id FROM bookings WHERE guest_id = (SELECT id FROM users WHERE email = '<EMAIL>') LIMIT 1),
    'SF-TEST-001',
    1200.00,
    60.00,
    1140.00,
    'payfast',
    'completed',
    '2024-06-20 10:00:00'
),
(
    'payment-2',
    (SELECT id FROM bookings WHERE guest_id = (SELECT id FROM users WHERE email = '<EMAIL>') LIMIT 1 OFFSET 1),
    'SF-TEST-002',
    800.00,
    40.00,
    760.00,
    'payfast',
    'pending',
    '2024-06-21 14:30:00'
);

-- Insert sample escrow data
INSERT IGNORE INTO payment_escrow (
    id, payment_id, booking_id, host_id, escrow_amount, platform_fee, host_payout_amount,
    escrow_status, hold_until_date, created_at
) VALUES 
(
    'escrow-1',
    'payment-1',
    (SELECT booking_id FROM payments WHERE id = 'payment-1'),
    (SELECT host_id FROM properties WHERE id = (SELECT property_id FROM bookings WHERE id = (SELECT booking_id FROM payments WHERE id = 'payment-1'))),
    1200.00,
    60.00,
    1140.00,
    'held',
    '2024-06-25',
    '2024-06-20 10:00:00'
);

-- Insert sample payout data
INSERT IGNORE INTO host_payouts (
    id, host_id, payment_id, booking_id, payout_amount, payout_method, payout_status, created_at
) VALUES 
(
    'payout-1',
    (SELECT host_id FROM properties WHERE id = (SELECT property_id FROM bookings WHERE id = (SELECT booking_id FROM payments WHERE id = 'payment-1'))),
    'payment-1',
    (SELECT booking_id FROM payments WHERE id = 'payment-1'),
    1140.00,
    'bank_transfer',
    'pending',
    '2024-06-20 10:00:00'
);
