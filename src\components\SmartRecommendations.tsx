import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { PropertyCard } from './PropertyCard';
import { 
  Star, 
  TrendingUp, 
  Clock, 
  Heart,
  Sparkles,
  ChevronLeft,
  ChevronRight,
  RefreshCw
} from 'lucide-react';

interface Property {
  id: string;
  title: string;
  location: string;
  price: number;
  images: string[];
  averageRating?: number;
  reviewCount?: number;
  propertyType?: string;
  maxGuests?: number;
  bedrooms?: number;
  bathrooms?: number;
  amenities?: string[];
  available?: boolean;
  owner?: {
    firstName: string;
    lastName: string;
  };
}

interface SmartRecommendationsProps {
  type: 'similar' | 'recently_viewed' | 'personalized' | 'trending';
  propertyId?: string;
  title?: string;
  subtitle?: string;
  limit?: number;
  showHeader?: boolean;
  className?: string;
  onPropertyClick?: (property: Property) => void;
}

export const SmartRecommendations: React.FC<SmartRecommendationsProps> = ({
  type,
  propertyId,
  title,
  subtitle,
  limit = 6,
  showHeader = true,
  className,
  onPropertyClick
}) => {
  const [properties, setProperties] = useState<Property[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentIndex, setCurrentIndex] = useState(0);

  const getDefaultTitle = () => {
    switch (type) {
      case 'similar':
        return 'Similar Properties';
      case 'recently_viewed':
        return 'Recently Viewed';
      case 'personalized':
        return 'Recommended for You';
      case 'trending':
        return 'Trending Destinations';
      default:
        return 'Recommendations';
    }
  };

  const getDefaultSubtitle = () => {
    switch (type) {
      case 'similar':
        return 'Properties you might also like';
      case 'recently_viewed':
        return 'Continue exploring these properties';
      case 'personalized':
        return 'Based on your preferences and activity';
      case 'trending':
        return 'Popular properties right now';
      default:
        return '';
    }
  };

  const getIcon = () => {
    switch (type) {
      case 'similar':
        return <Sparkles className="h-5 w-5" />;
      case 'recently_viewed':
        return <Clock className="h-5 w-5" />;
      case 'personalized':
        return <Heart className="h-5 w-5" />;
      case 'trending':
        return <TrendingUp className="h-5 w-5" />;
      default:
        return <Star className="h-5 w-5" />;
    }
  };

  const fetchRecommendations = async () => {
    try {
      setLoading(true);
      setError(null);

      let url = `http://localhost/stayfinder/api/recommendations.php?type=${type}&limit=${limit}`;
      
      if (type === 'similar' && propertyId) {
        url += `&property_id=${propertyId}`;
      }

      const token = localStorage.getItem('auth_token');
      const headers: HeadersInit = {
        'Content-Type': 'application/json',
      };

      if (token && (type === 'recently_viewed' || type === 'personalized')) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const response = await fetch(url, {
        method: 'GET',
        headers,
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        setProperties(data.data || []);
      } else {
        throw new Error(data.error || 'Failed to fetch recommendations');
      }
    } catch (err) {
      console.error('Error fetching recommendations:', err);
      setError(err instanceof Error ? err.message : 'Failed to load recommendations');
      setProperties([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchRecommendations();
  }, [type, propertyId, limit]);

  const handlePropertyClick = (property: Property) => {
    // Log property view
    const token = localStorage.getItem('auth_token');
    if (token) {
      fetch('http://localhost/stayfinder/api/recommendations.php', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        credentials: 'include',
        body: JSON.stringify({ property_id: property.id }),
      }).catch(err => console.error('Error logging property view:', err));
    }

    if (onPropertyClick) {
      onPropertyClick(property);
    }
  };

  const nextSlide = () => {
    setCurrentIndex((prev) => 
      prev + 3 >= properties.length ? 0 : prev + 3
    );
  };

  const prevSlide = () => {
    setCurrentIndex((prev) => 
      prev - 3 < 0 ? Math.max(0, properties.length - 3) : prev - 3
    );
  };

  const visibleProperties = properties.slice(currentIndex, currentIndex + 3);
  const canNavigate = properties.length > 3;

  if (loading) {
    return (
      <Card className={className}>
        {showHeader && (
          <CardHeader>
            <div className="flex items-center gap-2">
              {getIcon()}
              <div className="h-6 bg-gray-200 rounded w-48 animate-pulse"></div>
            </div>
          </CardHeader>
        )}
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[...Array(3)].map((_, index) => (
              <div key={index} className="space-y-3">
                <div className="h-48 bg-gray-200 rounded-lg animate-pulse"></div>
                <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                <div className="h-3 bg-gray-200 rounded w-2/3 animate-pulse"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        {showHeader && (
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {getIcon()}
              {title || getDefaultTitle()}
            </CardTitle>
          </CardHeader>
        )}
        <CardContent>
          <div className="text-center py-8">
            <p className="text-gray-600 mb-4">{error}</p>
            <Button
              variant="outline"
              onClick={fetchRecommendations}
              className="flex items-center gap-2"
            >
              <RefreshCw className="h-4 w-4" />
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (properties.length === 0) {
    return (
      <Card className={className}>
        {showHeader && (
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {getIcon()}
              {title || getDefaultTitle()}
            </CardTitle>
          </CardHeader>
        )}
        <CardContent>
          <div className="text-center py-8 text-gray-500">
            <div className="mb-4">{getIcon()}</div>
            <p>No recommendations available at the moment</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      {showHeader && (
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                {getIcon()}
                {title || getDefaultTitle()}
                <Badge variant="secondary">{properties.length}</Badge>
              </CardTitle>
              {(subtitle || getDefaultSubtitle()) && (
                <p className="text-sm text-gray-600 mt-1">
                  {subtitle || getDefaultSubtitle()}
                </p>
              )}
            </div>
            {canNavigate && (
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={prevSlide}
                  disabled={currentIndex === 0}
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={nextSlide}
                  disabled={currentIndex + 3 >= properties.length}
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            )}
          </div>
        </CardHeader>
      )}
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {visibleProperties.map((property) => (
            <div key={property.id} onClick={() => handlePropertyClick(property)}>
              <PropertyCard
                property={property}
                variant="grid"
                compact={true}
                showQuickActions={false}
              />
            </div>
          ))}
        </div>

        {/* Navigation Dots */}
        {canNavigate && (
          <div className="flex justify-center mt-6 gap-2">
            {Array.from({ length: Math.ceil(properties.length / 3) }).map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentIndex(index * 3)}
                className={`w-2 h-2 rounded-full transition-colors ${
                  Math.floor(currentIndex / 3) === index
                    ? 'bg-sea-green-500'
                    : 'bg-gray-300'
                }`}
              />
            ))}
          </div>
        )}

        {/* View All Button */}
        {properties.length > 3 && (
          <div className="text-center mt-6">
            <Button variant="outline" className="w-full sm:w-auto">
              View All {properties.length} Properties
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
