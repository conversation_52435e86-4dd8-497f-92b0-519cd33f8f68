import React, { useState, useMemo } from 'react';
import { Grid, List, Map, Filter, SortAsc, SortDesc, ChevronLeft, ChevronRight, Eye, Heart, Share2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from '@/components/ui/pagination';
import { cn } from '@/lib/utils';

interface Property {
  id: string;
  name: string;
  location: string;
  price: number;
  rating: number;
  reviewCount: number;
  images: string[];
  amenities: string[];
  propertyType: string;
  capacity: number;
  isInstantBook: boolean;
  isSuperhost: boolean;
  distance?: number;
}

interface SearchResultsProps {
  properties: Property[];
  totalResults: number;
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  onSortChange: (sortBy: string, sortOrder: 'asc' | 'desc') => void;
  onViewModeChange: (mode: 'grid' | 'list' | 'map') => void;
  onPropertyClick: (propertyId: string) => void;
  onPropertySave: (propertyId: string) => void;
  onPropertyShare: (propertyId: string) => void;
  isLoading?: boolean;
  searchQuery?: string;
  className?: string;
}

type ViewMode = 'grid' | 'list' | 'map';
type SortOption = 'price' | 'rating' | 'distance' | 'popularity' | 'newest';

export const SearchResultsOptimized: React.FC<SearchResultsProps> = ({
  properties,
  totalResults,
  currentPage,
  totalPages,
  onPageChange,
  onSortChange,
  onViewModeChange,
  onPropertyClick,
  onPropertySave,
  onPropertyShare,
  isLoading = false,
  searchQuery,
  className
}) => {
  const [viewMode, setViewMode] = useState<ViewMode>('grid');
  const [sortBy, setSortBy] = useState<SortOption>('popularity');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [itemsPerPage, setItemsPerPage] = useState(12);

  const sortOptions = [
    { value: 'popularity', label: 'Most Popular' },
    { value: 'price', label: 'Price' },
    { value: 'rating', label: 'Rating' },
    { value: 'distance', label: 'Distance' },
    { value: 'newest', label: 'Newest' }
  ];

  const handleViewModeChange = (mode: ViewMode) => {
    setViewMode(mode);
    onViewModeChange(mode);
  };

  const handleSortChange = (newSortBy: SortOption) => {
    const newOrder = sortBy === newSortBy && sortOrder === 'desc' ? 'asc' : 'desc';
    setSortBy(newSortBy);
    setSortOrder(newOrder);
    onSortChange(newSortBy, newOrder);
  };

  const formatPrice = (price: number) => `R${price.toLocaleString()}`;

  const PropertyCard: React.FC<{ property: Property; mode: ViewMode }> = ({ property, mode }) => {
    if (mode === 'list') {
      return (
        <Card className="hover:shadow-lg transition-shadow cursor-pointer">
          <CardContent className="p-0">
            <div className="flex">
              <div className="w-64 h-48 relative flex-shrink-0">
                <img
                  src={property.images[0]}
                  alt={property.name}
                  className="w-full h-full object-cover rounded-l-lg"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = `https://images.unsplash.com/photo-1566073771259-6a8506099945?w=400&h=300&fit=crop`;
                  }}
                />
                <Button
                  size="sm"
                  variant="ghost"
                  className="absolute top-2 right-2 bg-white/80 hover:bg-white"
                  onClick={(e) => {
                    e.stopPropagation();
                    onPropertySave(property.id);
                  }}
                >
                  <Heart className="h-4 w-4" />
                </Button>
              </div>
              
              <div className="flex-1 p-6">
                <div className="flex justify-between items-start mb-2">
                  <div>
                    <h3 className="text-lg font-semibold mb-1">{property.name}</h3>
                    <p className="text-gray-600 text-sm">{property.location}</p>
                  </div>
                  <div className="text-right">
                    <div className="text-xl font-bold">{formatPrice(property.price)}</div>
                    <div className="text-sm text-gray-600">per night</div>
                  </div>
                </div>
                
                <div className="flex items-center gap-4 mb-3">
                  <div className="flex items-center gap-1">
                    <span className="text-sm font-medium">{property.rating}</span>
                    <div className="flex">
                      {[...Array(5)].map((_, i) => (
                        <span key={i} className={cn("text-xs", i < Math.floor(property.rating) ? "text-yellow-400" : "text-gray-300")}>
                          ★
                        </span>
                      ))}
                    </div>
                    <span className="text-sm text-gray-600">({property.reviewCount})</span>
                  </div>
                  
                  <Badge variant="secondary">{property.propertyType}</Badge>
                  <span className="text-sm text-gray-600">{property.capacity} guests</span>
                  
                  {property.isInstantBook && (
                    <Badge variant="default" className="bg-green-600">Instant Book</Badge>
                  )}
                  {property.isSuperhost && (
                    <Badge variant="default" className="bg-purple-600">Superhost</Badge>
                  )}
                </div>
                
                <div className="flex flex-wrap gap-2 mb-4">
                  {property.amenities.slice(0, 4).map((amenity, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {amenity}
                    </Badge>
                  ))}
                  {property.amenities.length > 4 && (
                    <Badge variant="outline" className="text-xs">
                      +{property.amenities.length - 4} more
                    </Badge>
                  )}
                </div>
                
                <div className="flex items-center justify-between">
                  <Button
                    onClick={() => onPropertyClick(property.id)}
                    className="bg-sea-green-600 hover:bg-sea-green-700"
                  >
                    View Details
                  </Button>
                  
                  <div className="flex items-center gap-2">
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={(e) => {
                        e.stopPropagation();
                        onPropertyShare(property.id);
                      }}
                    >
                      <Share2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      );
    }

    // Grid mode
    return (
      <Card className="hover:shadow-lg transition-shadow cursor-pointer group">
        <CardContent className="p-0">
          <div className="relative">
            <img
              src={property.images[0]}
              alt={property.name}
              className="w-full h-48 object-cover rounded-t-lg"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.src = `https://images.unsplash.com/photo-1566073771259-6a8506099945?w=400&h=300&fit=crop`;
              }}
            />
            <Button
              size="sm"
              variant="ghost"
              className="absolute top-2 right-2 bg-white/80 hover:bg-white opacity-0 group-hover:opacity-100 transition-opacity"
              onClick={(e) => {
                e.stopPropagation();
                onPropertySave(property.id);
              }}
            >
              <Heart className="h-4 w-4" />
            </Button>
            
            {property.isInstantBook && (
              <Badge className="absolute top-2 left-2 bg-green-600">
                Instant Book
              </Badge>
            )}
          </div>
          
          <div className="p-4">
            <div className="flex justify-between items-start mb-2">
              <div className="flex-1 min-w-0">
                <h3 className="font-semibold truncate">{property.name}</h3>
                <p className="text-gray-600 text-sm truncate">{property.location}</p>
              </div>
              <div className="text-right ml-2">
                <div className="font-bold">{formatPrice(property.price)}</div>
                <div className="text-xs text-gray-600">per night</div>
              </div>
            </div>
            
            <div className="flex items-center gap-2 mb-3">
              <div className="flex items-center gap-1">
                <span className="text-sm font-medium">{property.rating}</span>
                <div className="flex">
                  {[...Array(5)].map((_, i) => (
                    <span key={i} className={cn("text-xs", i < Math.floor(property.rating) ? "text-yellow-400" : "text-gray-300")}>
                      ★
                    </span>
                  ))}
                </div>
                <span className="text-xs text-gray-600">({property.reviewCount})</span>
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Badge variant="secondary" className="text-xs">{property.propertyType}</Badge>
                <span className="text-xs text-gray-600">{property.capacity} guests</span>
              </div>
              
              <Button
                size="sm"
                onClick={() => onPropertyClick(property.id)}
                className="bg-sea-green-600 hover:bg-sea-green-700"
              >
                <Eye className="h-4 w-4 mr-1" />
                View
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  const LoadingSkeleton = () => (
    <div className="space-y-4">
      {[...Array(itemsPerPage)].map((_, index) => (
        <Card key={index} className="animate-pulse">
          <CardContent className="p-0">
            {viewMode === 'list' ? (
              <div className="flex">
                <div className="w-64 h-48 bg-gray-200 rounded-l-lg" />
                <div className="flex-1 p-6 space-y-3">
                  <div className="h-6 bg-gray-200 rounded w-3/4" />
                  <div className="h-4 bg-gray-200 rounded w-1/2" />
                  <div className="h-4 bg-gray-200 rounded w-full" />
                  <div className="h-8 bg-gray-200 rounded w-24" />
                </div>
              </div>
            ) : (
              <div>
                <div className="w-full h-48 bg-gray-200 rounded-t-lg" />
                <div className="p-4 space-y-3">
                  <div className="h-5 bg-gray-200 rounded w-3/4" />
                  <div className="h-4 bg-gray-200 rounded w-1/2" />
                  <div className="h-8 bg-gray-200 rounded w-20" />
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      ))}
    </div>
  );

  return (
    <div className={cn("space-y-6", className)}>
      {/* Results Header */}
      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4 bg-white p-4 rounded-lg border">
        <div className="flex items-center gap-4">
          <div>
            <h2 className="text-lg font-semibold">
              {totalResults.toLocaleString()} properties found
            </h2>
            {searchQuery && (
              <p className="text-sm text-gray-600">
                for "{searchQuery}"
              </p>
            )}
          </div>
        </div>
        
        <div className="flex items-center gap-4">
          {/* View Mode Toggle */}
          <div className="flex items-center border rounded-lg">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => handleViewModeChange('grid')}
              className="rounded-r-none"
            >
              <Grid className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => handleViewModeChange('list')}
              className="rounded-none border-x"
            >
              <List className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === 'map' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => handleViewModeChange('map')}
              className="rounded-l-none"
            >
              <Map className="h-4 w-4" />
            </Button>
          </div>
          
          {/* Sort Options */}
          <Select value={sortBy} onValueChange={(value) => handleSortChange(value as SortOption)}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              {sortOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
          >
            {sortOrder === 'asc' ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />}
          </Button>
        </div>
      </div>

      {/* Results Grid/List */}
      {isLoading ? (
        <LoadingSkeleton />
      ) : (
        <div className={cn(
          viewMode === 'grid' 
            ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
            : "space-y-4"
        )}>
          {properties.map((property) => (
            <div key={property.id} onClick={() => onPropertyClick(property.id)}>
              <PropertyCard property={property} mode={viewMode} />
            </div>
          ))}
        </div>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-600">
            Showing {((currentPage - 1) * itemsPerPage) + 1} to {Math.min(currentPage * itemsPerPage, totalResults)} of {totalResults.toLocaleString()} results
          </div>
          
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious 
                  onClick={() => currentPage > 1 && onPageChange(currentPage - 1)}
                  className={cn(currentPage <= 1 && "pointer-events-none opacity-50")}
                />
              </PaginationItem>
              
              {[...Array(Math.min(5, totalPages))].map((_, index) => {
                const pageNumber = Math.max(1, currentPage - 2) + index;
                if (pageNumber > totalPages) return null;
                
                return (
                  <PaginationItem key={pageNumber}>
                    <PaginationLink
                      onClick={() => onPageChange(pageNumber)}
                      isActive={pageNumber === currentPage}
                    >
                      {pageNumber}
                    </PaginationLink>
                  </PaginationItem>
                );
              })}
              
              <PaginationItem>
                <PaginationNext 
                  onClick={() => currentPage < totalPages && onPageChange(currentPage + 1)}
                  className={cn(currentPage >= totalPages && "pointer-events-none opacity-50")}
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </div>
      )}
    </div>
  );
};
