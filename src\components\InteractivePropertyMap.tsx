import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  MapPin, 
  Maximize2, 
  Minimize2, 
  RotateCcw,
  Search,
  Filter,
  Star,
  DollarSign,
  Navigation,
  Layers,
  ZoomIn,
  ZoomOut,
  Target,
  Map as MapIcon,
  Satellite,
  Route,
  Clock,
  Car,
  Walking
} from 'lucide-react';
import { 
  HoverAnimation, 
  SlideIn, 
  ScaleIn 
} from '@/components/ui/page-transitions';
import { QuickTooltip } from '@/components/ui/enhanced-tooltip';

interface Property {
  id: string;
  title: string;
  location: string;
  price: number;
  images: string[];
  coordinates?: {
    latitude: number;
    longitude: number;
  };
  averageRating?: number;
  reviewCount?: number;
  propertyType?: string;
  maxGuests?: number;
  bedrooms?: number;
  bathrooms?: number;
  amenities?: string[];
  available?: boolean;
  instantBook?: boolean;
  trending?: boolean;
}

interface InteractivePropertyMapProps {
  properties: Property[];
  onPropertySelect?: (property: Property) => void;
  onMapBoundsChange?: (bounds: any) => void;
  selectedProperty?: Property | null;
  className?: string;
  height?: string;
  showControls?: boolean;
  showSearch?: boolean;
  showNeighborhood?: boolean;
  enableClustering?: boolean;
}

// Enhanced coordinates for South African locations
const SOUTH_AFRICAN_LOCATIONS = {
  'Cape Town': { lat: -33.9249, lng: 18.4241, zoom: 12 },
  'Camps Bay': { lat: -33.9553, lng: 18.3756, zoom: 14 },
  'V&A Waterfront': { lat: -33.9017, lng: 18.4197, zoom: 15 },
  'Stellenbosch': { lat: -33.9321, lng: 18.8602, zoom: 13 },
  'Hermanus': { lat: -34.4187, lng: 19.2345, zoom: 13 },
  'Johannesburg': { lat: -26.2041, lng: 28.0473, zoom: 11 },
  'Sandton': { lat: -26.1076, lng: 28.0567, zoom: 13 },
  'Pretoria': { lat: -25.7479, lng: 28.2293, zoom: 12 },
  'Durban': { lat: -29.8587, lng: 31.0218, zoom: 12 },
  'Umhlanga': { lat: -29.7277, lng: 31.0927, zoom: 14 },
  'Margate': { lat: -30.8647, lng: 30.3707, zoom: 14 },
  'Scottburgh': { lat: -30.2867, lng: 30.7533, zoom: 14 },
  'Port Elizabeth': { lat: -33.9608, lng: 25.6022, zoom: 12 },
  'Knysna': { lat: -34.0361, lng: 23.0471, zoom: 13 },
  'Plettenberg Bay': { lat: -34.0527, lng: 23.3716, zoom: 14 },
  'Kruger National Park': { lat: -24.0000, lng: 31.5000, zoom: 10 },
  'Sun City': { lat: -25.3400, lng: 27.0900, zoom: 13 },
  'Garden Route': { lat: -34.0000, lng: 22.5000, zoom: 9 }
};

export const InteractivePropertyMap: React.FC<InteractivePropertyMapProps> = ({
  properties,
  onPropertySelect,
  onMapBoundsChange,
  selectedProperty,
  className,
  height = '500px',
  showControls = true,
  showSearch = false,
  showNeighborhood = true,
  enableClustering = true
}) => {
  const mapRef = useRef<HTMLDivElement>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [mapCenter, setMapCenter] = useState({ lat: -30.5, lng: 24.5 });
  const [zoom, setZoom] = useState(6);
  const [mapType, setMapType] = useState<'roadmap' | 'satellite' | 'hybrid'>('roadmap');
  const [hoveredProperty, setHoveredProperty] = useState<Property | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [priceRange, setPriceRange] = useState({ min: 0, max: 5000 });
  const [selectedAmenities, setSelectedAmenities] = useState<string[]>([]);

  // Add coordinates to properties if missing
  const propertiesWithCoords = properties.map(property => {
    if (!property.coordinates) {
      // Try to find coordinates based on location
      const locationKey = Object.keys(SOUTH_AFRICAN_LOCATIONS).find(key => 
        property.location.toLowerCase().includes(key.toLowerCase())
      );
      
      if (locationKey) {
        const coords = SOUTH_AFRICAN_LOCATIONS[locationKey as keyof typeof SOUTH_AFRICAN_LOCATIONS];
        return {
          ...property,
          coordinates: { 
            latitude: coords.lat + (Math.random() - 0.5) * 0.02, // Add slight randomization
            longitude: coords.lng + (Math.random() - 0.5) * 0.02 
          }
        };
      }
    }
    return property;
  }).filter(p => p.coordinates);

  // Filter properties based on search and filters
  const filteredProperties = propertiesWithCoords.filter(property => {
    const matchesSearch = !searchQuery || 
      property.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      property.location.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesPrice = property.price >= priceRange.min && property.price <= priceRange.max;
    
    const matchesAmenities = selectedAmenities.length === 0 || 
      selectedAmenities.every(amenity => property.amenities?.includes(amenity));
    
    return matchesSearch && matchesPrice && matchesAmenities;
  });

  const handleMarkerClick = (property: Property) => {
    onPropertySelect?.(property);
  };

  const handleMapReset = () => {
    setMapCenter({ lat: -30.5, lng: 24.5 });
    setZoom(6);
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  const zoomIn = () => {
    setZoom(prev => Math.min(prev + 1, 18));
  };

  const zoomOut = () => {
    setZoom(prev => Math.max(prev - 1, 3));
  };

  const centerOnUserLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          setMapCenter({
            lat: position.coords.latitude,
            lng: position.coords.longitude
          });
          setZoom(12);
        },
        (error) => {
          console.error('Error getting user location:', error);
        }
      );
    }
  };

  const getMarkerColor = (property: Property) => {
    if (property.id === selectedProperty?.id) return '#ef4444'; // red
    if (property.trending) return '#f59e0b'; // amber
    if (property.instantBook) return '#10b981'; // emerald
    return '#3b82f6'; // blue
  };

  const getMarkerSize = (property: Property) => {
    if (property.id === selectedProperty?.id) return 'large';
    if (property.trending) return 'medium';
    return 'small';
  };

  return (
    <SlideIn direction="up" delay={100}>
      <Card className={`overflow-hidden border-0 shadow-xl ${isFullscreen ? 'fixed inset-4 z-50' : ''} ${className}`}>
        {showControls && (
          <CardHeader className="pb-4">
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <MapIcon className="h-5 w-5 text-sea-green-600" />
                Interactive Property Map
                <Badge variant="secondary" className="ml-2">
                  {filteredProperties.length} properties
                </Badge>
              </CardTitle>
              
              <div className="flex items-center gap-2">
                {/* Map Type Toggle */}
                <div className="flex border rounded-lg overflow-hidden">
                  {[
                    { type: 'roadmap', icon: MapIcon, label: 'Map' },
                    { type: 'satellite', icon: Satellite, label: 'Satellite' },
                    { type: 'hybrid', icon: Layers, label: 'Hybrid' }
                  ].map(({ type, icon: Icon, label }) => (
                    <QuickTooltip key={type} text={label}>
                      <Button
                        variant={mapType === type ? 'default' : 'ghost'}
                        size="sm"
                        onClick={() => setMapType(type as any)}
                        className="rounded-none"
                      >
                        <Icon className="h-4 w-4" />
                      </Button>
                    </QuickTooltip>
                  ))}
                </div>

                {/* Control Buttons */}
                <QuickTooltip text="My Location">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={centerOnUserLocation}
                  >
                    <Target className="h-4 w-4" />
                  </Button>
                </QuickTooltip>

                <QuickTooltip text="Reset View">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleMapReset}
                  >
                    <RotateCcw className="h-4 w-4" />
                  </Button>
                </QuickTooltip>

                <QuickTooltip text={isFullscreen ? "Exit Fullscreen" : "Fullscreen"}>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={toggleFullscreen}
                  >
                    {isFullscreen ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
                  </Button>
                </QuickTooltip>
              </div>
            </div>

            {/* Search and Filters */}
            {showSearch && (
              <div className="flex items-center gap-4 mt-4">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search properties on map..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowFilters(!showFilters)}
                  className="flex items-center gap-2"
                >
                  <Filter className="h-4 w-4" />
                  Filters
                </Button>
              </div>
            )}

            {/* Expanded Filters */}
            {showFilters && (
              <div className="mt-4 p-4 bg-gray-50 rounded-lg space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-700 mb-2 block">
                      Price Range (per night)
                    </label>
                    <div className="flex items-center gap-2">
                      <Input
                        type="number"
                        placeholder="Min"
                        value={priceRange.min}
                        onChange={(e) => setPriceRange(prev => ({ ...prev, min: Number(e.target.value) }))}
                        className="w-20"
                      />
                      <span>-</span>
                      <Input
                        type="number"
                        placeholder="Max"
                        value={priceRange.max}
                        onChange={(e) => setPriceRange(prev => ({ ...prev, max: Number(e.target.value) }))}
                        className="w-20"
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium text-gray-700 mb-2 block">
                      Property Type
                    </label>
                    <select className="w-full p-2 border rounded-md">
                      <option value="">All Types</option>
                      <option value="apartment">Apartment</option>
                      <option value="house">House</option>
                      <option value="villa">Villa</option>
                      <option value="cottage">Cottage</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium text-gray-700 mb-2 block">
                      Quick Filters
                    </label>
                    <div className="flex flex-wrap gap-2">
                      {['Instant Book', 'Trending', 'Pool', 'WiFi', 'Parking'].map((filter) => (
                        <Badge
                          key={filter}
                          variant="outline"
                          className="cursor-pointer hover:bg-sea-green-50 hover:border-sea-green-300"
                        >
                          {filter}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </CardHeader>
        )}

        <CardContent className="p-0">
          <div
            ref={mapRef}
            className="relative bg-gradient-to-br from-blue-100 to-green-100 overflow-hidden"
            style={{ height: isFullscreen ? 'calc(100vh - 200px)' : height }}
          >
            {/* Enhanced Map Background */}
            <div className={`absolute inset-0 ${
              mapType === 'satellite' 
                ? 'bg-gradient-to-br from-gray-800 to-gray-900' 
                : mapType === 'hybrid'
                ? 'bg-gradient-to-br from-gray-700 to-blue-900'
                : 'bg-gradient-to-br from-blue-200 via-blue-100 to-green-100'
            }`}>
              {/* South African Coastline */}
              <svg className="absolute inset-0 w-full h-full" viewBox="0 0 800 600">
                {mapType !== 'satellite' && (
                  <>
                    {/* Ocean */}
                    <rect width="800" height="600" fill="#3b82f6" fillOpacity="0.3" />
                    
                    {/* Coastline */}
                    <path
                      d="M 100 300 Q 200 280 300 290 Q 400 300 500 310 Q 600 320 700 330 L 700 600 L 100 600 Z"
                      fill="#059669"
                      fillOpacity="0.4"
                      stroke="#047857"
                      strokeWidth="2"
                    />
                    
                    {/* Major Cities */}
                    <circle cx="150" cy="350" r="8" fill="#ef4444" fillOpacity="0.8" />
                    <text x="160" y="355" fontSize="12" fill="#374151" fontWeight="bold">Cape Town</text>
                    
                    <circle cx="400" cy="200" r="6" fill="#f59e0b" fillOpacity="0.8" />
                    <text x="410" y="205" fontSize="10" fill="#374151">Johannesburg</text>
                    
                    <circle cx="550" cy="280" r="6" fill="#10b981" fillOpacity="0.8" />
                    <text x="560" y="285" fontSize="10" fill="#374151">Durban</text>
                  </>
                )}
              </svg>

              {/* Property Markers */}
              {filteredProperties.map((property, index) => {
                if (!property.coordinates) return null;
                
                // Convert lat/lng to screen coordinates (simplified)
                const x = ((property.coordinates.longitude + 35) / 15) * 800;
                const y = ((property.coordinates.latitude + 35) / 10) * 600;
                
                return (
                  <HoverAnimation key={property.id} type="scale">
                    <div
                      className="absolute transform -translate-x-1/2 -translate-y-1/2 cursor-pointer group"
                      style={{ left: `${Math.max(0, Math.min(100, (x / 800) * 100))}%`, top: `${Math.max(0, Math.min(100, (y / 600) * 100))}%` }}
                      onClick={() => handleMarkerClick(property)}
                      onMouseEnter={() => setHoveredProperty(property)}
                      onMouseLeave={() => setHoveredProperty(null)}
                    >
                      {/* Marker */}
                      <div className={`w-8 h-8 rounded-full border-2 border-white shadow-lg flex items-center justify-center transition-all duration-200 ${
                        property.id === selectedProperty?.id 
                          ? 'bg-red-500 scale-125' 
                          : property.trending
                          ? 'bg-amber-500 scale-110'
                          : 'bg-blue-500 hover:scale-110'
                      }`}>
                        <MapPin className="h-4 w-4 text-white" />
                      </div>
                      
                      {/* Price Badge */}
                      <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-white rounded-full px-2 py-1 shadow-md border opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                        <span className="text-xs font-bold text-gray-900">R{property.price}</span>
                      </div>
                      
                      {/* Property Card on Hover */}
                      {hoveredProperty?.id === property.id && (
                        <div className="absolute top-10 left-1/2 transform -translate-x-1/2 w-64 bg-white rounded-lg shadow-xl border p-4 z-10">
                          <img
                            src={property.images[0] || 'https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=400&h=300&fit=crop'}
                            alt={property.title}
                            className="w-full h-32 object-cover rounded-lg mb-3"
                          />
                          <h4 className="font-bold text-gray-900 mb-1 line-clamp-1">{property.title}</h4>
                          <p className="text-sm text-gray-600 mb-2">{property.location}</p>
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-1">
                              <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                              <span className="text-sm font-medium">{property.averageRating || 4.5}</span>
                            </div>
                            <div className="text-right">
                              <span className="text-lg font-bold text-gray-900">R{property.price}</span>
                              <span className="text-sm text-gray-500">/night</span>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </HoverAnimation>
                );
              })}
            </div>

            {/* Map Controls */}
            <div className="absolute top-4 right-4 flex flex-col gap-2">
              <QuickTooltip text="Zoom In">
                <Button
                  size="sm"
                  variant="secondary"
                  onClick={zoomIn}
                  className="bg-white/90 hover:bg-white shadow-md"
                >
                  <ZoomIn className="h-4 w-4" />
                </Button>
              </QuickTooltip>
              
              <QuickTooltip text="Zoom Out">
                <Button
                  size="sm"
                  variant="secondary"
                  onClick={zoomOut}
                  className="bg-white/90 hover:bg-white shadow-md"
                >
                  <ZoomOut className="h-4 w-4" />
                </Button>
              </QuickTooltip>
            </div>

            {/* Legend */}
            <div className="absolute bottom-4 left-4 bg-white/90 backdrop-blur-sm rounded-lg p-3 shadow-md">
              <h5 className="text-sm font-bold text-gray-900 mb-2">Legend</h5>
              <div className="space-y-1">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <span className="text-xs text-gray-700">Available</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-amber-500 rounded-full"></div>
                  <span className="text-xs text-gray-700">Trending</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                  <span className="text-xs text-gray-700">Selected</span>
                </div>
              </div>
            </div>

            {/* Zoom Level Indicator */}
            <div className="absolute bottom-4 right-4 bg-white/90 backdrop-blur-sm rounded-lg px-3 py-1 shadow-md">
              <span className="text-xs text-gray-700">Zoom: {zoom}</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </SlideIn>
  );
};
