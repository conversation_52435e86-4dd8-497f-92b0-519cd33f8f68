import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  DollarSign,
  TrendingUp,
  Calendar,
  Percent
} from 'lucide-react';

interface PropertyPricingProps {
  data: {
    basePrice: number;
    cleaningFee: number;
    weeklyDiscount: number;
    monthlyDiscount: number;
  };
  errors: Record<string, string>;
  onChange: (updates: any) => void;
}

export const PropertyPricing: React.FC<PropertyPricingProps> = ({
  data,
  errors,
  onChange
}) => {
  const calculateWeeklyPrice = () => {
    const weeklyTotal = data.basePrice * 7;
    const discount = (weeklyTotal * data.weeklyDiscount) / 100;
    return weeklyTotal - discount;
  };

  const calculateMonthlyPrice = () => {
    const monthlyTotal = data.basePrice * 30;
    const discount = (monthlyTotal * data.monthlyDiscount) / 100;
    return monthlyTotal - discount;
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          Set your pricing
        </h2>
        <p className="text-gray-600">
          Set competitive rates to attract guests while maximizing your earnings
        </p>
      </div>

      {/* Base Price */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            Base Price per Night
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="basePrice">Nightly Rate (ZAR) *</Label>
              <div className="relative">
                <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                  R
                </span>
                <Input
                  id="basePrice"
                  type="number"
                  min="0"
                  step="10"
                  placeholder="0"
                  value={data.basePrice || ''}
                  onChange={(e) => onChange({ basePrice: parseFloat(e.target.value) || 0 })}
                  className={`pl-8 ${errors.basePrice ? 'border-red-300' : ''}`}
                />
              </div>
              {errors.basePrice && (
                <p className="text-red-600 text-sm">{errors.basePrice}</p>
              )}
              <p className="text-gray-500 text-sm">
                This is your base rate before any discounts or additional fees
              </p>
            </div>

            {/* Price Suggestions */}
            {data.basePrice > 0 && (
              <div className="bg-blue-50 p-4 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-2">💡 Pricing Suggestions</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-3 text-sm">
                  <div className="text-center p-2 bg-white rounded border">
                    <p className="text-gray-600">Conservative</p>
                    <p className="font-bold text-green-600">{formatCurrency(data.basePrice * 0.9)}</p>
                    <p className="text-xs text-gray-500">Higher occupancy</p>
                  </div>
                  <div className="text-center p-2 bg-white rounded border border-blue-300">
                    <p className="text-gray-600">Current</p>
                    <p className="font-bold text-blue-600">{formatCurrency(data.basePrice)}</p>
                    <p className="text-xs text-gray-500">Your rate</p>
                  </div>
                  <div className="text-center p-2 bg-white rounded border">
                    <p className="text-gray-600">Premium</p>
                    <p className="font-bold text-orange-600">{formatCurrency(data.basePrice * 1.1)}</p>
                    <p className="text-xs text-gray-500">Higher profit</p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Additional Fees */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Additional Fees
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="cleaningFee">Cleaning Fee (ZAR)</Label>
              <div className="relative">
                <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                  R
                </span>
                <Input
                  id="cleaningFee"
                  type="number"
                  min="0"
                  step="10"
                  placeholder="0"
                  value={data.cleaningFee || ''}
                  onChange={(e) => onChange({ cleaningFee: parseFloat(e.target.value) || 0 })}
                  className={`pl-8 ${errors.cleaningFee ? 'border-red-300' : ''}`}
                />
              </div>
              {errors.cleaningFee && (
                <p className="text-red-600 text-sm">{errors.cleaningFee}</p>
              )}
              <p className="text-gray-500 text-sm">
                One-time fee charged per booking to cover cleaning costs
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Discounts */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Percent className="h-5 w-5" />
            Long-stay Discounts
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="weeklyDiscount">Weekly Discount (%)</Label>
                <Input
                  id="weeklyDiscount"
                  type="number"
                  min="0"
                  max="50"
                  step="1"
                  placeholder="0"
                  value={data.weeklyDiscount || ''}
                  onChange={(e) => onChange({ weeklyDiscount: parseFloat(e.target.value) || 0 })}
                  className={errors.weeklyDiscount ? 'border-red-300' : ''}
                />
                {errors.weeklyDiscount && (
                  <p className="text-red-600 text-sm">{errors.weeklyDiscount}</p>
                )}
                <p className="text-gray-500 text-sm">
                  Discount for stays of 7+ nights
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="monthlyDiscount">Monthly Discount (%)</Label>
                <Input
                  id="monthlyDiscount"
                  type="number"
                  min="0"
                  max="50"
                  step="1"
                  placeholder="0"
                  value={data.monthlyDiscount || ''}
                  onChange={(e) => onChange({ monthlyDiscount: parseFloat(e.target.value) || 0 })}
                  className={errors.monthlyDiscount ? 'border-red-300' : ''}
                />
                {errors.monthlyDiscount && (
                  <p className="text-red-600 text-sm">{errors.monthlyDiscount}</p>
                )}
                <p className="text-gray-500 text-sm">
                  Discount for stays of 30+ nights
                </p>
              </div>
            </div>

            {/* Discount Preview */}
            {(data.weeklyDiscount > 0 || data.monthlyDiscount > 0) && data.basePrice > 0 && (
              <div className="bg-green-50 p-4 rounded-lg">
                <h4 className="font-medium text-green-900 mb-2">Discount Preview</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                  {data.weeklyDiscount > 0 && (
                    <div className="text-center p-2 bg-white rounded border">
                      <p className="text-gray-600">7-night stay</p>
                      <p className="font-bold text-green-600">{formatCurrency(calculateWeeklyPrice())}</p>
                      <p className="text-xs text-gray-500">
                        Save {formatCurrency((data.basePrice * 7 * data.weeklyDiscount) / 100)}
                      </p>
                    </div>
                  )}
                  {data.monthlyDiscount > 0 && (
                    <div className="text-center p-2 bg-white rounded border">
                      <p className="text-gray-600">30-night stay</p>
                      <p className="font-bold text-green-600">{formatCurrency(calculateMonthlyPrice())}</p>
                      <p className="text-xs text-gray-500">
                        Save {formatCurrency((data.basePrice * 30 * data.monthlyDiscount) / 100)}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Pricing Summary */}
      {data.basePrice > 0 && (
        <Card className="bg-gray-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Pricing Summary
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Base price per night</span>
                <span className="font-medium">{formatCurrency(data.basePrice)}</span>
              </div>
              
              {data.cleaningFee > 0 && (
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Cleaning fee</span>
                  <span className="font-medium">{formatCurrency(data.cleaningFee)}</span>
                </div>
              )}

              <hr className="border-gray-300" />

              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">1 night total</span>
                  <span className="font-bold text-lg">{formatCurrency(data.basePrice + (data.cleaningFee || 0))}</span>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">3 nights total</span>
                  <span className="font-medium">{formatCurrency((data.basePrice * 3) + (data.cleaningFee || 0))}</span>
                </div>

                {data.weeklyDiscount > 0 && (
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">7 nights total (with discount)</span>
                    <span className="font-medium text-green-600">
                      {formatCurrency(calculateWeeklyPrice() + (data.cleaningFee || 0))}
                    </span>
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Tips */}
      <Card className="bg-blue-50 border-blue-200">
        <CardContent className="p-4">
          <h4 className="font-medium text-blue-900 mb-2">💡 Pricing Tips</h4>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• Research similar properties in your area to set competitive rates</li>
            <li>• Consider seasonal demand when setting your base price</li>
            <li>• Weekly and monthly discounts can attract longer stays</li>
            <li>• Cleaning fees should reflect actual cleaning costs</li>
            <li>• You can always adjust your pricing later based on demand</li>
            <li>• Higher prices during peak seasons can maximize earnings</li>
          </ul>
        </CardContent>
      </Card>
    </div>
  );
};
