interface HostProperty {
  id: string;
  title: string;
  description: string;
  location: string;
  property_type: string;
  max_guests: number;
  bedrooms: number;
  bathrooms: number;
  price_per_night: number;
  cleaning_fee: number;
  status: 'active' | 'inactive' | 'pending' | 'suspended';
  created_at: string;
  updated_at: string;
  images: string[];
  amenities: string[];
  average_rating: number;
  review_count: number;
  total_bookings: number;
  confirmed_bookings: number;
  total_revenue: number;
  last_booking_date?: string;
}

interface PropertyAnalytics {
  period: string;
  start_date: string;
  end_date: string;
  booking_stats: {
    total_bookings: number;
    confirmed_bookings: number;
    cancelled_bookings: number;
    total_revenue: number;
    average_booking_value: number;
    average_stay_length: number;
    occupancy_rate: number;
  };
  daily_data: Array<{
    date: string;
    bookings: number;
    revenue: number;
  }>;
}

interface HostDashboardStats {
  overview: {
    total_properties: number;
    active_properties: number;
    total_bookings: number;
    confirmed_bookings: number;
    total_revenue: number;
    average_rating: number;
    total_reviews: number;
  };
  recent_bookings: Array<{
    id: string;
    property_id: string;
    guest_id: string;
    check_in_date: string;
    check_out_date: string;
    total_amount: number;
    booking_status: string;
    created_at: string;
    property_title: string;
    guest_name: string;
  }>;
  monthly_revenue: Array<{
    month: string;
    revenue: number;
    bookings: number;
  }>;
}

interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

class PropertyManagementService {
  private baseUrl = 'http://localhost:3001/api/property-management';

  private async makeRequest<T>(url: string, options: RequestInit = {}): Promise<ApiResponse<T>> {
    try {
      const token = localStorage.getItem('auth_token');
      const headers: HeadersInit = {
        'Content-Type': 'application/json',
        ...options.headers,
      };

      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const response = await fetch(url, {
        ...options,
        headers,
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Property Management API error:', error);
      throw error;
    }
  }

  // Get host properties
  async getHostProperties(options: {
    limit?: number;
    offset?: number;
    status?: string;
  } = {}): Promise<HostProperty[]> {
    try {
      const params = new URLSearchParams({
        action: 'properties',
        ...(options.limit && { limit: options.limit.toString() }),
        ...(options.offset && { offset: options.offset.toString() }),
        ...(options.status && { status: options.status }),
      });

      const response = await this.makeRequest<HostProperty[]>(`${this.baseUrl}?${params}`);
      
      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to fetch host properties');
      }
    } catch (error) {
      console.error('Error fetching host properties:', error);
      // Return mock data for testing
      return this.getMockHostProperties();
    }
  }

  // Get property analytics
  async getPropertyAnalytics(propertyId: string, period: string = '30d'): Promise<PropertyAnalytics> {
    try {
      const params = new URLSearchParams({
        action: 'analytics',
        property_id: propertyId,
        period: period,
      });

      const response = await this.makeRequest<PropertyAnalytics>(`${this.baseUrl}?${params}`);
      
      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to fetch property analytics');
      }
    } catch (error) {
      console.error('Error fetching property analytics:', error);
      // Return mock data for testing
      return this.getMockPropertyAnalytics(propertyId, period);
    }
  }

  // Get host dashboard stats
  async getHostDashboardStats(): Promise<HostDashboardStats> {
    try {
      const params = new URLSearchParams({ action: 'dashboard' });

      const response = await this.makeRequest<HostDashboardStats>(`${this.baseUrl}?${params}`);
      
      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to fetch dashboard stats');
      }
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
      // Return mock data for testing
      return this.getMockDashboardStats();
    }
  }

  // Update property status
  async updatePropertyStatus(propertyId: string, status: string): Promise<boolean> {
    try {
      const params = new URLSearchParams({
        property_id: propertyId,
      });

      const response = await this.makeRequest<void>(`${this.baseUrl}?${params}`, {
        method: 'PUT',
        body: JSON.stringify({ status }),
      });
      
      return response.success;
    } catch (error) {
      console.error('Error updating property status:', error);
      throw error;
    }
  }

  // Helper methods
  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
      minimumFractionDigits: 0,
    }).format(amount);
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('en-ZA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  }

  formatDateTime(dateString: string): string {
    return new Date(dateString).toLocaleString('en-ZA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  }

  getStatusColor(status: string): string {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'inactive':
        return 'bg-gray-100 text-gray-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'suspended':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  getBookingStatusColor(status: string): string {
    switch (status) {
      case 'confirmed':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      case 'completed':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  calculateOccupancyRate(totalDays: number, bookedDays: number): number {
    return totalDays > 0 ? Math.round((bookedDays / totalDays) * 100) : 0;
  }

  // Mock data for testing
  getMockHostProperties(): HostProperty[] {
    return [
      {
        id: 'prop-1',
        title: 'Beachfront Villa in Margate',
        description: 'Stunning beachfront villa with panoramic ocean views',
        location: 'Margate, KZN',
        property_type: 'villa',
        max_guests: 8,
        bedrooms: 4,
        bathrooms: 3,
        price_per_night: 1200,
        cleaning_fee: 200,
        status: 'active',
        created_at: '2024-01-15T10:00:00Z',
        updated_at: '2024-06-20T15:30:00Z',
        images: [
          'https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=800',
          'https://images.unsplash.com/photo-1502672260266-1c1ef2d93688?w=800'
        ],
        amenities: ['pool', 'wifi', 'parking', 'beach_access', 'kitchen'],
        average_rating: 4.8,
        review_count: 24,
        total_bookings: 45,
        confirmed_bookings: 42,
        total_revenue: 48600,
        last_booking_date: '2024-06-18T00:00:00Z'
      },
      {
        id: 'prop-2',
        title: 'Cozy Cottage in Scottburgh',
        description: 'Charming cottage perfect for a peaceful getaway',
        location: 'Scottburgh, KZN',
        property_type: 'cottage',
        max_guests: 4,
        bedrooms: 2,
        bathrooms: 1,
        price_per_night: 800,
        cleaning_fee: 150,
        status: 'active',
        created_at: '2024-02-10T14:00:00Z',
        updated_at: '2024-06-19T09:15:00Z',
        images: [
          'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=800'
        ],
        amenities: ['wifi', 'parking', 'garden', 'fireplace'],
        average_rating: 4.6,
        review_count: 18,
        total_bookings: 28,
        confirmed_bookings: 26,
        total_revenue: 22400,
        last_booking_date: '2024-06-15T00:00:00Z'
      }
    ];
  }

  getMockPropertyAnalytics(propertyId: string, period: string): PropertyAnalytics {
    return {
      period,
      start_date: '2024-05-21',
      end_date: '2024-06-21',
      booking_stats: {
        total_bookings: 12,
        confirmed_bookings: 11,
        cancelled_bookings: 1,
        total_revenue: 13200,
        average_booking_value: 1200,
        average_stay_length: 3.2,
        occupancy_rate: 68
      },
      daily_data: [
        { date: '2024-06-01', bookings: 2, revenue: 2400 },
        { date: '2024-06-05', bookings: 1, revenue: 1200 },
        { date: '2024-06-10', bookings: 3, revenue: 3600 },
        { date: '2024-06-15', bookings: 2, revenue: 2400 },
        { date: '2024-06-20', bookings: 1, revenue: 1200 }
      ]
    };
  }

  getMockDashboardStats(): HostDashboardStats {
    return {
      overview: {
        total_properties: 2,
        active_properties: 2,
        total_bookings: 73,
        confirmed_bookings: 68,
        total_revenue: 71000,
        average_rating: 4.7,
        total_reviews: 42
      },
      recent_bookings: [
        {
          id: 'booking-1',
          property_id: 'prop-1',
          guest_id: 'guest-1',
          check_in_date: '2024-06-25',
          check_out_date: '2024-06-28',
          total_amount: 3600,
          booking_status: 'confirmed',
          created_at: '2024-06-21T10:30:00Z',
          property_title: 'Beachfront Villa in Margate',
          guest_name: 'John Smith'
        },
        {
          id: 'booking-2',
          property_id: 'prop-2',
          guest_id: 'guest-2',
          check_in_date: '2024-06-30',
          check_out_date: '2024-07-02',
          total_amount: 1600,
          booking_status: 'pending',
          created_at: '2024-06-20T15:45:00Z',
          property_title: 'Cozy Cottage in Scottburgh',
          guest_name: 'Sarah Johnson'
        }
      ],
      monthly_revenue: [
        { month: '2024-01', revenue: 8400, bookings: 7 },
        { month: '2024-02', revenue: 12600, bookings: 10 },
        { month: '2024-03', revenue: 15800, bookings: 13 },
        { month: '2024-04', revenue: 11200, bookings: 9 },
        { month: '2024-05', revenue: 14200, bookings: 12 },
        { month: '2024-06', revenue: 9800, bookings: 8 }
      ]
    };
  }
}

export const propertyManagementService = new PropertyManagementService();
export type { 
  HostProperty, 
  PropertyAnalytics, 
  HostDashboardStats, 
  ApiResponse 
};
