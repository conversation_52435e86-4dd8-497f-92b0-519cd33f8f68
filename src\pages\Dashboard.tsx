import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useBooking } from '../contexts/BookingContext';
import { Header } from '../components/Header';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { User, Mail, Phone, Calendar, MapPin, Star, Clock, CheckCircle } from 'lucide-react';

export const Dashboard: React.FC = () => {
  const { user } = useAuth();
  const { upcomingBookings, getUpcomingBookings, loading } = useBooking();
  const navigate = useNavigate();

  useEffect(() => {
    if (user) {
      getUpcomingBookings();
    }
  }, [user, getUpcomingBookings]);

  if (!user) {
    return null; // This should be handled by ProtectedRoute
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Welcome back, {user.firstName}!
          </h1>
          <p className="text-gray-600">
            Manage your bookings, properties, and account settings.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* User Profile Card */}
          <Card className="lg:col-span-1">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Profile Information
              </CardTitle>
              <CardDescription>
                Your account details and verification status
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Name:</span>
                <span className="text-sm text-gray-600">
                  {user.firstName} {user.lastName}
                </span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Email:</span>
                <div className="flex items-center gap-2">
                  <Mail className="h-4 w-4 text-gray-400" />
                  <span className="text-sm text-gray-600">{user.email}</span>
                </div>
              </div>

              {user.phone && (
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Phone:</span>
                  <div className="flex items-center gap-2">
                    <Phone className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-600">{user.phone}</span>
                  </div>
                </div>
              )}

              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Role:</span>
                <Badge variant={user.role === 'admin' ? 'default' : 'secondary'}>
                  {user.role}
                </Badge>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Email Verified:</span>
                <Badge variant={user.emailVerified ? 'default' : 'destructive'}>
                  {user.emailVerified ? 'Verified' : 'Not Verified'}
                </Badge>
              </div>

              <div className="pt-4">
                <Button variant="outline" className="w-full">
                  Edit Profile
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
              <CardDescription>
                Common tasks and shortcuts
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Button
                  className="h-20 flex flex-col items-center justify-center gap-2"
                  onClick={() => navigate('/bookings')}
                >
                  <Calendar className="h-6 w-6" />
                  <span>My Bookings</span>
                </Button>
                
                <Button variant="outline" className="h-20 flex flex-col items-center justify-center gap-2">
                  <MapPin className="h-6 w-6" />
                  <span>List Property</span>
                </Button>
                
                <Button variant="outline" className="h-20 flex flex-col items-center justify-center gap-2">
                  <Star className="h-6 w-6" />
                  <span>My Reviews</span>
                </Button>
                
                <Button variant="outline" className="h-20 flex flex-col items-center justify-center gap-2">
                  <User className="h-6 w-6" />
                  <span>Account Settings</span>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Upcoming Bookings */}
        <Card className="mt-6">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Upcoming Bookings</span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => navigate('/bookings')}
              >
                View All
              </Button>
            </CardTitle>
            <CardDescription>
              Your upcoming trips and reservations
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-sea-green-600 mx-auto mb-4"></div>
                <p className="text-gray-600">Loading bookings...</p>
              </div>
            ) : upcomingBookings.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <Calendar className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p>No upcoming bookings.</p>
                <p className="text-sm">Start by exploring properties and making a booking!</p>
                <Button
                  className="mt-4 bg-sea-green-500 hover:bg-sea-green-600"
                  onClick={() => navigate('/')}
                >
                  Browse Properties
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                {upcomingBookings.slice(0, 3).map((booking) => (
                  <div key={booking.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900">{booking.propertyTitle}</h4>
                      <p className="text-sm text-gray-600 flex items-center gap-1">
                        <MapPin className="h-3 w-3" />
                        {booking.propertyLocation}
                      </p>
                      <p className="text-sm text-gray-600 flex items-center gap-1 mt-1">
                        <Calendar className="h-3 w-3" />
                        {new Date(booking.checkInDate).toLocaleDateString()} - {new Date(booking.checkOutDate).toLocaleDateString()}
                      </p>
                    </div>
                    <div className="text-right">
                      <Badge
                        variant="outline"
                        className={`mb-2 ${
                          booking.bookingStatus === 'confirmed'
                            ? 'bg-green-100 text-green-800 border-green-200'
                            : 'bg-yellow-100 text-yellow-800 border-yellow-200'
                        }`}
                      >
                        {booking.bookingStatus === 'confirmed' ? (
                          <CheckCircle className="h-3 w-3 mr-1" />
                        ) : (
                          <Clock className="h-3 w-3 mr-1" />
                        )}
                        {booking.bookingStatus}
                      </Badge>
                      <p className="text-sm font-medium">R{booking.totalAmount.toLocaleString()}</p>
                    </div>
                  </div>
                ))}
                {upcomingBookings.length > 3 && (
                  <div className="text-center pt-4">
                    <Button
                      variant="outline"
                      onClick={() => navigate('/bookings')}
                    >
                      View {upcomingBookings.length - 3} More Bookings
                    </Button>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Dashboard;
