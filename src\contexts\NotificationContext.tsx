import React, { createContext, useContext, useState, useCallback, useEffect } from 'react';

export type NotificationType = 'info' | 'success' | 'warning' | 'error' | 'booking' | 'message' | 'promotion';

export interface Notification {
  id: string;
  type: NotificationType;
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
  actionUrl?: string;
  actionLabel?: string;
  icon?: string;
  priority?: 'low' | 'medium' | 'high';
  autoHide?: boolean;
  hideAfter?: number; // milliseconds
  persistent?: boolean;
  metadata?: Record<string, any>;
}

interface NotificationContextType {
  notifications: Notification[];
  unreadCount: number;
  addNotification: (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => string;
  removeNotification: (id: string) => void;
  markAsRead: (id: string) => void;
  markAllAsRead: () => void;
  clearAll: () => void;
  clearRead: () => void;
  getNotificationsByType: (type: NotificationType) => Notification[];
  getUnreadNotifications: () => Notification[];
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

export const useNotifications = () => {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
};

interface NotificationProviderProps {
  children: React.ReactNode;
  maxNotifications?: number;
  storageKey?: string;
}

export const NotificationProvider: React.FC<NotificationProviderProps> = ({
  children,
  maxNotifications = 50,
  storageKey = 'stayfinder-notifications'
}) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);

  // Load notifications from localStorage on mount
  useEffect(() => {
    try {
      const stored = localStorage.getItem(storageKey);
      if (stored) {
        const parsed = JSON.parse(stored);
        // Convert date strings back to Date objects
        const notificationsWithDates = parsed.map((notification: any) => ({
          ...notification,
          timestamp: new Date(notification.timestamp)
        }));
        setNotifications(notificationsWithDates);
      }
    } catch (error) {
      console.warn('Failed to load notifications from localStorage:', error);
    }
  }, [storageKey]);

  // Save notifications to localStorage whenever they change
  const saveNotifications = useCallback((notifs: Notification[]) => {
    try {
      localStorage.setItem(storageKey, JSON.stringify(notifs));
    } catch (error) {
      console.warn('Failed to save notifications to localStorage:', error);
    }
  }, [storageKey]);

  const addNotification = useCallback((
    notificationData: Omit<Notification, 'id' | 'timestamp' | 'read'>
  ): string => {
    const id = `notification-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    const newNotification: Notification = {
      ...notificationData,
      id,
      timestamp: new Date(),
      read: false,
      priority: notificationData.priority || 'medium',
      autoHide: notificationData.autoHide ?? true,
      hideAfter: notificationData.hideAfter || 5000,
      persistent: notificationData.persistent || false
    };

    setNotifications(prev => {
      // Add new notification to the beginning
      let newNotifications = [newNotification, ...prev];
      
      // Limit the number of notifications
      if (newNotifications.length > maxNotifications) {
        newNotifications = newNotifications.slice(0, maxNotifications);
      }
      
      saveNotifications(newNotifications);
      return newNotifications;
    });

    // Auto-hide notification if specified
    if (newNotification.autoHide && !newNotification.persistent) {
      setTimeout(() => {
        removeNotification(id);
      }, newNotification.hideAfter);
    }

    return id;
  }, [maxNotifications, saveNotifications]);

  const removeNotification = useCallback((id: string) => {
    setNotifications(prev => {
      const newNotifications = prev.filter(notification => notification.id !== id);
      saveNotifications(newNotifications);
      return newNotifications;
    });
  }, [saveNotifications]);

  const markAsRead = useCallback((id: string) => {
    setNotifications(prev => {
      const newNotifications = prev.map(notification =>
        notification.id === id ? { ...notification, read: true } : notification
      );
      saveNotifications(newNotifications);
      return newNotifications;
    });
  }, [saveNotifications]);

  const markAllAsRead = useCallback(() => {
    setNotifications(prev => {
      const newNotifications = prev.map(notification => ({ ...notification, read: true }));
      saveNotifications(newNotifications);
      return newNotifications;
    });
  }, [saveNotifications]);

  const clearAll = useCallback(() => {
    setNotifications([]);
    saveNotifications([]);
  }, [saveNotifications]);

  const clearRead = useCallback(() => {
    setNotifications(prev => {
      const newNotifications = prev.filter(notification => !notification.read);
      saveNotifications(newNotifications);
      return newNotifications;
    });
  }, [saveNotifications]);

  const getNotificationsByType = useCallback((type: NotificationType) => {
    return notifications.filter(notification => notification.type === type);
  }, [notifications]);

  const getUnreadNotifications = useCallback(() => {
    return notifications.filter(notification => !notification.read);
  }, [notifications]);

  const unreadCount = notifications.filter(notification => !notification.read).length;

  const value: NotificationContextType = {
    notifications,
    unreadCount,
    addNotification,
    removeNotification,
    markAsRead,
    markAllAsRead,
    clearAll,
    clearRead,
    getNotificationsByType,
    getUnreadNotifications
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
};

// Utility functions for common notification types
export const createBookingNotification = (
  title: string,
  message: string,
  bookingId?: string
): Omit<Notification, 'id' | 'timestamp' | 'read'> => ({
  type: 'booking',
  title,
  message,
  icon: '📅',
  priority: 'high',
  persistent: true,
  autoHide: false,
  actionUrl: bookingId ? `/bookings/${bookingId}` : '/bookings',
  actionLabel: 'View Booking',
  metadata: { bookingId }
});

export const createMessageNotification = (
  title: string,
  message: string,
  senderId?: string
): Omit<Notification, 'id' | 'timestamp' | 'read'> => ({
  type: 'message',
  title,
  message,
  icon: '💬',
  priority: 'medium',
  persistent: false,
  autoHide: true,
  hideAfter: 8000,
  actionUrl: senderId ? `/messages/${senderId}` : '/messages',
  actionLabel: 'View Message',
  metadata: { senderId }
});

export const createPromotionNotification = (
  title: string,
  message: string,
  promoCode?: string
): Omit<Notification, 'id' | 'timestamp' | 'read'> => ({
  type: 'promotion',
  title,
  message,
  icon: '🎉',
  priority: 'low',
  persistent: false,
  autoHide: true,
  hideAfter: 10000,
  actionUrl: '/search',
  actionLabel: 'Browse Properties',
  metadata: { promoCode }
});

export const createSystemNotification = (
  type: 'success' | 'error' | 'warning' | 'info',
  title: string,
  message: string,
  persistent = false
): Omit<Notification, 'id' | 'timestamp' | 'read'> => ({
  type,
  title,
  message,
  icon: type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️',
  priority: type === 'error' ? 'high' : type === 'warning' ? 'medium' : 'low',
  persistent,
  autoHide: !persistent,
  hideAfter: type === 'error' ? 8000 : 5000
});
