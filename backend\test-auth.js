// Test authentication endpoints
const axios = require('axios');

const API_BASE = 'http://localhost:3001/api';

async function testAuthEndpoints() {
  console.log('🧪 Testing Authentication Endpoints...\n');

  try {
    // Test 1: Registration endpoint
    console.log('1️⃣ Testing Registration Endpoint...');
    
    const registrationData = {
      email: '<EMAIL>',
      password: 'password123',
      firstName: 'Test',
      lastName: 'User'
    };

    try {
      const registerResponse = await axios.post(`${API_BASE}/auth/register`, registrationData);
      
      if (registerResponse.status === 201) {
        console.log('✅ Registration endpoint responds with 201 status');
        console.log('✅ JWT token generated and returned');
        
        const { user, token } = registerResponse.data;
        console.log(`   User ID: ${user.id}`);
        console.log(`   Email: ${user.email}`);
        console.log(`   Token: ${token ? 'Generated' : 'Missing'}`);
        
        // Test 2: Login endpoint
        console.log('\n2️⃣ Testing Login Endpoint...');
        
        const loginData = {
          email: '<EMAIL>',
          password: 'password123'
        };

        const loginResponse = await axios.post(`${API_BASE}/auth/login`, loginData);
        
        if (loginResponse.status === 200) {
          console.log('✅ Login endpoint responds with 200 status for valid credentials');
          console.log('✅ JWT token generated and returned');
          console.log('✅ User data returned (without password)');
          
          const loginToken = loginResponse.data.token;
          
          // Test 3: Profile endpoint
          console.log('\n3️⃣ Testing Profile Endpoint...');
          
          const profileResponse = await axios.get(`${API_BASE}/auth/profile`, {
            headers: {
              'Authorization': `Bearer ${loginToken}`
            }
          });
          
          if (profileResponse.status === 200) {
            console.log('✅ Profile endpoint requires authentication');
            console.log('✅ Returns user data for valid token');
            console.log(`   Profile data: ${JSON.stringify(profileResponse.data.user, null, 2)}`);
          }
          
          // Test 4: Profile endpoint without token
          console.log('\n4️⃣ Testing Profile Endpoint without token...');
          
          try {
            await axios.get(`${API_BASE}/auth/profile`);
          } catch (error) {
            if (error.response && error.response.status === 401) {
              console.log('✅ Returns 401 for missing/invalid token');
            }
          }
          
        }
        
      }
      
    } catch (error) {
      if (error.response && error.response.status === 409) {
        console.log('ℹ️  User already exists, testing login instead...');
        
        // Test login with existing user
        const loginData = {
          email: '<EMAIL>',
          password: 'password123'
        };

        const loginResponse = await axios.post(`${API_BASE}/auth/login`, loginData);
        
        if (loginResponse.status === 200) {
          console.log('✅ Login endpoint responds with 200 status for valid credentials');
          console.log('✅ JWT token generated and returned');
        }
        
      } else {
        throw error;
      }
    }

    // Test 5: Invalid login credentials
    console.log('\n5️⃣ Testing Login with invalid credentials...');
    
    try {
      await axios.post(`${API_BASE}/auth/login`, {
        email: '<EMAIL>',
        password: 'wrongpassword'
      });
    } catch (error) {
      if (error.response && error.response.status === 401) {
        console.log('✅ Returns 401 for invalid credentials');
      }
    }

    console.log('\n🎉 All authentication tests completed successfully!');
    
  } catch (error) {
    console.error('❌ Authentication test failed:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Solution: Please start the backend server first');
      console.log('   1. Open terminal in backend folder');
      console.log('   2. Run: npm run dev');
      console.log('   3. Wait for "Server running on port 3001"');
      console.log('   4. Run this test again');
    }
  }
}

// Run the tests
testAuthEndpoints();
