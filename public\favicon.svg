<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#14b8a6;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#14b8a6;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="16" cy="16" r="15" fill="url(#gradient)" stroke="#ffffff" stroke-width="2"/>
  
  <!-- House icon -->
  <path d="M8 20V12L16 6L24 12V20H20V16H12V20H8Z" fill="white" stroke="white" stroke-width="1" stroke-linejoin="round"/>
  
  <!-- Roof detail -->
  <path d="M6 14L16 6L26 14" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
  
  <!-- Door -->
  <rect x="14" y="16" width="4" height="4" fill="url(#gradient)" rx="0.5"/>
  
  <!-- Window -->
  <circle cx="18" cy="14" r="1" fill="white"/>
</svg>
