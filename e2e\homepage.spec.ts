import { test, expect } from '@playwright/test';

test.describe('Homepage', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
  });

  test('should load homepage successfully', async ({ page }) => {
    // Check page title
    await expect(page).toHaveTitle(/StayFinder/);
    
    // Check main heading
    await expect(page.getByRole('heading', { name: /find your perfect stay/i })).toBeVisible();
    
    // Check search bar is present
    await expect(page.getByPlaceholder(/where are you going/i)).toBeVisible();
    
    // Check navigation menu
    await expect(page.getByRole('navigation')).toBeVisible();
  });

  test('should display hero section', async ({ page }) => {
    // Check hero content
    await expect(page.getByText(/discover amazing places/i)).toBeVisible();
    
    // Check CTA button
    await expect(page.getByRole('button', { name: /start exploring/i })).toBeVisible();
    
    // Check hero image or background
    const heroSection = page.locator('[data-testid="hero-section"]').first();
    await expect(heroSection).toBeVisible();
  });

  test('should show featured properties', async ({ page }) => {
    // Wait for properties to load
    await page.waitForSelector('[data-testid="property-card"]', { timeout: 10000 });
    
    // Check that property cards are displayed
    const propertyCards = page.locator('[data-testid="property-card"]');
    await expect(propertyCards.first()).toBeVisible();
    
    // Check property card content
    await expect(propertyCards.first().getByText(/R/)).toBeVisible(); // Price
    await expect(propertyCards.first().getByText(/guests/)).toBeVisible(); // Guest count
  });

  test('should navigate to search page from search bar', async ({ page }) => {
    // Fill search form
    await page.getByPlaceholder(/where are you going/i).fill('Cape Town');
    
    // Click search button
    await page.getByRole('button', { name: /search/i }).click();
    
    // Should navigate to search results
    await expect(page).toHaveURL(/\/search/);
    await expect(page.getByText(/search results/i)).toBeVisible();
  });

  test('should show location suggestions', async ({ page }) => {
    // Type in location input
    await page.getByPlaceholder(/where are you going/i).fill('Cape');
    
    // Wait for suggestions to appear
    await page.waitForSelector('[data-testid="location-suggestions"]', { timeout: 5000 });
    
    // Check suggestions are visible
    await expect(page.getByText(/cape town/i)).toBeVisible();
  });

  test('should handle guest count selection', async ({ page }) => {
    // Click guests selector
    await page.getByText(/guests/i).click();
    
    // Increment guest count
    await page.getByRole('button', { name: /\+/i }).first().click();
    
    // Check updated count
    await expect(page.getByText(/2 guests/i)).toBeVisible();
  });

  test('should display footer information', async ({ page }) => {
    // Scroll to footer
    await page.locator('footer').scrollIntoViewIfNeeded();
    
    // Check footer content
    await expect(page.getByText(/stayfinder/i)).toBeVisible();
    await expect(page.getByText(/about us/i)).toBeVisible();
    await expect(page.getByText(/contact/i)).toBeVisible();
  });

  test('should be responsive on mobile', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Check mobile navigation
    const mobileMenu = page.getByRole('button', { name: /menu/i });
    if (await mobileMenu.isVisible()) {
      await mobileMenu.click();
      await expect(page.getByRole('navigation')).toBeVisible();
    }
    
    // Check search bar is still functional
    await expect(page.getByPlaceholder(/where are you going/i)).toBeVisible();
  });

  test('should handle loading states', async ({ page }) => {
    // Intercept API calls to simulate slow loading
    await page.route('**/api/properties**', async route => {
      await new Promise(resolve => setTimeout(resolve, 2000));
      await route.continue();
    });
    
    await page.goto('/');
    
    // Check loading indicators
    await expect(page.getByTestId('loading-spinner')).toBeVisible();
    
    // Wait for content to load
    await expect(page.getByTestId('property-card')).toBeVisible({ timeout: 15000 });
  });

  test('should handle error states gracefully', async ({ page }) => {
    // Intercept API calls to simulate errors
    await page.route('**/api/properties**', route => {
      route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Internal server error' })
      });
    });
    
    await page.goto('/');
    
    // Check error message is displayed
    await expect(page.getByText(/something went wrong/i)).toBeVisible();
    
    // Check retry button if available
    const retryButton = page.getByRole('button', { name: /try again/i });
    if (await retryButton.isVisible()) {
      await expect(retryButton).toBeVisible();
    }
  });

  test('should track user interactions', async ({ page }) => {
    // Mock analytics tracking
    await page.addInitScript(() => {
      window.gtag = () => {};
      window.analytics = { track: () => {} };
    });
    
    // Perform trackable actions
    await page.getByRole('button', { name: /start exploring/i }).click();
    
    // Check that tracking events are fired (implementation dependent)
    const trackingCalls = await page.evaluate(() => {
      return window.trackingCalls || [];
    });
    
    // Verify tracking (if implemented)
    expect(trackingCalls.length).toBeGreaterThanOrEqual(0);
  });

  test('should maintain accessibility standards', async ({ page }) => {
    // Check for proper heading hierarchy
    const h1 = page.getByRole('heading', { level: 1 });
    await expect(h1).toBeVisible();
    
    // Check for alt text on images
    const images = page.locator('img');
    const imageCount = await images.count();
    
    for (let i = 0; i < imageCount; i++) {
      const img = images.nth(i);
      await expect(img).toHaveAttribute('alt');
    }
    
    // Check for proper form labels
    const inputs = page.locator('input');
    const inputCount = await inputs.count();
    
    for (let i = 0; i < inputCount; i++) {
      const input = inputs.nth(i);
      const hasLabel = await input.getAttribute('aria-label') || 
                      await input.getAttribute('aria-labelledby') ||
                      await page.locator(`label[for="${await input.getAttribute('id')}"]`).count() > 0;
      
      expect(hasLabel).toBeTruthy();
    }
  });
});
