// Code splitting utilities for StayFinder
import React, { Suspense, ComponentType, LazyExoticComponent } from 'react';
import { LoadingSpinner } from '@/components/ui/loading-spinner';

/**
 * Enhanced lazy loading with retry mechanism
 */
export const lazyWithRetry = <T extends ComponentType<any>>(
  importFunc: () => Promise<{ default: T }>,
  retries: number = 3
): LazyExoticComponent<T> => {
  return React.lazy(async () => {
    let lastError: Error | null = null;
    
    for (let i = 0; i <= retries; i++) {
      try {
        return await importFunc();
      } catch (error) {
        lastError = error as Error;
        
        if (i < retries) {
          // Wait before retrying (exponential backoff)
          await new Promise(resolve => setTimeout(resolve, Math.pow(2, i) * 1000));
        }
      }
    }
    
    throw lastError;
  });
};

/**
 * Preload a lazy component
 */
export const preloadComponent = (importFunc: () => Promise<any>) => {
  return importFunc();
};

/**
 * Create a lazy component with custom loading fallback
 */
export const createLazyComponent = <T extends ComponentType<any>>(
  importFunc: () => Promise<{ default: T }>,
  fallback?: React.ComponentType,
  retries?: number
) => {
  const LazyComponent = lazyWithRetry(importFunc, retries);
  const FallbackComponent = fallback || LoadingSpinner;
  
  return (props: React.ComponentProps<T>) => (
    <Suspense fallback={<FallbackComponent />}>
      <LazyComponent {...props} />
    </Suspense>
  );
};

/**
 * Route-based code splitting helper
 */
export const createLazyRoute = (
  importFunc: () => Promise<{ default: ComponentType<any> }>,
  fallback?: React.ComponentType
) => {
  return createLazyComponent(importFunc, fallback);
};

/**
 * Feature-based code splitting
 */
export const createLazyFeature = <T extends ComponentType<any>>(
  featureName: string,
  importFunc: () => Promise<{ default: T }>
) => {
  const LazyComponent = lazyWithRetry(importFunc);
  
  return (props: React.ComponentProps<T>) => (
    <Suspense 
      fallback={
        <div className="flex items-center justify-center p-8">
          <LoadingSpinner />
          <span className="ml-2 text-gray-600">Loading {featureName}...</span>
        </div>
      }
    >
      <LazyComponent {...props} />
    </Suspense>
  );
};

/**
 * Bundle analyzer helper
 */
export const analyzeBundleSize = () => {
  if (process.env.NODE_ENV === 'development') {
    // Log bundle information in development
    const scripts = Array.from(document.querySelectorAll('script[src]'));
    const totalSize = scripts.reduce((total, script) => {
      const src = (script as HTMLScriptElement).src;
      if (src.includes('chunk') || src.includes('bundle')) {
        // This is a rough estimation - in production you'd use webpack-bundle-analyzer
        return total + 1;
      }
      return total;
    }, 0);
    
    console.log(`Estimated bundle chunks: ${totalSize}`);
  }
};

/**
 * Dynamic import with error handling
 */
export const dynamicImport = async <T>(
  importFunc: () => Promise<T>,
  errorHandler?: (error: Error) => void
): Promise<T | null> => {
  try {
    return await importFunc();
  } catch (error) {
    console.error('Dynamic import failed:', error);
    errorHandler?.(error as Error);
    return null;
  }
};

/**
 * Lazy load utility functions
 */
export const lazyLoadUtils = {
  // Lazy load a utility module
  loadUtils: () => dynamicImport(() => import('@/utils/helpers')),
  
  // Lazy load chart library
  loadCharts: () => dynamicImport(() => import('recharts')),
  
  // Lazy load date utilities
  loadDateUtils: () => dynamicImport(() => import('date-fns')),
  
  // Lazy load animation library
  loadAnimations: () => dynamicImport(() => import('framer-motion')),
  
  // Lazy load map components
  loadMaps: () => dynamicImport(() => import('@/components/maps/InteractivePropertyMap'))
};

/**
 * Component preloader for critical routes
 */
export class ComponentPreloader {
  private preloadedComponents = new Set<string>();
  
  preload(componentName: string, importFunc: () => Promise<any>) {
    if (this.preloadedComponents.has(componentName)) {
      return;
    }
    
    this.preloadedComponents.add(componentName);
    
    // Preload on idle or after a delay
    if ('requestIdleCallback' in window) {
      requestIdleCallback(() => {
        importFunc().catch(console.error);
      });
    } else {
      setTimeout(() => {
        importFunc().catch(console.error);
      }, 100);
    }
  }
  
  preloadCriticalComponents() {
    // Preload components that are likely to be needed soon
    this.preload('PropertyCard', () => import('@/components/PropertyCard'));
    this.preload('SearchFilters', () => import('@/components/SearchFilters'));
    this.preload('UserDashboard', () => import('@/pages/UserDashboard'));
  }
}

export const componentPreloader = new ComponentPreloader();

/**
 * Intersection Observer based lazy loading for components
 */
export const createIntersectionLazyLoader = <T extends ComponentType<any>>(
  importFunc: () => Promise<{ default: T }>,
  options: IntersectionObserverInit = {}
) => {
  const LazyComponent = React.lazy(importFunc);
  
  return React.forwardRef<HTMLDivElement, React.ComponentProps<T>>((props, ref) => {
    const [shouldLoad, setShouldLoad] = React.useState(false);
    const containerRef = React.useRef<HTMLDivElement>(null);
    
    React.useEffect(() => {
      const observer = new IntersectionObserver(
        ([entry]) => {
          if (entry.isIntersecting) {
            setShouldLoad(true);
            observer.disconnect();
          }
        },
        { threshold: 0.1, ...options }
      );
      
      if (containerRef.current) {
        observer.observe(containerRef.current);
      }
      
      return () => observer.disconnect();
    }, []);
    
    return (
      <div ref={ref || containerRef}>
        {shouldLoad ? (
          <Suspense fallback={<LoadingSpinner />}>
            <LazyComponent {...props} />
          </Suspense>
        ) : (
          <div className="h-32 flex items-center justify-center text-gray-500">
            Component will load when visible
          </div>
        )}
      </div>
    );
  });
};

/**
 * Resource hints for better loading performance
 */
export const addResourceHints = () => {
  const head = document.head;
  
  // Preconnect to external domains
  const preconnectDomains = [
    'https://fonts.googleapis.com',
    'https://fonts.gstatic.com',
    'https://api.mapbox.com'
  ];
  
  preconnectDomains.forEach(domain => {
    const link = document.createElement('link');
    link.rel = 'preconnect';
    link.href = domain;
    head.appendChild(link);
  });
  
  // DNS prefetch for other domains
  const dnsPrefetchDomains = [
    'https://images.unsplash.com',
    'https://cdn.jsdelivr.net'
  ];
  
  dnsPrefetchDomains.forEach(domain => {
    const link = document.createElement('link');
    link.rel = 'dns-prefetch';
    link.href = domain;
    head.appendChild(link);
  });
};

/**
 * Module federation helper (for micro-frontends)
 */
export const loadRemoteModule = async (
  remoteName: string,
  moduleName: string
): Promise<any> => {
  try {
    // @ts-ignore - Module federation runtime
    const container = window[remoteName];
    await container.init(__webpack_share_scopes__.default);
    const factory = await container.get(moduleName);
    return factory();
  } catch (error) {
    console.error(`Failed to load remote module ${remoteName}/${moduleName}:`, error);
    throw error;
  }
};

/**
 * Webpack chunk loading optimization
 */
export const optimizeChunkLoading = () => {
  // Set webpack public path dynamically if needed
  if (typeof __webpack_public_path__ !== 'undefined') {
    __webpack_public_path__ = process.env.NODE_ENV === 'production' 
      ? '/static/' 
      : '/';
  }
  
  // Handle chunk load errors
  window.addEventListener('error', (event) => {
    if (event.filename && event.filename.includes('chunk')) {
      console.error('Chunk loading error:', event);
      // Could implement retry logic here
    }
  });
};

/**
 * Performance monitoring for code splitting
 */
export const monitorCodeSplitting = () => {
  if ('performance' in window && 'getEntriesByType' in performance) {
    // Monitor resource loading
    const observer = new PerformanceObserver((list) => {
      list.getEntries().forEach((entry) => {
        if (entry.name.includes('chunk') || entry.name.includes('bundle')) {
          console.log(`Chunk loaded: ${entry.name} in ${entry.duration}ms`);
        }
      });
    });
    
    observer.observe({ entryTypes: ['resource'] });
  }
};

/**
 * Initialize code splitting optimizations
 */
export const initializeCodeSplitting = () => {
  addResourceHints();
  optimizeChunkLoading();
  componentPreloader.preloadCriticalComponents();
  
  if (process.env.NODE_ENV === 'development') {
    monitorCodeSplitting();
    analyzeBundleSize();
  }
};
