import React, { useState } from 'react';
import { InteractivePropertyMap } from '@/components/InteractivePropertyMap';
import { NeighborhoodInfo } from '@/components/NeighborhoodInfo';
import { DistanceCalculator } from '@/components/DistanceCalculator';
import { MapSearch } from '@/components/MapSearch';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Header } from '@/components/Header';
import { 
  PageTransition, 
  SlideIn, 
  StaggeredAnimation 
} from '@/components/ui/page-transitions';
import { EnhancedBreadcrumb } from '@/components/ui/enhanced-breadcrumb';
import { 
  Home, 
  MapPin, 
  Navigation, 
  Route, 
  Target, 
  Layers,
  Map,
  Calculator,
  Compass,
  Globe
} from 'lucide-react';

export const MapsLocationDemo: React.FC = () => {
  const [selectedDemo, setSelectedDemo] = useState<'map' | 'neighborhood' | 'distance' | 'search'>('map');

  // Sample properties for demonstration
  const sampleProperties = [
    {
      id: '1',
      title: 'Luxury Beachfront Villa',
      location: 'Camps Bay, Cape Town',
      price: 2500,
      images: ['https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=800&h=600&fit=crop'],
      coordinates: { latitude: -33.9553, longitude: 18.3756 },
      averageRating: 4.9,
      reviewCount: 127,
      propertyType: 'Villa',
      maxGuests: 8,
      bedrooms: 4,
      bathrooms: 3,
      amenities: ['WiFi', 'Pool', 'Kitchen', 'Parking', 'Ocean View'],
      available: true,
      instantBook: true,
      trending: true
    },
    {
      id: '2',
      title: 'Modern City Apartment',
      location: 'Sandton, Johannesburg',
      price: 850,
      images: ['https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?w=800&h=600&fit=crop'],
      coordinates: { latitude: -26.1076, longitude: 28.0567 },
      averageRating: 4.7,
      reviewCount: 89,
      propertyType: 'Apartment',
      maxGuests: 4,
      bedrooms: 2,
      bathrooms: 2,
      amenities: ['WiFi', 'Gym', 'Kitchen', 'Parking'],
      available: true,
      instantBook: false,
      trending: false
    },
    {
      id: '3',
      title: 'Safari Lodge Experience',
      location: 'Kruger National Park',
      price: 1200,
      images: ['https://images.unsplash.com/photo-1516026672322-bc52d61a55d5?w=800&h=600&fit=crop'],
      coordinates: { latitude: -24.9947, longitude: 31.5914 },
      averageRating: 4.8,
      reviewCount: 156,
      propertyType: 'Lodge',
      maxGuests: 2,
      bedrooms: 1,
      bathrooms: 1,
      amenities: ['WiFi', 'Restaurant', 'Game Drives', 'Spa'],
      available: true,
      instantBook: true,
      trending: true
    },
    {
      id: '4',
      title: 'Coastal Cottage Retreat',
      location: 'Hermanus, Western Cape',
      price: 950,
      images: ['https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=800&h=600&fit=crop'],
      coordinates: { latitude: -34.4187, longitude: 19.2345 },
      averageRating: 4.6,
      reviewCount: 73,
      propertyType: 'Cottage',
      maxGuests: 6,
      bedrooms: 3,
      bathrooms: 2,
      amenities: ['WiFi', 'Kitchen', 'Garden', 'Whale Watching'],
      available: true,
      instantBook: false,
      trending: false
    },
    {
      id: '5',
      title: 'Garden Route Getaway',
      location: 'Knysna, Garden Route',
      price: 750,
      images: ['https://images.unsplash.com/photo-1582268611958-ebfd161ef9cf?w=800&h=600&fit=crop'],
      coordinates: { latitude: -34.0361, longitude: 23.0471 },
      averageRating: 4.5,
      reviewCount: 92,
      propertyType: 'House',
      maxGuests: 5,
      bedrooms: 2,
      bathrooms: 2,
      amenities: ['WiFi', 'Kitchen', 'Forest Views', 'Hiking'],
      available: true,
      instantBook: true,
      trending: false
    }
  ];

  const demoSections = [
    {
      id: 'map',
      title: 'Interactive Property Map',
      description: 'Advanced map with clustering, filters, and property visualization',
      icon: Map,
      color: 'from-blue-500 to-blue-600'
    },
    {
      id: 'neighborhood',
      title: 'Neighborhood Information',
      description: 'Comprehensive area insights with attractions and amenities',
      icon: MapPin,
      color: 'from-green-500 to-green-600'
    },
    {
      id: 'distance',
      title: 'Distance Calculator',
      description: 'Multi-modal travel calculations with South African destinations',
      icon: Calculator,
      color: 'from-purple-500 to-purple-600'
    },
    {
      id: 'search',
      title: 'Enhanced Map Search',
      description: 'Integrated search with advanced filtering and view options',
      icon: Navigation,
      color: 'from-orange-500 to-orange-600'
    }
  ];

  return (
    <PageTransition>
      <div className="min-h-screen bg-gray-50">
        <Header />
        
        <div className="container mx-auto px-4 py-8">
          <SlideIn direction="up" delay={100}>
            <div className="mb-8">
              <EnhancedBreadcrumb 
                items={[
                  { label: 'Home', href: '/', icon: <Home className="h-4 w-4" /> },
                  { label: 'Maps & Location Demo', current: true }
                ]}
              />
              <div className="text-center mt-8">
                <div className="inline-flex items-center gap-2 bg-gradient-to-r from-blue-100 to-green-100 rounded-full px-6 py-3 mb-6">
                  <Globe className="h-5 w-5 text-blue-600" />
                  <span className="text-sm font-semibold text-blue-700">Maps & Location Features</span>
                  <Badge className="bg-blue-500 text-white text-xs">New</Badge>
                </div>
                <h1 className="text-5xl font-bold text-gray-900 mb-4">
                  Interactive Maps & Location Services
                </h1>
                <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                  Explore properties with advanced mapping features, neighborhood insights, 
                  and comprehensive location-based services
                </p>
              </div>
            </div>
          </SlideIn>

          {/* Demo Section Selector */}
          <SlideIn direction="up" delay={200}>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
              <StaggeredAnimation delay={100}>
                {demoSections.map((section) => {
                  const Icon = section.icon;
                  return (
                    <Card 
                      key={section.id}
                      className={`cursor-pointer transition-all duration-300 border-2 ${
                        selectedDemo === section.id 
                          ? 'border-blue-500 shadow-lg scale-105' 
                          : 'border-gray-200 hover:border-blue-300 hover:shadow-md'
                      }`}
                      onClick={() => setSelectedDemo(section.id as any)}
                    >
                      <CardContent className="p-6 text-center">
                        <div className={`w-16 h-16 bg-gradient-to-br ${section.color} rounded-2xl flex items-center justify-center mx-auto mb-4`}>
                          <Icon className="h-8 w-8 text-white" />
                        </div>
                        <h3 className="text-lg font-bold text-gray-900 mb-2">{section.title}</h3>
                        <p className="text-gray-600 text-sm">{section.description}</p>
                      </CardContent>
                    </Card>
                  );
                })}
              </StaggeredAnimation>
            </div>
          </SlideIn>

          {/* Demo Content */}
          <SlideIn direction="up" delay={300}>
            <div className="space-y-8">
              {/* Interactive Property Map Demo */}
              {selectedDemo === 'map' && (
                <div>
                  <Card className="border-0 shadow-lg mb-6">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Map className="h-5 w-5 text-blue-600" />
                        Interactive Property Map
                      </CardTitle>
                      <p className="text-gray-600">
                        Advanced mapping with clustering, multiple view modes, and interactive controls
                      </p>
                    </CardHeader>
                    <CardContent>
                      <InteractivePropertyMap
                        properties={sampleProperties}
                        showControls={true}
                        showSearch={true}
                        showNeighborhood={true}
                        enableClustering={true}
                        height="600px"
                      />
                    </CardContent>
                  </Card>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    {[
                      { title: 'Map Controls', features: ['Zoom in/out', 'Map type toggle', 'Reset view', 'My location', 'Fullscreen mode'] },
                      { title: 'Property Markers', features: ['Color-coded by status', 'Hover for quick info', 'Click for details', 'Price display', 'Clustering'] },
                      { title: 'Interactive Features', features: ['Search on map', 'Filter properties', 'Legend display', 'Responsive design', 'Touch support'] }
                    ].map((feature, index) => (
                      <Card key={index} className="border-0 shadow-md">
                        <CardHeader>
                          <CardTitle className="text-lg">{feature.title}</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <ul className="space-y-2">
                            {feature.features.map((item, i) => (
                              <li key={i} className="flex items-center gap-2 text-sm text-gray-600">
                                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                                {item}
                              </li>
                            ))}
                          </ul>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>
              )}

              {/* Neighborhood Information Demo */}
              {selectedDemo === 'neighborhood' && (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  <NeighborhoodInfo 
                    location="Cape Town, Western Cape"
                    coordinates={{ latitude: -33.9249, longitude: 18.4241 }}
                  />
                  <NeighborhoodInfo 
                    location="Sandton, Johannesburg"
                    coordinates={{ latitude: -26.1076, longitude: 28.0567 }}
                  />
                </div>
              )}

              {/* Distance Calculator Demo */}
              {selectedDemo === 'distance' && (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  <DistanceCalculator 
                    propertyLocation="Cape Town, Western Cape"
                    propertyCoordinates={{ latitude: -33.9249, longitude: 18.4241 }}
                    showNearbyPlaces={true}
                    maxDistance={100}
                  />
                  <DistanceCalculator 
                    propertyLocation="Kruger National Park"
                    propertyCoordinates={{ latitude: -24.9947, longitude: 31.5914 }}
                    showNearbyPlaces={true}
                    maxDistance={150}
                  />
                </div>
              )}

              {/* Enhanced Map Search Demo */}
              {selectedDemo === 'search' && (
                <MapSearch 
                  properties={sampleProperties}
                  onPropertySelect={(property) => console.log('Selected:', property)}
                  onSearchArea={(bounds) => console.log('Search area:', bounds)}
                  className="w-full"
                />
              )}
            </div>
          </SlideIn>

          {/* Features Summary */}
          <SlideIn direction="up" delay={400}>
            <Card className="border-0 shadow-lg mt-12">
              <CardHeader>
                <CardTitle className="text-2xl text-center">Maps & Location Features Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  {[
                    { icon: Map, title: 'Interactive Maps', count: '5+', description: 'Map types and controls' },
                    { icon: MapPin, title: 'Location Data', count: '100+', description: 'South African POIs' },
                    { icon: Route, title: 'Travel Modes', count: '4', description: 'Driving, walking, cycling, transit' },
                    { icon: Target, title: 'Accuracy', count: '99%', description: 'Location precision' }
                  ].map((stat, index) => (
                    <div key={index} className="text-center">
                      <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-green-500 rounded-2xl flex items-center justify-center mx-auto mb-4">
                        <stat.icon className="h-8 w-8 text-white" />
                      </div>
                      <div className="text-3xl font-bold text-gray-900 mb-1">{stat.count}</div>
                      <div className="text-lg font-semibold text-gray-700 mb-1">{stat.title}</div>
                      <div className="text-sm text-gray-600">{stat.description}</div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </SlideIn>
        </div>
      </div>
    </PageTransition>
  );
};
