import { http, HttpResponse } from 'msw';

// Mock data
const mockUser = {
  id: '1',
  email: '<EMAIL>',
  firstName: 'Test',
  lastName: 'User',
  role: 'guest',
  createdAt: '2024-01-01T00:00:00.000Z'
};

const mockProperties = [
  {
    id: '1',
    title: 'Luxury Beachfront Villa',
    description: 'Beautiful villa with ocean views',
    location: 'Camps Bay, Cape Town',
    price: 2500,
    maxGuests: 8,
    bedrooms: 4,
    bathrooms: 3,
    images: ['https://images.unsplash.com/photo-1564013799919-ab600027ffc6'],
    amenities: ['wifi', 'pool', 'parking'],
    rating: 4.9,
    reviewCount: 18,
    hostId: '2',
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z'
  },
  {
    id: '2',
    title: 'Modern City Apartment',
    description: 'Stylish apartment in the heart of the city',
    location: 'Sandton, Johannesburg',
    price: 850,
    maxGuests: 4,
    bedrooms: 2,
    bathrooms: 2,
    images: ['https://images.unsplash.com/photo-1522708323590-d24dbb6b0267'],
    amenities: ['wifi', 'gym', 'parking'],
    rating: 4.7,
    reviewCount: 23,
    hostId: '3',
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z'
  }
];

const mockBookings = [
  {
    id: '1',
    propertyId: '1',
    userId: '1',
    checkIn: '2024-02-15',
    checkOut: '2024-02-20',
    guests: 4,
    totalAmount: 12500,
    status: 'confirmed',
    createdAt: '2024-01-20T00:00:00.000Z'
  }
];

const mockReviews = [
  {
    id: '1',
    propertyId: '1',
    userId: '1',
    rating: 5,
    comment: 'Amazing stay! The villa was perfect.',
    createdAt: '2024-01-25T00:00:00.000Z'
  }
];

export const handlers = [
  // Auth endpoints
  http.post('/api/auth/register', async ({ request }) => {
    const body = await request.json() as any;
    
    if (!body.email || !body.password) {
      return HttpResponse.json(
        { error: 'Email and password are required' },
        { status: 400 }
      );
    }

    return HttpResponse.json({
      user: { ...mockUser, email: body.email },
      token: 'mock-jwt-token'
    });
  }),

  http.post('/api/auth/login', async ({ request }) => {
    const body = await request.json() as any;
    
    if (body.email === '<EMAIL>' && body.password === 'password123') {
      return HttpResponse.json({
        user: mockUser,
        token: 'mock-jwt-token'
      });
    }

    return HttpResponse.json(
      { error: 'Invalid credentials' },
      { status: 401 }
    );
  }),

  http.post('/api/auth/logout', () => {
    return HttpResponse.json({ message: 'Logged out successfully' });
  }),

  http.get('/api/auth/me', ({ request }) => {
    const authHeader = request.headers.get('Authorization');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return HttpResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    return HttpResponse.json({ user: mockUser });
  }),

  // Properties endpoints
  http.get('/api/properties', ({ request }) => {
    const url = new URL(request.url);
    const search = url.searchParams.get('search');
    const location = url.searchParams.get('location');
    const minPrice = url.searchParams.get('minPrice');
    const maxPrice = url.searchParams.get('maxPrice');
    const guests = url.searchParams.get('guests');
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '10');

    let filteredProperties = [...mockProperties];

    // Apply filters
    if (search) {
      filteredProperties = filteredProperties.filter(p => 
        p.title.toLowerCase().includes(search.toLowerCase()) ||
        p.description.toLowerCase().includes(search.toLowerCase())
      );
    }

    if (location) {
      filteredProperties = filteredProperties.filter(p => 
        p.location.toLowerCase().includes(location.toLowerCase())
      );
    }

    if (minPrice) {
      filteredProperties = filteredProperties.filter(p => p.price >= parseInt(minPrice));
    }

    if (maxPrice) {
      filteredProperties = filteredProperties.filter(p => p.price <= parseInt(maxPrice));
    }

    if (guests) {
      filteredProperties = filteredProperties.filter(p => p.maxGuests >= parseInt(guests));
    }

    // Pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedProperties = filteredProperties.slice(startIndex, endIndex);

    return HttpResponse.json({
      properties: paginatedProperties,
      pagination: {
        page,
        limit,
        total: filteredProperties.length,
        pages: Math.ceil(filteredProperties.length / limit)
      }
    });
  }),

  http.get('/api/properties/:id', ({ params }) => {
    const property = mockProperties.find(p => p.id === params.id);
    
    if (!property) {
      return HttpResponse.json(
        { error: 'Property not found' },
        { status: 404 }
      );
    }

    return HttpResponse.json({ property });
  }),

  http.post('/api/properties', async ({ request }) => {
    const authHeader = request.headers.get('Authorization');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return HttpResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json() as any;
    
    const newProperty = {
      id: String(mockProperties.length + 1),
      ...body,
      hostId: mockUser.id,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    mockProperties.push(newProperty);

    return HttpResponse.json({ property: newProperty }, { status: 201 });
  }),

  // Bookings endpoints
  http.get('/api/bookings', ({ request }) => {
    const authHeader = request.headers.get('Authorization');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return HttpResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    return HttpResponse.json({ bookings: mockBookings });
  }),

  http.post('/api/bookings', async ({ request }) => {
    const authHeader = request.headers.get('Authorization');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return HttpResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json() as any;
    
    const newBooking = {
      id: String(mockBookings.length + 1),
      ...body,
      userId: mockUser.id,
      status: 'pending',
      createdAt: new Date().toISOString()
    };

    mockBookings.push(newBooking);

    return HttpResponse.json({ booking: newBooking }, { status: 201 });
  }),

  // Reviews endpoints
  http.get('/api/reviews', ({ request }) => {
    const url = new URL(request.url);
    const propertyId = url.searchParams.get('propertyId');

    let filteredReviews = [...mockReviews];

    if (propertyId) {
      filteredReviews = filteredReviews.filter(r => r.propertyId === propertyId);
    }

    return HttpResponse.json({ reviews: filteredReviews });
  }),

  http.post('/api/reviews', async ({ request }) => {
    const authHeader = request.headers.get('Authorization');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return HttpResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json() as any;
    
    const newReview = {
      id: String(mockReviews.length + 1),
      ...body,
      userId: mockUser.id,
      createdAt: new Date().toISOString()
    };

    mockReviews.push(newReview);

    return HttpResponse.json({ review: newReview }, { status: 201 });
  }),

  // Health check
  http.get('/api/health', () => {
    return HttpResponse.json({ 
      status: 'ok', 
      timestamp: new Date().toISOString(),
      service: 'StayFinder API'
    });
  }),

  // Fallback for unhandled requests
  http.all('*', ({ request }) => {
    console.warn(`Unhandled ${request.method} request to ${request.url}`);
    return HttpResponse.json(
      { error: 'Not found' },
      { status: 404 }
    );
  })
];
