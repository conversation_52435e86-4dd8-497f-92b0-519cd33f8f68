import React, { useState } from 'react';
import { Heart, Star, MapPin, Users, Bed, Bath, Wifi, Car, Waves, Calendar, ArrowRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { OptimizedImage } from './OptimizedImage';
import { useLazyLoading } from '@/hooks/useLazyLoading';

interface Property {
  id: string;
  title: string;
  location: string;
  price: number;
  images: string[];
  description: string;
  available: boolean;
  propertyType?: string;
  maxGuests?: number;
  bedrooms?: number;
  bathrooms?: number;
  amenities?: string[];
  averageRating?: number;
  reviewCount?: number;
  owner?: {
    firstName: string;
    lastName: string;
  };
  coordinates?: {
    latitude: number;
    longitude: number;
  };
  cleaningFee?: number;
}

interface ModernPropertyCardProps {
  property: Property;
  variant?: 'grid' | 'list';
  onFavoriteToggle?: (propertyId: string) => void;
  onBookNow?: (propertyId: string) => void;
  className?: string;
  isFavorited?: boolean;
}

export const ModernPropertyCard: React.FC<ModernPropertyCardProps> = ({
  property,
  variant = 'grid',
  onFavoriteToggle,
  onBookNow,
  className,
  isFavorited = false
}) => {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [imageLoading, setImageLoading] = useState(true);

  const handleFavoriteClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onFavoriteToggle?.(property.id);
  };

  const handleBookNowClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onBookNow?.(property.id);
  };

  const nextImage = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setCurrentImageIndex((prev) => 
      prev === property.images.length - 1 ? 0 : prev + 1
    );
  };

  const prevImage = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setCurrentImageIndex((prev) => 
      prev === 0 ? property.images.length - 1 : prev - 1
    );
  };

  const getAmenityIcon = (amenity: string) => {
    const amenityLower = amenity.toLowerCase();
    if (amenityLower.includes('wifi')) return <Wifi className="h-4 w-4" />;
    if (amenityLower.includes('parking') || amenityLower.includes('car')) return <Car className="h-4 w-4" />;
    if (amenityLower.includes('beach') || amenityLower.includes('pool')) return <Waves className="h-4 w-4" />;
    return null;
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price);
  };

  if (variant === 'list') {
    return (
      <div className={cn(
        "bg-white rounded-2xl shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100 hover:border-sea-green-200 group",
        className
      )}>
        <div className="flex flex-col md:flex-row">
          {/* Image Section */}
          <div className="relative md:w-80 h-64 md:h-auto overflow-hidden">
            {property.images.length > 0 && (
              <>
                <OptimizedImage
                  src={property.images[currentImageIndex]}
                  alt={property.title}
                  className="w-full h-full group-hover:scale-105 transition-transform duration-300"
                  objectFit="cover"
                  lazy={true}
                  priority={false}
                  enableWebP={true}
                  enableAVIF={true}
                  onLoad={() => setImageLoading(false)}
                  aspectRatio="4/3"
                  quality={85}
                />
                {imageLoading && (
                  <div className="absolute inset-0 bg-gray-200 animate-pulse" />
                )}
                
                {/* Image Navigation */}
                {property.images.length > 1 && (
                  <>
                    <button
                      onClick={prevImage}
                      className="absolute left-2 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white rounded-full p-2 shadow-md transition-all duration-200 opacity-0 group-hover:opacity-100"
                    >
                      <ArrowRight className="h-4 w-4 rotate-180" />
                    </button>
                    <button
                      onClick={nextImage}
                      className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white rounded-full p-2 shadow-md transition-all duration-200 opacity-0 group-hover:opacity-100"
                    >
                      <ArrowRight className="h-4 w-4" />
                    </button>
                    
                    {/* Image Dots */}
                    <div className="absolute bottom-3 left-1/2 transform -translate-x-1/2 flex space-x-1">
                      {property.images.map((_, index) => (
                        <button
                          key={index}
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            setCurrentImageIndex(index);
                          }}
                          className={cn(
                            "w-2 h-2 rounded-full transition-all duration-200",
                            index === currentImageIndex ? "bg-white" : "bg-white/50"
                          )}
                        />
                      ))}
                    </div>
                  </>
                )}
              </>
            )}
            
            {/* Favorite Button */}
            <button
              onClick={handleFavoriteClick}
              className="absolute top-3 right-3 bg-white/80 hover:bg-white rounded-full p-2 shadow-md transition-all duration-200"
            >
              <Heart className={cn(
                "h-4 w-4 transition-colors duration-200",
                isFavorited ? "fill-red-500 text-red-500" : "text-gray-600"
              )} />
            </button>
            
            {/* Property Type Badge */}
            {property.propertyType && (
              <Badge className="absolute top-3 left-3 bg-sea-green-500 text-white">
                {property.propertyType}
              </Badge>
            )}
          </div>
          
          {/* Content Section */}
          <div className="flex-1 p-6">
            <div className="flex justify-between items-start mb-3">
              <div>
                <h3 className="text-xl font-bold text-gray-900 mb-1 group-hover:text-sea-green-700 transition-colors duration-200">
                  {property.title}
                </h3>
                <div className="flex items-center text-gray-600 mb-2">
                  <MapPin className="h-4 w-4 mr-1" />
                  <span className="text-sm">{property.location}</span>
                </div>
              </div>
              
              {/* Rating */}
              {property.averageRating && (
                <div className="flex items-center bg-gray-50 rounded-lg px-2 py-1">
                  <Star className="h-4 w-4 text-yellow-400 fill-current mr-1" />
                  <span className="text-sm font-medium">{property.averageRating}</span>
                  {property.reviewCount && (
                    <span className="text-xs text-gray-500 ml-1">({property.reviewCount})</span>
                  )}
                </div>
              )}
            </div>
            
            {/* Property Details */}
            <div className="flex items-center space-x-4 mb-4 text-sm text-gray-600">
              {property.maxGuests && (
                <div className="flex items-center">
                  <Users className="h-4 w-4 mr-1" />
                  <span>{property.maxGuests} guests</span>
                </div>
              )}
              {property.bedrooms && (
                <div className="flex items-center">
                  <Bed className="h-4 w-4 mr-1" />
                  <span>{property.bedrooms} bed{property.bedrooms > 1 ? 's' : ''}</span>
                </div>
              )}
              {property.bathrooms && (
                <div className="flex items-center">
                  <Bath className="h-4 w-4 mr-1" />
                  <span>{property.bathrooms} bath{property.bathrooms > 1 ? 's' : ''}</span>
                </div>
              )}
            </div>
            
            {/* Amenities */}
            {property.amenities && property.amenities.length > 0 && (
              <div className="flex flex-wrap gap-2 mb-4">
                {property.amenities.slice(0, 4).map((amenity, index) => (
                  <div key={index} className="flex items-center bg-gray-50 rounded-lg px-2 py-1">
                    {getAmenityIcon(amenity)}
                    <span className="text-xs text-gray-600 ml-1">{amenity}</span>
                  </div>
                ))}
                {property.amenities.length > 4 && (
                  <div className="flex items-center bg-gray-50 rounded-lg px-2 py-1">
                    <span className="text-xs text-gray-600">+{property.amenities.length - 4} more</span>
                  </div>
                )}
              </div>
            )}
            
            {/* Description */}
            <p className="text-gray-600 text-sm mb-4 line-clamp-2">
              {property.description}
            </p>
            
            {/* Price and Book Button */}
            <div className="flex items-center justify-between">
              <div>
                <div className="text-2xl font-bold text-gray-900">
                  {formatPrice(property.price)}
                  <span className="text-sm font-normal text-gray-600 ml-1">/ night</span>
                </div>
                {property.cleaningFee && (
                  <div className="text-xs text-gray-500">
                    + {formatPrice(property.cleaningFee)} cleaning fee
                  </div>
                )}
              </div>
              
              <Button
                onClick={handleBookNowClick}
                className="bg-gradient-to-r from-sea-green-500 to-ocean-blue-500 hover:from-sea-green-600 hover:to-ocean-blue-600 text-white rounded-xl px-6 py-2 font-semibold transition-all duration-200 hover:scale-105"
              >
                Book Now
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Grid variant (default)
  return (
    <div className={cn(
      "bg-white rounded-2xl shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100 hover:border-sea-green-200 group cursor-pointer",
      className
    )}>
      {/* Image Section */}
      <div className="relative h-64 overflow-hidden">
        {property.images.length > 0 && (
          <>
            <OptimizedImage
              src={property.images[currentImageIndex]}
              alt={property.title}
              className="w-full h-full group-hover:scale-105 transition-transform duration-300"
              objectFit="cover"
              lazy={true}
              priority={false}
              enableWebP={true}
              enableAVIF={true}
              onLoad={() => setImageLoading(false)}
              aspectRatio="4/3"
              quality={85}
            />
            {imageLoading && (
              <div className="absolute inset-0 bg-gray-200 animate-pulse" />
            )}
            
            {/* Image Navigation */}
            {property.images.length > 1 && (
              <>
                <button
                  onClick={prevImage}
                  className="absolute left-2 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white rounded-full p-2 shadow-md transition-all duration-200 opacity-0 group-hover:opacity-100"
                >
                  <ArrowRight className="h-4 w-4 rotate-180" />
                </button>
                <button
                  onClick={nextImage}
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white rounded-full p-2 shadow-md transition-all duration-200 opacity-0 group-hover:opacity-100"
                >
                  <ArrowRight className="h-4 w-4" />
                </button>
                
                {/* Image Dots */}
                <div className="absolute bottom-3 left-1/2 transform -translate-x-1/2 flex space-x-1">
                  {property.images.map((_, index) => (
                    <button
                      key={index}
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        setCurrentImageIndex(index);
                      }}
                      className={cn(
                        "w-2 h-2 rounded-full transition-all duration-200",
                        index === currentImageIndex ? "bg-white" : "bg-white/50"
                      )}
                    />
                  ))}
                </div>
              </>
            )}
          </>
        )}
        
        {/* Favorite Button */}
        <button
          onClick={handleFavoriteClick}
          className="absolute top-3 right-3 bg-white/80 hover:bg-white rounded-full p-2 shadow-md transition-all duration-200"
        >
          <Heart className={cn(
            "h-4 w-4 transition-colors duration-200",
            isFavorited ? "fill-red-500 text-red-500" : "text-gray-600"
          )} />
        </button>
        
        {/* Property Type Badge */}
        {property.propertyType && (
          <Badge className="absolute top-3 left-3 bg-sea-green-500 text-white">
            {property.propertyType}
          </Badge>
        )}
        
        {/* Rating */}
        {property.averageRating && (
          <div className="absolute bottom-3 right-3 flex items-center bg-white/90 backdrop-blur-sm rounded-lg px-2 py-1">
            <Star className="h-4 w-4 text-yellow-400 fill-current mr-1" />
            <span className="text-sm font-medium">{property.averageRating}</span>
          </div>
        )}
      </div>
      
      {/* Content Section */}
      <div className="p-5">
        <div className="mb-3">
          <h3 className="text-lg font-bold text-gray-900 mb-1 group-hover:text-sea-green-700 transition-colors duration-200 line-clamp-1">
            {property.title}
          </h3>
          <div className="flex items-center text-gray-600 mb-2">
            <MapPin className="h-4 w-4 mr-1 flex-shrink-0" />
            <span className="text-sm line-clamp-1">{property.location}</span>
          </div>
        </div>
        
        {/* Property Details */}
        <div className="flex items-center space-x-3 mb-3 text-sm text-gray-600">
          {property.maxGuests && (
            <div className="flex items-center">
              <Users className="h-4 w-4 mr-1" />
              <span>{property.maxGuests}</span>
            </div>
          )}
          {property.bedrooms && (
            <div className="flex items-center">
              <Bed className="h-4 w-4 mr-1" />
              <span>{property.bedrooms}</span>
            </div>
          )}
          {property.bathrooms && (
            <div className="flex items-center">
              <Bath className="h-4 w-4 mr-1" />
              <span>{property.bathrooms}</span>
            </div>
          )}
        </div>
        
        {/* Price and Book Button */}
        <div className="flex items-center justify-between">
          <div>
            <div className="text-xl font-bold text-gray-900">
              {formatPrice(property.price)}
              <span className="text-sm font-normal text-gray-600 ml-1">/ night</span>
            </div>
          </div>
          
          <Button
            onClick={handleBookNowClick}
            size="sm"
            className="bg-gradient-to-r from-sea-green-500 to-ocean-blue-500 hover:from-sea-green-600 hover:to-ocean-blue-600 text-white rounded-xl font-semibold transition-all duration-200 hover:scale-105"
          >
            Book
          </Button>
        </div>
      </div>
    </div>
  );
};
