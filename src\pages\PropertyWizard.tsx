import React, { useState } from 'react';
import { Header } from '../components/Header';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { useAuth } from '../contexts/AuthContext';
import { PropertyBasicInfo } from '../components/wizard/PropertyBasicInfo';
import { PropertyAmenities } from '../components/wizard/PropertyAmenities';
import { PropertyRules } from '../components/wizard/PropertyRules';
import { PropertyPricing } from '../components/wizard/PropertyPricing';
import { PropertyImages } from '../components/wizard/PropertyImages';
import { PropertyVerification } from '../components/wizard/PropertyVerification';
import { 
  ChevronLeft, 
  ChevronRight, 
  Home,
  CheckCircle,
  AlertCircle
} from 'lucide-react';

interface PropertyData {
  // Basic Info
  title: string;
  description: string;
  propertyType: string;
  location: string;
  address: string;
  coordinates: { latitude: number; longitude: number } | null;
  maxGuests: number;
  bedrooms: number;
  bathrooms: number;
  
  // Amenities
  amenities: string[];
  
  // Rules
  checkInTime: string;
  checkOutTime: string;
  houseRules: string[];
  cancellationPolicy: string;
  
  // Pricing
  basePrice: number;
  cleaningFee: number;
  weeklyDiscount: number;
  monthlyDiscount: number;
  
  // Images (will be handled separately)
  images: string[];
}

const STEPS = [
  { id: 1, title: 'Basic Info', description: 'Property details and location' },
  { id: 2, title: 'Amenities', description: 'Features and facilities' },
  { id: 3, title: 'House Rules', description: 'Policies and guidelines' },
  { id: 4, title: 'Pricing', description: 'Rates and fees' },
  { id: 5, title: 'Photos', description: 'Property images' },
  { id: 6, title: 'Verification', description: 'Review and submit' }
];

export const PropertyWizard: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [propertyData, setPropertyData] = useState<PropertyData>({
    title: '',
    description: '',
    propertyType: '',
    location: '',
    address: '',
    coordinates: null,
    maxGuests: 1,
    bedrooms: 1,
    bathrooms: 1,
    amenities: [],
    checkInTime: '15:00',
    checkOutTime: '11:00',
    houseRules: [],
    cancellationPolicy: 'moderate',
    basePrice: 0,
    cleaningFee: 0,
    weeklyDiscount: 0,
    monthlyDiscount: 0,
    images: []
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const { isAuthenticated } = useAuth();

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="container mx-auto px-4 py-8">
          <Card className="max-w-md mx-auto">
            <CardContent className="text-center py-12">
              <AlertCircle className="h-12 w-12 mx-auto mb-4 text-gray-400" />
              <h2 className="text-xl font-semibold text-gray-900 mb-2">Login Required</h2>
              <p className="text-gray-600 mb-4">
                Please log in to create a property listing.
              </p>
              <Button onClick={() => window.location.href = '/'}>
                Go to Homepage
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  const updatePropertyData = (updates: Partial<PropertyData>) => {
    setPropertyData(prev => ({ ...prev, ...updates }));
    // Clear related errors when data is updated
    const updatedFields = Object.keys(updates);
    setErrors(prev => {
      const newErrors = { ...prev };
      updatedFields.forEach(field => {
        delete newErrors[field];
      });
      return newErrors;
    });
  };

  const validateCurrentStep = (): boolean => {
    const newErrors: Record<string, string> = {};

    switch (currentStep) {
      case 1: // Basic Info
        if (!propertyData.title.trim()) newErrors.title = 'Property title is required';
        if (!propertyData.description.trim()) newErrors.description = 'Property description is required';
        if (!propertyData.propertyType) newErrors.propertyType = 'Property type is required';
        if (!propertyData.location.trim()) newErrors.location = 'Location is required';
        if (!propertyData.address.trim()) newErrors.address = 'Address is required';
        if (propertyData.maxGuests < 1) newErrors.maxGuests = 'Must accommodate at least 1 guest';
        if (propertyData.bedrooms < 1) newErrors.bedrooms = 'Must have at least 1 bedroom';
        if (propertyData.bathrooms < 1) newErrors.bathrooms = 'Must have at least 1 bathroom';
        break;
        
      case 2: // Amenities
        // Amenities are optional, no validation needed
        break;
        
      case 3: // Rules
        if (!propertyData.checkInTime) newErrors.checkInTime = 'Check-in time is required';
        if (!propertyData.checkOutTime) newErrors.checkOutTime = 'Check-out time is required';
        if (!propertyData.cancellationPolicy) newErrors.cancellationPolicy = 'Cancellation policy is required';
        break;
        
      case 4: // Pricing
        if (propertyData.basePrice <= 0) newErrors.basePrice = 'Base price must be greater than 0';
        if (propertyData.cleaningFee < 0) newErrors.cleaningFee = 'Cleaning fee cannot be negative';
        if (propertyData.weeklyDiscount < 0 || propertyData.weeklyDiscount > 50) {
          newErrors.weeklyDiscount = 'Weekly discount must be between 0% and 50%';
        }
        if (propertyData.monthlyDiscount < 0 || propertyData.monthlyDiscount > 50) {
          newErrors.monthlyDiscount = 'Monthly discount must be between 0% and 50%';
        }
        break;

      case 5: // Images
        if (!propertyData.images || propertyData.images.length < 5) {
          newErrors.images = 'Please upload at least 5 photos of your property';
        }
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateCurrentStep()) {
      setCurrentStep(prev => Math.min(prev + 1, STEPS.length));
    }
  };

  const handlePrevious = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  const handleSubmit = async () => {
    if (!validateCurrentStep()) return;

    setIsSubmitting(true);
    try {
      // TODO: Implement property creation API call
      console.log('Creating property:', propertyData);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Redirect to host dashboard on success
      window.location.href = '/host';
    } catch (error) {
      console.error('Failed to create property:', error);
      setErrors({ submit: 'Failed to create property. Please try again.' });
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <PropertyBasicInfo
            data={propertyData}
            errors={errors}
            onChange={updatePropertyData}
          />
        );
      case 2:
        return (
          <PropertyAmenities
            data={propertyData}
            errors={errors}
            onChange={updatePropertyData}
          />
        );
      case 3:
        return (
          <PropertyRules
            data={propertyData}
            errors={errors}
            onChange={updatePropertyData}
          />
        );
      case 4:
        return (
          <PropertyPricing
            data={propertyData}
            errors={errors}
            onChange={updatePropertyData}
          />
        );
      case 5:
        return (
          <PropertyImages
            data={propertyData}
            errors={errors}
            onChange={updatePropertyData}
          />
        );
      case 6:
        return (
          <PropertyVerification
            data={propertyData}
            errors={errors}
            onSubmit={handleSubmit}
            isSubmitting={isSubmitting}
          />
        );
      default:
        return null;
    }
  };

  const progress = (currentStep / STEPS.length) * 100;

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-2 mb-4">
            <Home className="h-6 w-6 text-sea-green-600" />
            <h1 className="text-3xl font-bold text-gray-900">
              Create New Property
            </h1>
          </div>
          <p className="text-gray-600">
            Follow the steps below to list your property on StayFinder
          </p>
        </div>

        {/* Progress Bar */}
        <Card className="mb-8">
          <CardContent className="p-6">
            <div className="mb-4">
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium text-gray-700">
                  Step {currentStep} of {STEPS.length}
                </span>
                <span className="text-sm text-gray-500">
                  {Math.round(progress)}% Complete
                </span>
              </div>
              <Progress value={progress} className="h-2" />
            </div>
            
            <div className="flex justify-between">
              {STEPS.map((step) => (
                <div
                  key={step.id}
                  className={`flex-1 text-center ${
                    step.id <= currentStep ? 'text-sea-green-600' : 'text-gray-400'
                  }`}
                >
                  <div className={`w-8 h-8 rounded-full mx-auto mb-2 flex items-center justify-center text-sm font-medium ${
                    step.id < currentStep 
                      ? 'bg-sea-green-600 text-white' 
                      : step.id === currentStep
                      ? 'bg-sea-green-100 text-sea-green-600 border-2 border-sea-green-600'
                      : 'bg-gray-200 text-gray-400'
                  }`}>
                    {step.id < currentStep ? (
                      <CheckCircle className="h-4 w-4" />
                    ) : (
                      step.id
                    )}
                  </div>
                  <div className="text-xs font-medium">{step.title}</div>
                  <div className="text-xs text-gray-500 hidden sm:block">{step.description}</div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Step Content */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>
              {STEPS[currentStep - 1]?.title}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {renderStepContent()}
          </CardContent>
        </Card>

        {/* Navigation */}
        {currentStep < STEPS.length && (
          <div className="flex justify-between">
            <Button
              variant="outline"
              onClick={handlePrevious}
              disabled={currentStep === 1}
            >
              <ChevronLeft className="h-4 w-4 mr-2" />
              Previous
            </Button>
            
            <Button
              onClick={handleNext}
              className="bg-sea-green-500 hover:bg-sea-green-600 text-white"
            >
              Next
              <ChevronRight className="h-4 w-4 ml-2" />
            </Button>
          </div>
        )}

        {/* Error Display */}
        {errors.submit && (
          <Card className="mt-4 border-red-200 bg-red-50">
            <CardContent className="p-4">
              <div className="flex items-center gap-2 text-red-700">
                <AlertCircle className="h-4 w-4" />
                <span>{errors.submit}</span>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default PropertyWizard;
