import React, { useState, useRef, useEffect } from 'react';
import { Heart, Star, MapPin, Users, Wifi, Car, Waves, ChevronLeft, ChevronRight, Share2, Eye } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

interface MobilePropertyCardProps {
  property: {
    id: string;
    title: string;
    location: string;
    price: number;
    rating: number;
    reviewCount: number;
    images: string[];
    amenities: string[];
    capacity: number;
    type: string;
    isWishlisted?: boolean;
    distance?: string;
    availability?: string;
  };
  onWishlistToggle?: (propertyId: string) => void;
  onShare?: (propertyId: string) => void;
  onView?: (propertyId: string) => void;
  className?: string;
}

export const MobilePropertyCard: React.FC<MobilePropertyCardProps> = ({
  property,
  onWishlistToggle,
  onShare,
  onView,
  className
}) => {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isWishlisted, setIsWishlisted] = useState(property.isWishlisted || false);
  const [touchStart, setTouchStart] = useState<number | null>(null);
  const [touchEnd, setTouchEnd] = useState<number | null>(null);
  const imageContainerRef = useRef<HTMLDivElement>(null);

  // Minimum swipe distance (in px)
  const minSwipeDistance = 50;

  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd) return;
    
    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > minSwipeDistance;
    const isRightSwipe = distance < -minSwipeDistance;

    if (isLeftSwipe && currentImageIndex < property.images.length - 1) {
      setCurrentImageIndex(prev => prev + 1);
    }
    if (isRightSwipe && currentImageIndex > 0) {
      setCurrentImageIndex(prev => prev - 1);
    }
  };

  const handleWishlistToggle = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsWishlisted(!isWishlisted);
    onWishlistToggle?.(property.id);
  };

  const handleShare = (e: React.MouseEvent) => {
    e.stopPropagation();
    onShare?.(property.id);
  };

  const handleCardClick = () => {
    onView?.(property.id);
  };

  const nextImage = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (currentImageIndex < property.images.length - 1) {
      setCurrentImageIndex(prev => prev + 1);
    }
  };

  const prevImage = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (currentImageIndex > 0) {
      setCurrentImageIndex(prev => prev - 1);
    }
  };

  const getAmenityIcon = (amenity: string) => {
    switch (amenity.toLowerCase()) {
      case 'wifi':
        return <Wifi className="h-3 w-3" />;
      case 'parking':
        return <Car className="h-3 w-3" />;
      case 'pool':
        return <Waves className="h-3 w-3" />;
      default:
        return null;
    }
  };

  return (
    <Card 
      className={cn(
        "w-full overflow-hidden cursor-pointer transition-all duration-300",
        "hover:shadow-lg active:scale-[0.98] touch-manipulation",
        className
      )}
      onClick={handleCardClick}
    >
      <CardContent className="p-0">
        {/* Image Gallery */}
        <div 
          ref={imageContainerRef}
          className="relative h-48 overflow-hidden"
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
          onTouchEnd={handleTouchEnd}
        >
          {/* Main Image */}
          <div 
            className="flex transition-transform duration-300 ease-out h-full"
            style={{ transform: `translateX(-${currentImageIndex * 100}%)` }}
          >
            {property.images.map((image, index) => (
              <img
                key={index}
                src={image}
                alt={`${property.title} - Image ${index + 1}`}
                className="w-full h-full object-cover flex-shrink-0"
                loading={index === 0 ? 'eager' : 'lazy'}
              />
            ))}
          </div>

          {/* Navigation Arrows - Hidden on mobile, shown on larger screens */}
          {property.images.length > 1 && (
            <>
              <button
                onClick={prevImage}
                className={cn(
                  "absolute left-2 top-1/2 transform -translate-y-1/2",
                  "bg-black/50 text-white rounded-full p-1.5",
                  "opacity-0 md:opacity-100 transition-opacity",
                  "hover:bg-black/70 focus:outline-none focus:ring-2 focus:ring-white",
                  currentImageIndex === 0 && "opacity-30 cursor-not-allowed"
                )}
                disabled={currentImageIndex === 0}
              >
                <ChevronLeft className="h-4 w-4" />
              </button>
              
              <button
                onClick={nextImage}
                className={cn(
                  "absolute right-2 top-1/2 transform -translate-y-1/2",
                  "bg-black/50 text-white rounded-full p-1.5",
                  "opacity-0 md:opacity-100 transition-opacity",
                  "hover:bg-black/70 focus:outline-none focus:ring-2 focus:ring-white",
                  currentImageIndex === property.images.length - 1 && "opacity-30 cursor-not-allowed"
                )}
                disabled={currentImageIndex === property.images.length - 1}
              >
                <ChevronRight className="h-4 w-4" />
              </button>
            </>
          )}

          {/* Image Indicators */}
          {property.images.length > 1 && (
            <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 flex gap-1">
              {property.images.map((_, index) => (
                <button
                  key={index}
                  onClick={(e) => {
                    e.stopPropagation();
                    setCurrentImageIndex(index);
                  }}
                  className={cn(
                    "w-2 h-2 rounded-full transition-all duration-200",
                    "focus:outline-none focus:ring-1 focus:ring-white",
                    index === currentImageIndex 
                      ? "bg-white scale-110" 
                      : "bg-white/50 hover:bg-white/75"
                  )}
                  aria-label={`View image ${index + 1}`}
                />
              ))}
            </div>
          )}

          {/* Action Buttons */}
          <div className="absolute top-2 right-2 flex gap-2">
            <button
              onClick={handleWishlistToggle}
              className={cn(
                "p-2 rounded-full transition-all duration-200",
                "bg-white/90 hover:bg-white shadow-sm",
                "focus:outline-none focus:ring-2 focus:ring-sea-green-500",
                "min-h-[44px] min-w-[44px]" // Touch-friendly size
              )}
              aria-label={isWishlisted ? "Remove from wishlist" : "Add to wishlist"}
            >
              <Heart 
                className={cn(
                  "h-4 w-4 transition-colors",
                  isWishlisted ? "fill-red-500 text-red-500" : "text-gray-600"
                )}
              />
            </button>
            
            <button
              onClick={handleShare}
              className={cn(
                "p-2 rounded-full transition-all duration-200",
                "bg-white/90 hover:bg-white shadow-sm",
                "focus:outline-none focus:ring-2 focus:ring-sea-green-500",
                "min-h-[44px] min-w-[44px]" // Touch-friendly size
              )}
              aria-label="Share property"
            >
              <Share2 className="h-4 w-4 text-gray-600" />
            </button>
          </div>

          {/* Property Type Badge */}
          <div className="absolute top-2 left-2">
            <Badge className="bg-sea-green-600 text-white text-xs">
              {property.type}
            </Badge>
          </div>

          {/* Availability Badge */}
          {property.availability && (
            <div className="absolute bottom-2 left-2">
              <Badge variant="secondary" className="text-xs bg-white/90">
                {property.availability}
              </Badge>
            </div>
          )}
        </div>

        {/* Content */}
        <div className="p-4 space-y-3">
          {/* Title and Location */}
          <div>
            <h3 className="font-semibold text-lg leading-tight mb-1 line-clamp-2">
              {property.title}
            </h3>
            <div className="flex items-center text-gray-600 text-sm">
              <MapPin className="h-3 w-3 mr-1 flex-shrink-0" />
              <span className="truncate">{property.location}</span>
              {property.distance && (
                <span className="ml-2 text-xs">• {property.distance}</span>
              )}
            </div>
          </div>

          {/* Rating and Reviews */}
          <div className="flex items-center gap-2">
            <div className="flex items-center">
              <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
              <span className="ml-1 font-medium text-sm">{property.rating}</span>
            </div>
            <span className="text-gray-500 text-sm">
              ({property.reviewCount} reviews)
            </span>
          </div>

          {/* Amenities */}
          <div className="flex items-center gap-3">
            <div className="flex items-center text-gray-600 text-sm">
              <Users className="h-3 w-3 mr-1" />
              <span>{property.capacity} guests</span>
            </div>
            
            <div className="flex items-center gap-2">
              {property.amenities.slice(0, 3).map((amenity, index) => (
                <div key={index} className="flex items-center text-gray-600">
                  {getAmenityIcon(amenity)}
                  <span className="ml-1 text-xs hidden sm:inline">{amenity}</span>
                </div>
              ))}
              {property.amenities.length > 3 && (
                <span className="text-xs text-gray-500">
                  +{property.amenities.length - 3} more
                </span>
              )}
            </div>
          </div>

          {/* Price */}
          <div className="flex items-center justify-between pt-2 border-t">
            <div>
              <span className="text-xl font-bold text-gray-900">
                R{property.price.toLocaleString()}
              </span>
              <span className="text-gray-600 text-sm ml-1">/ night</span>
            </div>
            
            <Button 
              size="sm" 
              className={cn(
                "bg-sea-green-600 hover:bg-sea-green-700",
                "min-h-[44px] px-6" // Touch-friendly size
              )}
              onClick={(e) => {
                e.stopPropagation();
                onView?.(property.id);
              }}
            >
              <Eye className="h-4 w-4 mr-1" />
              View
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
