import React, { useState, useRef, useEffect, useCallback } from 'react';
import { cn } from '@/lib/utils';
import { 
  Loader2, 
  AlertCircle, 
  ImageIcon,
  Zap,
  Eye,
  Download
} from 'lucide-react';

interface OptimizedImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  placeholder?: string;
  blurDataURL?: string;
  priority?: boolean;
  lazy?: boolean;
  quality?: number;
  sizes?: string;
  objectFit?: 'cover' | 'contain' | 'fill' | 'none' | 'scale-down';
  onLoad?: () => void;
  onError?: (error: string) => void;
  onLoadStart?: () => void;
  enableWebP?: boolean;
  enableAVIF?: boolean;
  showLoadingIndicator?: boolean;
  showErrorFallback?: boolean;
  retryOnError?: boolean;
  maxRetries?: number;
  loadingComponent?: React.ReactNode;
  errorComponent?: React.ReactNode;
  aspectRatio?: string;
}

interface ImageState {
  isLoading: boolean;
  isLoaded: boolean;
  hasError: boolean;
  errorMessage: string;
  retryCount: number;
  isInView: boolean;
  loadStartTime: number;
  loadEndTime: number;
}

export const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  width,
  height,
  className,
  placeholder,
  blurDataURL,
  priority = false,
  lazy = true,
  quality = 80,
  sizes,
  objectFit = 'cover',
  onLoad,
  onError,
  onLoadStart,
  enableWebP = true,
  enableAVIF = true,
  showLoadingIndicator = true,
  showErrorFallback = true,
  retryOnError = true,
  maxRetries = 3,
  loadingComponent,
  errorComponent,
  aspectRatio
}) => {
  const [state, setState] = useState<ImageState>({
    isLoading: false,
    isLoaded: false,
    hasError: false,
    errorMessage: '',
    retryCount: 0,
    isInView: false,
    loadStartTime: 0,
    loadEndTime: 0
  });

  const imgRef = useRef<HTMLImageElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);

  // Generate optimized image URLs
  const generateOptimizedSrc = useCallback((originalSrc: string, format?: 'webp' | 'avif') => {
    if (!originalSrc) return originalSrc;

    // If it's already an optimized URL or external URL, return as is
    if (originalSrc.includes('?') || originalSrc.startsWith('http')) {
      return originalSrc;
    }

    const params = new URLSearchParams();
    if (width) params.set('w', width.toString());
    if (height) params.set('h', height.toString());
    if (quality) params.set('q', quality.toString());
    if (format) params.set('f', format);

    return `${originalSrc}?${params.toString()}`;
  }, [width, height, quality]);

  // Get the best supported image format
  const getOptimizedSources = useCallback(() => {
    const sources = [];
    
    if (enableAVIF) {
      sources.push({
        srcSet: generateOptimizedSrc(src, 'avif'),
        type: 'image/avif'
      });
    }
    
    if (enableWebP) {
      sources.push({
        srcSet: generateOptimizedSrc(src, 'webp'),
        type: 'image/webp'
      });
    }
    
    return sources;
  }, [src, enableAVIF, enableWebP, generateOptimizedSrc]);

  // Intersection Observer for lazy loading
  useEffect(() => {
    if (!lazy || priority) {
      setState(prev => ({ ...prev, isInView: true }));
      return;
    }

    if (!containerRef.current) return;

    observerRef.current = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        if (entry.isIntersecting) {
          setState(prev => ({ ...prev, isInView: true }));
          observerRef.current?.disconnect();
        }
      },
      {
        rootMargin: '50px', // Start loading 50px before the image comes into view
        threshold: 0.1
      }
    );

    observerRef.current.observe(containerRef.current);

    return () => {
      observerRef.current?.disconnect();
    };
  }, [lazy, priority]);

  // Handle image loading
  const handleImageLoad = useCallback(() => {
    const endTime = performance.now();
    setState(prev => ({
      ...prev,
      isLoading: false,
      isLoaded: true,
      hasError: false,
      loadEndTime: endTime
    }));

    // Log performance metrics
    if (state.loadStartTime > 0) {
      const loadTime = endTime - state.loadStartTime;
      console.log(`Image loaded in ${loadTime.toFixed(2)}ms:`, src);
    }

    onLoad?.();
  }, [onLoad, src, state.loadStartTime]);

  // Handle image error
  const handleImageError = useCallback((error: Event) => {
    const errorMessage = 'Failed to load image';
    setState(prev => ({
      ...prev,
      isLoading: false,
      hasError: true,
      errorMessage
    }));

    onError?.(errorMessage);

    // Retry logic
    if (retryOnError && state.retryCount < maxRetries) {
      setTimeout(() => {
        setState(prev => ({
          ...prev,
          retryCount: prev.retryCount + 1,
          hasError: false,
          isLoading: true
        }));
      }, 1000 * Math.pow(2, state.retryCount)); // Exponential backoff
    }
  }, [onError, retryOnError, maxRetries, state.retryCount]);

  // Handle image load start
  const handleImageLoadStart = useCallback(() => {
    const startTime = performance.now();
    setState(prev => ({
      ...prev,
      isLoading: true,
      loadStartTime: startTime
    }));

    onLoadStart?.();
  }, [onLoadStart]);

  // Start loading when image comes into view
  useEffect(() => {
    if (state.isInView && !state.isLoaded && !state.isLoading && !state.hasError) {
      handleImageLoadStart();
    }
  }, [state.isInView, state.isLoaded, state.isLoading, state.hasError, handleImageLoadStart]);

  // Render loading component
  const renderLoading = () => {
    if (loadingComponent) return loadingComponent;

    if (!showLoadingIndicator) return null;

    return (
      <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-gray-400 mx-auto mb-2" />
          <p className="text-sm text-gray-500">Loading image...</p>
        </div>
      </div>
    );
  };

  // Render error component
  const renderError = () => {
    if (errorComponent) return errorComponent;

    if (!showErrorFallback) return null;

    return (
      <div className="absolute inset-0 flex flex-col items-center justify-center bg-gray-100">
        <AlertCircle className="h-12 w-12 text-red-400 mb-2" />
        <p className="text-sm text-gray-600 text-center px-4">
          {state.errorMessage}
        </p>
        {retryOnError && state.retryCount < maxRetries && (
          <button
            onClick={() => {
              setState(prev => ({
                ...prev,
                hasError: false,
                retryCount: prev.retryCount + 1
              }));
            }}
            className="mt-2 px-3 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600 transition-colors"
          >
            Retry ({state.retryCount + 1}/{maxRetries})
          </button>
        )}
      </div>
    );
  };

  // Render placeholder
  const renderPlaceholder = () => {
    if (blurDataURL) {
      return (
        <img
          src={blurDataURL}
          alt=""
          className={cn(
            "absolute inset-0 w-full h-full transition-opacity duration-300",
            state.isLoaded ? "opacity-0" : "opacity-100"
          )}
          style={{ objectFit }}
        />
      );
    }

    if (placeholder) {
      return (
        <img
          src={placeholder}
          alt=""
          className={cn(
            "absolute inset-0 w-full h-full transition-opacity duration-300",
            state.isLoaded ? "opacity-0" : "opacity-100"
          )}
          style={{ objectFit }}
        />
      );
    }

    return (
      <div className={cn(
        "absolute inset-0 bg-gray-200 flex items-center justify-center transition-opacity duration-300",
        state.isLoaded ? "opacity-0" : "opacity-100"
      )}>
        <ImageIcon className="h-8 w-8 text-gray-400" />
      </div>
    );
  };

  const containerStyle: React.CSSProperties = {
    aspectRatio: aspectRatio || (width && height ? `${width}/${height}` : undefined),
    width: width ? `${width}px` : undefined,
    height: height ? `${height}px` : undefined,
  };

  return (
    <div
      ref={containerRef}
      className={cn("relative overflow-hidden", className)}
      style={containerStyle}
    >
      {/* Placeholder */}
      {!state.isLoaded && renderPlaceholder()}

      {/* Loading indicator */}
      {state.isLoading && renderLoading()}

      {/* Error state */}
      {state.hasError && renderError()}

      {/* Optimized image with multiple formats */}
      {state.isInView && !state.hasError && (
        <picture>
          {getOptimizedSources().map((source, index) => (
            <source
              key={index}
              srcSet={source.srcSet}
              type={source.type}
              sizes={sizes}
            />
          ))}
          <img
            ref={imgRef}
            src={generateOptimizedSrc(src)}
            alt={alt}
            width={width}
            height={height}
            sizes={sizes}
            onLoad={handleImageLoad}
            onError={handleImageError}
            onLoadStart={handleImageLoadStart}
            className={cn(
              "w-full h-full transition-opacity duration-300",
              state.isLoaded ? "opacity-100" : "opacity-0"
            )}
            style={{ objectFit }}
            loading={lazy && !priority ? "lazy" : "eager"}
            decoding="async"
          />
        </picture>
      )}

      {/* Performance indicator (development only) */}
      {process.env.NODE_ENV === 'development' && state.isLoaded && (
        <div className="absolute top-2 right-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
          <div className="flex items-center gap-1">
            <Zap className="h-3 w-3" />
            {state.loadEndTime - state.loadStartTime > 0 && (
              <span>{(state.loadEndTime - state.loadStartTime).toFixed(0)}ms</span>
            )}
          </div>
        </div>
      )}
    </div>
  );
};
