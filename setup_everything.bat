@echo off
echo ========================================
echo   KZN StayFinder - Complete Setup
echo ========================================
echo.

echo Step 1: Checking Node.js installation...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed or not in PATH
    echo.
    echo Please install Node.js from: https://nodejs.org/
    echo Download the LTS version and install it.
    echo After installation, restart this script.
    echo.
    pause
    exit /b 1
)

echo Node.js found!
node --version
echo.

echo Step 2: Installing backend dependencies...
cd backend
if not exist node_modules (
    echo Installing backend packages...
    npm install
    if %errorlevel% neq 0 (
        echo ERROR: Failed to install backend dependencies
        pause
        exit /b 1
    )
) else (
    echo Backend dependencies already installed.
)
cd ..
echo.

echo Step 3: Installing frontend dependencies...
if not exist node_modules (
    echo Installing frontend packages...
    npm install
    if %errorlevel% neq 0 (
        echo ERROR: Failed to install frontend dependencies
        pause
        exit /b 1
    )
) else (
    echo Frontend dependencies already installed.
)
echo.

echo Step 4: Opening phpMyAdmin for database setup...
start http://localhost/phpmyadmin
echo.
echo IMPORTANT: In phpMyAdmin, please:
echo 1. Click 'Databases' tab
echo 2. Create database named: stayfinder_dev
echo 3. Set collation to: utf8mb4_unicode_ci
echo 4. Click 'Create'
echo 5. Select the new database
echo 6. Click 'Import' tab
echo 7. Choose file: database_init.sql
echo 8. Click 'Go'
echo.
echo Press any key when database setup is complete...
pause
echo.

echo Step 5: Starting backend server...
start "StayFinder Backend" cmd /k "cd backend && npm run dev"
timeout /t 3 /nobreak >nul
echo.

echo Step 6: Starting frontend server...
start "StayFinder Frontend" cmd /k "npm run dev"
echo.

echo Step 7: Opening the application...
timeout /t 5 /nobreak >nul
start http://localhost:5173
echo.

echo ========================================
echo   Setup Complete!
echo ========================================
echo.
echo Your StayFinder application is now running:
echo - Frontend: http://localhost:5173
echo - Backend API: http://localhost:3001
echo - Database: phpMyAdmin (already open)
echo.
echo Test login credentials:
echo Email: <EMAIL>
echo Password: password123
echo.
echo Press any key to exit...
pause
