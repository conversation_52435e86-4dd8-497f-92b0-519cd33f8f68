import React, { useState, useRef, useEffect } from 'react';
import { Globe, ChevronDown, Check } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { useLanguageSelector, SupportedLanguage, LanguageInfo } from '@/hooks/useTranslation';

interface LanguageSelectorProps {
  variant?: 'dropdown' | 'modal' | 'inline';
  size?: 'sm' | 'md' | 'lg';
  showFlag?: boolean;
  showNativeName?: boolean;
  className?: string;
}

export const LanguageSelector: React.FC<LanguageSelectorProps> = ({
  variant = 'dropdown',
  size = 'md',
  showFlag = true,
  showNativeName = false,
  className
}) => {
  const {
    currentLanguage,
    availableLanguages,
    isOpen,
    setIsOpen,
    selectLanguage
  } = useLanguageSelector();

  const [searchQuery, setSearchQuery] = useState('');
  const dropdownRef = useRef<HTMLDivElement>(null);

  const sizeClasses = {
    sm: 'text-sm px-2 py-1',
    md: 'text-sm px-3 py-2',
    lg: 'text-base px-4 py-3'
  };

  const filteredLanguages = availableLanguages.filter(lang =>
    lang.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    lang.nativeName.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, setIsOpen]);

  const handleLanguageSelect = (langCode: SupportedLanguage) => {
    selectLanguage(langCode);
    setSearchQuery('');
  };

  const LanguageItem: React.FC<{ 
    language: LanguageInfo; 
    isSelected?: boolean; 
    onClick: () => void;
    showCheck?: boolean;
  }> = ({ language, isSelected = false, onClick, showCheck = true }) => (
    <button
      onClick={onClick}
      className={cn(
        "w-full flex items-center gap-3 px-3 py-2 text-left hover:bg-gray-50 transition-colors",
        isSelected && "bg-sea-green-50 text-sea-green-700"
      )}
    >
      {showFlag && (
        <span className="text-lg">{language.flag}</span>
      )}
      <div className="flex-1 min-w-0">
        <div className="font-medium truncate">{language.name}</div>
        {showNativeName && language.name !== language.nativeName && (
          <div className="text-sm text-gray-500 truncate">{language.nativeName}</div>
        )}
      </div>
      {showCheck && isSelected && (
        <Check className="h-4 w-4 text-sea-green-600" />
      )}
    </button>
  );

  if (variant === 'dropdown') {
    return (
      <div className={cn("relative", className)} ref={dropdownRef}>
        <Button
          variant="outline"
          onClick={() => setIsOpen(!isOpen)}
          className={cn(
            "flex items-center gap-2",
            sizeClasses[size]
          )}
        >
          {showFlag && (
            <span className="text-base">{currentLanguage.flag}</span>
          )}
          <span className="hidden sm:inline">
            {showNativeName ? currentLanguage.nativeName : currentLanguage.name}
          </span>
          <span className="sm:hidden">{currentLanguage.code.toUpperCase()}</span>
          <ChevronDown className={cn(
            "transition-transform",
            isOpen && "rotate-180",
            size === 'sm' ? "h-3 w-3" : "h-4 w-4"
          )} />
        </Button>

        {isOpen && (
          <Card className="absolute top-full mt-1 right-0 z-50 w-64 shadow-lg">
            <CardContent className="p-0">
              {/* Search */}
              {availableLanguages.length > 5 && (
                <div className="p-3 border-b">
                  <input
                    type="text"
                    placeholder="Search languages..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full px-3 py-2 text-sm border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-sea-green-500"
                  />
                </div>
              )}

              {/* Language List */}
              <div className="max-h-64 overflow-y-auto">
                {filteredLanguages.length > 0 ? (
                  filteredLanguages.map((language) => (
                    <LanguageItem
                      key={language.code}
                      language={language}
                      isSelected={language.code === currentLanguage.code}
                      onClick={() => handleLanguageSelect(language.code)}
                    />
                  ))
                ) : (
                  <div className="p-4 text-center text-gray-500 text-sm">
                    No languages found
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    );
  }

  if (variant === 'modal') {
    return (
      <>
        <Button
          variant="outline"
          onClick={() => setIsOpen(true)}
          className={cn(
            "flex items-center gap-2",
            sizeClasses[size],
            className
          )}
        >
          <Globe className="h-4 w-4" />
          <span>{showNativeName ? currentLanguage.nativeName : currentLanguage.name}</span>
        </Button>

        {isOpen && (
          <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
            <Card className="w-full max-w-md max-h-[80vh] overflow-hidden">
              <div className="p-4 border-b">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-lg font-semibold">Select Language</h3>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsOpen(false)}
                  >
                    ×
                  </Button>
                </div>
                
                {availableLanguages.length > 5 && (
                  <input
                    type="text"
                    placeholder="Search languages..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full px-3 py-2 text-sm border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-sea-green-500"
                  />
                )}
              </div>

              <div className="overflow-y-auto max-h-96">
                {filteredLanguages.length > 0 ? (
                  filteredLanguages.map((language) => (
                    <LanguageItem
                      key={language.code}
                      language={language}
                      isSelected={language.code === currentLanguage.code}
                      onClick={() => handleLanguageSelect(language.code)}
                    />
                  ))
                ) : (
                  <div className="p-4 text-center text-gray-500">
                    No languages found
                  </div>
                )}
              </div>
            </Card>
          </div>
        )}
      </>
    );
  }

  // Inline variant
  return (
    <div className={cn("space-y-2", className)}>
      <label className="text-sm font-medium text-gray-700">Language</label>
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
        {availableLanguages.slice(0, 6).map((language) => (
          <button
            key={language.code}
            onClick={() => handleLanguageSelect(language.code)}
            className={cn(
              "flex items-center gap-2 p-3 border rounded-lg text-left transition-colors",
              language.code === currentLanguage.code
                ? "border-sea-green-500 bg-sea-green-50 text-sea-green-700"
                : "border-gray-200 hover:border-gray-300 hover:bg-gray-50"
            )}
          >
            {showFlag && (
              <span className="text-lg">{language.flag}</span>
            )}
            <div className="flex-1 min-w-0">
              <div className="font-medium truncate">{language.name}</div>
              {showNativeName && language.name !== language.nativeName && (
                <div className="text-sm text-gray-500 truncate">{language.nativeName}</div>
              )}
            </div>
            {language.code === currentLanguage.code && (
              <Check className="h-4 w-4 text-sea-green-600" />
            )}
          </button>
        ))}
      </div>
      
      {availableLanguages.length > 6 && (
        <Button
          variant="outline"
          onClick={() => setIsOpen(true)}
          className="w-full"
        >
          View All Languages ({availableLanguages.length})
        </Button>
      )}
    </div>
  );
};

// Compact language selector for mobile/small spaces
export const CompactLanguageSelector: React.FC<{
  className?: string;
}> = ({ className }) => {
  const { currentLanguage, availableLanguages, selectLanguage } = useLanguageSelector();
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className={cn("relative", className)}>
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-1 px-2"
      >
        <span className="text-sm">{currentLanguage.flag}</span>
        <span className="text-xs font-medium">{currentLanguage.code.toUpperCase()}</span>
        <ChevronDown className="h-3 w-3" />
      </Button>

      {isOpen && (
        <>
          <div
            className="fixed inset-0 z-40"
            onClick={() => setIsOpen(false)}
          />
          <Card className="absolute top-full mt-1 right-0 z-50 w-48 shadow-lg">
            <CardContent className="p-1">
              {availableLanguages.map((language) => (
                <button
                  key={language.code}
                  onClick={() => {
                    selectLanguage(language.code);
                    setIsOpen(false);
                  }}
                  className={cn(
                    "w-full flex items-center gap-2 px-2 py-1.5 text-left hover:bg-gray-50 rounded text-sm transition-colors",
                    language.code === currentLanguage.code && "bg-sea-green-50 text-sea-green-700"
                  )}
                >
                  <span>{language.flag}</span>
                  <span className="truncate">{language.name}</span>
                  {language.code === currentLanguage.code && (
                    <Check className="h-3 w-3 text-sea-green-600 ml-auto" />
                  )}
                </button>
              ))}
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
};

// Language selector for settings page
export const SettingsLanguageSelector: React.FC<{
  className?: string;
}> = ({ className }) => {
  return (
    <div className={cn("space-y-4", className)}>
      <div>
        <h3 className="text-lg font-semibold mb-2">Language & Region</h3>
        <p className="text-gray-600 text-sm mb-4">
          Choose your preferred language for the StayFinder interface
        </p>
      </div>
      
      <LanguageSelector
        variant="inline"
        showFlag={true}
        showNativeName={true}
      />
      
      <div className="text-xs text-gray-500">
        <p>
          Language settings are saved to your browser and will be remembered for future visits.
          Some content may still appear in English if translations are not yet available.
        </p>
      </div>
    </div>
  );
};
