import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  Home, 
  Edit, 
  Eye, 
  Trash2, 
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  Star,
  Calendar,
  DollarSign,
  Users,
  MapPin,
  Camera
} from 'lucide-react';

interface Property {
  id: string;
  title: string;
  location: string;
  price: number;
  status: 'active' | 'inactive' | 'pending';
  bookings: number;
  revenue: number;
  rating: number;
  reviewCount: number;
  images: string[];
  propertyType: string;
  maxGuests: number;
  bedrooms: number;
  bathrooms: number;
  createdAt: string;
  lastBooking?: string;
}

interface PropertyManagementProps {
  properties: Property[];
  onEditProperty: (propertyId: string) => void;
  onDeleteProperty: (propertyId: string) => void;
  onViewProperty: (propertyId: string) => void;
  onAddProperty: () => void;
}

export const PropertyManagement: React.FC<PropertyManagementProps> = ({
  properties,
  onEditProperty,
  onDeleteProperty,
  onViewProperty,
  onAddProperty
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'inactive' | 'pending'>('all');
  const [sortBy, setSortBy] = useState<'title' | 'revenue' | 'bookings' | 'rating'>('title');
  const [showActions, setShowActions] = useState<string | null>(null);

  const filteredProperties = properties
    .filter(property => {
      const matchesSearch = property.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           property.location.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesStatus = statusFilter === 'all' || property.status === statusFilter;
      return matchesSearch && matchesStatus;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'revenue':
          return b.revenue - a.revenue;
        case 'bookings':
          return b.bookings - a.bookings;
        case 'rating':
          return b.rating - a.rating;
        default:
          return a.title.localeCompare(b.title);
      }
    });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'inactive':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const handleDeleteProperty = (propertyId: string, propertyTitle: string) => {
    const confirmed = window.confirm(
      `Are you sure you want to delete "${propertyTitle}"? This action cannot be undone.`
    );
    if (confirmed) {
      onDeleteProperty(propertyId);
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <CardTitle className="flex items-center gap-2">
            <Home className="h-5 w-5" />
            Property Management
          </CardTitle>
          <Button onClick={onAddProperty} className="bg-sea-green-500 hover:bg-sea-green-600 text-white">
            <Plus className="h-4 w-4 mr-2" />
            Add Property
          </Button>
        </div>

        {/* Filters and Search */}
        <div className="flex flex-col sm:flex-row gap-4 mt-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search properties..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value as any)}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm"
          >
            <option value="all">All Status</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
            <option value="pending">Pending</option>
          </select>

          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as any)}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm"
          >
            <option value="title">Sort by Name</option>
            <option value="revenue">Sort by Revenue</option>
            <option value="bookings">Sort by Bookings</option>
            <option value="rating">Sort by Rating</option>
          </select>
        </div>
      </CardHeader>

      <CardContent>
        {filteredProperties.length === 0 ? (
          <div className="text-center py-12">
            <Home className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {searchTerm || statusFilter !== 'all' ? 'No properties found' : 'No properties yet'}
            </h3>
            <p className="text-gray-600 mb-4">
              {searchTerm || statusFilter !== 'all' 
                ? 'Try adjusting your search or filters'
                : 'Start by adding your first property to begin hosting'
              }
            </p>
            {!searchTerm && statusFilter === 'all' && (
              <Button onClick={onAddProperty} className="bg-sea-green-500 hover:bg-sea-green-600 text-white">
                <Plus className="h-4 w-4 mr-2" />
                Add Your First Property
              </Button>
            )}
          </div>
        ) : (
          <div className="space-y-4">
            {filteredProperties.map((property) => (
              <div key={property.id} className="border rounded-lg p-6 hover:bg-gray-50 transition-colors">
                <div className="flex flex-col lg:flex-row lg:items-center gap-4">
                  {/* Property Image */}
                  <div className="w-full lg:w-32 h-24 bg-gray-200 rounded-lg flex items-center justify-center overflow-hidden">
                    {property.images && property.images.length > 0 ? (
                      <img
                        src={property.images[0]}
                        alt={property.title}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <Camera className="h-8 w-8 text-gray-400" />
                    )}
                  </div>

                  {/* Property Details */}
                  <div className="flex-1">
                    <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-2 mb-3">
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900">{property.title}</h3>
                        <div className="flex items-center gap-1 text-gray-600">
                          <MapPin className="h-4 w-4" />
                          <span className="text-sm">{property.location}</span>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className={getStatusColor(property.status)}>
                          {property.status}
                        </Badge>
                        <div className="relative">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setShowActions(showActions === property.id ? null : property.id)}
                            className="h-8 w-8 p-0"
                          >
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                          
                          {showActions === property.id && (
                            <div className="absolute right-0 top-8 bg-white border rounded-lg shadow-lg py-1 z-10 min-w-[140px]">
                              <button
                                onClick={() => {
                                  onViewProperty(property.id);
                                  setShowActions(null);
                                }}
                                className="w-full px-3 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2"
                              >
                                <Eye className="h-3 w-3" />
                                View
                              </button>
                              <button
                                onClick={() => {
                                  onEditProperty(property.id);
                                  setShowActions(null);
                                }}
                                className="w-full px-3 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2"
                              >
                                <Edit className="h-3 w-3" />
                                Edit
                              </button>
                              <button
                                onClick={() => {
                                  handleDeleteProperty(property.id, property.title);
                                  setShowActions(null);
                                }}
                                className="w-full px-3 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2 text-red-600"
                              >
                                <Trash2 className="h-3 w-3" />
                                Delete
                              </button>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Property Stats */}
                    <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 mb-4">
                      <div className="flex items-center gap-2">
                        <DollarSign className="h-4 w-4 text-green-600" />
                        <div>
                          <p className="text-xs text-gray-600">Price/night</p>
                          <p className="font-medium">R{property.price.toLocaleString()}</p>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-blue-600" />
                        <div>
                          <p className="text-xs text-gray-600">Bookings</p>
                          <p className="font-medium">{property.bookings}</p>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <DollarSign className="h-4 w-4 text-purple-600" />
                        <div>
                          <p className="text-xs text-gray-600">Revenue</p>
                          <p className="font-medium">R{property.revenue.toLocaleString()}</p>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Star className="h-4 w-4 text-yellow-500" />
                        <div>
                          <p className="text-xs text-gray-600">Rating</p>
                          <p className="font-medium">{property.rating.toFixed(1)} ({property.reviewCount})</p>
                        </div>
                      </div>
                    </div>

                    {/* Property Info */}
                    <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600">
                      <span>{property.propertyType}</span>
                      <span>•</span>
                      <span>{property.maxGuests} guests</span>
                      <span>•</span>
                      <span>{property.bedrooms} bed{property.bedrooms !== 1 ? 's' : ''}</span>
                      <span>•</span>
                      <span>{property.bathrooms} bath{property.bathrooms !== 1 ? 's' : ''}</span>
                      {property.lastBooking && (
                        <>
                          <span>•</span>
                          <span>Last booking: {new Date(property.lastBooking).toLocaleDateString()}</span>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Click outside to close actions menu */}
        {showActions && (
          <div
            className="fixed inset-0 z-5"
            onClick={() => setShowActions(null)}
          />
        )}
      </CardContent>
    </Card>
  );
};
