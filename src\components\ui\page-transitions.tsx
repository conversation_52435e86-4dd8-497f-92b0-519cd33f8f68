import React, { useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';
import { cn } from '@/lib/utils';

interface PageTransitionProps {
  children: React.ReactNode;
  className?: string;
  type?: 'fade' | 'slide' | 'scale' | 'blur';
  duration?: number;
}

export const PageTransition: React.FC<PageTransitionProps> = ({
  children,
  className,
  type = 'fade',
  duration = 300
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const location = useLocation();

  useEffect(() => {
    setIsVisible(false);
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 50);

    return () => clearTimeout(timer);
  }, [location.pathname]);

  const getTransitionClasses = () => {
    const baseClasses = 'transition-all ease-in-out';
    const durationClass = `duration-${duration}`;

    switch (type) {
      case 'slide':
        return cn(
          baseClasses,
          durationClass,
          isVisible 
            ? 'translate-x-0 opacity-100' 
            : 'translate-x-4 opacity-0'
        );
      
      case 'scale':
        return cn(
          baseClasses,
          durationClass,
          isVisible 
            ? 'scale-100 opacity-100' 
            : 'scale-95 opacity-0'
        );
      
      case 'blur':
        return cn(
          baseClasses,
          durationClass,
          isVisible 
            ? 'blur-0 opacity-100' 
            : 'blur-sm opacity-0'
        );
      
      default: // fade
        return cn(
          baseClasses,
          durationClass,
          isVisible ? 'opacity-100' : 'opacity-0'
        );
    }
  };

  return (
    <div className={cn(getTransitionClasses(), className)}>
      {children}
    </div>
  );
};

// Staggered Animation for Lists
export const StaggeredAnimation: React.FC<{
  children: React.ReactNode[];
  delay?: number;
  className?: string;
}> = ({ children, delay = 100, className }) => {
  const [visibleItems, setVisibleItems] = useState<number[]>([]);

  useEffect(() => {
    const timers: NodeJS.Timeout[] = [];
    
    children.forEach((_, index) => {
      const timer = setTimeout(() => {
        setVisibleItems(prev => [...prev, index]);
      }, index * delay);
      
      timers.push(timer);
    });

    return () => {
      timers.forEach(timer => clearTimeout(timer));
    };
  }, [children, delay]);

  return (
    <div className={className}>
      {children.map((child, index) => (
        <div
          key={index}
          className={cn(
            'transition-all duration-500 ease-out',
            visibleItems.includes(index)
              ? 'translate-y-0 opacity-100'
              : 'translate-y-4 opacity-0'
          )}
        >
          {child}
        </div>
      ))}
    </div>
  );
};

// Fade In Animation Hook
export const useFadeIn = (delay: number = 0) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, delay);

    return () => clearTimeout(timer);
  }, [delay]);

  return {
    isVisible,
    className: cn(
      'transition-all duration-500 ease-out',
      isVisible ? 'translate-y-0 opacity-100' : 'translate-y-4 opacity-0'
    )
  };
};

// Slide In Animation Component
export const SlideIn: React.FC<{
  children: React.ReactNode;
  direction?: 'left' | 'right' | 'up' | 'down';
  delay?: number;
  className?: string;
}> = ({ children, direction = 'up', delay = 0, className }) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, delay);

    return () => clearTimeout(timer);
  }, [delay]);

  const getDirectionClasses = () => {
    const base = 'transition-all duration-700 ease-out';
    
    if (isVisible) {
      return `${base} translate-x-0 translate-y-0 opacity-100`;
    }

    switch (direction) {
      case 'left':
        return `${base} -translate-x-8 opacity-0`;
      case 'right':
        return `${base} translate-x-8 opacity-0`;
      case 'up':
        return `${base} translate-y-8 opacity-0`;
      case 'down':
        return `${base} -translate-y-8 opacity-0`;
      default:
        return `${base} translate-y-8 opacity-0`;
    }
  };

  return (
    <div className={cn(getDirectionClasses(), className)}>
      {children}
    </div>
  );
};

// Scale In Animation
export const ScaleIn: React.FC<{
  children: React.ReactNode;
  delay?: number;
  className?: string;
}> = ({ children, delay = 0, className }) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, delay);

    return () => clearTimeout(timer);
  }, [delay]);

  return (
    <div className={cn(
      'transition-all duration-500 ease-out',
      isVisible 
        ? 'scale-100 opacity-100' 
        : 'scale-90 opacity-0',
      className
    )}>
      {children}
    </div>
  );
};

// Bounce In Animation
export const BounceIn: React.FC<{
  children: React.ReactNode;
  delay?: number;
  className?: string;
}> = ({ children, delay = 0, className }) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, delay);

    return () => clearTimeout(timer);
  }, [delay]);

  return (
    <div className={cn(
      'transition-all duration-700',
      isVisible 
        ? 'animate-bounce-in scale-100 opacity-100' 
        : 'scale-75 opacity-0',
      className
    )}>
      {children}
    </div>
  );
};

// Loading State Animation
export const LoadingStateAnimation: React.FC<{
  isLoading: boolean;
  children: React.ReactNode;
  loadingComponent?: React.ReactNode;
  className?: string;
}> = ({ isLoading, children, loadingComponent, className }) => {
  return (
    <div className={cn('relative', className)}>
      <div className={cn(
        'transition-all duration-300',
        isLoading ? 'opacity-0 scale-95' : 'opacity-100 scale-100'
      )}>
        {children}
      </div>
      
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="animate-fade-in">
            {loadingComponent}
          </div>
        </div>
      )}
    </div>
  );
};

// Hover Animation Wrapper
export const HoverAnimation: React.FC<{
  children: React.ReactNode;
  type?: 'lift' | 'scale' | 'glow' | 'rotate';
  className?: string;
}> = ({ children, type = 'lift', className }) => {
  const getHoverClasses = () => {
    const base = 'transition-all duration-200 ease-out';
    
    switch (type) {
      case 'scale':
        return `${base} hover:scale-105`;
      case 'glow':
        return `${base} hover:shadow-lg hover:shadow-sea-green-500/20`;
      case 'rotate':
        return `${base} hover:rotate-1`;
      default: // lift
        return `${base} hover:-translate-y-1 hover:shadow-lg`;
    }
  };

  return (
    <div className={cn(getHoverClasses(), className)}>
      {children}
    </div>
  );
};
