// Security utilities and validation for StayFinder

import DOMPurify from 'dompurify';

// Input validation patterns
export const ValidationPatterns = {
  email: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
  phone: /^(\+27|0)[0-9]{9}$/,
  password: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
  name: /^[a-zA-Z\s'-]{2,50}$/,
  alphanumeric: /^[a-zA-Z0-9]+$/,
  safeString: /^[a-zA-Z0-9\s\-_.,!?()]+$/,
  url: /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/,
  creditCard: /^[0-9]{13,19}$/,
  cvv: /^[0-9]{3,4}$/,
  postalCode: /^[0-9]{4}$/,
  idNumber: /^[0-9]{13}$/
};

// XSS Protection
export class XSSProtection {
  static sanitizeHTML(dirty: string): string {
    return DOMPurify.sanitize(dirty, {
      ALLOWED_TAGS: ['b', 'i', 'em', 'strong', 'p', 'br', 'ul', 'ol', 'li'],
      ALLOWED_ATTR: []
    });
  }

  static sanitizeText(input: string): string {
    return input
      .replace(/[<>]/g, '') // Remove angle brackets
      .replace(/javascript:/gi, '') // Remove javascript: protocol
      .replace(/on\w+=/gi, '') // Remove event handlers
      .trim();
  }

  static escapeHTML(text: string): string {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  static validateAndSanitize(input: string, allowHTML = false): string {
    if (typeof input !== 'string') {
      throw new Error('Input must be a string');
    }

    if (allowHTML) {
      return this.sanitizeHTML(input);
    }

    return this.sanitizeText(input);
  }
}

// Input Validation
export class InputValidator {
  static validateEmail(email: string): { isValid: boolean; error?: string } {
    if (!email) {
      return { isValid: false, error: 'Email is required' };
    }

    if (!ValidationPatterns.email.test(email)) {
      return { isValid: false, error: 'Please enter a valid email address' };
    }

    if (email.length > 254) {
      return { isValid: false, error: 'Email address is too long' };
    }

    return { isValid: true };
  }

  static validatePassword(password: string): { isValid: boolean; error?: string; strength?: number } {
    if (!password) {
      return { isValid: false, error: 'Password is required', strength: 0 };
    }

    if (password.length < 8) {
      return { isValid: false, error: 'Password must be at least 8 characters long', strength: 1 };
    }

    if (password.length > 128) {
      return { isValid: false, error: 'Password is too long', strength: 1 };
    }

    let strength = 0;
    if (/[a-z]/.test(password)) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/[0-9]/.test(password)) strength++;
    if (/[@$!%*?&]/.test(password)) strength++;

    if (strength < 3) {
      return { 
        isValid: false, 
        error: 'Password must contain at least 3 of: lowercase, uppercase, numbers, special characters',
        strength 
      };
    }

    // Check for common weak passwords
    const commonPasswords = ['password', '123456', 'qwerty', 'admin', 'letmein'];
    if (commonPasswords.some(weak => password.toLowerCase().includes(weak))) {
      return { isValid: false, error: 'Password is too common', strength };
    }

    return { isValid: true, strength };
  }

  static validatePhone(phone: string): { isValid: boolean; error?: string } {
    if (!phone) {
      return { isValid: false, error: 'Phone number is required' };
    }

    const cleanPhone = phone.replace(/[\s\-()]/g, '');
    
    if (!ValidationPatterns.phone.test(cleanPhone)) {
      return { isValid: false, error: 'Please enter a valid South African phone number' };
    }

    return { isValid: true };
  }

  static validateName(name: string): { isValid: boolean; error?: string } {
    if (!name) {
      return { isValid: false, error: 'Name is required' };
    }

    if (!ValidationPatterns.name.test(name)) {
      return { isValid: false, error: 'Name contains invalid characters' };
    }

    if (name.length < 2) {
      return { isValid: false, error: 'Name must be at least 2 characters long' };
    }

    if (name.length > 50) {
      return { isValid: false, error: 'Name is too long' };
    }

    return { isValid: true };
  }

  static validateCreditCard(cardNumber: string): { isValid: boolean; error?: string; type?: string } {
    if (!cardNumber) {
      return { isValid: false, error: 'Card number is required' };
    }

    const cleanNumber = cardNumber.replace(/[\s\-]/g, '');
    
    if (!ValidationPatterns.creditCard.test(cleanNumber)) {
      return { isValid: false, error: 'Invalid card number format' };
    }

    // Luhn algorithm validation
    if (!this.luhnCheck(cleanNumber)) {
      return { isValid: false, error: 'Invalid card number' };
    }

    // Detect card type
    const type = this.detectCardType(cleanNumber);
    
    return { isValid: true, type };
  }

  private static luhnCheck(cardNumber: string): boolean {
    let sum = 0;
    let isEven = false;

    for (let i = cardNumber.length - 1; i >= 0; i--) {
      let digit = parseInt(cardNumber.charAt(i), 10);

      if (isEven) {
        digit *= 2;
        if (digit > 9) {
          digit -= 9;
        }
      }

      sum += digit;
      isEven = !isEven;
    }

    return sum % 10 === 0;
  }

  private static detectCardType(cardNumber: string): string {
    if (/^4/.test(cardNumber)) return 'Visa';
    if (/^5[1-5]/.test(cardNumber)) return 'Mastercard';
    if (/^3[47]/.test(cardNumber)) return 'American Express';
    if (/^6/.test(cardNumber)) return 'Discover';
    return 'Unknown';
  }

  static validateAmount(amount: string | number): { isValid: boolean; error?: string } {
    const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;

    if (isNaN(numAmount)) {
      return { isValid: false, error: 'Amount must be a valid number' };
    }

    if (numAmount < 0) {
      return { isValid: false, error: 'Amount cannot be negative' };
    }

    if (numAmount > 1000000) {
      return { isValid: false, error: 'Amount is too large' };
    }

    return { isValid: true };
  }
}

// CSRF Protection
export class CSRFProtection {
  private static tokenKey = 'csrf_token';

  static generateToken(): string {
    const array = new Uint8Array(32);
    crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  }

  static setToken(token: string): void {
    sessionStorage.setItem(this.tokenKey, token);
  }

  static getToken(): string | null {
    return sessionStorage.getItem(this.tokenKey);
  }

  static validateToken(token: string): boolean {
    const storedToken = this.getToken();
    return storedToken !== null && storedToken === token;
  }

  static addTokenToHeaders(headers: Record<string, string> = {}): Record<string, string> {
    const token = this.getToken();
    if (token) {
      headers['X-CSRF-Token'] = token;
    }
    return headers;
  }
}

// Rate Limiting (Client-side)
export class RateLimiter {
  private static attempts: Map<string, number[]> = new Map();

  static checkLimit(key: string, maxAttempts: number, windowMs: number): boolean {
    const now = Date.now();
    const attempts = this.attempts.get(key) || [];
    
    // Remove old attempts outside the window
    const validAttempts = attempts.filter(time => now - time < windowMs);
    
    if (validAttempts.length >= maxAttempts) {
      return false; // Rate limit exceeded
    }

    // Add current attempt
    validAttempts.push(now);
    this.attempts.set(key, validAttempts);
    
    return true;
  }

  static getRemainingAttempts(key: string, maxAttempts: number, windowMs: number): number {
    const now = Date.now();
    const attempts = this.attempts.get(key) || [];
    const validAttempts = attempts.filter(time => now - time < windowMs);
    
    return Math.max(0, maxAttempts - validAttempts.length);
  }

  static getTimeUntilReset(key: string, windowMs: number): number {
    const attempts = this.attempts.get(key) || [];
    if (attempts.length === 0) return 0;
    
    const oldestAttempt = Math.min(...attempts);
    const resetTime = oldestAttempt + windowMs;
    
    return Math.max(0, resetTime - Date.now());
  }
}

// Content Security Policy helpers
export class CSPHelper {
  static generateNonce(): string {
    const array = new Uint8Array(16);
    crypto.getRandomValues(array);
    return btoa(String.fromCharCode(...array));
  }

  static isAllowedOrigin(url: string, allowedOrigins: string[]): boolean {
    try {
      const urlObj = new URL(url);
      return allowedOrigins.some(origin => {
        if (origin === '*') return true;
        if (origin.startsWith('*.')) {
          const domain = origin.slice(2);
          return urlObj.hostname.endsWith(domain);
        }
        return urlObj.origin === origin;
      });
    } catch {
      return false;
    }
  }
}

// Secure storage utilities
export class SecureStorage {
  private static encrypt(data: string, key: string): string {
    // Simple XOR encryption (for demo purposes - use proper encryption in production)
    let result = '';
    for (let i = 0; i < data.length; i++) {
      result += String.fromCharCode(data.charCodeAt(i) ^ key.charCodeAt(i % key.length));
    }
    return btoa(result);
  }

  private static decrypt(encryptedData: string, key: string): string {
    try {
      const data = atob(encryptedData);
      let result = '';
      for (let i = 0; i < data.length; i++) {
        result += String.fromCharCode(data.charCodeAt(i) ^ key.charCodeAt(i % key.length));
      }
      return result;
    } catch {
      return '';
    }
  }

  static setSecureItem(key: string, value: string, useEncryption = false): void {
    try {
      const dataToStore = useEncryption ? this.encrypt(value, key) : value;
      localStorage.setItem(key, dataToStore);
    } catch (error) {
      console.error('Failed to store secure item:', error);
    }
  }

  static getSecureItem(key: string, useEncryption = false): string | null {
    try {
      const storedData = localStorage.getItem(key);
      if (!storedData) return null;
      
      return useEncryption ? this.decrypt(storedData, key) : storedData;
    } catch (error) {
      console.error('Failed to retrieve secure item:', error);
      return null;
    }
  }

  static removeSecureItem(key: string): void {
    try {
      localStorage.removeItem(key);
    } catch (error) {
      console.error('Failed to remove secure item:', error);
    }
  }
}

// Security headers validation
export class SecurityHeaders {
  static validateResponse(response: Response): boolean {
    const requiredHeaders = [
      'x-content-type-options',
      'x-frame-options',
      'x-xss-protection'
    ];

    return requiredHeaders.every(header => response.headers.has(header));
  }

  static checkCSP(response: Response): boolean {
    return response.headers.has('content-security-policy') || 
           response.headers.has('content-security-policy-report-only');
  }

  static validateHSTS(response: Response): boolean {
    const hstsHeader = response.headers.get('strict-transport-security');
    return hstsHeader !== null && hstsHeader.includes('max-age=');
  }
}

// Export all security utilities
export {
  ValidationPatterns,
  XSSProtection,
  InputValidator,
  CSRFProtection,
  RateLimiter,
  CSPHelper,
  SecureStorage,
  SecurityHeaders
};
