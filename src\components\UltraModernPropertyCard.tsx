import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  Heart, 
  Star, 
  MapPin, 
  Users, 
  Bed, 
  Bath, 
  Wifi, 
  Car, 
  Coffee,
  Zap,
  Eye,
  Share2,
  Calendar,
  TrendingUp,
  Award,
  Verified,
  Camera,
  Play
} from 'lucide-react';
import { 
  HoverAnimation, 
  ScaleIn, 
  SlideIn 
} from '@/components/ui/page-transitions';
import { QuickTooltip } from '@/components/ui/enhanced-tooltip';

interface Property {
  id: string;
  title: string;
  location: string;
  price: number;
  rating: number;
  reviewCount: number;
  images: string[];
  bedrooms: number;
  bathrooms: number;
  guests: number;
  amenities: string[];
  host: {
    name: string;
    avatar?: string;
    verified?: boolean;
    superhost?: boolean;
  };
  available: boolean;
  special?: string;
  instantBook?: boolean;
  trending?: boolean;
  newListing?: boolean;
}

interface UltraModernPropertyCardProps {
  property: Property;
  variant?: 'grid' | 'featured' | 'compact';
  showHost?: boolean;
  onLike?: (id: string) => void;
  onShare?: (property: Property) => void;
  onView?: (property: Property) => void;
  className?: string;
}

export const UltraModernPropertyCard: React.FC<UltraModernPropertyCardProps> = ({
  property,
  variant = 'grid',
  showHost = true,
  onLike,
  onShare,
  onView,
  className
}) => {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isLiked, setIsLiked] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  const handleLike = () => {
    setIsLiked(!isLiked);
    onLike?.(property.id);
  };

  const handleShare = () => {
    onShare?.(property);
  };

  const handleView = () => {
    onView?.(property);
  };

  const nextImage = () => {
    setCurrentImageIndex((prev) => 
      prev === property.images.length - 1 ? 0 : prev + 1
    );
  };

  const prevImage = () => {
    setCurrentImageIndex((prev) => 
      prev === 0 ? property.images.length - 1 : prev - 1
    );
  };

  if (variant === 'featured') {
    return (
      <SlideIn direction="up" delay={100}>
        <HoverAnimation type="lift">
          <Card 
            className={`group overflow-hidden border-0 shadow-2xl hover:shadow-3xl transition-all duration-500 bg-gradient-to-br from-white via-gray-50 to-white ${className}`}
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
          >
            <div className="relative">
              {/* Enhanced Image Section */}
              <div className="relative h-80 overflow-hidden">
                <img
                  src={property.images[currentImageIndex] || property.images[0]}
                  alt={property.title}
                  className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                />
                
                {/* Gradient Overlay */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />
                
                {/* Top Badges */}
                <div className="absolute top-4 left-4 right-4 flex justify-between items-start">
                  <div className="flex flex-col gap-2">
                    {property.trending && (
                      <Badge className="bg-gradient-to-r from-red-500 to-pink-500 text-white shadow-lg">
                        <TrendingUp className="h-3 w-3 mr-1" />
                        Trending
                      </Badge>
                    )}
                    {property.newListing && (
                      <Badge className="bg-gradient-to-r from-green-500 to-emerald-500 text-white shadow-lg">
                        <Zap className="h-3 w-3 mr-1" />
                        New
                      </Badge>
                    )}
                    {property.special && (
                      <Badge className="bg-gradient-to-r from-purple-500 to-indigo-500 text-white shadow-lg">
                        {property.special}
                      </Badge>
                    )}
                  </div>
                  
                  <div className="flex gap-2">
                    <QuickTooltip text="Add to favorites">
                      <Button
                        size="icon-sm"
                        variant="secondary"
                        onClick={handleLike}
                        className="bg-white/90 hover:bg-white shadow-lg backdrop-blur-sm"
                      >
                        <Heart className={`h-4 w-4 ${isLiked ? 'fill-red-500 text-red-500' : 'text-gray-600'}`} />
                      </Button>
                    </QuickTooltip>
                    
                    <QuickTooltip text="Share property">
                      <Button
                        size="icon-sm"
                        variant="secondary"
                        onClick={handleShare}
                        className="bg-white/90 hover:bg-white shadow-lg backdrop-blur-sm"
                      >
                        <Share2 className="h-4 w-4 text-gray-600" />
                      </Button>
                    </QuickTooltip>
                  </div>
                </div>

                {/* Image Navigation */}
                {property.images.length > 1 && isHovered && (
                  <div className="absolute inset-x-4 top-1/2 transform -translate-y-1/2 flex justify-between">
                    <Button
                      size="icon-sm"
                      variant="secondary"
                      onClick={prevImage}
                      className="bg-white/90 hover:bg-white shadow-lg backdrop-blur-sm"
                    >
                      ←
                    </Button>
                    <Button
                      size="icon-sm"
                      variant="secondary"
                      onClick={nextImage}
                      className="bg-white/90 hover:bg-white shadow-lg backdrop-blur-sm"
                    >
                      →
                    </Button>
                  </div>
                )}

                {/* Image Indicators */}
                {property.images.length > 1 && (
                  <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex gap-1">
                    {property.images.map((_, index) => (
                      <button
                        key={index}
                        onClick={() => setCurrentImageIndex(index)}
                        className={`w-2 h-2 rounded-full transition-all duration-200 ${
                          index === currentImageIndex 
                            ? 'bg-white scale-125' 
                            : 'bg-white/50 hover:bg-white/75'
                        }`}
                      />
                    ))}
                  </div>
                )}

                {/* Photo Count */}
                <div className="absolute bottom-4 right-4">
                  <Badge className="bg-black/50 text-white backdrop-blur-sm">
                    <Camera className="h-3 w-3 mr-1" />
                    {property.images.length}
                  </Badge>
                </div>
              </div>

              {/* Enhanced Content */}
              <CardContent className="p-6">
                {/* Header */}
                <div className="flex justify-between items-start mb-4">
                  <div className="flex-1">
                    <h3 className="text-2xl font-bold text-gray-900 mb-1 group-hover:text-sea-green-600 transition-colors duration-300">
                      {property.title}
                    </h3>
                    <div className="flex items-center text-gray-600 mb-2">
                      <MapPin className="h-4 w-4 mr-1" />
                      <span className="font-medium">{property.location}</span>
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <div className="flex items-center gap-1 mb-1">
                      <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                      <span className="font-bold text-gray-900">{property.rating}</span>
                      <span className="text-sm text-gray-500">({property.reviewCount})</span>
                    </div>
                    {property.host.superhost && (
                      <Badge variant="outline" className="text-xs border-sea-green-300 text-sea-green-700">
                        <Award className="h-3 w-3 mr-1" />
                        Superhost
                      </Badge>
                    )}
                  </div>
                </div>

                {/* Property Details */}
                <div className="flex items-center gap-6 mb-4 text-sm text-gray-600">
                  <div className="flex items-center gap-1">
                    <Users className="h-4 w-4" />
                    <span>{property.guests} guests</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Bed className="h-4 w-4" />
                    <span>{property.bedrooms} beds</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Bath className="h-4 w-4" />
                    <span>{property.bathrooms} baths</span>
                  </div>
                </div>

                {/* Amenities */}
                <div className="flex flex-wrap gap-2 mb-6">
                  {property.amenities.slice(0, 4).map((amenity, index) => (
                    <Badge key={index} variant="secondary" className="text-xs bg-gray-100 text-gray-700">
                      {amenity === 'WiFi' && <Wifi className="h-3 w-3 mr-1" />}
                      {amenity === 'Parking' && <Car className="h-3 w-3 mr-1" />}
                      {amenity === 'Kitchen' && <Coffee className="h-3 w-3 mr-1" />}
                      {amenity}
                    </Badge>
                  ))}
                  {property.amenities.length > 4 && (
                    <Badge variant="secondary" className="text-xs bg-gray-100 text-gray-700">
                      +{property.amenities.length - 4} more
                    </Badge>
                  )}
                </div>

                {/* Host Info */}
                {showHost && (
                  <div className="flex items-center justify-between mb-6">
                    <div className="flex items-center gap-3">
                      <Avatar className="h-10 w-10 border-2 border-white shadow-md">
                        <AvatarImage src={property.host.avatar} />
                        <AvatarFallback className="bg-gradient-to-br from-sea-green-500 to-ocean-blue-500 text-white font-bold">
                          {property.host.name.charAt(0)}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="flex items-center gap-1">
                          <span className="font-medium text-gray-900">{property.host.name}</span>
                          {property.host.verified && (
                            <Verified className="h-4 w-4 text-blue-500" />
                          )}
                        </div>
                        <span className="text-sm text-gray-500">Host</span>
                      </div>
                    </div>
                  </div>
                )}

                {/* Price and CTA */}
                <div className="flex items-center justify-between">
                  <div>
                    <div className="flex items-baseline gap-1">
                      <span className="text-3xl font-bold text-gray-900">R{property.price}</span>
                      <span className="text-gray-500">/ night</span>
                    </div>
                    {property.instantBook && (
                      <div className="flex items-center gap-1 mt-1">
                        <Zap className="h-3 w-3 text-yellow-500" />
                        <span className="text-xs text-yellow-600 font-medium">Instant Book</span>
                      </div>
                    )}
                  </div>
                  
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      onClick={handleView}
                      className="border-sea-green-300 text-sea-green-700 hover:bg-sea-green-50"
                    >
                      <Eye className="h-4 w-4 mr-2" />
                      View
                    </Button>
                    <Button
                      onClick={handleView}
                      className="bg-gradient-to-r from-sea-green-500 to-ocean-blue-500 hover:from-sea-green-600 hover:to-ocean-blue-600 text-white shadow-lg"
                    >
                      <Calendar className="h-4 w-4 mr-2" />
                      Book Now
                    </Button>
                  </div>
                </div>
              </CardContent>
            </div>
          </Card>
        </HoverAnimation>
      </SlideIn>
    );
  }

  // Grid variant (default)
  return (
    <SlideIn direction="up" delay={100}>
      <HoverAnimation type="lift">
        <Card 
          className={`group overflow-hidden border-0 shadow-lg hover:shadow-2xl transition-all duration-500 bg-white ${className}`}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
        >
          {/* Compact Image Section */}
          <div className="relative h-64 overflow-hidden">
            <img
              src={property.images[currentImageIndex] || property.images[0]}
              alt={property.title}
              className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
            />
            
            {/* Quick Action Overlay */}
            <div className={`absolute inset-0 bg-black/20 flex items-center justify-center transition-opacity duration-300 ${
              isHovered ? 'opacity-100' : 'opacity-0'
            }`}>
              <Button
                onClick={handleView}
                className="bg-white/90 text-gray-900 hover:bg-white shadow-lg backdrop-blur-sm"
              >
                <Play className="h-4 w-4 mr-2" />
                Quick View
              </Button>
            </div>

            {/* Top Actions */}
            <div className="absolute top-3 right-3 flex gap-2">
              <Button
                size="icon-sm"
                variant="secondary"
                onClick={handleLike}
                className="bg-white/90 hover:bg-white shadow-md backdrop-blur-sm"
              >
                <Heart className={`h-4 w-4 ${isLiked ? 'fill-red-500 text-red-500' : 'text-gray-600'}`} />
              </Button>
            </div>

            {/* Badges */}
            <div className="absolute top-3 left-3 flex flex-col gap-1">
              {property.trending && (
                <Badge className="bg-red-500 text-white text-xs">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  Hot
                </Badge>
              )}
              {property.newListing && (
                <Badge className="bg-green-500 text-white text-xs">New</Badge>
              )}
            </div>
          </div>

          {/* Compact Content */}
          <CardContent className="p-4">
            <div className="flex justify-between items-start mb-2">
              <h3 className="font-bold text-gray-900 group-hover:text-sea-green-600 transition-colors duration-300 line-clamp-1">
                {property.title}
              </h3>
              <div className="flex items-center gap-1 text-sm">
                <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                <span className="font-medium">{property.rating}</span>
              </div>
            </div>
            
            <p className="text-gray-600 text-sm mb-3 flex items-center">
              <MapPin className="h-3 w-3 mr-1" />
              {property.location}
            </p>

            <div className="flex items-center justify-between">
              <div className="flex items-baseline gap-1">
                <span className="text-xl font-bold text-gray-900">R{property.price}</span>
                <span className="text-gray-500 text-sm">/ night</span>
              </div>
              
              <div className="flex items-center gap-3 text-xs text-gray-500">
                <span>{property.guests} guests</span>
                <span>{property.bedrooms} beds</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </HoverAnimation>
    </SlideIn>
  );
};
