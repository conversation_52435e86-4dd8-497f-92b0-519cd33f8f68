import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { advancedSearchService, SavedSearch, SearchCriteria } from '../services/advancedSearchService';
import { 
  Search, 
  Star,
  Bell,
  BellOff,
  Edit,
  Trash2,
  Plus,
  Calendar,
  MapPin,
  Users,
  Home,
  DollarSign
} from 'lucide-react';

interface SavedSearchesProps {
  onSearchSelect?: (criteria: SearchCriteria) => void;
  onCreateNew?: () => void;
  className?: string;
}

export const SavedSearches: React.FC<SavedSearchesProps> = ({
  onSearchSelect,
  onCreateNew,
  className
}) => {
  const [savedSearches, setSavedSearches] = useState<SavedSearch[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [editingSearch, setEditingSearch] = useState<string | null>(null);
  const [editName, setEditName] = useState('');

  useEffect(() => {
    fetchSavedSearches();
  }, []);

  const fetchSavedSearches = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const data = await advancedSearchService.getSavedSearches();
      setSavedSearches(data);
    } catch (err) {
      console.error('Error fetching saved searches:', err);
      setError('Failed to load saved searches');
    } finally {
      setLoading(false);
    }
  };

  const handleToggleNotifications = async (searchId: string, currentState: boolean) => {
    try {
      await advancedSearchService.updateSavedSearch(searchId, {
        notification_enabled: !currentState
      });
      
      setSavedSearches(prev => 
        prev.map(search => 
          search.id === searchId 
            ? { ...search, notification_enabled: !currentState }
            : search
        )
      );
    } catch (error) {
      console.error('Error toggling notifications:', error);
    }
  };

  const handleEditName = async (searchId: string) => {
    if (!editName.trim()) return;
    
    try {
      await advancedSearchService.updateSavedSearch(searchId, {
        name: editName.trim()
      });
      
      setSavedSearches(prev => 
        prev.map(search => 
          search.id === searchId 
            ? { ...search, name: editName.trim() }
            : search
        )
      );
      
      setEditingSearch(null);
      setEditName('');
    } catch (error) {
      console.error('Error updating search name:', error);
    }
  };

  const handleDeleteSearch = async (searchId: string) => {
    if (!confirm('Are you sure you want to delete this saved search?')) {
      return;
    }
    
    try {
      await advancedSearchService.deleteSavedSearch(searchId);
      setSavedSearches(prev => prev.filter(search => search.id !== searchId));
    } catch (error) {
      console.error('Error deleting search:', error);
    }
  };

  const startEditing = (search: SavedSearch) => {
    setEditingSearch(search.id);
    setEditName(search.name);
  };

  const cancelEditing = () => {
    setEditingSearch(null);
    setEditName('');
  };

  const renderSearchCriteria = (criteria: SearchCriteria) => {
    const items = [];
    
    if (criteria.location) {
      items.push(
        <div key="location" className="flex items-center gap-1 text-xs text-gray-600">
          <MapPin className="h-3 w-3" />
          <span>{criteria.location}</span>
        </div>
      );
    }
    
    if (criteria.propertyType) {
      items.push(
        <div key="type" className="flex items-center gap-1 text-xs text-gray-600">
          <Home className="h-3 w-3" />
          <span>{criteria.propertyType}</span>
        </div>
      );
    }
    
    if (criteria.guests) {
      items.push(
        <div key="guests" className="flex items-center gap-1 text-xs text-gray-600">
          <Users className="h-3 w-3" />
          <span>{criteria.guests} guests</span>
        </div>
      );
    }
    
    if (criteria.minPrice || criteria.maxPrice) {
      const priceText = criteria.minPrice && criteria.maxPrice 
        ? `R${criteria.minPrice} - R${criteria.maxPrice}`
        : criteria.minPrice 
        ? `From R${criteria.minPrice}`
        : `Up to R${criteria.maxPrice}`;
      
      items.push(
        <div key="price" className="flex items-center gap-1 text-xs text-gray-600">
          <DollarSign className="h-3 w-3" />
          <span>{priceText}</span>
        </div>
      );
    }
    
    if (criteria.checkIn && criteria.checkOut) {
      items.push(
        <div key="dates" className="flex items-center gap-1 text-xs text-gray-600">
          <Calendar className="h-3 w-3" />
          <span>{new Date(criteria.checkIn).toLocaleDateString()} - {new Date(criteria.checkOut).toLocaleDateString()}</span>
        </div>
      );
    }
    
    if (criteria.amenities && criteria.amenities.length > 0) {
      items.push(
        <Badge key="amenities" variant="outline" className="text-xs">
          {criteria.amenities.length} amenities
        </Badge>
      );
    }
    
    return (
      <div className="flex flex-wrap gap-2 mt-2">
        {items.slice(0, 4)}
        {items.length > 4 && (
          <Badge variant="outline" className="text-xs">
            +{items.length - 4} more
          </Badge>
        )}
      </div>
    );
  };

  const renderSearchItem = (search: SavedSearch) => {
    const isEditing = editingSearch === search.id;
    
    return (
      <div key={search.id} className="p-4 border border-gray-200 rounded-lg hover:border-sea-green-300 transition-colors">
        <div className="flex items-start justify-between mb-2">
          <div className="flex-1">
            {isEditing ? (
              <div className="flex items-center gap-2">
                <input
                  type="text"
                  value={editName}
                  onChange={(e) => setEditName(e.target.value)}
                  className="flex-1 px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-sea-green-500"
                  onKeyPress={(e) => e.key === 'Enter' && handleEditName(search.id)}
                  autoFocus
                />
                <Button
                  size="sm"
                  onClick={() => handleEditName(search.id)}
                  className="bg-sea-green-500 hover:bg-sea-green-600 text-xs px-2 py-1"
                >
                  Save
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={cancelEditing}
                  className="text-xs px-2 py-1"
                >
                  Cancel
                </Button>
              </div>
            ) : (
              <h4 className="font-medium text-gray-900 cursor-pointer hover:text-sea-green-600"
                  onClick={() => onSearchSelect?.(search.search_criteria)}>
                {search.name}
              </h4>
            )}
          </div>
          
          {!isEditing && (
            <div className="flex items-center gap-1">
              <Button
                size="sm"
                variant="ghost"
                onClick={() => handleToggleNotifications(search.id, search.notification_enabled)}
                className="p-1 h-auto"
              >
                {search.notification_enabled ? (
                  <Bell className="h-4 w-4 text-sea-green-500" />
                ) : (
                  <BellOff className="h-4 w-4 text-gray-400" />
                )}
              </Button>
              <Button
                size="sm"
                variant="ghost"
                onClick={() => startEditing(search)}
                className="p-1 h-auto"
              >
                <Edit className="h-4 w-4 text-gray-400 hover:text-gray-600" />
              </Button>
              <Button
                size="sm"
                variant="ghost"
                onClick={() => handleDeleteSearch(search.id)}
                className="p-1 h-auto"
              >
                <Trash2 className="h-4 w-4 text-gray-400 hover:text-red-500" />
              </Button>
            </div>
          )}
        </div>
        
        {!isEditing && (
          <>
            {renderSearchCriteria(search.search_criteria)}
            
            <div className="flex items-center justify-between mt-3 pt-3 border-t border-gray-100">
              <div className="flex items-center gap-2">
                {search.notification_enabled && (
                  <Badge variant="default" className="bg-sea-green-500 text-white text-xs">
                    Notifications On
                  </Badge>
                )}
                <span className="text-xs text-gray-500">
                  Saved {new Date(search.created_at).toLocaleDateString()}
                </span>
              </div>
              
              <Button
                size="sm"
                variant="outline"
                onClick={() => onSearchSelect?.(search.search_criteria)}
                className="text-xs"
              >
                <Search className="h-3 w-3 mr-1" />
                Search
              </Button>
            </div>
          </>
        )}
      </div>
    );
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="animate-pulse space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="border border-gray-200 rounded-lg p-4">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2 mb-3"></div>
                <div className="flex gap-2">
                  <div className="h-6 bg-gray-200 rounded w-16"></div>
                  <div className="h-6 bg-gray-200 rounded w-20"></div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="p-6 text-center">
          <Star className="h-12 w-12 mx-auto mb-4 text-gray-400" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Unable to Load Saved Searches
          </h3>
          <p className="text-gray-600 mb-4">{error}</p>
          <Button onClick={fetchSavedSearches}>
            Try Again
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Star className="h-5 w-5" />
            Saved Searches
          </CardTitle>
          {onCreateNew && (
            <Button size="sm" onClick={onCreateNew} className="bg-sea-green-500 hover:bg-sea-green-600">
              <Plus className="h-4 w-4 mr-1" />
              Save Current
            </Button>
          )}
        </div>
      </CardHeader>

      <CardContent>
        {savedSearches.length === 0 ? (
          <div className="text-center py-8">
            <Star className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No Saved Searches
            </h3>
            <p className="text-gray-600 mb-4">
              Save your search criteria to quickly find properties later
            </p>
            {onCreateNew && (
              <Button onClick={onCreateNew} className="bg-sea-green-500 hover:bg-sea-green-600">
                <Plus className="h-4 w-4 mr-1" />
                Save Your First Search
              </Button>
            )}
          </div>
        ) : (
          <div className="space-y-4">
            {savedSearches.map(renderSearchItem)}
          </div>
        )}
      </CardContent>
    </Card>
  );
};
