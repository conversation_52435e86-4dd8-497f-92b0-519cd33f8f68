# StayFinder Frontend Environment Configuration
# Copy this file to .env.local and fill in your actual Supabase values

# =============================================================================
# SUPABASE CONFIGURATION (Required for new features)
# =============================================================================

# Get these values from your Supabase project dashboard
VITE_SUPABASE_URL=https://your-project-ref.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key-here

# =============================================================================
# FEATURE FLAGS
# =============================================================================

# Enable Supabase features
VITE_ENABLE_SUPABASE_AUTH=true
VITE_ENABLE_SUPABASE_REALTIME=true
VITE_ENABLE_SUPABASE_STORAGE=true

# =============================================================================
# INSTRUCTIONS
# =============================================================================

# 1. Copy this file to .env.local
# 2. Replace the placeholder values with your actual Supabase credentials
# 3. Run the database migrations from database_migrations.sql in your Supabase SQL Editor
# 4. Restart the development server (npm run dev)

# Your Supabase project URL and anon key can be found in:
# Supabase Dashboard > Settings > API > Project URL & Project API keys
