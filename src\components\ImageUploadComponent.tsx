import React, { useState, useRef, useCallback } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  Upload, 
  X, 
  Star, 
  Image as ImageIcon, 
  Camera, 
  Edit3, 
  RotateCw, 
  Crop, 
  Download,
  Eye,
  Trash2,
  Plus,
  Move,
  ZoomIn,
  ZoomOut,
  MoreHorizontal
} from 'lucide-react';
import { 
  HoverAnimation, 
  SlideIn, 
  StaggeredAnimation,
  ScaleIn 
} from '@/components/ui/page-transitions';
import { QuickTooltip } from '@/components/ui/enhanced-tooltip';

interface ImageFile {
  id: string;
  file: File;
  url: string;
  caption?: string;
  isPrimary?: boolean;
  tags?: string[];
  uploadProgress?: number;
  isUploading?: boolean;
  error?: string;
}

interface ImageUploadComponentProps {
  images: ImageFile[];
  onImagesChange: (images: ImageFile[]) => void;
  maxImages?: number;
  maxFileSize?: number; // in MB
  acceptedFormats?: string[];
  showCaptions?: boolean;
  showTags?: boolean;
  allowReordering?: boolean;
  className?: string;
}

export const ImageUploadComponent: React.FC<ImageUploadComponentProps> = ({
  images,
  onImagesChange,
  maxImages = 20,
  maxFileSize = 10,
  acceptedFormats = ['image/jpeg', 'image/png', 'image/webp'],
  showCaptions = true,
  showTags = false,
  allowReordering = true,
  className
}) => {
  const [dragOver, setDragOver] = useState(false);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [editingCaption, setEditingCaption] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const generateId = () => Math.random().toString(36).substr(2, 9);

  const validateFile = (file: File): string | null => {
    if (!acceptedFormats.includes(file.type)) {
      return `Invalid file format. Accepted formats: ${acceptedFormats.join(', ')}`;
    }
    if (file.size > maxFileSize * 1024 * 1024) {
      return `File size too large. Maximum size: ${maxFileSize}MB`;
    }
    return null;
  };

  const handleFileSelect = useCallback((files: FileList | null) => {
    if (!files) return;

    const newImages: ImageFile[] = [];
    const errors: string[] = [];

    Array.from(files).forEach((file) => {
      const error = validateFile(file);
      if (error) {
        errors.push(`${file.name}: ${error}`);
        return;
      }

      if (images.length + newImages.length >= maxImages) {
        errors.push(`Maximum ${maxImages} images allowed`);
        return;
      }

      const imageFile: ImageFile = {
        id: generateId(),
        file,
        url: URL.createObjectURL(file),
        caption: '',
        isPrimary: images.length === 0 && newImages.length === 0,
        tags: [],
        uploadProgress: 0,
        isUploading: true
      };

      newImages.push(imageFile);
    });

    if (errors.length > 0) {
      alert(errors.join('\n'));
    }

    if (newImages.length > 0) {
      // Simulate upload progress
      newImages.forEach((image, index) => {
        setTimeout(() => {
          simulateUpload(image.id);
        }, index * 100);
      });

      onImagesChange([...images, ...newImages]);
    }
  }, [images, maxImages, onImagesChange]);

  const simulateUpload = (imageId: string) => {
    const updateProgress = (progress: number) => {
      onImagesChange(prev => 
        prev.map(img => 
          img.id === imageId 
            ? { ...img, uploadProgress: progress, isUploading: progress < 100 }
            : img
        )
      );
    };

    let progress = 0;
    const interval = setInterval(() => {
      progress += Math.random() * 30;
      if (progress >= 100) {
        updateProgress(100);
        clearInterval(interval);
      } else {
        updateProgress(progress);
      }
    }, 200);
  };

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
    handleFileSelect(e.dataTransfer.files);
  }, [handleFileSelect]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  }, []);

  const removeImage = (imageId: string) => {
    const imageToRemove = images.find(img => img.id === imageId);
    if (imageToRemove) {
      URL.revokeObjectURL(imageToRemove.url);
      const newImages = images.filter(img => img.id !== imageId);
      
      // If removed image was primary, make first image primary
      if (imageToRemove.isPrimary && newImages.length > 0) {
        newImages[0].isPrimary = true;
      }
      
      onImagesChange(newImages);
    }
  };

  const setPrimaryImage = (imageId: string) => {
    onImagesChange(
      images.map(img => ({
        ...img,
        isPrimary: img.id === imageId
      }))
    );
  };

  const updateCaption = (imageId: string, caption: string) => {
    onImagesChange(
      images.map(img => 
        img.id === imageId ? { ...img, caption } : img
      )
    );
  };

  const reorderImages = (fromIndex: number, toIndex: number) => {
    if (!allowReordering) return;
    
    const newImages = [...images];
    const [movedImage] = newImages.splice(fromIndex, 1);
    newImages.splice(toIndex, 0, movedImage);
    onImagesChange(newImages);
  };

  const openFileDialog = () => {
    fileInputRef.current?.click();
  };

  return (
    <SlideIn direction="up" delay={100}>
      <Card className={`border-0 shadow-lg ${className}`}>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Camera className="h-5 w-5 text-sea-green-600" />
              Property Images
              <Badge variant="secondary" className="bg-sea-green-100 text-sea-green-700">
                {images.length}/{maxImages}
              </Badge>
            </div>
            
            {images.length < maxImages && (
              <Button
                onClick={openFileDialog}
                size="sm"
                className="bg-sea-green-500 hover:bg-sea-green-600"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Photos
              </Button>
            )}
          </CardTitle>
        </CardHeader>

        <CardContent>
          {/* Upload Area */}
          {images.length < maxImages && (
            <div
              className={`border-2 border-dashed rounded-lg p-8 text-center transition-all cursor-pointer ${
                dragOver 
                  ? 'border-sea-green-500 bg-sea-green-50' 
                  : 'border-gray-300 hover:border-sea-green-400 hover:bg-gray-50'
              }`}
              onDrop={handleDrop}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onClick={openFileDialog}
            >
              <input
                ref={fileInputRef}
                type="file"
                multiple
                accept={acceptedFormats.join(',')}
                onChange={(e) => handleFileSelect(e.target.files)}
                className="hidden"
              />
              
              <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Upload Property Photos
              </h3>
              <p className="text-gray-600 mb-4">
                Drag and drop images here, or click to browse
              </p>
              <div className="text-sm text-gray-500">
                <p>Supported formats: JPEG, PNG, WebP</p>
                <p>Maximum file size: {maxFileSize}MB</p>
                <p>Maximum {maxImages} images</p>
              </div>
            </div>
          )}

          {/* Image Grid */}
          {images.length > 0 && (
            <div className="mt-6">
              <div className="flex items-center justify-between mb-4">
                <h4 className="font-semibold text-gray-900">
                  Uploaded Images ({images.length})
                </h4>
                {allowReordering && (
                  <p className="text-sm text-gray-600">
                    Drag to reorder • First image is the cover photo
                  </p>
                )}
              </div>

              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                <StaggeredAnimation delay={100}>
                  {images.map((image, index) => (
                    <HoverAnimation key={image.id} type="lift">
                      <div className="relative group">
                        {/* Image Container */}
                        <div className={`relative overflow-hidden rounded-lg border-2 transition-all ${
                          image.isPrimary 
                            ? 'border-yellow-400 ring-2 ring-yellow-200' 
                            : 'border-gray-200 group-hover:border-sea-green-300'
                        }`}>
                          <img
                            src={image.url}
                            alt={image.caption || `Property image ${index + 1}`}
                            className="w-full h-32 object-cover transition-transform group-hover:scale-105"
                          />
                          
                          {/* Upload Progress */}
                          {image.isUploading && (
                            <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                              <div className="text-white text-center">
                                <div className="w-16 h-16 border-4 border-white border-t-transparent rounded-full animate-spin mb-2"></div>
                                <div className="text-sm">{Math.round(image.uploadProgress || 0)}%</div>
                              </div>
                            </div>
                          )}

                          {/* Primary Badge */}
                          {image.isPrimary && (
                            <Badge className="absolute top-2 left-2 bg-yellow-500 text-white">
                              <Star className="h-3 w-3 mr-1" />
                              Primary
                            </Badge>
                          )}

                          {/* Action Buttons */}
                          <div className="absolute top-2 right-2 flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                            <QuickTooltip text="Set as primary">
                              <Button
                                size="icon-sm"
                                variant="secondary"
                                onClick={() => setPrimaryImage(image.id)}
                                className="bg-white/90 hover:bg-white"
                              >
                                <Star className="h-3 w-3" />
                              </Button>
                            </QuickTooltip>
                            
                            <QuickTooltip text="Remove image">
                              <Button
                                size="icon-sm"
                                variant="secondary"
                                onClick={() => removeImage(image.id)}
                                className="bg-white/90 hover:bg-white text-red-600 hover:text-red-700"
                              >
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </QuickTooltip>
                          </div>

                          {/* Image Index */}
                          <div className="absolute bottom-2 left-2">
                            <Badge variant="secondary" className="bg-black/50 text-white text-xs">
                              {index + 1}
                            </Badge>
                          </div>
                        </div>

                        {/* Caption Input */}
                        {showCaptions && (
                          <div className="mt-2">
                            {editingCaption === image.id ? (
                              <div className="flex gap-2">
                                <Input
                                  value={image.caption || ''}
                                  onChange={(e) => updateCaption(image.id, e.target.value)}
                                  placeholder="Add caption..."
                                  className="text-sm"
                                  onBlur={() => setEditingCaption(null)}
                                  onKeyDown={(e) => {
                                    if (e.key === 'Enter') setEditingCaption(null);
                                  }}
                                  autoFocus
                                />
                              </div>
                            ) : (
                              <div
                                onClick={() => setEditingCaption(image.id)}
                                className="text-sm text-gray-600 cursor-pointer hover:text-gray-900 min-h-[20px] p-1 rounded hover:bg-gray-50"
                              >
                                {image.caption || 'Add caption...'}
                              </div>
                            )}
                          </div>
                        )}

                        {/* Tags */}
                        {showTags && image.tags && image.tags.length > 0 && (
                          <div className="mt-2 flex flex-wrap gap-1">
                            {image.tags.map((tag, tagIndex) => (
                              <Badge key={tagIndex} variant="outline" className="text-xs">
                                {tag}
                              </Badge>
                            ))}
                          </div>
                        )}
                      </div>
                    </HoverAnimation>
                  ))}
                </StaggeredAnimation>
              </div>
            </div>
          )}

          {/* Tips */}
          <div className="mt-6 p-4 bg-blue-50 rounded-lg">
            <h5 className="font-medium text-blue-900 mb-2">📸 Photo Tips</h5>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Use high-quality, well-lit photos</li>
              <li>• Show different angles and rooms</li>
              <li>• Include outdoor spaces and views</li>
              <li>• The first image will be your cover photo</li>
              <li>• Add captions to highlight special features</li>
            </ul>
          </div>

          {/* Upload Summary */}
          {images.length > 0 && (
            <div className="mt-4 p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">
                  {images.length} image{images.length !== 1 ? 's' : ''} uploaded
                </span>
                <span className="text-gray-600">
                  {images.filter(img => img.isPrimary).length > 0 ? '✓ Primary photo set' : '⚠ Set primary photo'}
                </span>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </SlideIn>
  );
};
