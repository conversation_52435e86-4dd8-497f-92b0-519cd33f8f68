import React from 'react';
import { <PERSON>, <PERSON><PERSON>eader, <PERSON>T<PERSON>le, CardContent } from '@/components/ui/card';
import { SlideIn } from '@/components/ui/slide-in';
import { TrendingUp } from 'lucide-react';

interface PropertyAnalyticsProps {
  className?: string;
  analytics?: any;
}

export const PropertyAnalytics: React.FC<PropertyAnalyticsProps> = ({
  className = '',
  analytics
}) => {
  if (!analytics) {
    return null;
  }

  return (
    <SlideIn direction="up" delay={100}>
      <div className={`space-y-6 ${className}`}>
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Property Analytics</h2>
            <p className="text-gray-600">Track your property performance</p>
          </div>
        </div>

        <Card className="border-0 shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-sea-green-600" />
              Analytics Overview
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600">Analytics data will be displayed here.</p>
          </CardContent>
        </Card>
      </div>
    </SlideIn>
  );
};

// Export alias for backward compatibility
export const PropertyAnalyticsComponent = PropertyAnalytics;