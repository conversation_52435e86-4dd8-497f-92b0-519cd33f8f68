import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { propertyManagementService, PropertyAnalytics } from '../services/propertyManagementService';
import {
  TrendingUp,
  TrendingDown,
  Calendar,
  DollarSign,
  Users,
  BarChart3,
  PieChart,
  Activity,
  Clock,
  Star,
  Eye,
  Heart,
  MessageSquare,
  Target,
  Award,
  MapPin,
  Zap,
  Filter,
  Download,
  RefreshCw,
  ArrowUpRight,
  ArrowDownRight,
  Percent,
  Globe,
  Smartphone
} from 'lucide-react';
import {
  HoverAnimation,
  SlideIn,
  StaggeredAnimation,
  ScaleIn
} from '@/components/ui/page-transitions';
import { QuickTooltip } from '@/components/ui/enhanced-tooltip';

interface PropertyAnalyticsProps {
  propertyId: string;
  propertyTitle: string;
  className?: string;
}

export const PropertyAnalyticsComponent: React.FC<PropertyAnalyticsProps> = ({
  propertyId,
  propertyTitle,
  className
}) => {
  const [analytics, setAnalytics] = useState<PropertyAnalytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedPeriod, setSelectedPeriod] = useState('30d');

  const periods = [
    { value: '7d', label: '7 Days' },
    { value: '30d', label: '30 Days' },
    { value: '90d', label: '90 Days' },
    { value: '1y', label: '1 Year' }
  ];

  useEffect(() => {
    fetchAnalytics();
  }, [propertyId, selectedPeriod]);

  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const data = await propertyManagementService.getPropertyAnalytics(propertyId, selectedPeriod);
      setAnalytics(data);
    } catch (err) {
      console.error('Error fetching analytics:', err);
      setError('Failed to load analytics data');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return propertyManagementService.formatCurrency(amount);
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  const getChangeIndicator = (current: number, previous: number) => {
    if (previous === 0) return null;
    
    const change = ((current - previous) / previous) * 100;
    const isPositive = change > 0;
    
    return (
      <div className={`flex items-center gap-1 text-sm ${isPositive ? 'text-green-600' : 'text-red-600'}`}>
        {isPositive ? <TrendingUp className="h-3 w-3" /> : <TrendingDown className="h-3 w-3" />}
        <span>{Math.abs(change).toFixed(1)}%</span>
      </div>
    );
  };

  const renderMetricCard = (
    title: string,
    value: string | number,
    icon: React.ReactNode,
    subtitle?: string,
    change?: React.ReactNode
  ) => (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600">{title}</p>
            <p className="text-2xl font-bold text-gray-900">{value}</p>
            {subtitle && (
              <p className="text-sm text-gray-500 mt-1">{subtitle}</p>
            )}
          </div>
          <div className="flex flex-col items-end gap-2">
            <div className="p-2 bg-sea-green-100 rounded-lg">
              {icon}
            </div>
            {change}
          </div>
        </div>
      </CardContent>
    </Card>
  );

  const renderRevenueChart = () => {
    if (!analytics?.daily_data || analytics.daily_data.length === 0) {
      return (
        <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
          <div className="text-center">
            <BarChart3 className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <p className="text-gray-600">No revenue data available</p>
          </div>
        </div>
      );
    }

    const maxRevenue = Math.max(...analytics.daily_data.map(d => d.revenue));
    
    return (
      <div className="space-y-4">
        <div className="h-64 flex items-end justify-between gap-2 p-4 bg-gray-50 rounded-lg">
          {analytics.daily_data.map((day, index) => {
            const height = maxRevenue > 0 ? (day.revenue / maxRevenue) * 200 : 0;
            
            return (
              <div key={day.date} className="flex flex-col items-center gap-2 flex-1">
                <div className="text-xs text-gray-600 text-center">
                  {formatCurrency(day.revenue)}
                </div>
                <div 
                  className="bg-sea-green-500 rounded-t w-full min-h-[4px] transition-all duration-300 hover:bg-sea-green-600"
                  style={{ height: `${height}px` }}
                  title={`${day.date}: ${formatCurrency(day.revenue)} (${day.bookings} bookings)`}
                />
                <div className="text-xs text-gray-500 text-center">
                  {new Date(day.date).toLocaleDateString('en-US', { 
                    month: 'short', 
                    day: 'numeric' 
                  })}
                </div>
              </div>
            );
          })}
        </div>
        
        <div className="flex items-center justify-center gap-4 text-sm text-gray-600">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-sea-green-500 rounded"></div>
            <span>Daily Revenue</span>
          </div>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="animate-pulse space-y-4">
            <div className="h-6 bg-gray-200 rounded w-1/3"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="h-24 bg-gray-200 rounded"></div>
              ))}
            </div>
            <div className="h-64 bg-gray-200 rounded"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="p-6 text-center">
          <Activity className="h-12 w-12 mx-auto mb-4 text-gray-400" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Unable to Load Analytics
          </h3>
          <p className="text-gray-600 mb-4">{error}</p>
          <Button onClick={fetchAnalytics}>
            Try Again
          </Button>
        </CardContent>
      </Card>
    );
  }

  if (!analytics) {
    return null;
  }

  const { booking_stats } = analytics;

  return (
    <SlideIn direction="up" delay={100}>
      <div className={`space-y-6 ${className}`}>
        {/* Enhanced Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          <div>
            <h2 className="text-3xl font-bold text-gray-900 flex items-center gap-2">
              <BarChart3 className="h-8 w-8 text-sea-green-600" />
              Property Analytics
            </h2>
            <p className="text-gray-600 mt-1">{propertyTitle}</p>
            <Badge variant="secondary" className="mt-2 bg-sea-green-100 text-sea-green-700">
              Last updated: {new Date().toLocaleDateString()}
            </Badge>
          </div>

          <div className="flex flex-wrap items-center gap-3">
            {/* Period Selector */}
            <div className="flex items-center gap-2">
              {periods.map((period) => (
                <Button
                  key={period.value}
                  variant={selectedPeriod === period.value ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedPeriod(period.value)}
                  className={selectedPeriod === period.value ? 'bg-sea-green-500 hover:bg-sea-green-600' : ''}
                >
                  {period.label}
                </Button>
              ))}
            </div>

            {/* Action Buttons */}
            <div className="flex gap-2">
              <QuickTooltip text="Refresh Data">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={fetchAnalytics}
                  disabled={loading}
                >
                  <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
                </Button>
              </QuickTooltip>

              <QuickTooltip text="Export Report">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {/* Export functionality */}}
                >
                  <Download className="h-4 w-4" />
                </Button>
              </QuickTooltip>
            </div>
          </div>
        </div>

        {/* Enhanced Metrics Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <StaggeredAnimation delay={100}>
            <HoverAnimation type="lift">
              {renderMetricCard(
                'Total Revenue',
                formatCurrency(booking_stats.total_revenue),
                <DollarSign className="h-5 w-5 text-sea-green-600" />,
                `${booking_stats.confirmed_bookings} confirmed bookings`,
                getChangeIndicator(booking_stats.total_revenue, booking_stats.total_revenue * 0.85)
              )}
            </HoverAnimation>

            <HoverAnimation type="lift">
              {renderMetricCard(
                'Occupancy Rate',
                formatPercentage(booking_stats.occupancy_rate),
                <Calendar className="h-5 w-5 text-blue-600" />,
                `${booking_stats.total_bookings} total bookings`,
                getChangeIndicator(booking_stats.occupancy_rate, booking_stats.occupancy_rate * 0.92)
              )}
            </HoverAnimation>

            <HoverAnimation type="lift">
              {renderMetricCard(
                'Average Booking Value',
                formatCurrency(booking_stats.average_booking_value),
                <Users className="h-5 w-5 text-purple-600" />,
                `${booking_stats.average_stay_length.toFixed(1)} avg nights`,
                getChangeIndicator(booking_stats.average_booking_value, booking_stats.average_booking_value * 0.88)
              )}
            </HoverAnimation>

            <HoverAnimation type="lift">
              {renderMetricCard(
                'Booking Success Rate',
                formatPercentage(
                  booking_stats.total_bookings > 0
                    ? (booking_stats.confirmed_bookings / booking_stats.total_bookings) * 100
                    : 0
                ),
                <Target className="h-5 w-5 text-orange-600" />,
                `${booking_stats.cancelled_bookings} cancelled`,
                getChangeIndicator(
                  booking_stats.total_bookings > 0 ? (booking_stats.confirmed_bookings / booking_stats.total_bookings) * 100 : 0,
                  75
                )
              )}
            </HoverAnimation>
          </StaggeredAnimation>
        </div>

        {/* Additional Metrics Row */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          <StaggeredAnimation delay={200}>
            <HoverAnimation type="scale">
              <Card className="border-0 shadow-md">
                <CardContent className="p-4 text-center">
                  <Eye className="h-6 w-6 text-blue-500 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-gray-900">1,247</div>
                  <div className="text-sm text-gray-600">Page Views</div>
                </CardContent>
              </Card>
            </HoverAnimation>

            <HoverAnimation type="scale">
              <Card className="border-0 shadow-md">
                <CardContent className="p-4 text-center">
                  <Heart className="h-6 w-6 text-red-500 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-gray-900">89</div>
                  <div className="text-sm text-gray-600">Favorites</div>
                </CardContent>
              </Card>
            </HoverAnimation>

            <HoverAnimation type="scale">
              <Card className="border-0 shadow-md">
                <CardContent className="p-4 text-center">
                  <MessageSquare className="h-6 w-6 text-green-500 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-gray-900">23</div>
                  <div className="text-sm text-gray-600">Inquiries</div>
                </CardContent>
              </Card>
            </HoverAnimation>

            <HoverAnimation type="scale">
              <Card className="border-0 shadow-md">
                <CardContent className="p-4 text-center">
                  <Star className="h-6 w-6 text-yellow-500 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-gray-900">4.8</div>
                  <div className="text-sm text-gray-600">Avg Rating</div>
                </CardContent>
              </Card>
            </HoverAnimation>

            <HoverAnimation type="scale">
              <Card className="border-0 shadow-md">
                <CardContent className="p-4 text-center">
                  <Percent className="h-6 w-6 text-indigo-500 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-gray-900">12%</div>
                  <div className="text-sm text-gray-600">Conversion</div>
                </CardContent>
              </Card>
            </HoverAnimation>
          </StaggeredAnimation>
        </div>

      {/* Revenue Chart */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Revenue Trend
          </CardTitle>
        </CardHeader>
        <CardContent>
          {renderRevenueChart()}
        </CardContent>
      </Card>

      {/* Performance Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <PieChart className="h-5 w-5" />
            Performance Summary
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-sea-green-600 mb-2">
                {booking_stats.total_bookings}
              </div>
              <div className="text-sm text-gray-600">Total Bookings</div>
              <div className="flex items-center justify-center gap-2 mt-2">
                <Badge variant="outline" className="bg-green-100 text-green-800">
                  {booking_stats.confirmed_bookings} confirmed
                </Badge>
                {booking_stats.cancelled_bookings > 0 && (
                  <Badge variant="outline" className="bg-red-100 text-red-800">
                    {booking_stats.cancelled_bookings} cancelled
                  </Badge>
                )}
              </div>
            </div>
            
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600 mb-2">
                {booking_stats.average_stay_length.toFixed(1)}
              </div>
              <div className="text-sm text-gray-600">Average Stay (nights)</div>
              <div className="mt-2">
                <Badge variant="outline" className="bg-blue-100 text-blue-800">
                  {formatCurrency(booking_stats.average_booking_value)} avg value
                </Badge>
              </div>
            </div>
            
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-600 mb-2">
                {formatPercentage(booking_stats.occupancy_rate)}
              </div>
              <div className="text-sm text-gray-600">Occupancy Rate</div>
              <div className="mt-2">
                <Badge 
                  variant="outline" 
                  className={
                    booking_stats.occupancy_rate >= 70 
                      ? 'bg-green-100 text-green-800'
                      : booking_stats.occupancy_rate >= 50
                      ? 'bg-yellow-100 text-yellow-800'
                      : 'bg-red-100 text-red-800'
                  }
                >
                  {booking_stats.occupancy_rate >= 70 ? 'Excellent' : 
                   booking_stats.occupancy_rate >= 50 ? 'Good' : 'Needs Improvement'}
                </Badge>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
    </SlideIn>
  );
};
