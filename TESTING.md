# Testing Guide for StayFinder

## Overview

StayFinder uses a comprehensive testing strategy that includes unit tests, integration tests, and end-to-end (E2E) tests to ensure code quality and reliability.

## Testing Stack

### Unit & Integration Testing
- **Vitest**: Fast unit test runner with native ES modules support
- **React Testing Library**: Testing utilities for React components
- **Jest DOM**: Custom Jest matchers for DOM testing
- **MSW (Mock Service Worker)**: API mocking for integration tests
- **User Event**: Realistic user interaction simulation

### End-to-End Testing
- **Playwright**: Cross-browser E2E testing framework
- **Multiple browsers**: Chrome, Firefox, Safari, Edge
- **Mobile testing**: iOS Safari, Android Chrome
- **Visual regression**: Screenshot comparison testing

## Test Structure

```
src/
├── test/
│   ├── setup.ts                 # Test environment setup
│   ├── mocks/
│   │   └── handlers.ts          # MSW API handlers
│   └── utils/
│       └── test-utils.tsx       # Custom testing utilities
├── components/
│   ├── ui/
│   │   └── __tests__/
│   │       └── button.test.tsx  # Component unit tests
│   └── __tests__/
│       └── *.test.tsx           # Component tests
└── pages/
    └── __tests__/
        └── *.test.tsx           # Page component tests

e2e/
├── global-setup.ts              # E2E test setup
├── global-teardown.ts           # E2E test cleanup
├── homepage.spec.ts             # Homepage E2E tests
└── search-and-booking.spec.ts   # User flow E2E tests
```

## Running Tests

### Unit & Integration Tests

```bash
# Run all tests
npm run test

# Run tests in watch mode
npm run test:watch

# Run tests with UI
npm run test:ui

# Run tests once (CI mode)
npm run test:run

# Run tests with coverage
npm run test:coverage
```

### End-to-End Tests

```bash
# Install Playwright browsers (first time only)
npm run playwright:install

# Run E2E tests
npm run test:e2e

# Run E2E tests with UI
npm run test:e2e:ui

# Run E2E tests in headed mode (visible browser)
npm run test:e2e:headed

# Debug E2E tests
npm run test:e2e:debug
```

### All Tests

```bash
# Run all tests (unit + E2E)
npm run test:all

# Run CI test suite
npm run test:ci
```

## Test Coverage

### Coverage Thresholds
- **Branches**: 80%
- **Functions**: 80%
- **Lines**: 80%
- **Statements**: 80%

### Coverage Reports
- **Text**: Console output
- **HTML**: `coverage/index.html`
- **JSON**: `coverage/coverage.json`

## Writing Tests

### Unit Tests

```typescript
import { describe, it, expect, vi } from 'vitest';
import { render, screen, userEvent } from '@/test/utils/test-utils';
import { Button } from '../Button';

describe('Button Component', () => {
  it('renders with correct text', () => {
    render(<Button>Click me</Button>);
    expect(screen.getByRole('button', { name: /click me/i })).toBeInTheDocument();
  });

  it('handles click events', async () => {
    const handleClick = vi.fn();
    const user = userEvent.setup();
    
    render(<Button onClick={handleClick}>Click me</Button>);
    
    await user.click(screen.getByRole('button'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });
});
```

### Integration Tests

```typescript
import { describe, it, expect } from 'vitest';
import { render, screen, waitFor } from '@/test/utils/test-utils';
import { server } from '@/test/setup';
import { http, HttpResponse } from 'msw';
import { PropertyList } from '../PropertyList';

describe('PropertyList Integration', () => {
  it('loads and displays properties', async () => {
    render(<PropertyList />);
    
    await waitFor(() => {
      expect(screen.getByText('Luxury Villa')).toBeInTheDocument();
    });
  });

  it('handles API errors gracefully', async () => {
    server.use(
      http.get('/api/properties', () => {
        return HttpResponse.json({ error: 'Server error' }, { status: 500 });
      })
    );

    render(<PropertyList />);
    
    await waitFor(() => {
      expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
    });
  });
});
```

### E2E Tests

```typescript
import { test, expect } from '@playwright/test';

test.describe('Search Flow', () => {
  test('user can search for properties', async ({ page }) => {
    await page.goto('/');
    
    await page.getByPlaceholder(/where are you going/i).fill('Cape Town');
    await page.getByRole('button', { name: /search/i }).click();
    
    await expect(page).toHaveURL(/\/search/);
    await expect(page.getByText(/search results/i)).toBeVisible();
  });
});
```

## Test Utilities

### Custom Render Function
The `test-utils.tsx` provides a custom render function that wraps components with necessary providers:

```typescript
import { render } from '@/test/utils/test-utils';

// Automatically includes AuthProvider, QueryClient, etc.
render(<MyComponent />);

// Customize providers
render(<MyComponent />, {
  withAuth: false,
  queryClient: customQueryClient
});
```

### Mock Data
Consistent mock data is available for testing:

```typescript
import { mockUser, mockProperty, mockBooking } from '@/test/utils/test-utils';
```

### Common Mocks
Pre-configured mocks for browser APIs:

```typescript
import { setupCommonMocks } from '@/test/utils/test-utils';

beforeEach(() => {
  setupCommonMocks(); // Sets up IntersectionObserver, localStorage, etc.
});
```

## API Mocking

### MSW Handlers
API endpoints are mocked using MSW handlers in `src/test/mocks/handlers.ts`:

```typescript
export const handlers = [
  http.get('/api/properties', () => {
    return HttpResponse.json({ properties: mockProperties });
  }),
  
  http.post('/api/auth/login', async ({ request }) => {
    const body = await request.json();
    // Handle login logic
    return HttpResponse.json({ user: mockUser, token: 'mock-token' });
  })
];
```

### Runtime Handler Override
Override handlers for specific test scenarios:

```typescript
server.use(
  http.get('/api/properties', () => {
    return HttpResponse.json({ error: 'Server error' }, { status: 500 });
  })
);
```

## Best Practices

### Unit Tests
1. **Test behavior, not implementation**
2. **Use descriptive test names**
3. **Follow AAA pattern** (Arrange, Act, Assert)
4. **Mock external dependencies**
5. **Test edge cases and error states**

### Integration Tests
1. **Test component interactions**
2. **Mock API calls with realistic data**
3. **Test loading and error states**
4. **Verify user workflows**

### E2E Tests
1. **Test critical user journeys**
2. **Use data-testid for reliable selectors**
3. **Test across different browsers**
4. **Include mobile testing**
5. **Test accessibility standards**

### General Guidelines
1. **Write tests first (TDD)**
2. **Keep tests simple and focused**
3. **Use meaningful assertions**
4. **Clean up after tests**
5. **Maintain test data consistency**

## Continuous Integration

### GitHub Actions
Tests run automatically on:
- Pull requests
- Pushes to main branch
- Scheduled runs (daily)

### Test Reports
- **Unit test coverage**: Uploaded to Codecov
- **E2E test results**: Playwright HTML report
- **Performance metrics**: Lighthouse CI

## Debugging Tests

### Unit Tests
```bash
# Debug specific test
npm run test -- --reporter=verbose MyComponent.test.tsx

# Debug with browser devtools
npm run test:ui
```

### E2E Tests
```bash
# Debug mode with browser devtools
npm run test:e2e:debug

# Run specific test file
npx playwright test homepage.spec.ts

# Generate test code
npx playwright codegen localhost:5173
```

## Performance Testing

### Lighthouse CI
Automated performance testing runs on every deployment:
- **Performance score**: > 90
- **Accessibility score**: > 95
- **Best practices score**: > 90
- **SEO score**: > 90

### Load Testing
- **Artillery**: API load testing
- **k6**: User scenario load testing

## Security Testing

### Dependency Scanning
- **npm audit**: Vulnerability scanning
- **Snyk**: Continuous security monitoring

### OWASP Testing
- **ZAP**: Security vulnerability scanning
- **Bandit**: Static security analysis

## Accessibility Testing

### Automated Testing
- **axe-core**: Accessibility rule checking
- **Lighthouse**: Accessibility scoring

### Manual Testing
- **Screen readers**: NVDA, JAWS, VoiceOver
- **Keyboard navigation**: Tab order, focus management
- **Color contrast**: WCAG AA compliance

## Monitoring & Alerting

### Test Failures
- **Slack notifications**: Failed test alerts
- **Email reports**: Daily test summaries

### Performance Regression
- **Performance budgets**: Automated alerts
- **Core Web Vitals**: Real user monitoring
