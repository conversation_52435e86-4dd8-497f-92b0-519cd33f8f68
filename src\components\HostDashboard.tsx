import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { PropertyAnalytics } from './PropertyAnalytics';
import { PropertyCreationWizard } from './PropertyCreationWizard';
import { ImageUploadComponent } from './ImageUploadComponent';
import { 
  Home,
  Plus,
  Settings,
  BarChart3,
  Calendar,
  MessageSquare,
  Star,
  DollarSign,
  Users,
  Eye,
  Edit,
  Trash2,
  MoreHorizontal,
  Bell,
  Search,
  Filter,
  Download,
  Upload,
  Zap,
  Award,
  Shield,
  Clock,
  MapPin,
  Camera,
  Heart,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  XCircle
} from 'lucide-react';
import { 
  HoverAnimation, 
  SlideIn, 
  StaggeredAnimation,
  ScaleIn,
  PageTransition 
} from '@/components/ui/page-transitions';
import { QuickTooltip } from '@/components/ui/enhanced-tooltip';

interface Property {
  id: string;
  title: string;
  location: string;
  propertyType: string;
  price: number;
  images: string[];
  status: 'active' | 'inactive' | 'pending' | 'suspended';
  bookings: number;
  revenue: number;
  rating: number;
  reviewCount: number;
  occupancyRate: number;
  lastBooking?: string;
  createdAt: string;
  updatedAt: string;
}

interface Booking {
  id: string;
  propertyId: string;
  propertyTitle: string;
  guestName: string;
  guestEmail: string;
  checkIn: string;
  checkOut: string;
  guests: number;
  totalAmount: number;
  status: 'confirmed' | 'pending' | 'cancelled' | 'completed';
  createdAt: string;
}

interface HostDashboardProps {
  hostId: string;
  className?: string;
}

export const HostDashboard: React.FC<HostDashboardProps> = ({
  hostId,
  className
}) => {
  const [activeTab, setActiveTab] = useState<'overview' | 'properties' | 'bookings' | 'analytics' | 'settings'>('overview');
  const [properties, setProperties] = useState<Property[]>([]);
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [loading, setLoading] = useState(true);
  const [showCreateProperty, setShowCreateProperty] = useState(false);
  const [selectedProperty, setSelectedProperty] = useState<Property | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  // Mock data for demonstration
  useEffect(() => {
    const mockProperties: Property[] = [
      {
        id: '1',
        title: 'Luxury Beachfront Villa',
        location: 'Camps Bay, Cape Town',
        propertyType: 'Villa',
        price: 2500,
        images: ['https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=400&h=300&fit=crop'],
        status: 'active',
        bookings: 24,
        revenue: 60000,
        rating: 4.9,
        reviewCount: 18,
        occupancyRate: 85,
        lastBooking: '2024-01-15',
        createdAt: '2023-06-01',
        updatedAt: '2024-01-10'
      },
      {
        id: '2',
        title: 'Modern City Apartment',
        location: 'Sandton, Johannesburg',
        propertyType: 'Apartment',
        price: 850,
        images: ['https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?w=400&h=300&fit=crop'],
        status: 'active',
        bookings: 31,
        revenue: 26350,
        rating: 4.7,
        reviewCount: 23,
        occupancyRate: 72,
        lastBooking: '2024-01-12',
        createdAt: '2023-08-15',
        updatedAt: '2024-01-08'
      },
      {
        id: '3',
        title: 'Safari Lodge Experience',
        location: 'Kruger National Park',
        propertyType: 'Lodge',
        price: 1200,
        images: ['https://images.unsplash.com/photo-1516026672322-bc52d61a55d5?w=400&h=300&fit=crop'],
        status: 'pending',
        bookings: 12,
        revenue: 14400,
        rating: 4.8,
        reviewCount: 9,
        occupancyRate: 68,
        createdAt: '2023-11-01',
        updatedAt: '2023-12-20'
      }
    ];

    const mockBookings: Booking[] = [
      {
        id: '1',
        propertyId: '1',
        propertyTitle: 'Luxury Beachfront Villa',
        guestName: 'John Smith',
        guestEmail: '<EMAIL>',
        checkIn: '2024-02-15',
        checkOut: '2024-02-20',
        guests: 4,
        totalAmount: 12500,
        status: 'confirmed',
        createdAt: '2024-01-20'
      },
      {
        id: '2',
        propertyId: '2',
        propertyTitle: 'Modern City Apartment',
        guestName: 'Sarah Johnson',
        guestEmail: '<EMAIL>',
        checkIn: '2024-02-10',
        checkOut: '2024-02-12',
        guests: 2,
        totalAmount: 1700,
        status: 'pending',
        createdAt: '2024-01-18'
      }
    ];

    setTimeout(() => {
      setProperties(mockProperties);
      setBookings(mockBookings);
      setLoading(false);
    }, 1000);
  }, [hostId]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'inactive': return 'bg-gray-100 text-gray-800';
      case 'suspended': return 'bg-red-100 text-red-800';
      case 'confirmed': return 'bg-green-100 text-green-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      case 'completed': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <CheckCircle className="h-4 w-4" />;
      case 'pending': return <Clock className="h-4 w-4" />;
      case 'inactive': return <XCircle className="h-4 w-4" />;
      case 'suspended': return <AlertCircle className="h-4 w-4" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  const filteredProperties = properties.filter(property => {
    const matchesSearch = property.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         property.location.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = statusFilter === 'all' || property.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const totalRevenue = properties.reduce((sum, property) => sum + property.revenue, 0);
  const totalBookings = properties.reduce((sum, property) => sum + property.bookings, 0);
  const averageRating = properties.length > 0 
    ? properties.reduce((sum, property) => sum + property.rating, 0) / properties.length 
    : 0;
  const averageOccupancy = properties.length > 0 
    ? properties.reduce((sum, property) => sum + property.occupancyRate, 0) / properties.length 
    : 0;

  const tabs = [
    { id: 'overview', label: 'Overview', icon: Home },
    { id: 'properties', label: 'Properties', icon: Home },
    { id: 'bookings', label: 'Bookings', icon: Calendar },
    { id: 'analytics', label: 'Analytics', icon: BarChart3 },
    { id: 'settings', label: 'Settings', icon: Settings }
  ];

  const renderOverview = () => (
    <div className="space-y-6">
      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StaggeredAnimation delay={100}>
          <HoverAnimation type="lift">
            <Card className="border-0 shadow-lg">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Revenue</p>
                    <p className="text-3xl font-bold text-gray-900">R{totalRevenue.toLocaleString()}</p>
                    <p className="text-sm text-green-600 flex items-center gap-1 mt-1">
                      <TrendingUp className="h-3 w-3" />
                      +12.5% from last month
                    </p>
                  </div>
                  <div className="p-3 bg-green-100 rounded-full">
                    <DollarSign className="h-6 w-6 text-green-600" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </HoverAnimation>

          <HoverAnimation type="lift">
            <Card className="border-0 shadow-lg">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Bookings</p>
                    <p className="text-3xl font-bold text-gray-900">{totalBookings}</p>
                    <p className="text-sm text-blue-600 flex items-center gap-1 mt-1">
                      <TrendingUp className="h-3 w-3" />
                      +8.2% from last month
                    </p>
                  </div>
                  <div className="p-3 bg-blue-100 rounded-full">
                    <Calendar className="h-6 w-6 text-blue-600" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </HoverAnimation>

          <HoverAnimation type="lift">
            <Card className="border-0 shadow-lg">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Average Rating</p>
                    <p className="text-3xl font-bold text-gray-900">{averageRating.toFixed(1)}</p>
                    <p className="text-sm text-yellow-600 flex items-center gap-1 mt-1">
                      <Star className="h-3 w-3 fill-current" />
                      Excellent rating
                    </p>
                  </div>
                  <div className="p-3 bg-yellow-100 rounded-full">
                    <Star className="h-6 w-6 text-yellow-600" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </HoverAnimation>

          <HoverAnimation type="lift">
            <Card className="border-0 shadow-lg">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Occupancy Rate</p>
                    <p className="text-3xl font-bold text-gray-900">{averageOccupancy.toFixed(0)}%</p>
                    <p className="text-sm text-purple-600 flex items-center gap-1 mt-1">
                      <TrendingUp className="h-3 w-3" />
                      Above average
                    </p>
                  </div>
                  <div className="p-3 bg-purple-100 rounded-full">
                    <Users className="h-6 w-6 text-purple-600" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </HoverAnimation>
        </StaggeredAnimation>
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="border-0 shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bell className="h-5 w-5 text-sea-green-600" />
              Recent Activity
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[
                { type: 'booking', message: 'New booking for Luxury Beachfront Villa', time: '2 hours ago', icon: Calendar, color: 'text-green-600' },
                { type: 'review', message: 'New 5-star review received', time: '5 hours ago', icon: Star, color: 'text-yellow-600' },
                { type: 'inquiry', message: 'Guest inquiry for Modern City Apartment', time: '1 day ago', icon: MessageSquare, color: 'text-blue-600' },
                { type: 'payment', message: 'Payment received: R2,500', time: '2 days ago', icon: DollarSign, color: 'text-green-600' }
              ].map((activity, index) => {
                const Icon = activity.icon;
                return (
                  <div key={index} className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                    <Icon className={`h-5 w-5 ${activity.color}`} />
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900">{activity.message}</p>
                      <p className="text-xs text-gray-500">{activity.time}</p>
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5 text-sea-green-600" />
              Upcoming Bookings
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {bookings.filter(booking => booking.status === 'confirmed').map((booking) => (
                <div key={booking.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <p className="font-medium text-gray-900">{booking.guestName}</p>
                    <p className="text-sm text-gray-600">{booking.propertyTitle}</p>
                    <p className="text-xs text-gray-500">
                      {new Date(booking.checkIn).toLocaleDateString()} - {new Date(booking.checkOut).toLocaleDateString()}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-gray-900">R{booking.totalAmount.toLocaleString()}</p>
                    <Badge className={getStatusColor(booking.status)}>
                      {booking.status}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-sea-green-500 border-t-transparent rounded-full animate-spin mb-4"></div>
          <p className="text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <PageTransition>
      <div className={`space-y-6 ${className}`}>
        {/* Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Host Dashboard</h1>
            <p className="text-gray-600">Manage your properties and bookings</p>
          </div>
          
          <div className="flex items-center gap-3">
            <Button
              onClick={() => setShowCreateProperty(true)}
              className="bg-sea-green-500 hover:bg-sea-green-600"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Property
            </Button>
            
            <Button variant="outline">
              <Bell className="h-4 w-4 mr-2" />
              Notifications
              <Badge className="ml-2 bg-red-500 text-white">3</Badge>
            </Button>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`flex items-center gap-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === tab.id
                      ? 'border-sea-green-500 text-sea-green-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon className="h-4 w-4" />
                  {tab.label}
                </button>
              );
            })}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="mt-6">
          {activeTab === 'overview' && renderOverview()}
          {activeTab === 'properties' && (
            <div>Properties content here</div>
          )}
          {activeTab === 'bookings' && (
            <div>Bookings content here</div>
          )}
          {activeTab === 'analytics' && selectedProperty && (
            <PropertyAnalytics 
              propertyId={selectedProperty.id}
              propertyTitle={selectedProperty.title}
            />
          )}
          {activeTab === 'settings' && (
            <div>Settings content here</div>
          )}
        </div>

        {/* Property Creation Modal */}
        {showCreateProperty && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
              <PropertyCreationWizard
                onComplete={(propertyData) => {
                  console.log('Property created:', propertyData);
                  setShowCreateProperty(false);
                }}
                onCancel={() => setShowCreateProperty(false)}
              />
            </div>
          </div>
        )}
      </div>
    </PageTransition>
  );
};
