import React from 'react';
import { Helmet } from 'react-helmet-async';

interface SEOHeadProps {
  title?: string;
  description?: string;
  keywords?: string[];
  image?: string;
  url?: string;
  type?: 'website' | 'article' | 'product' | 'profile';
  siteName?: string;
  locale?: string;
  author?: string;
  publishedTime?: string;
  modifiedTime?: string;
  section?: string;
  tags?: string[];
  noindex?: boolean;
  nofollow?: boolean;
  canonical?: string;
  alternateLanguages?: Array<{ hreflang: string; href: string }>;
  structuredData?: object;
}

export const SEOHead: React.FC<SEOHeadProps> = ({
  title,
  description,
  keywords = [],
  image,
  url,
  type = 'website',
  siteName = 'StayFinder',
  locale = 'en_ZA',
  author,
  publishedTime,
  modifiedTime,
  section,
  tags = [],
  noindex = false,
  nofollow = false,
  canonical,
  alternateLanguages = [],
  structuredData
}) => {
  const defaultTitle = 'StayFinder - Find Your Perfect Stay in South Africa';
  const defaultDescription = 'Discover and book amazing accommodations across South Africa. From luxury villas in Cape Town to cozy apartments in Johannesburg, find your perfect stay with Stay<PERSON><PERSON>.';
  const defaultImage = '/images/stayfinder-og-image.jpg';
  const defaultUrl = 'https://stayfinder.co.za';

  const finalTitle = title ? `${title} | ${siteName}` : defaultTitle;
  const finalDescription = description || defaultDescription;
  const finalImage = image || defaultImage;
  const finalUrl = url || (typeof window !== 'undefined' ? window.location.href : defaultUrl);
  const finalCanonical = canonical || finalUrl;

  const allKeywords = [
    'accommodation',
    'booking',
    'hotels',
    'guesthouses',
    'vacation rentals',
    'South Africa',
    'Cape Town',
    'Johannesburg',
    'Durban',
    'travel',
    'tourism',
    ...keywords
  ];

  const robotsContent = [
    noindex ? 'noindex' : 'index',
    nofollow ? 'nofollow' : 'follow',
    'max-snippet:-1',
    'max-image-preview:large',
    'max-video-preview:-1'
  ].join(', ');

  return (
    <Helmet>
      {/* Basic Meta Tags */}
      <title>{finalTitle}</title>
      <meta name="description" content={finalDescription} />
      <meta name="keywords" content={allKeywords.join(', ')} />
      <meta name="author" content={author || siteName} />
      <meta name="robots" content={robotsContent} />
      <meta name="googlebot" content={robotsContent} />
      
      {/* Canonical URL */}
      <link rel="canonical" href={finalCanonical} />
      
      {/* Alternate Languages */}
      {alternateLanguages.map((alt) => (
        <link
          key={alt.hreflang}
          rel="alternate"
          hrefLang={alt.hreflang}
          href={alt.href}
        />
      ))}
      
      {/* Open Graph Meta Tags */}
      <meta property="og:type" content={type} />
      <meta property="og:title" content={finalTitle} />
      <meta property="og:description" content={finalDescription} />
      <meta property="og:image" content={finalImage} />
      <meta property="og:url" content={finalUrl} />
      <meta property="og:site_name" content={siteName} />
      <meta property="og:locale" content={locale} />
      
      {/* Article-specific Open Graph tags */}
      {type === 'article' && (
        <>
          {author && <meta property="article:author" content={author} />}
          {publishedTime && <meta property="article:published_time" content={publishedTime} />}
          {modifiedTime && <meta property="article:modified_time" content={modifiedTime} />}
          {section && <meta property="article:section" content={section} />}
          {tags.map((tag) => (
            <meta key={tag} property="article:tag" content={tag} />
          ))}
        </>
      )}
      
      {/* Twitter Card Meta Tags */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={finalTitle} />
      <meta name="twitter:description" content={finalDescription} />
      <meta name="twitter:image" content={finalImage} />
      <meta name="twitter:site" content="@StayFinderZA" />
      <meta name="twitter:creator" content="@StayFinderZA" />
      
      {/* Additional Meta Tags */}
      <meta name="theme-color" content="#059669" />
      <meta name="msapplication-TileColor" content="#059669" />
      <meta name="application-name" content={siteName} />
      <meta name="apple-mobile-web-app-title" content={siteName} />
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-status-bar-style" content="default" />
      <meta name="mobile-web-app-capable" content="yes" />
      <meta name="format-detection" content="telephone=no" />
      
      {/* Geo Meta Tags for South African focus */}
      <meta name="geo.region" content="ZA" />
      <meta name="geo.country" content="South Africa" />
      <meta name="geo.placename" content="South Africa" />
      <meta name="ICBM" content="-30.5595, 22.9375" />
      
      {/* Structured Data */}
      {structuredData && (
        <script type="application/ld+json">
          {JSON.stringify(structuredData)}
        </script>
      )}
    </Helmet>
  );
};

// Predefined SEO configurations for common pages
export const HomepageSEO: React.FC = () => (
  <SEOHead
    title="Find Your Perfect Stay in South Africa"
    description="Discover and book amazing accommodations across South Africa. From luxury villas in Cape Town to cozy apartments in Johannesburg, find your perfect stay with StayFinder."
    keywords={[
      'South Africa accommodation',
      'book hotels South Africa',
      'vacation rentals',
      'Cape Town hotels',
      'Johannesburg accommodation',
      'Durban guesthouses',
      'holiday rentals'
    ]}
    structuredData={{
      "@context": "https://schema.org",
      "@type": "WebSite",
      "name": "StayFinder",
      "url": "https://stayfinder.co.za",
      "description": "Find and book accommodations across South Africa",
      "potentialAction": {
        "@type": "SearchAction",
        "target": "https://stayfinder.co.za/search?q={search_term_string}",
        "query-input": "required name=search_term_string"
      },
      "sameAs": [
        "https://facebook.com/StayFinderZA",
        "https://twitter.com/StayFinderZA",
        "https://instagram.com/StayFinderZA"
      ]
    }}
  />
);

export const SearchPageSEO: React.FC<{ location?: string; propertyCount?: number }> = ({ 
  location, 
  propertyCount 
}) => (
  <SEOHead
    title={location ? `Accommodation in ${location}` : 'Search Accommodation'}
    description={
      location 
        ? `Find and book the best accommodation in ${location}, South Africa. ${propertyCount ? `${propertyCount} properties available.` : ''} Compare prices and book directly.`
        : 'Search and compare accommodation across South Africa. Find hotels, guesthouses, and vacation rentals at the best prices.'
    }
    keywords={[
      location ? `${location} accommodation` : 'accommodation search',
      location ? `${location} hotels` : 'hotel search',
      location ? `${location} guesthouses` : 'guesthouse search',
      'South Africa',
      'book accommodation',
      'compare prices'
    ]}
  />
);

export const PropertyPageSEO: React.FC<{
  property: {
    id: string;
    title: string;
    description: string;
    location: string;
    price: number;
    rating?: number;
    reviewCount?: number;
    images: string[];
    amenities: string[];
    propertyType: string;
    bedrooms: number;
    bathrooms: number;
    guests: number;
  };
}> = ({ property }) => (
  <SEOHead
    title={`${property.title} - ${property.location}`}
    description={`${property.description.substring(0, 150)}... Book this ${property.propertyType.toLowerCase()} in ${property.location} from R${property.price}/night. ${property.bedrooms} bedrooms, ${property.bathrooms} bathrooms, sleeps ${property.guests}.`}
    keywords={[
      property.location,
      property.propertyType,
      `${property.location} accommodation`,
      `${property.location} ${property.propertyType.toLowerCase()}`,
      ...property.amenities.slice(0, 5)
    ]}
    image={property.images[0]}
    type="product"
    structuredData={{
      "@context": "https://schema.org",
      "@type": "LodgingBusiness",
      "name": property.title,
      "description": property.description,
      "image": property.images,
      "address": {
        "@type": "PostalAddress",
        "addressLocality": property.location,
        "addressCountry": "ZA"
      },
      "priceRange": `R${property.price}`,
      "aggregateRating": property.rating && property.reviewCount ? {
        "@type": "AggregateRating",
        "ratingValue": property.rating,
        "reviewCount": property.reviewCount
      } : undefined,
      "amenityFeature": property.amenities.map(amenity => ({
        "@type": "LocationFeatureSpecification",
        "name": amenity
      })),
      "numberOfRooms": property.bedrooms,
      "occupancy": {
        "@type": "QuantitativeValue",
        "maxValue": property.guests
      }
    }}
  />
);

export const BookingPageSEO: React.FC = () => (
  <SEOHead
    title="Complete Your Booking"
    description="Secure booking process for your accommodation. Complete your reservation with our safe and easy booking system."
    noindex={true}
  />
);

export const ProfilePageSEO: React.FC = () => (
  <SEOHead
    title="My Profile"
    description="Manage your StayFinder account, view bookings, and update your preferences."
    noindex={true}
  />
);

export const WishlistPageSEO: React.FC = () => (
  <SEOHead
    title="My Wishlist"
    description="View and manage your saved properties. Keep track of accommodations you're interested in booking."
    noindex={true}
  />
);
