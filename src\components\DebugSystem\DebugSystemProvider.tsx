import React, { useState, useEffect, useCallback, createContext, useContext } from 'react';
import { DebugPanel } from './DebugPanel';
import { ErrorCapture } from './ErrorCapture';
import { useErrorCapture } from '@/hooks/useErrorCapture';
import { debugSystemManager, DebugSystemConfig } from '@/utils/debugSystem';

interface DebugSystemContextType {
  isEnabled: boolean;
  isVisible: boolean;
  togglePanel: () => void;
  config: DebugSystemConfig;
  updateConfig: (updates: Partial<DebugSystemConfig>) => void;
}

const DebugSystemContext = createContext<DebugSystemContextType | null>(null);

export const useDebugSystem = () => {
  const context = useContext(DebugSystemContext);
  if (!context) {
    throw new Error('useDebugSystem must be used within a DebugSystemProvider');
  }
  return context;
};

interface DebugSystemProviderProps {
  children: React.ReactNode;
  userRole?: string;
  autoStart?: boolean;
}

export const DebugSystemProvider: React.FC<DebugSystemProviderProps> = ({
  children,
  userRole,
  autoStart = true
}) => {
  const [config, setConfig] = useState<DebugSystemConfig>(debugSystemManager.getConfig());
  const [isPanelOpen, setIsPanelOpen] = useState(false);
  const { addError } = useErrorCapture();

  // Check if debug system should be visible
  const isVisible = debugSystemManager.isVisible(userRole);

  // Listen for configuration changes
  useEffect(() => {
    const handleConfigChange = (newConfig: DebugSystemConfig) => {
      setConfig(newConfig);
    };

    debugSystemManager.addListener(handleConfigChange);
    return () => debugSystemManager.removeListener(handleConfigChange);
  }, []);

  // Listen for keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!config.keyboardShortcuts || !isVisible) return;

      // Ctrl+Shift+D to toggle debug panel
      if (event.ctrlKey && event.shiftKey && event.key === 'D') {
        event.preventDefault();
        togglePanel();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [config.keyboardShortcuts, isVisible]);

  // Listen for custom debug events
  useEffect(() => {
    const handleReactError = (event: CustomEvent) => {
      const { error, errorInfo } = event.detail;
      addError(error, errorInfo);
    };

    const handleManualError = (event: CustomEvent) => {
      const { error } = event.detail;
      addError(error, { componentStack: '' });
    };

    window.addEventListener('debug-react-error', handleReactError as EventListener);
    window.addEventListener('debug-manual-error', handleManualError as EventListener);
    window.addEventListener('debug-manual-warning', handleManualError as EventListener);
    window.addEventListener('debug-manual-info', handleManualError as EventListener);

    return () => {
      window.removeEventListener('debug-react-error', handleReactError as EventListener);
      window.removeEventListener('debug-manual-error', handleManualError as EventListener);
      window.removeEventListener('debug-manual-warning', handleManualError as EventListener);
      window.removeEventListener('debug-manual-info', handleManualError as EventListener);
    };
  }, [addError]);

  const togglePanel = useCallback(() => {
    if (!isVisible) return;
    setIsPanelOpen(prev => !prev);
  }, [isVisible]);

  const updateConfig = useCallback((updates: Partial<DebugSystemConfig>) => {
    debugSystemManager.updateConfig(updates);
  }, []);

  const contextValue: DebugSystemContextType = {
    isEnabled: config.enabled,
    isVisible,
    togglePanel,
    config,
    updateConfig
  };

  return (
    <DebugSystemContext.Provider value={contextValue}>
      <ErrorCapture
        onError={(error, errorInfo) => {
          addError(error, errorInfo);
        }}
      >
        {children}
      </ErrorCapture>
      
      {isVisible && isPanelOpen && (
        <DebugPanel
          userRole={userRole}
          onClose={() => setIsPanelOpen(false)}
        />
      )}
    </DebugSystemContext.Provider>
  );
};

// Debug System Toggle Button Component
interface DebugToggleButtonProps {
  className?: string;
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
}

export const DebugToggleButton: React.FC<DebugToggleButtonProps> = ({
  className,
  position = 'bottom-right'
}) => {
  const { isVisible, togglePanel, config } = useDebugSystem();

  if (!isVisible || !config.enabled) {
    return null;
  }

  const positionClasses = {
    'top-left': 'top-4 left-4',
    'top-right': 'top-4 right-4',
    'bottom-left': 'bottom-4 left-4',
    'bottom-right': 'bottom-4 right-4'
  };

  return (
    <button
      onClick={togglePanel}
      className={`
        fixed z-[9998] p-3 bg-sea-green-600 hover:bg-sea-green-700 
        text-white rounded-full shadow-lg transition-all duration-200
        hover:scale-110 focus:outline-none focus:ring-2 focus:ring-sea-green-500 focus:ring-offset-2
        ${positionClasses[position]} ${className}
      `}
      title="Toggle Debug Panel (Ctrl+Shift+D)"
    >
      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
      </svg>
    </button>
  );
};

// Admin Debug Controls Component
export const AdminDebugControls: React.FC = () => {
  const { config, updateConfig, isVisible } = useDebugSystem();

  if (!isVisible) {
    return null;
  }

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">Debug System Controls</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <label className="text-sm font-medium">Enable Debug System</label>
            <input
              type="checkbox"
              checked={config.enabled}
              onChange={(e) => updateConfig({ enabled: e.target.checked })}
              className="rounded border-gray-300 text-sea-green-600 focus:ring-sea-green-500"
            />
          </div>

          <div className="flex items-center justify-between">
            <label className="text-sm font-medium">Admin Only</label>
            <input
              type="checkbox"
              checked={config.adminOnly}
              onChange={(e) => updateConfig({ adminOnly: e.target.checked })}
              className="rounded border-gray-300 text-sea-green-600 focus:ring-sea-green-500"
            />
          </div>

          <div className="flex items-center justify-between">
            <label className="text-sm font-medium">Show in Production</label>
            <input
              type="checkbox"
              checked={config.showInProduction}
              onChange={(e) => updateConfig({ showInProduction: e.target.checked })}
              className="rounded border-gray-300 text-sea-green-600 focus:ring-sea-green-500"
            />
          </div>
        </div>

        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <label className="text-sm font-medium">Auto Capture</label>
            <input
              type="checkbox"
              checked={config.autoCapture}
              onChange={(e) => updateConfig({ autoCapture: e.target.checked })}
              className="rounded border-gray-300 text-sea-green-600 focus:ring-sea-green-500"
            />
          </div>

          <div className="flex items-center justify-between">
            <label className="text-sm font-medium">Keyboard Shortcuts</label>
            <input
              type="checkbox"
              checked={config.keyboardShortcuts}
              onChange={(e) => updateConfig({ keyboardShortcuts: e.target.checked })}
              className="rounded border-gray-300 text-sea-green-600 focus:ring-sea-green-500"
            />
          </div>

          <div>
            <label className="text-sm font-medium block mb-1">Max Errors</label>
            <input
              type="number"
              min="10"
              max="1000"
              value={config.maxErrors}
              onChange={(e) => updateConfig({ maxErrors: parseInt(e.target.value) || 100 })}
              className="w-full px-3 py-1 border border-gray-300 rounded-md text-sm focus:ring-sea-green-500 focus:border-sea-green-500"
            />
          </div>
        </div>
      </div>

      <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-md">
        <p className="text-sm text-yellow-800">
          <strong>Note:</strong> Debug system changes take effect immediately. 
          Use keyboard shortcut <kbd className="px-1 py-0.5 bg-yellow-200 rounded text-xs">Ctrl+Shift+D</kbd> to toggle the debug panel.
        </p>
      </div>
    </div>
  );
};
