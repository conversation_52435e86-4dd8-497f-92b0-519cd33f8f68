# 🛠️ Manual Schema Deployment Guide

**StayFinder Database Schema Deployment**  
**Target:** Supabase PostgreSQL Database  
**Status:** Ready for Deployment

---

## 📋 Deployment Overview

Since Supabase has restrictions on executing DDL statements through the client API, the schema needs to be deployed manually through the Supabase SQL Editor. This guide provides step-by-step instructions.

---

## 🚀 Quick Deployment Steps

### 1. **Access Supabase Dashboard**
1. Go to [https://app.supabase.com](https://app.supabase.com)
2. Select your StayFinder project
3. Navigate to **SQL Editor** in the left sidebar

### 2. **Execute Schema Files in Order**
Execute the following SQL files in the exact order listed:

#### **Step 1: Create Schema** ⭐ **REQUIRED**
- **File:** `create-schema.sql`
- **Purpose:** Creates all tables, enums, and basic structure
- **Action:** Copy the entire contents of `create-schema.sql` and paste into SQL Editor
- **Click:** "Run" button
- **Expected:** All tables and enums created successfully

#### **Step 2: Create Indexes & Functions** ⭐ **REQUIRED**
- **File:** `create-indexes-functions.sql`
- **Purpose:** Adds performance indexes, functions, and triggers
- **Action:** Copy the entire contents and execute
- **Expected:** Indexes created, functions defined, triggers activated

#### **Step 3: Setup Row Level Security** ⭐ **REQUIRED**
- **File:** `create-rls-policies.sql`
- **Purpose:** Implements security policies for data protection
- **Action:** Copy the entire contents and execute
- **Expected:** RLS enabled on all tables with appropriate policies

#### **Step 4: Seed Initial Data** 🔧 **OPTIONAL**
- **File:** `seed-data.sql`
- **Purpose:** Adds amenities and sample data for development
- **Action:** Copy the entire contents and execute
- **Expected:** Amenities populated, sample users and properties created

---

## 📊 Schema Components

### **Tables Created (12 total)**
1. **users** - User profiles and authentication
2. **properties** - Property listings
3. **amenities** - Master amenities list
4. **property_amenities** - Property-amenity relationships
5. **property_images** - Property photos
6. **bookings** - Reservation management
7. **booking_payments** - Payment tracking
8. **reviews** - Property reviews and ratings
9. **messages** - Host-guest communication
10. **notifications** - System notifications
11. **user_preferences** - User settings
12. **property_availability** - Availability calendar

### **Enums Created (11 total)**
- `user_type_enum` - User roles
- `property_type_enum` - Property types
- `property_status_enum` - Property statuses
- `cancellation_policy_enum` - Cancellation policies
- `amenity_category_enum` - Amenity categories
- `booking_status_enum` - Booking statuses
- `payment_status_enum` - Payment statuses
- `payment_method_enum` - Payment methods
- `review_status_enum` - Review statuses
- `message_type_enum` - Message types
- `notification_type_enum` - Notification types

### **Indexes Created (40+ total)**
- Performance indexes for all major query patterns
- Full-text search indexes
- Geospatial indexes for location queries
- Composite indexes for complex queries

### **Functions & Triggers (8 total)**
- Automatic timestamp updates
- Search vector maintenance
- Booking reference generation
- Property statistics updates
- Booking overlap prevention

### **RLS Policies (25+ total)**
- User data protection
- Property access control
- Booking security
- Message privacy
- Admin privileges

---

## ✅ Verification Steps

After deployment, verify the schema using these SQL queries in the SQL Editor:

### **1. Check Tables**
```sql
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
ORDER BY table_name;
```
**Expected:** 12 tables listed

### **2. Check Enums**
```sql
SELECT typname 
FROM pg_type 
WHERE typtype = 'e' 
ORDER BY typname;
```
**Expected:** 11 enum types listed

### **3. Check Indexes**
```sql
SELECT indexname, tablename 
FROM pg_indexes 
WHERE schemaname = 'public' 
ORDER BY tablename, indexname;
```
**Expected:** 40+ indexes listed

### **4. Check RLS Status**
```sql
SELECT tablename, rowsecurity 
FROM pg_tables 
WHERE schemaname = 'public' 
ORDER BY tablename;
```
**Expected:** Most tables show `rowsecurity = true`

### **5. Check Amenities Data**
```sql
SELECT category, COUNT(*) as count 
FROM amenities 
GROUP BY category 
ORDER BY category;
```
**Expected:** 7 categories with amenities

### **6. Test Sample Data**
```sql
SELECT 
    u.first_name || ' ' || u.last_name as host_name,
    p.title,
    p.city,
    p.price_per_night
FROM properties p
JOIN users u ON p.host_id = u.id
WHERE p.status = 'active';
```
**Expected:** 3 sample properties listed

---

## 🔧 Troubleshooting

### **Common Issues & Solutions**

#### **Issue: "Permission denied" errors**
**Solution:** Ensure you're using the service role key and have admin privileges

#### **Issue: "Function already exists" errors**
**Solution:** This is normal if re-running scripts. Functions will be replaced.

#### **Issue: "Table already exists" errors**
**Solution:** Drop existing tables first or use `CREATE TABLE IF NOT EXISTS`

#### **Issue: RLS policies fail**
**Solution:** Ensure tables exist before creating policies

#### **Issue: Seed data fails**
**Solution:** Ensure schema is fully created before running seed data

### **Reset Schema (if needed)**
If you need to start over, run this in SQL Editor:
```sql
-- WARNING: This will delete all data!
DROP SCHEMA public CASCADE;
CREATE SCHEMA public;
GRANT ALL ON SCHEMA public TO postgres;
GRANT ALL ON SCHEMA public TO public;
```

---

## 📱 Real-time Configuration

After schema deployment, enable real-time for tables that need live updates:

### **Enable Real-time Tables**
```sql
-- Enable real-time for bookings
ALTER PUBLICATION supabase_realtime ADD TABLE bookings;

-- Enable real-time for messages
ALTER PUBLICATION supabase_realtime ADD TABLE messages;

-- Enable real-time for notifications
ALTER PUBLICATION supabase_realtime ADD TABLE notifications;

-- Enable real-time for property availability
ALTER PUBLICATION supabase_realtime ADD TABLE property_availability;
```

---

## 🔒 Security Configuration

### **Additional Security Steps**

1. **Review RLS Policies**
   - Check that all policies are appropriate for your use case
   - Test with different user roles

2. **Configure Auth Settings**
   - Set up email templates in Supabase Dashboard
   - Configure social login providers if needed

3. **Storage Bucket Policies**
   - Set up policies for property images
   - Configure user avatar policies

---

## 📊 Performance Optimization

### **Post-Deployment Optimizations**

1. **Monitor Query Performance**
   - Use Supabase Dashboard to monitor slow queries
   - Add additional indexes as needed

2. **Configure Connection Pooling**
   - Adjust connection limits based on usage
   - Monitor connection usage

3. **Set up Caching**
   - Configure appropriate cache TTL values
   - Use Supabase Edge Functions for complex queries

---

## 🎯 Next Steps After Deployment

1. **✅ Verify Schema** - Run all verification queries
2. **🔧 Configure Real-time** - Enable for required tables
3. **🔒 Test Security** - Verify RLS policies work correctly
4. **📱 Update Application** - Connect your app to the new schema
5. **🧪 Run Tests** - Execute comprehensive testing
6. **🚀 Deploy to Production** - When ready, deploy to production

---

## 📞 Support

If you encounter issues during deployment:

1. **Check Supabase Logs** - View logs in Dashboard
2. **Review Error Messages** - SQL Editor shows detailed errors
3. **Consult Documentation** - Refer to schema.md for details
4. **Test Incrementally** - Deploy one file at a time if needed

---

**🎉 Once deployed, your StayFinder database will be ready for production use!**
