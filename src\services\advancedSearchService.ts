interface SavedSearch {
  id: string;
  name: string;
  search_criteria: SearchCriteria;
  notification_enabled: boolean;
  created_at: string;
  updated_at: string;
}

interface SearchHistory {
  id: string;
  search_criteria: SearchCriteria;
  results_count: number;
  search_timestamp: string;
}

interface SearchSuggestion {
  term: string;
  category: 'location' | 'property_type' | 'amenity' | 'general';
  search_count: number;
}

interface SearchCriteria {
  location?: string;
  minPrice?: number;
  maxPrice?: number;
  guests?: number;
  propertyType?: string;
  bedrooms?: number;
  bathrooms?: number;
  amenities?: string[];
  rating?: number;
  instantBook?: boolean;
  checkIn?: string;
  checkOut?: string;
  radius?: number;
}

interface CreateSavedSearchData {
  name: string;
  search_criteria: SearchCriteria;
  notification_enabled?: boolean;
}

interface UpdateSavedSearchData {
  name?: string;
  search_criteria?: SearchCriteria;
  notification_enabled?: boolean;
}

interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

class AdvancedSearchService {
  private baseUrl = 'http://localhost/stayfinder/api/advanced-search.php';

  private async makeRequest<T>(url: string, options: RequestInit = {}): Promise<ApiResponse<T>> {
    try {
      const token = localStorage.getItem('auth_token');
      const headers: HeadersInit = {
        'Content-Type': 'application/json',
        ...options.headers,
      };

      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const response = await fetch(url, {
        ...options,
        headers,
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Advanced Search API error:', error);
      throw error;
    }
  }

  // Saved Searches
  async getSavedSearches(options: {
    limit?: number;
    offset?: number;
  } = {}): Promise<SavedSearch[]> {
    try {
      const params = new URLSearchParams({
        action: 'saved_searches',
        ...(options.limit && { limit: options.limit.toString() }),
        ...(options.offset && { offset: options.offset.toString() }),
      });

      const response = await this.makeRequest<SavedSearch[]>(`${this.baseUrl}?${params}`);
      
      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to fetch saved searches');
      }
    } catch (error) {
      console.error('Error fetching saved searches:', error);
      // Return mock data for testing
      return this.getMockSavedSearches();
    }
  }

  async createSavedSearch(searchData: CreateSavedSearchData): Promise<string> {
    try {
      const params = new URLSearchParams({ action: 'save_search' });
      
      const response = await this.makeRequest<{ search_id: string }>(`${this.baseUrl}?${params}`, {
        method: 'POST',
        body: JSON.stringify(searchData),
      });
      
      if (response.success && response.data) {
        return response.data.search_id;
      } else {
        throw new Error(response.error || 'Failed to save search');
      }
    } catch (error) {
      console.error('Error saving search:', error);
      throw error;
    }
  }

  async updateSavedSearch(searchId: string, updateData: UpdateSavedSearchData): Promise<boolean> {
    try {
      const params = new URLSearchParams({ search_id: searchId });
      
      const response = await this.makeRequest<void>(`${this.baseUrl}?${params}`, {
        method: 'PUT',
        body: JSON.stringify(updateData),
      });
      
      return response.success;
    } catch (error) {
      console.error('Error updating saved search:', error);
      throw error;
    }
  }

  async deleteSavedSearch(searchId: string): Promise<boolean> {
    try {
      const params = new URLSearchParams({ search_id: searchId });
      
      const response = await this.makeRequest<void>(`${this.baseUrl}?${params}`, {
        method: 'DELETE',
      });
      
      return response.success;
    } catch (error) {
      console.error('Error deleting saved search:', error);
      throw error;
    }
  }

  // Search History
  async getSearchHistory(limit: number = 10): Promise<SearchHistory[]> {
    try {
      const params = new URLSearchParams({
        action: 'search_history',
        limit: limit.toString(),
      });

      const response = await this.makeRequest<SearchHistory[]>(`${this.baseUrl}?${params}`);
      
      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to fetch search history');
      }
    } catch (error) {
      console.error('Error fetching search history:', error);
      // Return mock data for testing
      return this.getMockSearchHistory();
    }
  }

  async addToSearchHistory(searchCriteria: SearchCriteria, resultsCount: number): Promise<string | null> {
    try {
      const params = new URLSearchParams({ action: 'add_to_history' });
      
      const response = await this.makeRequest<{ history_id: string }>(`${this.baseUrl}?${params}`, {
        method: 'POST',
        body: JSON.stringify({
          search_criteria: searchCriteria,
          results_count: resultsCount,
        }),
      });
      
      if (response.success && response.data) {
        return response.data.history_id;
      }
      
      return null;
    } catch (error) {
      console.error('Error adding to search history:', error);
      return null;
    }
  }

  // Search Suggestions
  async getSearchSuggestions(
    query: string, 
    category?: 'location' | 'property_type' | 'amenity' | 'general',
    limit: number = 10
  ): Promise<SearchSuggestion[]> {
    try {
      if (!query.trim()) {
        return [];
      }

      const params = new URLSearchParams({
        action: 'suggestions',
        query: query.trim(),
        limit: limit.toString(),
        ...(category && { category }),
      });

      const response = await this.makeRequest<SearchSuggestion[]>(`${this.baseUrl}?${params}`);
      
      if (response.success && response.data) {
        return response.data;
      } else {
        return [];
      }
    } catch (error) {
      console.error('Error fetching search suggestions:', error);
      // Return mock suggestions for testing
      return this.getMockSuggestions(query, category);
    }
  }

  async updateSearchSuggestion(term: string, category: string = 'general'): Promise<boolean> {
    try {
      const params = new URLSearchParams({ action: 'update_suggestion' });
      
      const response = await this.makeRequest<void>(`${this.baseUrl}?${params}`, {
        method: 'POST',
        body: JSON.stringify({
          term: term.trim(),
          category,
        }),
      });
      
      return response.success;
    } catch (error) {
      console.error('Error updating search suggestion:', error);
      return false;
    }
  }

  // Helper methods
  formatSearchCriteria(criteria: SearchCriteria): string {
    const parts: string[] = [];
    
    if (criteria.location) parts.push(criteria.location);
    if (criteria.propertyType) parts.push(criteria.propertyType);
    if (criteria.guests) parts.push(`${criteria.guests} guests`);
    if (criteria.minPrice || criteria.maxPrice) {
      const priceRange = `R${criteria.minPrice || 0} - R${criteria.maxPrice || '∞'}`;
      parts.push(priceRange);
    }
    if (criteria.bedrooms) parts.push(`${criteria.bedrooms} bed${criteria.bedrooms > 1 ? 's' : ''}`);
    if (criteria.amenities && criteria.amenities.length > 0) {
      parts.push(`${criteria.amenities.length} amenities`);
    }
    
    return parts.join(', ') || 'All properties';
  }

  getSearchCriteriaHash(criteria: SearchCriteria): string {
    return btoa(JSON.stringify(criteria)).replace(/[^a-zA-Z0-9]/g, '').substring(0, 16);
  }

  isSearchCriteriaEmpty(criteria: SearchCriteria): boolean {
    return Object.keys(criteria).length === 0 || 
           Object.values(criteria).every(value => 
             value === undefined || 
             value === '' || 
             value === false ||
             (Array.isArray(value) && value.length === 0)
           );
  }

  // Mock data for testing
  getMockSavedSearches(): SavedSearch[] {
    return [
      {
        id: 'search-1',
        name: 'Beach Houses in Margate',
        search_criteria: {
          location: 'Margate',
          propertyType: 'house',
          amenities: ['pool', 'beach_access'],
          maxPrice: 2000
        },
        notification_enabled: true,
        created_at: '2024-06-20T10:00:00Z',
        updated_at: '2024-06-20T10:00:00Z'
      },
      {
        id: 'search-2',
        name: 'Family Villas with Pool',
        search_criteria: {
          guests: 6,
          propertyType: 'villa',
          amenities: ['pool', 'wifi', 'parking'],
          bedrooms: 3
        },
        notification_enabled: false,
        created_at: '2024-06-19T15:30:00Z',
        updated_at: '2024-06-19T15:30:00Z'
      }
    ];
  }

  getMockSearchHistory(): SearchHistory[] {
    return [
      {
        id: 'history-1',
        search_criteria: {
          location: 'Durban',
          guests: 2,
          checkIn: '2024-07-01',
          checkOut: '2024-07-05'
        },
        results_count: 15,
        search_timestamp: '2024-06-21T14:30:00Z'
      },
      {
        id: 'history-2',
        search_criteria: {
          location: 'Port Shepstone',
          propertyType: 'cottage',
          amenities: ['wifi']
        },
        results_count: 8,
        search_timestamp: '2024-06-21T12:15:00Z'
      }
    ];
  }

  getMockSuggestions(query: string, category?: string): SearchSuggestion[] {
    const allSuggestions = [
      { term: 'Margate', category: 'location' as const, search_count: 45 },
      { term: 'Durban', category: 'location' as const, search_count: 38 },
      { term: 'Port Shepstone', category: 'location' as const, search_count: 22 },
      { term: 'villa', category: 'property_type' as const, search_count: 32 },
      { term: 'cottage', category: 'property_type' as const, search_count: 28 },
      { term: 'pool', category: 'amenity' as const, search_count: 42 },
      { term: 'beach access', category: 'amenity' as const, search_count: 35 },
    ];

    return allSuggestions
      .filter(s => 
        s.term.toLowerCase().includes(query.toLowerCase()) &&
        (!category || s.category === category)
      )
      .slice(0, 10);
  }
}

export const advancedSearchService = new AdvancedSearchService();
export type { 
  SavedSearch, 
  SearchHistory, 
  SearchSuggestion, 
  SearchCriteria, 
  CreateSavedSearchData, 
  UpdateSavedSearchData,
  ApiResponse 
};
