import React, { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { AlertCircle, CheckCircle, Eye, EyeOff } from 'lucide-react';
import { SlideIn } from './page-transitions';

interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  email?: boolean;
  phone?: boolean;
  custom?: (value: string) => string | null;
}

interface EnhancedInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  success?: string;
  hint?: string;
  validation?: ValidationRule;
  showValidation?: boolean;
  icon?: React.ReactNode;
  onValidationChange?: (isValid: boolean, error?: string) => void;
}

export const EnhancedInput: React.FC<EnhancedInputProps> = ({
  label,
  error: externalError,
  success,
  hint,
  validation,
  showValidation = true,
  icon,
  onValidationChange,
  className,
  type = 'text',
  ...props
}) => {
  const [value, setValue] = useState(props.value || '');
  const [internalError, setInternalError] = useState<string>('');
  const [touched, setTouched] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const error = externalError || internalError;
  const isValid = !error && touched && value;

  const validateValue = (val: string): string => {
    if (!validation) return '';

    if (validation.required && !val.trim()) {
      return `${label || 'This field'} is required`;
    }

    if (val && validation.minLength && val.length < validation.minLength) {
      return `${label || 'This field'} must be at least ${validation.minLength} characters`;
    }

    if (val && validation.maxLength && val.length > validation.maxLength) {
      return `${label || 'This field'} must be no more than ${validation.maxLength} characters`;
    }

    if (val && validation.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val)) {
      return 'Please enter a valid email address';
    }

    if (val && validation.phone && !/^[\+]?[1-9][\d]{0,15}$/.test(val.replace(/\s/g, ''))) {
      return 'Please enter a valid phone number';
    }

    if (val && validation.pattern && !validation.pattern.test(val)) {
      return `${label || 'This field'} format is invalid`;
    }

    if (val && validation.custom) {
      const customError = validation.custom(val);
      if (customError) return customError;
    }

    return '';
  };

  useEffect(() => {
    if (touched) {
      const validationError = validateValue(value as string);
      setInternalError(validationError);
      onValidationChange?.(validationError === '', validationError || undefined);
    }
  }, [value, touched, validation, onValidationChange]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setValue(newValue);
    props.onChange?.(e);
  };

  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    setTouched(true);
    props.onBlur?.(e);
  };

  const inputType = type === 'password' && showPassword ? 'text' : type;

  return (
    <div className="space-y-2">
      {label && (
        <label className="block text-sm font-medium text-gray-700">
          {label}
          {validation?.required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      
      <div className="relative">
        {icon && (
          <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
            {icon}
          </div>
        )}
        
        <input
          {...props}
          type={inputType}
          value={value}
          onChange={handleChange}
          onBlur={handleBlur}
          className={cn(
            'w-full px-3 py-2 border rounded-lg transition-all duration-200',
            'focus:outline-none focus:ring-2 focus:ring-sea-green-500/20 focus:border-sea-green-500',
            'placeholder:text-gray-400',
            icon && 'pl-10',
            (type === 'password' || showValidation) && 'pr-10',
            error && 'border-red-300 bg-red-50',
            isValid && 'border-green-300 bg-green-50',
            !error && !isValid && 'border-gray-300 hover:border-gray-400',
            className
          )}
        />
        
        {/* Password toggle */}
        {type === 'password' && (
          <button
            type="button"
            onClick={() => setShowPassword(!showPassword)}
            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
          >
            {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
          </button>
        )}
        
        {/* Validation icon */}
        {showValidation && touched && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            {error ? (
              <AlertCircle className="h-4 w-4 text-red-500" />
            ) : isValid ? (
              <CheckCircle className="h-4 w-4 text-green-500" />
            ) : null}
          </div>
        )}
      </div>
      
      {/* Messages */}
      {(error || success || hint) && (
        <SlideIn direction="up" delay={0}>
          <div className="space-y-1">
            {error && (
              <p className="text-sm text-red-600 flex items-center">
                <AlertCircle className="h-3 w-3 mr-1" />
                {error}
              </p>
            )}
            {success && !error && (
              <p className="text-sm text-green-600 flex items-center">
                <CheckCircle className="h-3 w-3 mr-1" />
                {success}
              </p>
            )}
            {hint && !error && !success && (
              <p className="text-sm text-gray-500">{hint}</p>
            )}
          </div>
        </SlideIn>
      )}
    </div>
  );
};

// Enhanced Textarea
interface EnhancedTextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  label?: string;
  error?: string;
  success?: string;
  hint?: string;
  validation?: ValidationRule;
  showValidation?: boolean;
  showCharCount?: boolean;
  onValidationChange?: (isValid: boolean, error?: string) => void;
}

export const EnhancedTextarea: React.FC<EnhancedTextareaProps> = ({
  label,
  error: externalError,
  success,
  hint,
  validation,
  showValidation = true,
  showCharCount = false,
  onValidationChange,
  className,
  maxLength,
  ...props
}) => {
  const [value, setValue] = useState(props.value || '');
  const [internalError, setInternalError] = useState<string>('');
  const [touched, setTouched] = useState(false);

  const error = externalError || internalError;
  const isValid = !error && touched && value;
  const charCount = (value as string).length;

  const validateValue = (val: string): string => {
    if (!validation) return '';

    if (validation.required && !val.trim()) {
      return `${label || 'This field'} is required`;
    }

    if (val && validation.minLength && val.length < validation.minLength) {
      return `${label || 'This field'} must be at least ${validation.minLength} characters`;
    }

    if (val && validation.maxLength && val.length > validation.maxLength) {
      return `${label || 'This field'} must be no more than ${validation.maxLength} characters`;
    }

    if (val && validation.custom) {
      const customError = validation.custom(val);
      if (customError) return customError;
    }

    return '';
  };

  useEffect(() => {
    if (touched) {
      const validationError = validateValue(value as string);
      setInternalError(validationError);
      onValidationChange?.(validationError === '', validationError || undefined);
    }
  }, [value, touched, validation, onValidationChange]);

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    setValue(newValue);
    props.onChange?.(e);
  };

  const handleBlur = (e: React.FocusEvent<HTMLTextAreaElement>) => {
    setTouched(true);
    props.onBlur?.(e);
  };

  return (
    <div className="space-y-2">
      {label && (
        <label className="block text-sm font-medium text-gray-700">
          {label}
          {validation?.required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      
      <div className="relative">
        <textarea
          {...props}
          value={value}
          onChange={handleChange}
          onBlur={handleBlur}
          maxLength={maxLength}
          className={cn(
            'w-full px-3 py-2 border rounded-lg transition-all duration-200 resize-none',
            'focus:outline-none focus:ring-2 focus:ring-sea-green-500/20 focus:border-sea-green-500',
            'placeholder:text-gray-400',
            error && 'border-red-300 bg-red-50',
            isValid && 'border-green-300 bg-green-50',
            !error && !isValid && 'border-gray-300 hover:border-gray-400',
            className
          )}
        />
        
        {/* Validation icon */}
        {showValidation && touched && (
          <div className="absolute right-3 top-3">
            {error ? (
              <AlertCircle className="h-4 w-4 text-red-500" />
            ) : isValid ? (
              <CheckCircle className="h-4 w-4 text-green-500" />
            ) : null}
          </div>
        )}
      </div>
      
      {/* Character count */}
      {showCharCount && maxLength && (
        <div className="flex justify-end">
          <span className={cn(
            'text-xs',
            charCount > maxLength * 0.9 ? 'text-red-500' : 'text-gray-500'
          )}>
            {charCount}/{maxLength}
          </span>
        </div>
      )}
      
      {/* Messages */}
      {(error || success || hint) && (
        <SlideIn direction="up" delay={0}>
          <div className="space-y-1">
            {error && (
              <p className="text-sm text-red-600 flex items-center">
                <AlertCircle className="h-3 w-3 mr-1" />
                {error}
              </p>
            )}
            {success && !error && (
              <p className="text-sm text-green-600 flex items-center">
                <CheckCircle className="h-3 w-3 mr-1" />
                {success}
              </p>
            )}
            {hint && !error && !success && (
              <p className="text-sm text-gray-500">{hint}</p>
            )}
          </div>
        </SlideIn>
      )}
    </div>
  );
};
