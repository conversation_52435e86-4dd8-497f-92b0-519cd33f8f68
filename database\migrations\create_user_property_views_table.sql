-- Create user_property_views table for tracking property views
CREATE TABLE IF NOT EXISTS user_property_views (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    property_id INT NOT NULL,
    viewed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    <PERSON>OR<PERSON><PERSON><PERSON> KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (property_id) REFERENCES properties(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_property (user_id, property_id),
    INDEX idx_user_views (user_id, viewed_at),
    INDEX idx_property_views (property_id, viewed_at),
    INDEX idx_viewed_at (viewed_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert some sample data for testing
INSERT IGNORE INTO user_property_views (user_id, property_id, viewed_at) VALUES
(1, 1, <PERSON>ATE_SUB(NOW(), INTERVAL 1 DAY)),
(1, 2, DATE_SUB(NOW(), INTERVAL 2 DAY)),
(1, 3, DATE_SUB(NOW(), INTERVAL 3 DAY)),
(2, 1, DATE_SUB(NOW(), INTERVAL 1 HOUR)),
(2, 4, DATE_SUB(NOW(), INTERVAL 2 HOUR)),
(2, 5, DATE_SUB(NOW(), INTERVAL 3 HOUR)),
(3, 2, DATE_SUB(NOW(), INTERVAL 30 MINUTE)),
(3, 3, DATE_SUB(NOW(), INTERVAL 1 HOUR)),
(3, 1, DATE_SUB(NOW(), INTERVAL 2 HOUR));
