import React, { useState, useEffect } from 'react';
import { Search, Filter, X, MapPin, Calendar, Users, DollarSign, Home, Wifi, Car, Waves, ChevronDown, SlidersHorizontal } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { cn } from '@/lib/utils';

interface SearchFilters {
  location: string;
  checkIn: string;
  checkOut: string;
  guests: number;
  priceRange: [number, number];
  propertyType: string;
  amenities: string[];
  rating: number;
  instantBook: boolean;
}

interface MobileSearchFiltersProps {
  filters: SearchFilters;
  onFiltersChange: (filters: SearchFilters) => void;
  onSearch: () => void;
  isSticky?: boolean;
  className?: string;
}

export const MobileSearchFilters: React.FC<MobileSearchFiltersProps> = ({
  filters,
  onFiltersChange,
  onSearch,
  isSticky = true,
  className
}) => {
  const [isFilterSheetOpen, setIsFilterSheetOpen] = useState(false);
  const [isSearchExpanded, setIsSearchExpanded] = useState(false);
  const [activeFiltersCount, setActiveFiltersCount] = useState(0);

  const propertyTypes = [
    { value: 'all', label: 'All Types' },
    { value: 'apartment', label: 'Apartment' },
    { value: 'house', label: 'House' },
    { value: 'villa', label: 'Villa' },
    { value: 'guesthouse', label: 'Guest House' },
    { value: 'bnb', label: 'B&B' }
  ];

  const amenitiesList = [
    { id: 'wifi', label: 'WiFi', icon: Wifi },
    { id: 'parking', label: 'Parking', icon: Car },
    { id: 'pool', label: 'Pool', icon: Waves },
    { id: 'kitchen', label: 'Kitchen', icon: Home },
    { id: 'aircon', label: 'Air Conditioning', icon: Home },
    { id: 'tv', label: 'TV', icon: Home },
    { id: 'washer', label: 'Washing Machine', icon: Home },
    { id: 'balcony', label: 'Balcony', icon: Home }
  ];

  // Calculate active filters count
  useEffect(() => {
    let count = 0;
    if (filters.location) count++;
    if (filters.checkIn) count++;
    if (filters.checkOut) count++;
    if (filters.guests > 1) count++;
    if (filters.priceRange[0] > 0 || filters.priceRange[1] < 5000) count++;
    if (filters.propertyType && filters.propertyType !== 'all') count++;
    if (filters.amenities.length > 0) count++;
    if (filters.rating > 0) count++;
    if (filters.instantBook) count++;
    
    setActiveFiltersCount(count);
  }, [filters]);

  const updateFilters = (updates: Partial<SearchFilters>) => {
    onFiltersChange({ ...filters, ...updates });
  };

  const toggleAmenity = (amenityId: string) => {
    const newAmenities = filters.amenities.includes(amenityId)
      ? filters.amenities.filter(id => id !== amenityId)
      : [...filters.amenities, amenityId];
    
    updateFilters({ amenities: newAmenities });
  };

  const clearAllFilters = () => {
    onFiltersChange({
      location: '',
      checkIn: '',
      checkOut: '',
      guests: 1,
      priceRange: [0, 5000],
      propertyType: 'all',
      amenities: [],
      rating: 0,
      instantBook: false
    });
  };

  const handleSearch = () => {
    setIsSearchExpanded(false);
    setIsFilterSheetOpen(false);
    onSearch();
  };

  return (
    <>
      {/* Sticky Search Bar */}
      <div 
        className={cn(
          "bg-white border-b shadow-sm transition-all duration-300",
          isSticky && "sticky top-0 z-40",
          className
        )}
      >
        <div className="px-4 py-3">
          {/* Compact Search Bar */}
          {!isSearchExpanded && (
            <div className="flex items-center gap-2">
              <div 
                className="flex-1 flex items-center gap-2 p-3 bg-gray-50 rounded-lg cursor-pointer"
                onClick={() => setIsSearchExpanded(true)}
              >
                <Search className="h-4 w-4 text-gray-500" />
                <div className="flex-1 text-left">
                  {filters.location ? (
                    <span className="text-sm font-medium">{filters.location}</span>
                  ) : (
                    <span className="text-sm text-gray-500">Where to?</span>
                  )}
                  {(filters.checkIn || filters.guests > 1) && (
                    <div className="text-xs text-gray-400">
                      {filters.checkIn && `${filters.checkIn}`}
                      {filters.checkIn && filters.guests > 1 && ' • '}
                      {filters.guests > 1 && `${filters.guests} guests`}
                    </div>
                  )}
                </div>
              </div>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsFilterSheetOpen(true)}
                className="relative min-h-[48px] px-3"
              >
                <SlidersHorizontal className="h-4 w-4" />
                {activeFiltersCount > 0 && (
                  <Badge 
                    className="absolute -top-2 -right-2 h-5 w-5 p-0 text-xs bg-sea-green-600"
                  >
                    {activeFiltersCount}
                  </Badge>
                )}
              </Button>
            </div>
          )}

          {/* Expanded Search Form */}
          {isSearchExpanded && (
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <h3 className="font-semibold">Search Properties</h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsSearchExpanded(false)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>

              {/* Location */}
              <div>
                <label className="text-sm font-medium text-gray-700 mb-1 block">
                  Location
                </label>
                <div className="relative">
                  <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="City, neighborhood, or address"
                    value={filters.location}
                    onChange={(e) => updateFilters({ location: e.target.value })}
                    className="pl-10 min-h-[48px]"
                  />
                </div>
              </div>

              {/* Dates */}
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label className="text-sm font-medium text-gray-700 mb-1 block">
                    Check-in
                  </label>
                  <Input
                    type="date"
                    value={filters.checkIn}
                    onChange={(e) => updateFilters({ checkIn: e.target.value })}
                    className="min-h-[48px]"
                  />
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700 mb-1 block">
                    Check-out
                  </label>
                  <Input
                    type="date"
                    value={filters.checkOut}
                    onChange={(e) => updateFilters({ checkOut: e.target.value })}
                    className="min-h-[48px]"
                  />
                </div>
              </div>

              {/* Guests */}
              <div>
                <label className="text-sm font-medium text-gray-700 mb-1 block">
                  Guests
                </label>
                <Select 
                  value={filters.guests.toString()} 
                  onValueChange={(value) => updateFilters({ guests: parseInt(value) })}
                >
                  <SelectTrigger className="min-h-[48px]">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {[1, 2, 3, 4, 5, 6, 7, 8].map(num => (
                      <SelectItem key={num} value={num.toString()}>
                        {num} {num === 1 ? 'guest' : 'guests'}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <Button 
                onClick={handleSearch}
                className="w-full min-h-[48px] bg-sea-green-600 hover:bg-sea-green-700"
              >
                <Search className="h-4 w-4 mr-2" />
                Search Properties
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* Filter Bottom Sheet */}
      {isFilterSheetOpen && (
        <div className="fixed inset-0 z-50 bg-black/50" onClick={() => setIsFilterSheetOpen(false)}>
          <div 
            className="absolute bottom-0 left-0 right-0 bg-white rounded-t-xl max-h-[90vh] overflow-y-auto"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header */}
            <div className="sticky top-0 bg-white border-b px-4 py-4 flex items-center justify-between">
              <h2 className="text-lg font-semibold">Filters</h2>
              <div className="flex items-center gap-2">
                {activeFiltersCount > 0 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={clearAllFilters}
                    className="text-sea-green-600"
                  >
                    Clear all
                  </Button>
                )}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsFilterSheetOpen(false)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>

            <div className="p-4 space-y-6">
              {/* Price Range */}
              <div>
                <h3 className="font-medium mb-3">Price Range (per night)</h3>
                <div className="px-2">
                  <Slider
                    value={filters.priceRange}
                    onValueChange={(value) => updateFilters({ priceRange: value as [number, number] })}
                    max={5000}
                    min={0}
                    step={50}
                    className="mb-3"
                  />
                  <div className="flex justify-between text-sm text-gray-600">
                    <span>R{filters.priceRange[0]}</span>
                    <span>R{filters.priceRange[1]}</span>
                  </div>
                </div>
              </div>

              {/* Property Type */}
              <div>
                <h3 className="font-medium mb-3">Property Type</h3>
                <Select 
                  value={filters.propertyType} 
                  onValueChange={(value) => updateFilters({ propertyType: value })}
                >
                  <SelectTrigger className="min-h-[48px]">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {propertyTypes.map(type => (
                      <SelectItem key={type.value} value={type.value}>
                        {type.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Amenities */}
              <div>
                <h3 className="font-medium mb-3">Amenities</h3>
                <div className="grid grid-cols-2 gap-3">
                  {amenitiesList.map(amenity => {
                    const Icon = amenity.icon;
                    const isSelected = filters.amenities.includes(amenity.id);
                    
                    return (
                      <button
                        key={amenity.id}
                        onClick={() => toggleAmenity(amenity.id)}
                        className={cn(
                          "flex items-center gap-2 p-3 rounded-lg border transition-all min-h-[48px]",
                          isSelected 
                            ? "border-sea-green-600 bg-sea-green-50 text-sea-green-700" 
                            : "border-gray-200 hover:border-gray-300"
                        )}
                      >
                        <Icon className="h-4 w-4" />
                        <span className="text-sm font-medium">{amenity.label}</span>
                      </button>
                    );
                  })}
                </div>
              </div>

              {/* Rating */}
              <div>
                <h3 className="font-medium mb-3">Minimum Rating</h3>
                <div className="grid grid-cols-5 gap-2">
                  {[0, 1, 2, 3, 4].map(rating => (
                    <button
                      key={rating}
                      onClick={() => updateFilters({ rating })}
                      className={cn(
                        "p-2 rounded-lg border text-center min-h-[48px] transition-all",
                        filters.rating === rating
                          ? "border-sea-green-600 bg-sea-green-50 text-sea-green-700"
                          : "border-gray-200 hover:border-gray-300"
                      )}
                    >
                      <div className="text-sm font-medium">
                        {rating === 0 ? 'Any' : `${rating}+`}
                      </div>
                    </button>
                  ))}
                </div>
              </div>

              {/* Instant Book */}
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium">Instant Book</h3>
                  <p className="text-sm text-gray-600">Book without waiting for host approval</p>
                </div>
                <Switch
                  checked={filters.instantBook}
                  onCheckedChange={(checked) => updateFilters({ instantBook: checked })}
                />
              </div>
            </div>

            {/* Footer */}
            <div className="sticky bottom-0 bg-white border-t p-4">
              <div className="flex gap-3">
                <Button
                  variant="outline"
                  onClick={clearAllFilters}
                  className="flex-1 min-h-[48px]"
                >
                  Clear All
                </Button>
                <Button
                  onClick={handleSearch}
                  className="flex-1 min-h-[48px] bg-sea-green-600 hover:bg-sea-green-700"
                >
                  Show Results
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};
