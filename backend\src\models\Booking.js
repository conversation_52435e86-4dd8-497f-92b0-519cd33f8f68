const { executeQuery, findOne, findMany } = require('../utils/database');

class Booking {
  static async create(bookingData) {
    const query = `
      INSERT INTO bookings (
        id, property_id, guest_id, check_in_date, check_out_date,
        total_amount, booking_status, payment_status, guest_count, special_requests
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    const params = [
      bookingData.id,
      bookingData.property_id,
      bookingData.guest_id,
      bookingData.check_in_date,
      bookingData.check_out_date,
      bookingData.total_amount,
      bookingData.booking_status || 'pending',
      bookingData.payment_status || 'pending',
      bookingData.guest_count,
      bookingData.special_requests || null
    ];
    
    return await executeQuery(query, params);
  }

  static async findById(id) {
    const query = `
      SELECT 
        b.*,
        p.title as property_title,
        p.description as property_description,
        p.location as property_location,
        p.price_per_night,
        p.cleaning_fee,
        p.owner_id,
        guest.first_name as guest_first_name,
        guest.last_name as guest_last_name,
        guest.email as guest_email,
        guest.phone as guest_phone,
        owner.first_name as owner_first_name,
        owner.last_name as owner_last_name,
        owner.email as owner_email,
        owner.phone as owner_phone
      FROM bookings b
      LEFT JOIN properties p ON b.property_id = p.id
      LEFT JOIN users guest ON b.guest_id = guest.id
      LEFT JOIN users owner ON p.owner_id = owner.id
      WHERE b.id = ?
    `;
    
    return await findOne(query, [id]);
  }

  static async findByUserId(userId, userRole = 'guest') {
    let query;
    let queryParams;

    if (userRole === 'admin') {
      // Admin can see all bookings
      query = `
        SELECT 
          b.*,
          p.title as property_title,
          p.location as property_location,
          p.owner_id,
          guest.first_name as guest_first_name,
          guest.last_name as guest_last_name,
          owner.first_name as owner_first_name,
          owner.last_name as owner_last_name
        FROM bookings b
        LEFT JOIN properties p ON b.property_id = p.id
        LEFT JOIN users guest ON b.guest_id = guest.id
        LEFT JOIN users owner ON p.owner_id = owner.id
        ORDER BY b.created_at DESC
      `;
      queryParams = [];
    } else {
      // Regular users see their bookings (as guest or host)
      query = `
        SELECT 
          b.*,
          p.title as property_title,
          p.location as property_location,
          p.owner_id,
          guest.first_name as guest_first_name,
          guest.last_name as guest_last_name,
          owner.first_name as owner_first_name,
          owner.last_name as owner_last_name
        FROM bookings b
        LEFT JOIN properties p ON b.property_id = p.id
        LEFT JOIN users guest ON b.guest_id = guest.id
        LEFT JOIN users owner ON p.owner_id = owner.id
        WHERE b.guest_id = ? OR p.owner_id = ?
        ORDER BY b.created_at DESC
      `;
      queryParams = [userId, userId];
    }

    return await findMany(query, queryParams);
  }

  static async updateStatus(id, status) {
    const query = 'UPDATE bookings SET booking_status = ? WHERE id = ?';
    return await executeQuery(query, [status, id]);
  }

  static async updatePaymentStatus(id, paymentStatus) {
    const query = 'UPDATE bookings SET payment_status = ? WHERE id = ?';
    return await executeQuery(query, [paymentStatus, id]);
  }

  static async checkAvailability(propertyId, checkInDate, checkOutDate, excludeBookingId = null) {
    let query = `
      SELECT id, check_in_date, check_out_date, booking_status
      FROM bookings 
      WHERE property_id = ? 
        AND booking_status IN ('confirmed', 'pending')
        AND (
          (check_in_date <= ? AND check_out_date > ?) OR
          (check_in_date < ? AND check_out_date >= ?) OR
          (check_in_date >= ? AND check_out_date <= ?)
        )
    `;
    
    let params = [propertyId, checkInDate, checkInDate, checkOutDate, checkOutDate, checkInDate, checkOutDate];
    
    if (excludeBookingId) {
      query += ' AND id != ?';
      params.push(excludeBookingId);
    }
    
    return await findMany(query, params);
  }

  static async calculateTotalAmount(propertyId, checkInDate, checkOutDate) {
    const property = await findOne(`
      SELECT price_per_night, cleaning_fee
      FROM properties 
      WHERE id = ? AND status = 'active'
    `, [propertyId]);

    if (!property) {
      throw new Error('Property not found or not available');
    }

    const checkIn = new Date(checkInDate);
    const checkOut = new Date(checkOutDate);
    const nights = Math.ceil((checkOut - checkIn) / (1000 * 60 * 60 * 24));
    
    const accommodationCost = nights * parseFloat(property.price_per_night);
    const cleaningFee = parseFloat(property.cleaning_fee || 0);
    const totalAmount = accommodationCost + cleaningFee;

    return {
      nights,
      accommodationCost,
      cleaningFee,
      totalAmount,
      pricePerNight: parseFloat(property.price_per_night)
    };
  }

  static async getBookingsByProperty(propertyId) {
    const query = `
      SELECT 
        b.*,
        guest.first_name as guest_first_name,
        guest.last_name as guest_last_name,
        guest.email as guest_email,
        guest.phone as guest_phone
      FROM bookings b
      LEFT JOIN users guest ON b.guest_id = guest.id
      WHERE b.property_id = ?
      ORDER BY b.check_in_date DESC
    `;
    
    return await findMany(query, [propertyId]);
  }

  static async getUpcomingBookings(userId, userRole = 'guest') {
    const today = new Date().toISOString().split('T')[0];
    
    let query;
    let queryParams;

    if (userRole === 'admin') {
      query = `
        SELECT 
          b.*,
          p.title as property_title,
          p.location as property_location,
          guest.first_name as guest_first_name,
          guest.last_name as guest_last_name
        FROM bookings b
        LEFT JOIN properties p ON b.property_id = p.id
        LEFT JOIN users guest ON b.guest_id = guest.id
        WHERE b.check_in_date >= ? AND b.booking_status IN ('confirmed', 'pending')
        ORDER BY b.check_in_date ASC
      `;
      queryParams = [today];
    } else {
      query = `
        SELECT 
          b.*,
          p.title as property_title,
          p.location as property_location,
          p.owner_id,
          guest.first_name as guest_first_name,
          guest.last_name as guest_last_name,
          owner.first_name as owner_first_name,
          owner.last_name as owner_last_name
        FROM bookings b
        LEFT JOIN properties p ON b.property_id = p.id
        LEFT JOIN users guest ON b.guest_id = guest.id
        LEFT JOIN users owner ON p.owner_id = owner.id
        WHERE (b.guest_id = ? OR p.owner_id = ?)
          AND b.check_in_date >= ? 
          AND b.booking_status IN ('confirmed', 'pending')
        ORDER BY b.check_in_date ASC
      `;
      queryParams = [userId, userId, today];
    }

    return await findMany(query, queryParams);
  }

  static async deleteById(id) {
    const query = 'DELETE FROM bookings WHERE id = ?';
    return await executeQuery(query, [id]);
  }

  static async getBookingStats(userId = null, userRole = 'guest') {
    let query;
    let queryParams = [];

    if (userRole === 'admin') {
      query = `
        SELECT 
          COUNT(*) as total_bookings,
          COUNT(CASE WHEN booking_status = 'confirmed' THEN 1 END) as confirmed_bookings,
          COUNT(CASE WHEN booking_status = 'pending' THEN 1 END) as pending_bookings,
          COUNT(CASE WHEN booking_status = 'cancelled' THEN 1 END) as cancelled_bookings,
          COUNT(CASE WHEN booking_status = 'completed' THEN 1 END) as completed_bookings,
          SUM(CASE WHEN booking_status IN ('confirmed', 'completed') THEN total_amount ELSE 0 END) as total_revenue
        FROM bookings
      `;
    } else if (userId) {
      query = `
        SELECT 
          COUNT(*) as total_bookings,
          COUNT(CASE WHEN booking_status = 'confirmed' THEN 1 END) as confirmed_bookings,
          COUNT(CASE WHEN booking_status = 'pending' THEN 1 END) as pending_bookings,
          COUNT(CASE WHEN booking_status = 'cancelled' THEN 1 END) as cancelled_bookings,
          COUNT(CASE WHEN booking_status = 'completed' THEN 1 END) as completed_bookings,
          SUM(CASE WHEN booking_status IN ('confirmed', 'completed') THEN total_amount ELSE 0 END) as total_revenue
        FROM bookings b
        LEFT JOIN properties p ON b.property_id = p.id
        WHERE b.guest_id = ? OR p.owner_id = ?
      `;
      queryParams = [userId, userId];
    }

    const result = await findOne(query, queryParams);
    return result || {
      total_bookings: 0,
      confirmed_bookings: 0,
      pending_bookings: 0,
      cancelled_bookings: 0,
      completed_bookings: 0,
      total_revenue: 0
    };
  }
}

module.exports = Booking;
