import React, { ReactElement } from 'react';
import { render, RenderOptions } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { AuthProvider } from '@/contexts/AuthContext';
import { BookingProvider } from '@/contexts/BookingContext';
import { ReviewsProvider } from '@/contexts/ReviewsContext';
import { TooltipProvider } from '@/components/ui/tooltip';

// Create a custom render function that includes providers
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  initialEntries?: string[];
  queryClient?: QueryClient;
  withAuth?: boolean;
  withBooking?: boolean;
  withReviews?: boolean;
}

function createTestQueryClient() {
  return new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        cacheTime: 0,
        staleTime: 0,
      },
      mutations: {
        retry: false,
      },
    },
  });
}

function AllTheProviders({ 
  children, 
  queryClient = createTestQueryClient(),
  withAuth = true,
  withBooking = true,
  withReviews = true,
  initialEntries = ['/']
}: {
  children: React.ReactNode;
  queryClient?: QueryClient;
  withAuth?: boolean;
  withBooking?: boolean;
  withReviews?: boolean;
  initialEntries?: string[];
}) {
  let component = (
    <BrowserRouter>
      <TooltipProvider>
        <QueryClientProvider client={queryClient}>
          {children}
        </QueryClientProvider>
      </TooltipProvider>
    </BrowserRouter>
  );

  if (withAuth) {
    component = (
      <BrowserRouter>
        <TooltipProvider>
          <QueryClientProvider client={queryClient}>
            <AuthProvider>
              {withBooking ? (
                <BookingProvider>
                  {withReviews ? (
                    <ReviewsProvider>
                      {children}
                    </ReviewsProvider>
                  ) : children}
                </BookingProvider>
              ) : withReviews ? (
                <ReviewsProvider>
                  {children}
                </ReviewsProvider>
              ) : children}
            </AuthProvider>
          </QueryClientProvider>
        </TooltipProvider>
      </BrowserRouter>
    );
  }

  return component;
}

const customRender = (
  ui: ReactElement,
  options: CustomRenderOptions = {}
) => {
  const {
    initialEntries,
    queryClient,
    withAuth,
    withBooking,
    withReviews,
    ...renderOptions
  } = options;

  const Wrapper = ({ children }: { children: React.ReactNode }) => (
    <AllTheProviders
      queryClient={queryClient}
      withAuth={withAuth}
      withBooking={withBooking}
      withReviews={withReviews}
      initialEntries={initialEntries}
    >
      {children}
    </AllTheProviders>
  );

  return render(ui, { wrapper: Wrapper, ...renderOptions });
};

// Mock user data
export const mockUser = {
  id: '1',
  email: '<EMAIL>',
  firstName: 'Test',
  lastName: 'User',
  role: 'guest' as const,
  createdAt: '2024-01-01T00:00:00.000Z'
};

export const mockHost = {
  id: '2',
  email: '<EMAIL>',
  firstName: 'Host',
  lastName: 'User',
  role: 'host' as const,
  createdAt: '2024-01-01T00:00:00.000Z'
};

// Mock property data
export const mockProperty = {
  id: '1',
  title: 'Test Property',
  description: 'A beautiful test property',
  location: 'Test Location',
  price: 1000,
  maxGuests: 4,
  bedrooms: 2,
  bathrooms: 2,
  images: ['https://example.com/image1.jpg'],
  amenities: ['wifi', 'parking'],
  rating: 4.5,
  reviewCount: 10,
  hostId: '2',
  createdAt: '2024-01-01T00:00:00.000Z',
  updatedAt: '2024-01-01T00:00:00.000Z'
};

// Mock booking data
export const mockBooking = {
  id: '1',
  propertyId: '1',
  userId: '1',
  checkIn: '2024-02-15',
  checkOut: '2024-02-20',
  guests: 2,
  totalAmount: 5000,
  status: 'confirmed' as const,
  createdAt: '2024-01-20T00:00:00.000Z'
};

// Mock review data
export const mockReview = {
  id: '1',
  propertyId: '1',
  userId: '1',
  rating: 5,
  comment: 'Great stay!',
  createdAt: '2024-01-25T00:00:00.000Z'
};

// Helper functions for testing
export const waitForLoadingToFinish = () => {
  return new Promise(resolve => setTimeout(resolve, 0));
};

export const createMockIntersectionObserver = () => {
  const mockIntersectionObserver = vi.fn();
  mockIntersectionObserver.mockReturnValue({
    observe: () => null,
    unobserve: () => null,
    disconnect: () => null
  });
  window.IntersectionObserver = mockIntersectionObserver;
  return mockIntersectionObserver;
};

export const createMockResizeObserver = () => {
  const mockResizeObserver = vi.fn();
  mockResizeObserver.mockReturnValue({
    observe: () => null,
    unobserve: () => null,
    disconnect: () => null
  });
  window.ResizeObserver = mockResizeObserver;
  return mockResizeObserver;
};

// Mock localStorage
export const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};

// Mock sessionStorage
export const mockSessionStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};

// Mock window.location
export const mockLocation = {
  href: 'http://localhost:3000',
  origin: 'http://localhost:3000',
  protocol: 'http:',
  host: 'localhost:3000',
  hostname: 'localhost',
  port: '3000',
  pathname: '/',
  search: '',
  hash: '',
  assign: vi.fn(),
  replace: vi.fn(),
  reload: vi.fn(),
};

// Mock window.history
export const mockHistory = {
  length: 1,
  state: null,
  back: vi.fn(),
  forward: vi.fn(),
  go: vi.fn(),
  pushState: vi.fn(),
  replaceState: vi.fn(),
};

// Mock fetch
export const createMockFetch = (mockResponse: any) => {
  return vi.fn().mockResolvedValue({
    ok: true,
    status: 200,
    json: () => Promise.resolve(mockResponse),
    text: () => Promise.resolve(JSON.stringify(mockResponse)),
  });
};

// Mock file upload
export const createMockFile = (name = 'test.jpg', type = 'image/jpeg') => {
  return new File(['test'], name, { type });
};

// Mock image load
export const mockImageLoad = () => {
  Object.defineProperty(HTMLImageElement.prototype, 'onload', {
    get() {
      return this._onload;
    },
    set(fn) {
      this._onload = fn;
      // Simulate image load
      setTimeout(() => {
        if (fn) fn();
      }, 0);
    },
  });
};

// Mock performance API
export const mockPerformance = {
  now: vi.fn(() => Date.now()),
  mark: vi.fn(),
  measure: vi.fn(),
  getEntriesByName: vi.fn(() => []),
  getEntriesByType: vi.fn(() => []),
  clearMarks: vi.fn(),
  clearMeasures: vi.fn(),
};

// Setup function for common mocks
export const setupCommonMocks = () => {
  createMockIntersectionObserver();
  createMockResizeObserver();
  mockImageLoad();
  
  Object.defineProperty(window, 'localStorage', { value: mockLocalStorage });
  Object.defineProperty(window, 'sessionStorage', { value: mockSessionStorage });
  Object.defineProperty(window, 'location', { value: mockLocation, writable: true });
  Object.defineProperty(window, 'history', { value: mockHistory, writable: true });
  Object.defineProperty(window, 'performance', { value: mockPerformance, writable: true });
  
  global.URL.createObjectURL = vi.fn(() => 'mocked-url');
  global.URL.revokeObjectURL = vi.fn();
};

// Cleanup function
export const cleanupMocks = () => {
  vi.clearAllMocks();
  vi.resetAllMocks();
};

// Re-export everything from React Testing Library
export * from '@testing-library/react';
export { default as userEvent } from '@testing-library/user-event';

// Override render method
export { customRender as render };
