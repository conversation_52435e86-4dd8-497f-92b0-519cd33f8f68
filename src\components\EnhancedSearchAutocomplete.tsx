import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Search, MapPin, Clock, Star, X, Loader2, TrendingUp } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useSearchSuggestions, SearchSuggestion } from '@/hooks/useSearchSuggestions';
import { Button } from '@/components/ui/button';

interface EnhancedSearchAutocompleteProps {
  value: string;
  onChange: (value: string) => void;
  onSelect?: (suggestion: SearchSuggestion) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  autoFocus?: boolean;
  showRecentSearches?: boolean;
  showPopularDestinations?: boolean;
  maxSuggestions?: number;
}

export const EnhancedSearchAutocomplete: React.FC<EnhancedSearchAutocompleteProps> = ({
  value,
  onChange,
  onSelect,
  placeholder = "Where are you going?",
  className,
  disabled = false,
  autoFocus = false,
  showRecentSearches = true,
  showPopularDestinations = true,
  maxSuggestions = 8
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const [inputFocused, setInputFocused] = useState(false);
  
  const inputRef = useRef<HTMLInputElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);

  const {
    suggestions,
    isLoading,
    error,
    clearSuggestions,
    addToRecent,
    removeFromRecent,
    clearRecent
  } = useSearchSuggestions(value, {
    maxSuggestions,
    includeRecent: showRecentSearches,
    includePopular: showPopularDestinations
  });

  // Handle input focus
  const handleFocus = useCallback(() => {
    setInputFocused(true);
    setIsOpen(true);
  }, []);

  // Handle input blur
  const handleBlur = useCallback((e: React.FocusEvent) => {
    // Don't close if clicking on suggestions
    if (containerRef.current?.contains(e.relatedTarget as Node)) {
      return;
    }
    setInputFocused(false);
    setIsOpen(false);
    setSelectedIndex(-1);
  }, []);

  // Handle input change
  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    onChange(newValue);
    setSelectedIndex(-1);
    
    if (newValue.trim() || showRecentSearches || showPopularDestinations) {
      setIsOpen(true);
    } else {
      setIsOpen(false);
    }
  }, [onChange, showRecentSearches, showPopularDestinations]);

  // Handle suggestion selection
  const handleSuggestionSelect = useCallback((suggestion: SearchSuggestion) => {
    onChange(suggestion.text);
    addToRecent(suggestion);
    setIsOpen(false);
    setSelectedIndex(-1);
    onSelect?.(suggestion);
    inputRef.current?.blur();
  }, [onChange, addToRecent, onSelect]);

  // Handle keyboard navigation
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (!isOpen || suggestions.length === 0) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev < suggestions.length - 1 ? prev + 1 : 0
        );
        break;
      
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev > 0 ? prev - 1 : suggestions.length - 1
        );
        break;
      
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0 && selectedIndex < suggestions.length) {
          handleSuggestionSelect(suggestions[selectedIndex]);
        }
        break;
      
      case 'Escape':
        setIsOpen(false);
        setSelectedIndex(-1);
        inputRef.current?.blur();
        break;
    }
  }, [isOpen, suggestions, selectedIndex, handleSuggestionSelect]);

  // Clear input
  const handleClear = useCallback(() => {
    onChange('');
    setIsOpen(false);
    setSelectedIndex(-1);
    inputRef.current?.focus();
  }, [onChange]);

  // Close suggestions when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setSelectedIndex(-1);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Scroll selected item into view
  useEffect(() => {
    if (selectedIndex >= 0 && suggestionsRef.current) {
      const selectedElement = suggestionsRef.current.children[selectedIndex] as HTMLElement;
      if (selectedElement) {
        selectedElement.scrollIntoView({
          block: 'nearest',
          behavior: 'smooth'
        });
      }
    }
  }, [selectedIndex]);

  // Get icon for suggestion type
  const getSuggestionIcon = (suggestion: SearchSuggestion) => {
    if (suggestion.icon) return suggestion.icon;
    
    switch (suggestion.type) {
      case 'recent':
        return <Clock className="h-4 w-4 text-gray-400" />;
      case 'popular':
        return <TrendingUp className="h-4 w-4 text-orange-500" />;
      case 'location':
        return <MapPin className="h-4 w-4 text-blue-500" />;
      default:
        return <Search className="h-4 w-4 text-gray-400" />;
    }
  };

  // Get suggestion type label
  const getSuggestionTypeLabel = (type: string) => {
    switch (type) {
      case 'recent': return 'Recent';
      case 'popular': return 'Popular';
      case 'location': return 'Location';
      default: return '';
    }
  };

  const showSuggestions = isOpen && (suggestions.length > 0 || isLoading || error);

  return (
    <div ref={containerRef} className={cn("relative w-full", className)}>
      {/* Search Input */}
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <Search className="h-5 w-5 text-gray-400" />
        </div>
        
        <input
          ref={inputRef}
          type="text"
          value={value}
          onChange={handleInputChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          disabled={disabled}
          autoFocus={autoFocus}
          className={cn(
            "w-full pl-10 pr-10 py-3 border border-gray-300 rounded-lg",
            "focus:ring-2 focus:ring-sea-green-500 focus:border-sea-green-500",
            "placeholder-gray-500 text-gray-900",
            "transition-all duration-200",
            disabled && "bg-gray-100 cursor-not-allowed",
            inputFocused && "ring-2 ring-sea-green-500 border-sea-green-500"
          )}
        />

        {/* Clear Button */}
        {value && !disabled && (
          <button
            onClick={handleClear}
            className="absolute inset-y-0 right-0 pr-3 flex items-center hover:text-gray-600 transition-colors"
            type="button"
          >
            <X className="h-5 w-5 text-gray-400" />
          </button>
        )}

        {/* Loading Indicator */}
        {isLoading && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
            <Loader2 className="h-5 w-5 text-sea-green-500 animate-spin" />
          </div>
        )}
      </div>

      {/* Suggestions Dropdown */}
      {showSuggestions && (
        <div className="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-80 overflow-y-auto">
          <div ref={suggestionsRef}>
            {/* Error State */}
            {error && (
              <div className="p-4 text-center text-red-600">
                <p className="text-sm">{error}</p>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => window.location.reload()}
                  className="mt-2"
                >
                  Try Again
                </Button>
              </div>
            )}

            {/* Loading State */}
            {isLoading && suggestions.length === 0 && (
              <div className="p-4 text-center">
                <Loader2 className="h-6 w-6 animate-spin mx-auto mb-2 text-sea-green-500" />
                <p className="text-sm text-gray-600">Searching...</p>
              </div>
            )}

            {/* Suggestions */}
            {suggestions.map((suggestion, index) => (
              <div
                key={suggestion.id}
                onClick={() => handleSuggestionSelect(suggestion)}
                className={cn(
                  "flex items-center px-4 py-3 cursor-pointer transition-colors",
                  "hover:bg-gray-50",
                  selectedIndex === index && "bg-sea-green-50 border-l-4 border-sea-green-500"
                )}
              >
                <div className="flex-shrink-0 mr-3">
                  {typeof getSuggestionIcon(suggestion) === 'string' ? (
                    <span className="text-lg">{getSuggestionIcon(suggestion)}</span>
                  ) : (
                    getSuggestionIcon(suggestion)
                  )}
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {suggestion.text}
                    </p>
                    {suggestion.type === 'recent' && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          removeFromRecent(suggestion.id);
                        }}
                        className="ml-2 p-1 hover:bg-gray-200 rounded transition-colors"
                      >
                        <X className="h-3 w-3 text-gray-400" />
                      </button>
                    )}
                  </div>
                  
                  <div className="flex items-center justify-between mt-1">
                    <p className="text-xs text-gray-500 truncate">
                      {suggestion.description}
                    </p>
                    
                    <div className="flex items-center space-x-2 ml-2">
                      {suggestion.propertyCount && (
                        <span className="text-xs text-gray-400">
                          {suggestion.propertyCount} properties
                        </span>
                      )}
                      
                      <span className={cn(
                        "text-xs px-2 py-1 rounded-full",
                        suggestion.type === 'popular' && "bg-orange-100 text-orange-700",
                        suggestion.type === 'recent' && "bg-blue-100 text-blue-700",
                        suggestion.type === 'location' && "bg-green-100 text-green-700"
                      )}>
                        {getSuggestionTypeLabel(suggestion.type)}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            ))}

            {/* Clear Recent Searches */}
            {showRecentSearches && suggestions.some(s => s.type === 'recent') && (
              <div className="border-t border-gray-100 p-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearRecent}
                  className="w-full text-xs text-gray-500 hover:text-gray-700"
                >
                  Clear recent searches
                </Button>
              </div>
            )}

            {/* No Results */}
            {!isLoading && !error && suggestions.length === 0 && value.trim() && (
              <div className="p-4 text-center text-gray-500">
                <Search className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                <p className="text-sm">No suggestions found</p>
                <p className="text-xs mt-1">Try searching for a city or region</p>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};
