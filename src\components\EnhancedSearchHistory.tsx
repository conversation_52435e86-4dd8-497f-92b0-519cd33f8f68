import React, { useState, useEffect } from 'react';
import { Clock, MapPin, Calendar, Users, Star, Trash2, Search, Heart, Bell, X, Filter } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { toast } from '@/components/ui/use-toast';
import { cn } from '@/lib/utils';

interface SearchQuery {
  id: string;
  location: string;
  checkIn: string;
  checkOut: string;
  guests: number;
  filters?: {
    priceRange?: [number, number];
    propertyTypes?: string[];
    amenities?: string[];
    rating?: number;
  };
  timestamp: Date;
  resultCount?: number;
}

interface SavedSearch extends SearchQuery {
  name: string;
  alertsEnabled: boolean;
  lastAlertSent?: Date;
}

interface EnhancedSearchHistoryProps {
  onSearchSelect: (search: SearchQuery) => void;
  className?: string;
}

export const EnhancedSearchHistory: React.FC<EnhancedSearchHistoryProps> = ({
  onSearchSelect,
  className
}) => {
  const [searchHistory, setSearchHistory] = useState<SearchQuery[]>([]);
  const [savedSearches, setSavedSearches] = useState<SavedSearch[]>([]);
  const [activeTab, setActiveTab] = useState<'history' | 'saved'>('history');
  const [searchFilter, setSearchFilter] = useState('');
  const [showSaveDialog, setShowSaveDialog] = useState<SearchQuery | null>(null);
  const [saveName, setSaveName] = useState('');
  const [alertsEnabled, setAlertsEnabled] = useState(true);

  // Load data from localStorage on mount
  useEffect(() => {
    const savedHistory = localStorage.getItem('enhancedSearchHistory');
    const savedSearchesList = localStorage.getItem('enhancedSavedSearches');

    if (savedHistory) {
      try {
        const history = JSON.parse(savedHistory).map((item: any) => ({
          ...item,
          timestamp: new Date(item.timestamp)
        }));
        setSearchHistory(history);
      } catch (error) {
        console.error('Failed to load search history:', error);
      }
    }

    if (savedSearchesList) {
      try {
        const searches = JSON.parse(savedSearchesList).map((item: any) => ({
          ...item,
          timestamp: new Date(item.timestamp),
          lastAlertSent: item.lastAlertSent ? new Date(item.lastAlertSent) : undefined
        }));
        setSavedSearches(searches);
      } catch (error) {
        console.error('Failed to load saved searches:', error);
      }
    }
  }, []);

  // Save to localStorage when data changes
  useEffect(() => {
    localStorage.setItem('enhancedSearchHistory', JSON.stringify(searchHistory));
  }, [searchHistory]);

  useEffect(() => {
    localStorage.setItem('enhancedSavedSearches', JSON.stringify(savedSearches));
  }, [savedSearches]);

  const addToHistory = (search: SearchQuery) => {
    const newHistory = [search, ...searchHistory.filter(s => s.id !== search.id)].slice(0, 20);
    setSearchHistory(newHistory);
  };

  const removeFromHistory = (searchId: string) => {
    setSearchHistory(prev => prev.filter(s => s.id !== searchId));
    toast({
      title: "Search removed",
      description: "Search has been removed from your history",
    });
  };

  const clearHistory = () => {
    setSearchHistory([]);
    toast({
      title: "History cleared",
      description: "All search history has been cleared",
    });
  };

  const saveSearch = (search: SearchQuery, name: string, alerts: boolean) => {
    const savedSearch: SavedSearch = {
      ...search,
      name,
      alertsEnabled: alerts,
      id: `saved_${Date.now()}`
    };

    setSavedSearches(prev => [savedSearch, ...prev]);
    setShowSaveDialog(null);
    setSaveName('');
    
    toast({
      title: "Search saved",
      description: `"${name}" has been saved to your searches`,
    });
  };

  const removeSavedSearch = (searchId: string) => {
    setSavedSearches(prev => prev.filter(s => s.id !== searchId));
    toast({
      title: "Saved search removed",
      description: "Search has been removed from your saved searches",
    });
  };

  const toggleAlerts = (searchId: string) => {
    setSavedSearches(prev => prev.map(search => 
      search.id === searchId 
        ? { ...search, alertsEnabled: !search.alertsEnabled }
        : search
    ));
  };

  const formatDate = (date: Date) => {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) return 'Today';
    if (diffDays === 1) return 'Yesterday';
    if (diffDays < 7) return `${diffDays} days ago`;
    return date.toLocaleDateString();
  };

  const formatDateRange = (checkIn: string, checkOut: string) => {
    const start = new Date(checkIn);
    const end = new Date(checkOut);
    const nights = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24));
    
    return `${start.toLocaleDateString()} - ${end.toLocaleDateString()} (${nights} nights)`;
  };

  const filteredHistory = searchHistory.filter(search =>
    search.location.toLowerCase().includes(searchFilter.toLowerCase())
  );

  const filteredSavedSearches = savedSearches.filter(search =>
    search.name.toLowerCase().includes(searchFilter.toLowerCase()) ||
    search.location.toLowerCase().includes(searchFilter.toLowerCase())
  );

  const SearchItem: React.FC<{ search: SearchQuery; isSaved?: boolean }> = ({ search, isSaved = false }) => (
    <Card className="hover:shadow-md transition-shadow cursor-pointer group">
      <CardContent className="p-4">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0" onClick={() => onSearchSelect(search)}>
            <div className="flex items-center gap-2 mb-2">
              <MapPin className="h-4 w-4 text-gray-500 flex-shrink-0" />
              <span className="font-medium truncate">{search.location}</span>
              {isSaved && (
                <Badge variant="secondary" className="ml-auto">
                  <Heart className="h-3 w-3 mr-1" />
                  {(search as SavedSearch).name}
                </Badge>
              )}
            </div>
            
            <div className="space-y-1 text-sm text-gray-600">
              <div className="flex items-center gap-2">
                <Calendar className="h-3 w-3" />
                <span>{formatDateRange(search.checkIn, search.checkOut)}</span>
              </div>
              
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-1">
                  <Users className="h-3 w-3" />
                  <span>{search.guests} guests</span>
                </div>
                
                {search.resultCount && (
                  <div className="flex items-center gap-1">
                    <Search className="h-3 w-3" />
                    <span>{search.resultCount} results</span>
                  </div>
                )}
                
                <div className="flex items-center gap-1 ml-auto">
                  <Clock className="h-3 w-3" />
                  <span>{formatDate(search.timestamp)}</span>
                </div>
              </div>
            </div>

            {search.filters && (
              <div className="flex flex-wrap gap-1 mt-2">
                {search.filters.priceRange && (
                  <Badge variant="outline" className="text-xs">
                    R{search.filters.priceRange[0]} - R{search.filters.priceRange[1]}
                  </Badge>
                )}
                {search.filters.propertyTypes?.map((type, index) => (
                  <Badge key={index} variant="outline" className="text-xs">
                    {type}
                  </Badge>
                ))}
                {search.filters.rating && (
                  <Badge variant="outline" className="text-xs">
                    <Star className="h-3 w-3 mr-1" />
                    {search.filters.rating}+
                  </Badge>
                )}
              </div>
            )}
          </div>
          
          <div className="flex items-center gap-1 ml-2 opacity-0 group-hover:opacity-100 transition-opacity">
            {!isSaved ? (
              <>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={(e) => {
                    e.stopPropagation();
                    setShowSaveDialog(search);
                    setSaveName(`${search.location} - ${formatDate(search.timestamp)}`);
                  }}
                >
                  <Heart className="h-4 w-4" />
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={(e) => {
                    e.stopPropagation();
                    removeFromHistory(search.id);
                  }}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </>
            ) : (
              <>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={(e) => {
                    e.stopPropagation();
                    toggleAlerts(search.id);
                  }}
                  className={cn(
                    (search as SavedSearch).alertsEnabled && "text-blue-600"
                  )}
                >
                  <Bell className="h-4 w-4" />
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={(e) => {
                    e.stopPropagation();
                    removeSavedSearch(search.id);
                  }}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className={cn("space-y-4", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant={activeTab === 'history' ? 'default' : 'ghost'}
            onClick={() => setActiveTab('history')}
          >
            <Clock className="h-4 w-4 mr-2" />
            Recent Searches ({searchHistory.length})
          </Button>
          <Button
            variant={activeTab === 'saved' ? 'default' : 'ghost'}
            onClick={() => setActiveTab('saved')}
          >
            <Heart className="h-4 w-4 mr-2" />
            Saved Searches ({savedSearches.length})
          </Button>
        </div>
        
        {activeTab === 'history' && searchHistory.length > 0 && (
          <Button variant="outline" size="sm" onClick={clearHistory}>
            Clear All
          </Button>
        )}
      </div>

      {/* Search Filter */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
        <Input
          placeholder={`Search ${activeTab === 'history' ? 'history' : 'saved searches'}...`}
          value={searchFilter}
          onChange={(e) => setSearchFilter(e.target.value)}
          className="pl-9"
        />
      </div>

      {/* Content */}
      <div className="space-y-3">
        {activeTab === 'history' ? (
          filteredHistory.length > 0 ? (
            filteredHistory.map((search) => (
              <SearchItem key={search.id} search={search} />
            ))
          ) : (
            <Card>
              <CardContent className="p-8 text-center">
                <Clock className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                <h3 className="text-lg font-medium mb-2">No search history</h3>
                <p className="text-gray-600">
                  {searchFilter ? 'No searches match your filter' : 'Start searching to see your recent searches here'}
                </p>
              </CardContent>
            </Card>
          )
        ) : (
          filteredSavedSearches.length > 0 ? (
            filteredSavedSearches.map((search) => (
              <SearchItem key={search.id} search={search} isSaved />
            ))
          ) : (
            <Card>
              <CardContent className="p-8 text-center">
                <Heart className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                <h3 className="text-lg font-medium mb-2">No saved searches</h3>
                <p className="text-gray-600">
                  {searchFilter ? 'No saved searches match your filter' : 'Save your favorite searches to access them quickly'}
                </p>
              </CardContent>
            </Card>
          )
        )}
      </div>

      {/* Save Search Dialog */}
      {showSaveDialog && (
        <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
          <Card className="w-full max-w-md">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                Save Search
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowSaveDialog(null)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="saveName">Search Name</Label>
                <Input
                  id="saveName"
                  value={saveName}
                  onChange={(e) => setSaveName(e.target.value)}
                  placeholder="Enter a name for this search"
                />
              </div>
              
              <div className="flex items-center space-x-2">
                <Switch
                  id="alerts"
                  checked={alertsEnabled}
                  onCheckedChange={setAlertsEnabled}
                />
                <Label htmlFor="alerts">
                  Enable price alerts for this search
                </Label>
              </div>
              
              <Separator />
              
              <div className="text-sm text-gray-600">
                <p><strong>Location:</strong> {showSaveDialog.location}</p>
                <p><strong>Dates:</strong> {formatDateRange(showSaveDialog.checkIn, showSaveDialog.checkOut)}</p>
                <p><strong>Guests:</strong> {showSaveDialog.guests}</p>
              </div>
              
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  onClick={() => setShowSaveDialog(null)}
                  className="flex-1"
                >
                  Cancel
                </Button>
                <Button
                  onClick={() => saveSearch(showSaveDialog, saveName, alertsEnabled)}
                  disabled={!saveName.trim()}
                  className="flex-1"
                >
                  Save Search
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};

// Export the component and hook for external use
export { EnhancedSearchHistory as SearchHistory };
