
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Search, User, Calendar, Phone, Mail, LogOut, Settings, Star, Home, MessageSquare, Plus, Menu, X } from 'lucide-react';
import { ModernSearchInput } from './ModernSearchInput';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useAuth } from '../contexts/AuthContext';
import { LoginForm } from './LoginForm';
import { RegisterForm } from './RegisterForm';
import { ThemeToggle } from './ThemeToggle';

export const Header = () => {
  const [isLoginOpen, setIsLoginOpen] = useState(false);
  const [isRegisterOpen, setIsRegisterOpen] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [searchLocation, setSearchLocation] = useState('');
  const { user, isAuthenticated, logout } = useAuth();
  const navigate = useNavigate();

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const getUserInitials = () => {
    if (!user) return '';
    return `${user.firstName?.charAt(0) || ''}${user.lastName?.charAt(0) || ''}`.toUpperCase();
  };

  const handleSearchSubmit = (query: string) => {
    if (query.trim()) {
      navigate(`/search?location=${encodeURIComponent(query.trim())}`);
    }
  };

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };

  const handleMobileNavigation = (path: string) => {
    navigate(path);
    closeMobileMenu();
  };

  return (
    <header className="bg-white/95 backdrop-blur-md shadow-lg border-b border-sea-green-100 sticky top-0 z-50">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <div className="flex items-center space-x-3 cursor-pointer" onClick={() => navigate('/')}>
            <div className="w-12 h-12 bg-gradient-to-br from-sea-green-500 via-ocean-blue-500 to-sea-green-600 rounded-2xl flex items-center justify-center shadow-lg">
              <span className="text-white font-bold text-xl">S</span>
            </div>
            <div>
              <h1 className="text-xl font-bold text-gray-900">StayFinder</h1>
              <p className="text-sm text-gray-600">South Africa</p>
            </div>
          </div>

          {/* Search Bar */}
          <div className="hidden md:flex flex-1 max-w-lg mx-8">
            <ModernSearchInput
              value={searchLocation}
              onChange={setSearchLocation}
              onSearch={handleSearchSubmit}
              placeholder="Search destinations, properties..."
              size="sm"
              className="w-full"
            />
          </div>

          {/* Navigation & Auth */}
          <div className="flex items-center space-x-3">
            {/* Desktop List Property Button */}
            <Button
              variant="ghost"
              className="hidden md:flex text-gray-700 hover:bg-sea-green-50 hover:text-sea-green-700 font-medium transition-all duration-200"
              onClick={() => navigate('/host')}
            >
              <Plus className="h-4 w-4 mr-2" />
              List Property
            </Button>

            {/* Theme Toggle */}
            <ThemeToggle variant="dropdown" size="sm" className="hidden md:flex" />

            {/* Mobile Menu Button */}
            <Button
              variant="ghost"
              className="md:hidden p-2"
              onClick={toggleMobileMenu}
            >
              {isMobileMenuOpen ? (
                <X className="h-6 w-6 text-gray-700" />
              ) : (
                <Menu className="h-6 w-6 text-gray-700" />
              )}
            </Button>

            {/* Desktop Authentication */}
            <div className="hidden md:flex">
              {isAuthenticated ? (
                // Authenticated user menu
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="relative h-10 w-10 rounded-full hover:bg-sea-green-50 transition-all duration-200">
                      <Avatar className="h-10 w-10 border-2 border-white shadow-md">
                        <AvatarImage src="" alt={user?.firstName} />
                        <AvatarFallback className="bg-gradient-to-br from-sea-green-500 to-ocean-blue-500 text-white font-semibold">
                          {getUserInitials()}
                        </AvatarFallback>
                      </Avatar>
                    </Button>
                  </DropdownMenuTrigger>
                <DropdownMenuContent className="w-56" align="end" forceMount>
                  <DropdownMenuLabel className="font-normal">
                    <div className="flex flex-col space-y-1">
                      <p className="text-sm font-medium leading-none">
                        {user?.firstName} {user?.lastName}
                      </p>
                      <p className="text-xs leading-none text-muted-foreground">
                        {user?.email}
                      </p>
                    </div>
                  </DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => navigate('/dashboard')}>
                    <User className="mr-2 h-4 w-4" />
                    <span>Dashboard</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => navigate('/bookings')}>
                    <Calendar className="mr-2 h-4 w-4" />
                    <span>My Bookings</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => navigate('/messages')}>
                    <MessageSquare className="mr-2 h-4 w-4" />
                    <span>Messages</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => navigate('/reviews')}>
                    <Star className="mr-2 h-4 w-4" />
                    <span>My Reviews</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => navigate('/host')}>
                    <Home className="mr-2 h-4 w-4" />
                    <span>Host Dashboard</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => navigate('/dashboard')}>
                    <Settings className="mr-2 h-4 w-4" />
                    <span>Settings</span>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={handleLogout}>
                    <LogOut className="mr-2 h-4 w-4" />
                    <span>Log out</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
              ) : (
                // Guest user buttons
                <>
                  <Dialog open={isLoginOpen} onOpenChange={setIsLoginOpen}>
                    <DialogTrigger asChild>
                      <Button variant="outline" className="border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-sea-green-300 hover:text-sea-green-700 transition-all duration-200 rounded-xl">
                        <User className="h-4 w-4 mr-2" />
                        Login
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Welcome Back</DialogTitle>
                        <DialogDescription>
                          Sign in to your KZN South Coast StayFinder account
                        </DialogDescription>
                      </DialogHeader>
                      <LoginForm onClose={() => setIsLoginOpen(false)} />
                    </DialogContent>
                  </Dialog>

                  <Dialog open={isRegisterOpen} onOpenChange={setIsRegisterOpen}>
                    <DialogTrigger asChild>
                      <Button className="bg-gradient-to-r from-sea-green-500 to-ocean-blue-500 hover:from-sea-green-600 hover:to-ocean-blue-600 text-white font-semibold rounded-xl shadow-md hover:shadow-lg transition-all duration-200">
                        Register
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Join StayFinder</DialogTitle>
                        <DialogDescription>
                          Create your account to start listing properties
                        </DialogDescription>
                      </DialogHeader>
                      <RegisterForm onClose={() => setIsRegisterOpen(false)} />
                    </DialogContent>
                  </Dialog>
                </>
              )}
            </div>
          </div>
        </div>

        {/* Mobile Search */}
        <div className="md:hidden mt-4">
          <ModernSearchInput
            value={searchLocation}
            onChange={setSearchLocation}
            onSearch={handleSearchSubmit}
            placeholder="Search destinations, properties..."
            size="sm"
            className="w-full"
          />
        </div>

        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <div className="md:hidden mt-4 border-t border-gray-200 pt-4">
            <div className="space-y-3">
              {/* List Property Button */}
              <Button
                variant="ghost"
                className="w-full justify-start text-gray-700 hover:bg-sea-green-50 hover:text-sea-green-700 font-medium transition-all duration-200"
                onClick={() => handleMobileNavigation('/host')}
              >
                <Plus className="h-4 w-4 mr-3" />
                List Property
              </Button>

              {isAuthenticated ? (
                // Authenticated user menu items
                <>
                  <div className="px-3 py-2 border-b border-gray-100">
                    <div className="flex items-center space-x-3">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src="" alt={user?.firstName} />
                        <AvatarFallback className="bg-gradient-to-br from-sea-green-500 to-ocean-blue-500 text-white text-sm font-semibold">
                          {getUserInitials()}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="text-sm font-medium text-gray-900">
                          {user?.firstName} {user?.lastName}
                        </p>
                        <p className="text-xs text-gray-500">{user?.email}</p>
                      </div>
                    </div>
                  </div>

                  <Button
                    variant="ghost"
                    className="w-full justify-start text-gray-700 hover:bg-gray-50"
                    onClick={() => handleMobileNavigation('/dashboard')}
                  >
                    <User className="h-4 w-4 mr-3" />
                    Dashboard
                  </Button>

                  <Button
                    variant="ghost"
                    className="w-full justify-start text-gray-700 hover:bg-gray-50"
                    onClick={() => handleMobileNavigation('/bookings')}
                  >
                    <Calendar className="h-4 w-4 mr-3" />
                    My Bookings
                  </Button>

                  <Button
                    variant="ghost"
                    className="w-full justify-start text-gray-700 hover:bg-gray-50"
                    onClick={() => handleMobileNavigation('/messages')}
                  >
                    <MessageSquare className="h-4 w-4 mr-3" />
                    Messages
                  </Button>

                  <Button
                    variant="ghost"
                    className="w-full justify-start text-gray-700 hover:bg-gray-50"
                    onClick={() => handleMobileNavigation('/reviews')}
                  >
                    <Star className="h-4 w-4 mr-3" />
                    My Reviews
                  </Button>

                  <Button
                    variant="ghost"
                    className="w-full justify-start text-gray-700 hover:bg-gray-50"
                    onClick={() => handleMobileNavigation('/host')}
                  >
                    <Home className="h-4 w-4 mr-3" />
                    Host Dashboard
                  </Button>

                  <Button
                    variant="ghost"
                    className="w-full justify-start text-gray-700 hover:bg-gray-50"
                    onClick={() => handleMobileNavigation('/dashboard')}
                  >
                    <Settings className="h-4 w-4 mr-3" />
                    Settings
                  </Button>

                  <div className="border-t border-gray-200 pt-3">
                    <Button
                      variant="ghost"
                      className="w-full justify-start text-red-600 hover:bg-red-50 hover:text-red-700"
                      onClick={() => {
                        handleLogout();
                        closeMobileMenu();
                      }}
                    >
                      <LogOut className="h-4 w-4 mr-3" />
                      Log out
                    </Button>
                  </div>
                </>
              ) : (
                // Guest user menu items
                <>
                  <Button
                    variant="outline"
                    className="w-full justify-start border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-sea-green-300 hover:text-sea-green-700 transition-all duration-200"
                    onClick={() => {
                      setIsLoginOpen(true);
                      closeMobileMenu();
                    }}
                  >
                    <User className="h-4 w-4 mr-3" />
                    Login
                  </Button>

                  <Button
                    className="w-full justify-start bg-gradient-to-r from-sea-green-500 to-ocean-blue-500 hover:from-sea-green-600 hover:to-ocean-blue-600 text-white font-semibold shadow-md hover:shadow-lg transition-all duration-200"
                    onClick={() => {
                      setIsRegisterOpen(true);
                      closeMobileMenu();
                    }}
                  >
                    Register
                  </Button>
                </>
              )}

              {/* Theme Toggle for Mobile */}
              <div className="border-t border-gray-200 pt-3">
                <div className="flex items-center justify-between px-3 py-2">
                  <span className="text-sm font-medium text-gray-700">Theme</span>
                  <ThemeToggle variant="dropdown" size="sm" />
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </header>
  );
};
