import React, { useState, useMemo } from 'react';
import { Search, ChevronDown, ChevronUp, HelpCircle, MessageCircle, Mail, Phone } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

interface FAQ {
  id: string;
  question: string;
  answer: string;
  category: string;
  tags: string[];
}

const faqs: FAQ[] = [
  {
    id: '1',
    question: 'How do I book a property on StayFinder?',
    answer: 'Booking is simple! Search for your destination, select your dates and number of guests, browse available properties, and click "Book Now" on your chosen accommodation. You\'ll need to provide guest details and payment information to complete your booking.',
    category: 'Booking',
    tags: ['booking', 'reservation', 'how-to']
  },
  {
    id: '2',
    question: 'What payment methods do you accept?',
    answer: 'We accept all major credit cards (Visa, Mastercard, American Express), debit cards, and bank transfers. All payments are processed securely through our encrypted payment system.',
    category: 'Payment',
    tags: ['payment', 'credit card', 'security']
  },
  {
    id: '3',
    question: 'Can I cancel my booking?',
    answer: 'Yes, you can cancel your booking according to the property\'s cancellation policy. Each property has its own policy which is clearly displayed during booking. You can cancel through your account dashboard or by contacting our support team.',
    category: 'Booking',
    tags: ['cancellation', 'refund', 'policy']
  },
  {
    id: '4',
    question: 'How do I contact the property owner?',
    answer: 'Once your booking is confirmed, you\'ll receive the host\'s contact details. You can also message them directly through our platform\'s messaging system in your account dashboard.',
    category: 'Communication',
    tags: ['contact', 'host', 'messaging']
  },
  {
    id: '5',
    question: 'What if I need to modify my booking?',
    answer: 'You can modify your booking dates or guest count through your account dashboard, subject to availability and the property\'s modification policy. Additional charges may apply for changes.',
    category: 'Booking',
    tags: ['modification', 'changes', 'dates']
  },
  {
    id: '6',
    question: 'Is my personal information secure?',
    answer: 'Absolutely! We use industry-standard encryption and security measures to protect your personal and payment information. We never share your details with third parties without your consent.',
    category: 'Security',
    tags: ['security', 'privacy', 'data protection']
  },
  {
    id: '7',
    question: 'How do I leave a review?',
    answer: 'After your stay, you\'ll receive an email invitation to leave a review. You can also access the review form through your account dashboard under "Past Bookings". Reviews help other travelers and improve our service.',
    category: 'Reviews',
    tags: ['review', 'feedback', 'rating']
  },
  {
    id: '8',
    question: 'What happens if there\'s an issue with my accommodation?',
    answer: 'If you encounter any issues during your stay, contact our 24/7 support team immediately. We\'ll work with you and the host to resolve the problem quickly, and if necessary, help you find alternative accommodation.',
    category: 'Support',
    tags: ['issues', 'problems', 'support']
  },
  {
    id: '9',
    question: 'Do you charge booking fees?',
    answer: 'We charge a small service fee to cover payment processing and customer support. This fee is clearly displayed before you complete your booking, with no hidden charges.',
    category: 'Payment',
    tags: ['fees', 'charges', 'pricing']
  },
  {
    id: '10',
    question: 'How do I become a host on StayFinder?',
    answer: 'To become a host, click "List Your Property" and follow our simple registration process. You\'ll need to provide property details, photos, and verify your identity. Our team will review your listing before it goes live.',
    category: 'Hosting',
    tags: ['hosting', 'list property', 'become host']
  },
  {
    id: '11',
    question: 'What areas does StayFinder cover?',
    answer: 'StayFinder covers all major destinations across South Africa, including Cape Town, Johannesburg, Durban, the Garden Route, Kruger Park area, and many more beautiful locations.',
    category: 'General',
    tags: ['coverage', 'locations', 'south africa']
  },
  {
    id: '12',
    question: 'Can I book for someone else?',
    answer: 'Yes, you can make a booking for someone else. Just ensure you provide the correct guest details during booking and inform the host about the arrangement.',
    category: 'Booking',
    tags: ['booking for others', 'guest details']
  }
];

const categories = ['All', 'Booking', 'Payment', 'Communication', 'Security', 'Reviews', 'Support', 'Hosting', 'General'];

export const FAQPage: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [expandedFAQ, setExpandedFAQ] = useState<string | null>(null);

  const filteredFAQs = useMemo(() => {
    let filtered = faqs;

    // Filter by category
    if (selectedCategory !== 'All') {
      filtered = filtered.filter(faq => faq.category === selectedCategory);
    }

    // Filter by search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(faq =>
        faq.question.toLowerCase().includes(query) ||
        faq.answer.toLowerCase().includes(query) ||
        faq.tags.some(tag => tag.toLowerCase().includes(query))
      );
    }

    return filtered;
  }, [searchQuery, selectedCategory]);

  const toggleFAQ = (id: string) => {
    setExpandedFAQ(expandedFAQ === id ? null : id);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-sea-green-600 to-ocean-blue-600 text-white py-16">
        <div className="container mx-auto px-4 text-center">
          <HelpCircle className="h-16 w-16 mx-auto mb-6 opacity-90" />
          <h1 className="text-4xl font-bold mb-4">Frequently Asked Questions</h1>
          <p className="text-xl opacity-90 max-w-2xl mx-auto">
            Find answers to common questions about booking, payments, and using StayFinder
          </p>
        </div>
      </section>

      <div className="container mx-auto px-4 py-12">
        {/* Search and Filters */}
        <div className="max-w-4xl mx-auto mb-12">
          <div className="relative mb-6">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            <Input
              placeholder="Search FAQs..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 py-3 text-lg"
            />
          </div>

          {/* Category Filters */}
          <div className="flex flex-wrap gap-2 justify-center">
            {categories.map((category) => (
              <Button
                key={category}
                variant={selectedCategory === category ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedCategory(category)}
                className={cn(
                  "transition-all duration-200",
                  selectedCategory === category && "bg-sea-green-600 hover:bg-sea-green-700"
                )}
              >
                {category}
                {category !== 'All' && (
                  <Badge variant="secondary" className="ml-2 text-xs">
                    {faqs.filter(faq => faq.category === category).length}
                  </Badge>
                )}
              </Button>
            ))}
          </div>
        </div>

        {/* FAQ List */}
        <div className="max-w-4xl mx-auto">
          {filteredFAQs.length === 0 ? (
            <Card className="text-center py-12">
              <CardContent>
                <HelpCircle className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                <h3 className="text-xl font-semibold mb-2">No FAQs found</h3>
                <p className="text-gray-600">
                  Try adjusting your search terms or browse different categories
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {filteredFAQs.map((faq) => (
                <Card key={faq.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-0">
                    <button
                      onClick={() => toggleFAQ(faq.id)}
                      className="w-full p-6 text-left hover:bg-gray-50 transition-colors"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <h3 className="text-lg font-semibold mb-2">{faq.question}</h3>
                          <div className="flex items-center gap-2">
                            <Badge variant="secondary" className="text-xs">
                              {faq.category}
                            </Badge>
                            {searchQuery && faq.tags.some(tag => 
                              tag.toLowerCase().includes(searchQuery.toLowerCase())
                            ) && (
                              <Badge variant="outline" className="text-xs">
                                Matches: {faq.tags.filter(tag => 
                                  tag.toLowerCase().includes(searchQuery.toLowerCase())
                                ).join(', ')}
                              </Badge>
                            )}
                          </div>
                        </div>
                        <div className="ml-4">
                          {expandedFAQ === faq.id ? (
                            <ChevronUp className="h-5 w-5 text-gray-500" />
                          ) : (
                            <ChevronDown className="h-5 w-5 text-gray-500" />
                          )}
                        </div>
                      </div>
                    </button>
                    
                    {expandedFAQ === faq.id && (
                      <div className="px-6 pb-6">
                        <div className="border-t pt-4">
                          <p className="text-gray-700 leading-relaxed">{faq.answer}</p>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>

        {/* Contact Support Section */}
        <div className="max-w-4xl mx-auto mt-16">
          <Card className="bg-gradient-to-r from-sea-green-50 to-ocean-blue-50 border-sea-green-200">
            <CardContent className="p-8 text-center">
              <MessageCircle className="h-12 w-12 mx-auto mb-4 text-sea-green-600" />
              <h3 className="text-2xl font-bold mb-4">Still need help?</h3>
              <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
                Can't find the answer you're looking for? Our friendly support team is here to help you 24/7.
              </p>
              
              <div className="grid md:grid-cols-3 gap-4">
                <Button variant="outline" className="flex items-center gap-2 h-auto py-4">
                  <MessageCircle className="h-5 w-5" />
                  <div className="text-left">
                    <div className="font-semibold">Live Chat</div>
                    <div className="text-sm text-gray-600">Available 24/7</div>
                  </div>
                </Button>
                
                <Button variant="outline" className="flex items-center gap-2 h-auto py-4">
                  <Mail className="h-5 w-5" />
                  <div className="text-left">
                    <div className="font-semibold">Email Support</div>
                    <div className="text-sm text-gray-600"><EMAIL></div>
                  </div>
                </Button>
                
                <Button variant="outline" className="flex items-center gap-2 h-auto py-4">
                  <Phone className="h-5 w-5" />
                  <div className="text-left">
                    <div className="font-semibold">Phone Support</div>
                    <div className="text-sm text-gray-600">+27 21 123 4567</div>
                  </div>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};
