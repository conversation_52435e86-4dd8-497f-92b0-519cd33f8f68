import { supabase } from '@/integrations/supabase/client';

export interface Conversation {
  id: string;
  property_id?: string;
  booking_id?: string;
  participant_1_id: string;
  participant_2_id: string;
  last_message_at: string;
  is_archived: boolean;
  created_at: string;
  updated_at: string;
  // Joined data
  property?: {
    id: string;
    title: string;
    city: string;
    province: string;
  };
  participant_1?: {
    id: string;
    first_name: string;
    last_name: string;
    avatar_url?: string;
  };
  participant_2?: {
    id: string;
    first_name: string;
    last_name: string;
    avatar_url?: string;
  };
  last_message?: {
    id: string;
    content: string;
    sender_id: string;
    created_at: string;
  };
}

export interface ConversationMessage {
  id: string;
  conversation_id: string;
  sender_id: string;
  recipient_id: string;
  content: string;
  message_type: string;
  is_read: boolean;
  created_at: string;
  updated_at: string;
  // Joined data
  sender?: {
    id: string;
    first_name: string;
    last_name: string;
    avatar_url?: string;
  };
}

export class ConversationsService {
  /**
   * Get all conversations for a user
   */
  static async getUserConversations(userId: string): Promise<Conversation[]> {
    try {
      const { data, error } = await supabase
        .from('conversations')
        .select(`
          *,
          property:properties (
            id,
            title,
            city,
            province
          ),
          participant_1:users!participant_1_id (
            id,
            first_name,
            last_name,
            avatar_url
          ),
          participant_2:users!participant_2_id (
            id,
            first_name,
            last_name,
            avatar_url
          )
        `)
        .or(`participant_1_id.eq.${userId},participant_2_id.eq.${userId}`)
        .eq('is_archived', false)
        .order('last_message_at', { ascending: false });

      if (error) {
        console.error('Error fetching conversations:', error);
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Failed to fetch conversations:', error);
      throw error;
    }
  }

  /**
   * Get or create a conversation between two users for a property
   */
  static async getOrCreateConversation(
    userId: string,
    otherUserId: string,
    propertyId?: string,
    bookingId?: string
  ): Promise<Conversation> {
    try {
      // First, try to find existing conversation
      let query = supabase
        .from('conversations')
        .select(`
          *,
          property:properties (
            id,
            title,
            city,
            province
          ),
          participant_1:users!participant_1_id (
            id,
            first_name,
            last_name,
            avatar_url
          ),
          participant_2:users!participant_2_id (
            id,
            first_name,
            last_name,
            avatar_url
          )
        `)
        .or(
          `and(participant_1_id.eq.${userId},participant_2_id.eq.${otherUserId}),and(participant_1_id.eq.${otherUserId},participant_2_id.eq.${userId})`
        );

      if (propertyId) {
        query = query.eq('property_id', propertyId);
      }

      const { data: existingConversations, error: fetchError } = await query;

      if (fetchError) {
        console.error('Error fetching existing conversation:', fetchError);
        throw fetchError;
      }

      if (existingConversations && existingConversations.length > 0) {
        return existingConversations[0];
      }

      // Create new conversation
      const { data: newConversation, error: createError } = await supabase
        .from('conversations')
        .insert({
          participant_1_id: userId,
          participant_2_id: otherUserId,
          property_id: propertyId || null,
          booking_id: bookingId || null,
        })
        .select(`
          *,
          property:properties (
            id,
            title,
            city,
            province
          ),
          participant_1:users!participant_1_id (
            id,
            first_name,
            last_name,
            avatar_url
          ),
          participant_2:users!participant_2_id (
            id,
            first_name,
            last_name,
            avatar_url
          )
        `)
        .single();

      if (createError) {
        console.error('Error creating conversation:', createError);
        throw createError;
      }

      return newConversation;
    } catch (error) {
      console.error('Failed to get or create conversation:', error);
      throw error;
    }
  }

  /**
   * Get messages for a conversation
   */
  static async getConversationMessages(
    conversationId: string,
    limit: number = 50,
    offset: number = 0
  ): Promise<ConversationMessage[]> {
    try {
      const { data, error } = await supabase
        .from('messages')
        .select(`
          *,
          sender:users!sender_id (
            id,
            first_name,
            last_name,
            avatar_url
          )
        `)
        .eq('conversation_id', conversationId)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (error) {
        console.error('Error fetching conversation messages:', error);
        throw error;
      }

      return (data || []).reverse(); // Reverse to show oldest first
    } catch (error) {
      console.error('Failed to fetch conversation messages:', error);
      throw error;
    }
  }

  /**
   * Send a message in a conversation
   */
  static async sendMessage(
    conversationId: string,
    senderId: string,
    recipientId: string,
    content: string,
    messageType: string = 'text'
  ): Promise<ConversationMessage> {
    try {
      const { data, error } = await supabase
        .from('messages')
        .insert({
          conversation_id: conversationId,
          sender_id: senderId,
          recipient_id: recipientId,
          content: content,
          message_type: messageType,
        })
        .select(`
          *,
          sender:users!sender_id (
            id,
            first_name,
            last_name,
            avatar_url
          )
        `)
        .single();

      if (error) {
        console.error('Error sending message:', error);
        throw error;
      }

      // Update conversation's last_message_at
      await supabase
        .from('conversations')
        .update({ last_message_at: new Date().toISOString() })
        .eq('id', conversationId);

      return data;
    } catch (error) {
      console.error('Failed to send message:', error);
      throw error;
    }
  }

  /**
   * Mark messages as read
   */
  static async markMessagesAsRead(conversationId: string, userId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('messages')
        .update({ is_read: true })
        .eq('conversation_id', conversationId)
        .eq('recipient_id', userId)
        .eq('is_read', false);

      if (error) {
        console.error('Error marking messages as read:', error);
        throw error;
      }
    } catch (error) {
      console.error('Failed to mark messages as read:', error);
      throw error;
    }
  }

  /**
   * Archive a conversation
   */
  static async archiveConversation(conversationId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('conversations')
        .update({ is_archived: true })
        .eq('id', conversationId);

      if (error) {
        console.error('Error archiving conversation:', error);
        throw error;
      }
    } catch (error) {
      console.error('Failed to archive conversation:', error);
      throw error;
    }
  }

  /**
   * Get unread message count for a user
   */
  static async getUnreadMessageCount(userId: string): Promise<number> {
    try {
      const { count, error } = await supabase
        .from('messages')
        .select('*', { count: 'exact', head: true })
        .eq('recipient_id', userId)
        .eq('is_read', false);

      if (error) {
        console.error('Error fetching unread message count:', error);
        return 0;
      }

      return count || 0;
    } catch (error) {
      console.error('Failed to fetch unread message count:', error);
      return 0;
    }
  }

  /**
   * Subscribe to real-time conversation updates
   */
  static subscribeToConversation(
    conversationId: string,
    onMessage: (message: ConversationMessage) => void
  ) {
    return supabase
      .channel(`conversation:${conversationId}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'messages',
          filter: `conversation_id=eq.${conversationId}`,
        },
        async (payload) => {
          // Fetch the complete message with sender info
          const { data } = await supabase
            .from('messages')
            .select(`
              *,
              sender:users!sender_id (
                id,
                first_name,
                last_name,
                avatar_url
              )
            `)
            .eq('id', payload.new.id)
            .single();

          if (data) {
            onMessage(data);
          }
        }
      )
      .subscribe();
  }
}
