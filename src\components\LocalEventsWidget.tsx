import React, { useState, useEffect } from 'react';
import { Calendar, MapPin, Clock, Users, Star, ExternalLink, Music, Camera, Utensils, Trophy, Heart } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { cn } from '@/lib/utils';

interface LocalEvent {
  id: string;
  title: string;
  description: string;
  category: 'music' | 'food' | 'culture' | 'sports' | 'art' | 'festival' | 'market' | 'tour';
  date: Date;
  endDate?: Date;
  time: string;
  venue: string;
  address: string;
  price: {
    min: number;
    max?: number;
    currency: string;
    isFree: boolean;
  };
  image: string;
  rating: number;
  attendees: number;
  organizer: string;
  website?: string;
  ticketUrl?: string;
  distance: number; // km from property
  featured: boolean;
  tags: string[];
}

interface LocalEventsWidgetProps {
  location: string;
  coordinates?: { lat: number; lon: number };
  maxEvents?: number;
  showCategories?: boolean;
  compact?: boolean;
  className?: string;
}

export const LocalEventsWidget: React.FC<LocalEventsWidgetProps> = ({
  location,
  coordinates,
  maxEvents = 6,
  showCategories = true,
  compact = false,
  className
}) => {
  const [events, setEvents] = useState<LocalEvent[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  useEffect(() => {
    fetchLocalEvents();
  }, [location, coordinates]);

  const fetchLocalEvents = async () => {
    try {
      setLoading(true);
      setError(null);

      // Mock events data for demonstration
      // In production, replace with actual events API call
      const mockEvents: LocalEvent[] = [
        {
          id: '1',
          title: 'Cape Town Jazz Festival',
          description: 'Annual international jazz festival featuring world-class musicians and local talent.',
          category: 'music',
          date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
          time: '18:00 - 23:00',
          venue: 'Cape Town International Convention Centre',
          address: 'Convention Square, Cape Town',
          price: { min: 350, max: 850, currency: 'ZAR', isFree: false },
          image: '/events/jazz-festival.jpg',
          rating: 4.8,
          attendees: 15000,
          organizer: 'Cape Town Events',
          website: 'https://capetownjazzfest.com',
          ticketUrl: 'https://tickets.capetownjazzfest.com',
          distance: 2.5,
          featured: true,
          tags: ['jazz', 'music', 'international', 'festival']
        },
        {
          id: '2',
          title: 'V&A Waterfront Night Market',
          description: 'Weekly night market with local crafts, food vendors, and live entertainment.',
          category: 'market',
          date: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000),
          time: '17:00 - 22:00',
          venue: 'V&A Waterfront Amphitheatre',
          address: 'V&A Waterfront, Cape Town',
          price: { min: 0, currency: 'ZAR', isFree: true },
          image: '/events/night-market.jpg',
          rating: 4.5,
          attendees: 2000,
          organizer: 'V&A Waterfront',
          distance: 1.8,
          featured: false,
          tags: ['market', 'food', 'crafts', 'family']
        },
        {
          id: '3',
          title: 'Table Mountain Sunset Hike',
          description: 'Guided sunset hike up Table Mountain with professional guide and refreshments.',
          category: 'tour',
          date: new Date(Date.now() + 1 * 24 * 60 * 60 * 1000),
          time: '16:00 - 20:00',
          venue: 'Table Mountain National Park',
          address: 'Tafelberg Road, Cape Town',
          price: { min: 450, currency: 'ZAR', isFree: false },
          image: '/events/table-mountain-hike.jpg',
          rating: 4.9,
          attendees: 25,
          organizer: 'Cape Adventures',
          website: 'https://capeadventures.co.za',
          distance: 3.2,
          featured: true,
          tags: ['hiking', 'sunset', 'nature', 'guided']
        },
        {
          id: '4',
          title: 'Stellenbosch Wine Tasting',
          description: 'Premium wine tasting experience at award-winning Stellenbosch winery.',
          category: 'food',
          date: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000),
          time: '10:00 - 16:00',
          venue: 'Klein Constantia Wine Estate',
          address: 'Klein Constantia Road, Constantia',
          price: { min: 180, max: 350, currency: 'ZAR', isFree: false },
          image: '/events/wine-tasting.jpg',
          rating: 4.7,
          attendees: 50,
          organizer: 'Klein Constantia',
          distance: 12.5,
          featured: false,
          tags: ['wine', 'tasting', 'vineyard', 'premium']
        },
        {
          id: '5',
          title: 'Kirstenbosch Summer Concert',
          description: 'Outdoor concert series in the beautiful Kirstenbosch Botanical Gardens.',
          category: 'music',
          date: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000),
          time: '17:30 - 21:00',
          venue: 'Kirstenbosch National Botanical Garden',
          address: 'Rhodes Drive, Newlands',
          price: { min: 120, max: 250, currency: 'ZAR', isFree: false },
          image: '/events/kirstenbosch-concert.jpg',
          rating: 4.6,
          attendees: 3000,
          organizer: 'Kirstenbosch',
          distance: 8.1,
          featured: false,
          tags: ['concert', 'outdoor', 'botanical', 'summer']
        },
        {
          id: '6',
          title: 'Zeitz Museum Art Exhibition',
          description: 'Contemporary African art exhibition featuring emerging and established artists.',
          category: 'art',
          date: new Date(Date.now() + 4 * 24 * 60 * 60 * 1000),
          time: '10:00 - 18:00',
          venue: 'Zeitz Museum of Contemporary African Art',
          address: 'Silo District, V&A Waterfront',
          price: { min: 200, currency: 'ZAR', isFree: false },
          image: '/events/zeitz-exhibition.jpg',
          rating: 4.4,
          attendees: 500,
          organizer: 'Zeitz MOCAA',
          distance: 2.1,
          featured: false,
          tags: ['art', 'contemporary', 'african', 'museum']
        }
      ];

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setEvents(mockEvents);
    } catch (err) {
      setError('Failed to fetch local events');
      console.error('Events API error:', err);
    } finally {
      setLoading(false);
    }
  };

  const categories = [
    { id: 'all', name: 'All Events', icon: Calendar },
    { id: 'music', name: 'Music', icon: Music },
    { id: 'food', name: 'Food & Drink', icon: Utensils },
    { id: 'art', name: 'Art & Culture', icon: Camera },
    { id: 'sports', name: 'Sports', icon: Trophy },
    { id: 'tour', name: 'Tours', icon: MapPin },
    { id: 'market', name: 'Markets', icon: Heart }
  ];

  const filteredEvents = events
    .filter(event => selectedCategory === 'all' || event.category === selectedCategory)
    .slice(0, maxEvents);

  const getCategoryIcon = (category: string) => {
    const iconClass = "h-4 w-4";
    switch (category) {
      case 'music': return <Music className={iconClass} />;
      case 'food': return <Utensils className={iconClass} />;
      case 'art': return <Camera className={iconClass} />;
      case 'sports': return <Trophy className={iconClass} />;
      case 'tour': return <MapPin className={iconClass} />;
      case 'market': return <Heart className={iconClass} />;
      default: return <Calendar className={iconClass} />;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'music': return 'bg-purple-100 text-purple-800';
      case 'food': return 'bg-orange-100 text-orange-800';
      case 'art': return 'bg-pink-100 text-pink-800';
      case 'sports': return 'bg-green-100 text-green-800';
      case 'tour': return 'bg-blue-100 text-blue-800';
      case 'market': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (date: Date) => {
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    if (date.toDateString() === today.toDateString()) {
      return 'Today';
    } else if (date.toDateString() === tomorrow.toDateString()) {
      return 'Tomorrow';
    } else {
      return date.toLocaleDateString('en-ZA', { 
        weekday: 'short', 
        month: 'short', 
        day: 'numeric' 
      });
    }
  };

  const formatPrice = (price: LocalEvent['price']) => {
    if (price.isFree) return 'Free';
    if (price.max) {
      return `R${price.min} - R${price.max}`;
    }
    return `R${price.min}`;
  };

  if (loading) {
    return (
      <Card className={cn("w-full", className)}>
        <CardHeader>
          <Skeleton className="h-6 w-48" />
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="flex gap-4">
                <Skeleton className="h-20 w-20 rounded-lg" />
                <div className="flex-1 space-y-2">
                  <Skeleton className="h-5 w-3/4" />
                  <Skeleton className="h-4 w-1/2" />
                  <Skeleton className="h-4 w-1/3" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error || events.length === 0) {
    return (
      <Card className={cn("w-full", className)}>
        <CardContent className="p-6 text-center">
          <Calendar className="h-12 w-12 mx-auto mb-4 text-gray-400" />
          <p className="text-gray-600">{error || 'No local events found'}</p>
        </CardContent>
      </Card>
    );
  }

  if (compact) {
    return (
      <div className={cn("space-y-3", className)}>
        <h3 className="font-semibold text-lg">Local Events</h3>
        {filteredEvents.slice(0, 3).map((event) => (
          <div key={event.id} className="flex items-center gap-3 p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow">
            <img 
              src={event.image} 
              alt={event.title}
              className="w-12 h-12 object-cover rounded-lg"
            />
            <div className="flex-1 min-w-0">
              <h4 className="font-medium truncate">{event.title}</h4>
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <span>{formatDate(event.date)}</span>
                <span>•</span>
                <span>{formatPrice(event.price)}</span>
              </div>
            </div>
            <Badge className={getCategoryColor(event.category)}>
              {getCategoryIcon(event.category)}
            </Badge>
          </div>
        ))}
      </div>
    );
  }

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Calendar className="h-5 w-5" />
          Local Events in {location}
        </CardTitle>
      </CardHeader>
      <CardContent>
        {showCategories && (
          <Tabs value={selectedCategory} onValueChange={setSelectedCategory} className="mb-6">
            <TabsList className="grid w-full grid-cols-4 lg:grid-cols-7">
              {categories.map((category) => {
                const Icon = category.icon;
                return (
                  <TabsTrigger 
                    key={category.id} 
                    value={category.id}
                    className="flex items-center gap-1 text-xs"
                  >
                    <Icon className="h-3 w-3" />
                    <span className="hidden sm:inline">{category.name}</span>
                  </TabsTrigger>
                );
              })}
            </TabsList>
          </Tabs>
        )}

        <div className="space-y-4">
          {filteredEvents.map((event) => (
            <div key={event.id} className="flex gap-4 p-4 border rounded-lg hover:shadow-md transition-shadow">
              <img 
                src={event.image} 
                alt={event.title}
                className="w-20 h-20 object-cover rounded-lg flex-shrink-0"
              />
              
              <div className="flex-1 min-w-0">
                <div className="flex items-start justify-between mb-2">
                  <div>
                    <h4 className="font-semibold text-lg mb-1">{event.title}</h4>
                    <div className="flex items-center gap-2 mb-2">
                      <Badge className={getCategoryColor(event.category)}>
                        {getCategoryIcon(event.category)}
                        <span className="ml-1 capitalize">{event.category}</span>
                      </Badge>
                      {event.featured && (
                        <Badge variant="secondary">Featured</Badge>
                      )}
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-semibold text-lg">{formatPrice(event.price)}</div>
                    {event.rating && (
                      <div className="flex items-center gap-1 text-sm text-gray-600">
                        <Star className="h-3 w-3 fill-current text-yellow-400" />
                        {event.rating}
                      </div>
                    )}
                  </div>
                </div>
                
                <p className="text-gray-600 text-sm mb-3 line-clamp-2">{event.description}</p>
                
                <div className="flex items-center gap-4 text-sm text-gray-600 mb-3">
                  <div className="flex items-center gap-1">
                    <Calendar className="h-4 w-4" />
                    {formatDate(event.date)}
                  </div>
                  <div className="flex items-center gap-1">
                    <Clock className="h-4 w-4" />
                    {event.time}
                  </div>
                  <div className="flex items-center gap-1">
                    <MapPin className="h-4 w-4" />
                    {event.distance}km away
                  </div>
                  <div className="flex items-center gap-1">
                    <Users className="h-4 w-4" />
                    {event.attendees.toLocaleString()}
                  </div>
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium">{event.venue}</div>
                    <div className="text-sm text-gray-500">{event.address}</div>
                  </div>
                  
                  <div className="flex gap-2">
                    {event.website && (
                      <Button variant="outline" size="sm" asChild>
                        <a href={event.website} target="_blank" rel="noopener noreferrer">
                          <ExternalLink className="h-4 w-4 mr-1" />
                          Info
                        </a>
                      </Button>
                    )}
                    {event.ticketUrl && (
                      <Button size="sm" asChild>
                        <a href={event.ticketUrl} target="_blank" rel="noopener noreferrer">
                          Get Tickets
                        </a>
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {events.length > maxEvents && (
          <div className="text-center mt-6">
            <Button variant="outline">
              View All Events ({events.length})
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
