import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { paymentService, PaymentData } from '../services/paymentService';
import { 
  CreditCard, 
  Shield,
  Lock,
  CheckCircle,
  AlertCircle,
  Loader2,
  DollarSign,
  Calendar,
  Users,
  MapPin
} from 'lucide-react';

interface PaymentFormProps {
  bookingId: string;
  bookingDetails: {
    property_title: string;
    check_in_date: string;
    check_out_date: string;
    guest_count: number;
    total_amount: number;
    cleaning_fee?: number;
    property_location?: string;
  };
  onPaymentSuccess?: (paymentData: PaymentData) => void;
  onPaymentCancel?: () => void;
  className?: string;
}

export const PaymentForm: React.FC<PaymentFormProps> = ({
  bookingId,
  bookingDetails,
  onPaymentSuccess,
  onPaymentCancel,
  className
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [paymentData, setPaymentData] = useState<PaymentData | null>(null);
  const [step, setStep] = useState<'review' | 'payment' | 'processing'>('review');

  const platformFee = paymentService.calculatePlatformFee(bookingDetails.total_amount);
  const hostAmount = paymentService.calculateHostAmount(bookingDetails.total_amount, platformFee);

  const handleCreatePayment = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const payment = await paymentService.createPayment(bookingId);
      setPaymentData(payment);
      setStep('payment');
      
      if (onPaymentSuccess) {
        onPaymentSuccess(payment);
      }
    } catch (err: any) {
      console.error('Error creating payment:', err);
      setError(err.message || 'Failed to create payment');
    } finally {
      setLoading(false);
    }
  };

  const handleProceedToPayment = () => {
    if (paymentData) {
      setStep('processing');
      // Redirect to PayFast
      paymentService.redirectToPayment(paymentData);
    }
  };

  const formatCurrency = (amount: number) => {
    return paymentService.formatCurrency(amount);
  };

  const formatDate = (dateString: string) => {
    return paymentService.formatDate(dateString);
  };

  const renderBookingReview = () => (
    <div className="space-y-6">
      {/* Booking Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MapPin className="h-5 w-5" />
            Booking Summary
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h3 className="font-semibold text-lg text-gray-900">
              {bookingDetails.property_title}
            </h3>
            {bookingDetails.property_location && (
              <p className="text-gray-600">{bookingDetails.property_location}</p>
            )}
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-gray-500" />
              <div>
                <p className="text-gray-600">Check-in</p>
                <p className="font-medium">{formatDate(bookingDetails.check_in_date)}</p>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-gray-500" />
              <div>
                <p className="text-gray-600">Check-out</p>
                <p className="font-medium">{formatDate(bookingDetails.check_out_date)}</p>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <Users className="h-4 w-4 text-gray-500" />
              <div>
                <p className="text-gray-600">Guests</p>
                <p className="font-medium">{bookingDetails.guest_count}</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Payment Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            Payment Breakdown
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="flex justify-between">
            <span className="text-gray-600">Accommodation</span>
            <span className="font-medium">{formatCurrency(bookingDetails.total_amount - (bookingDetails.cleaning_fee || 0))}</span>
          </div>
          
          {bookingDetails.cleaning_fee && bookingDetails.cleaning_fee > 0 && (
            <div className="flex justify-between">
              <span className="text-gray-600">Cleaning fee</span>
              <span className="font-medium">{formatCurrency(bookingDetails.cleaning_fee)}</span>
            </div>
          )}
          
          <div className="flex justify-between text-sm text-gray-500">
            <span>Platform fee (5%)</span>
            <span>{formatCurrency(platformFee)}</span>
          </div>
          
          <div className="border-t pt-3">
            <div className="flex justify-between text-lg font-bold">
              <span>Total</span>
              <span>{formatCurrency(bookingDetails.total_amount)}</span>
            </div>
          </div>
          
          <div className="text-xs text-gray-500 mt-2">
            <p>• Payment will be held in escrow until check-in</p>
            <p>• Host receives {formatCurrency(hostAmount)} after check-in</p>
            <p>• Full refund available if cancelled 7+ days before check-in</p>
          </div>
        </CardContent>
      </Card>

      {/* Security Information */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center gap-3">
            <Shield className="h-8 w-8 text-green-600" />
            <div>
              <h4 className="font-medium text-gray-900">Secure Payment</h4>
              <p className="text-sm text-gray-600">
                Your payment is processed securely through PayFast, South Africa's leading payment gateway
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="flex gap-3">
        <Button
          variant="outline"
          onClick={onPaymentCancel}
          className="flex-1"
        >
          Cancel
        </Button>
        <Button
          onClick={handleCreatePayment}
          disabled={loading}
          className="flex-1 bg-sea-green-500 hover:bg-sea-green-600 text-white"
        >
          {loading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Creating Payment...
            </>
          ) : (
            <>
              <Lock className="mr-2 h-4 w-4" />
              Proceed to Payment
            </>
          )}
        </Button>
      </div>
    </div>
  );

  const renderPaymentOptions = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            Payment Method
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="border rounded-lg p-4 bg-blue-50 border-blue-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-12 h-8 bg-blue-600 rounded flex items-center justify-center">
                    <span className="text-white text-xs font-bold">PF</span>
                  </div>
                  <div>
                    <h4 className="font-medium">PayFast</h4>
                    <p className="text-sm text-gray-600">Credit card, EFT, Instant EFT</p>
                  </div>
                </div>
                <Badge variant="default" className="bg-green-100 text-green-800">
                  Recommended
                </Badge>
              </div>
            </div>
            
            <div className="text-sm text-gray-600 space-y-2">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span>Secure 256-bit SSL encryption</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span>PCI DSS compliant</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span>Instant payment confirmation</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="flex gap-3">
        <Button
          variant="outline"
          onClick={() => setStep('review')}
          className="flex-1"
        >
          Back to Review
        </Button>
        <Button
          onClick={handleProceedToPayment}
          className="flex-1 bg-sea-green-500 hover:bg-sea-green-600 text-white"
        >
          <Lock className="mr-2 h-4 w-4" />
          Pay {formatCurrency(bookingDetails.total_amount)}
        </Button>
      </div>
    </div>
  );

  const renderProcessing = () => (
    <div className="text-center py-8">
      <Loader2 className="h-12 w-12 mx-auto mb-4 text-sea-green-500 animate-spin" />
      <h3 className="text-lg font-medium text-gray-900 mb-2">
        Redirecting to Payment Gateway
      </h3>
      <p className="text-gray-600 mb-4">
        Please wait while we redirect you to PayFast to complete your payment
      </p>
      <div className="text-sm text-gray-500">
        <p>• Do not close this window</p>
        <p>• You will be redirected back after payment</p>
      </div>
    </div>
  );

  return (
    <div className={className}>
      {error && (
        <Card className="mb-6 border-red-200 bg-red-50">
          <CardContent className="p-4">
            <div className="flex items-center gap-2 text-red-800">
              <AlertCircle className="h-5 w-5" />
              <span className="font-medium">Payment Error</span>
            </div>
            <p className="text-red-700 mt-1">{error}</p>
          </CardContent>
        </Card>
      )}

      {step === 'review' && renderBookingReview()}
      {step === 'payment' && renderPaymentOptions()}
      {step === 'processing' && renderProcessing()}
    </div>
  );
};
