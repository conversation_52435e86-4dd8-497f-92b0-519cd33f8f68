import { useState, useEffect, useCallback, useRef } from 'react';
import { debounce } from 'lodash';

export interface SearchSuggestion {
  id: string;
  text: string;
  type: 'location' | 'property' | 'recent' | 'popular';
  icon?: string;
  description?: string;
  coordinates?: [number, number];
  propertyCount?: number;
}

interface UseSearchSuggestionsOptions {
  minLength?: number;
  debounceMs?: number;
  maxSuggestions?: number;
  includeRecent?: boolean;
  includePopular?: boolean;
}

interface UseSearchSuggestionsReturn {
  suggestions: SearchSuggestion[];
  isLoading: boolean;
  error: string | null;
  clearSuggestions: () => void;
  addToRecent: (suggestion: SearchSuggestion) => void;
  removeFromRecent: (suggestionId: string) => void;
  clearRecent: () => void;
}

// Mock data for South African locations
const POPULAR_DESTINATIONS: SearchSuggestion[] = [
  {
    id: 'cape-town',
    text: 'Cape Town',
    type: 'popular',
    icon: '🏔️',
    description: 'Mother City with Table Mountain',
    coordinates: [-33.9249, 18.4241],
    propertyCount: 1250
  },
  {
    id: 'johannesburg',
    text: 'Johannesburg',
    type: 'popular',
    icon: '🏙️',
    description: 'City of Gold',
    coordinates: [-26.2041, 28.0473],
    propertyCount: 890
  },
  {
    id: 'durban',
    text: 'Durban',
    type: 'popular',
    icon: '🏖️',
    description: 'Golden Mile beaches',
    coordinates: [-29.8587, 31.0218],
    propertyCount: 650
  },
  {
    id: 'stellenbosch',
    text: 'Stellenbosch',
    type: 'popular',
    icon: '🍷',
    description: 'Wine country',
    coordinates: [-33.9321, 18.8602],
    propertyCount: 320
  },
  {
    id: 'hermanus',
    text: 'Hermanus',
    type: 'popular',
    icon: '🐋',
    description: 'Whale watching capital',
    coordinates: [-34.4187, 19.2345],
    propertyCount: 180
  },
  {
    id: 'knysna',
    text: 'Knysna',
    type: 'popular',
    icon: '🌊',
    description: 'Garden Route gem',
    coordinates: [-34.0361, 23.0471],
    propertyCount: 240
  }
];

const LOCATION_SUGGESTIONS: SearchSuggestion[] = [
  // Cape Town areas
  { id: 'camps-bay', text: 'Camps Bay', type: 'location', icon: '🏖️', description: 'Beachfront luxury', coordinates: [-33.9553, 18.3756], propertyCount: 85 },
  { id: 'waterfront', text: 'V&A Waterfront', type: 'location', icon: '⚓', description: 'Shopping and dining', coordinates: [-33.9017, 18.4197], propertyCount: 120 },
  { id: 'sea-point', text: 'Sea Point', type: 'location', icon: '🌊', description: 'Promenade views', coordinates: [-33.9248, 18.3917], propertyCount: 95 },
  { id: 'constantia', text: 'Constantia', type: 'location', icon: '🍇', description: 'Wine estates', coordinates: [-34.0145, 18.4241], propertyCount: 65 },
  
  // Johannesburg areas
  { id: 'sandton', text: 'Sandton', type: 'location', icon: '🏢', description: 'Business district', coordinates: [-26.1076, 28.0567], propertyCount: 180 },
  { id: 'rosebank', text: 'Rosebank', type: 'location', icon: '🛍️', description: 'Shopping hub', coordinates: [-26.1467, 28.0436], propertyCount: 95 },
  { id: 'melville', text: 'Melville', type: 'location', icon: '🎨', description: 'Bohemian quarter', coordinates: [-26.1849, 27.9876], propertyCount: 75 },
  
  // Durban areas
  { id: 'umhlanga', text: 'Umhlanga', type: 'location', icon: '🏖️', description: 'Upmarket beachfront', coordinates: [-29.7277, 31.0820], propertyCount: 110 },
  { id: 'berea', text: 'Berea', type: 'location', icon: '🌳', description: 'Historic suburb', coordinates: [-29.8478, 31.0103], propertyCount: 85 },
  
  // Garden Route
  { id: 'plettenberg-bay', text: 'Plettenberg Bay', type: 'location', icon: '🐬', description: 'Coastal paradise', coordinates: [-34.0527, 23.3716], propertyCount: 95 },
  { id: 'mossel-bay', text: 'Mossel Bay', type: 'location', icon: '🦭', description: 'Historic harbor', coordinates: [-34.1816, 22.1460], propertyCount: 70 },
  
  // Wine regions
  { id: 'franschhoek', text: 'Franschhoek', type: 'location', icon: '🥂', description: 'French wine valley', coordinates: [-33.9031, 19.1311], propertyCount: 45 },
  { id: 'paarl', text: 'Paarl', type: 'location', icon: '🍷', description: 'Wine and mountains', coordinates: [-33.7434, 18.9707], propertyCount: 55 }
];

export const useSearchSuggestions = (
  query: string,
  options: UseSearchSuggestionsOptions = {}
): UseSearchSuggestionsReturn => {
  const {
    minLength = 2,
    debounceMs = 300,
    maxSuggestions = 8,
    includeRecent = true,
    includePopular = true
  } = options;

  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [recentSearches, setRecentSearches] = useState<SearchSuggestion[]>([]);
  
  const abortControllerRef = useRef<AbortController | null>(null);

  // Load recent searches from localStorage
  useEffect(() => {
    try {
      const stored = localStorage.getItem('stayfinder_recent_searches');
      if (stored) {
        const parsed = JSON.parse(stored);
        setRecentSearches(parsed.slice(0, 5)); // Keep only last 5
      }
    } catch (error) {
      console.warn('Failed to load recent searches:', error);
    }
  }, []);

  // Save recent searches to localStorage
  const saveRecentSearches = useCallback((searches: SearchSuggestion[]) => {
    try {
      localStorage.setItem('stayfinder_recent_searches', JSON.stringify(searches));
    } catch (error) {
      console.warn('Failed to save recent searches:', error);
    }
  }, []);

  // Fetch suggestions from API (simulated)
  const fetchSuggestions = useCallback(async (searchQuery: string, signal: AbortSignal) => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 200));
    
    if (signal.aborted) return [];

    const normalizedQuery = searchQuery.toLowerCase().trim();
    
    // Filter location suggestions
    const locationMatches = LOCATION_SUGGESTIONS.filter(location =>
      location.text.toLowerCase().includes(normalizedQuery) ||
      location.description?.toLowerCase().includes(normalizedQuery)
    );

    // Filter popular destinations
    const popularMatches = POPULAR_DESTINATIONS.filter(destination =>
      destination.text.toLowerCase().includes(normalizedQuery) ||
      destination.description?.toLowerCase().includes(normalizedQuery)
    );

    // Combine and sort by relevance
    const allMatches = [...locationMatches, ...popularMatches];
    
    // Sort by exact match first, then by property count
    allMatches.sort((a, b) => {
      const aExact = a.text.toLowerCase().startsWith(normalizedQuery) ? 1 : 0;
      const bExact = b.text.toLowerCase().startsWith(normalizedQuery) ? 1 : 0;
      
      if (aExact !== bExact) return bExact - aExact;
      
      return (b.propertyCount || 0) - (a.propertyCount || 0);
    });

    return allMatches.slice(0, maxSuggestions);
  }, [maxSuggestions]);

  // Debounced search function
  const debouncedSearch = useCallback(
    debounce(async (searchQuery: string) => {
      if (searchQuery.length < minLength) {
        setSuggestions([]);
        setIsLoading(false);
        return;
      }

      // Cancel previous request
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }

      // Create new abort controller
      abortControllerRef.current = new AbortController();
      const signal = abortControllerRef.current.signal;

      setIsLoading(true);
      setError(null);

      try {
        const results = await fetchSuggestions(searchQuery, signal);
        
        if (!signal.aborted) {
          let finalSuggestions = [...results];

          // Add recent searches if enabled and query is short
          if (includeRecent && searchQuery.length <= 3 && recentSearches.length > 0) {
            const recentMatches = recentSearches.filter(recent =>
              recent.text.toLowerCase().includes(searchQuery.toLowerCase())
            );
            finalSuggestions = [...recentMatches, ...finalSuggestions];
          }

          // Add popular destinations if no query or short query
          if (includePopular && searchQuery.length <= 2) {
            const popularToAdd = POPULAR_DESTINATIONS.filter(popular =>
              !finalSuggestions.some(s => s.id === popular.id)
            ).slice(0, 3);
            finalSuggestions = [...finalSuggestions, ...popularToAdd];
          }

          // Limit total suggestions
          finalSuggestions = finalSuggestions.slice(0, maxSuggestions);

          setSuggestions(finalSuggestions);
        }
      } catch (err) {
        if (!signal.aborted) {
          setError('Failed to fetch suggestions');
          console.error('Search suggestions error:', err);
        }
      } finally {
        if (!signal.aborted) {
          setIsLoading(false);
        }
      }
    }, debounceMs),
    [minLength, debounceMs, maxSuggestions, includeRecent, includePopular, recentSearches, fetchSuggestions]
  );

  // Effect to trigger search when query changes
  useEffect(() => {
    if (query.trim() === '') {
      setSuggestions([]);
      setIsLoading(false);
      return;
    }

    debouncedSearch(query);

    // Cleanup function
    return () => {
      debouncedSearch.cancel();
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [query, debouncedSearch]);

  // Clear suggestions
  const clearSuggestions = useCallback(() => {
    setSuggestions([]);
    setIsLoading(false);
    setError(null);
    debouncedSearch.cancel();
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
  }, [debouncedSearch]);

  // Add to recent searches
  const addToRecent = useCallback((suggestion: SearchSuggestion) => {
    const newRecent = [
      { ...suggestion, type: 'recent' as const },
      ...recentSearches.filter(r => r.id !== suggestion.id)
    ].slice(0, 5);
    
    setRecentSearches(newRecent);
    saveRecentSearches(newRecent);
  }, [recentSearches, saveRecentSearches]);

  // Remove from recent searches
  const removeFromRecent = useCallback((suggestionId: string) => {
    const newRecent = recentSearches.filter(r => r.id !== suggestionId);
    setRecentSearches(newRecent);
    saveRecentSearches(newRecent);
  }, [recentSearches, saveRecentSearches]);

  // Clear recent searches
  const clearRecent = useCallback(() => {
    setRecentSearches([]);
    localStorage.removeItem('stayfinder_recent_searches');
  }, []);

  return {
    suggestions,
    isLoading,
    error,
    clearSuggestions,
    addToRecent,
    removeFromRecent,
    clearRecent
  };
};
