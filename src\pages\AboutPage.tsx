import React from 'react';
import { Heart, Users, Shield, Award, MapPin, Star, Zap, Globe } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

export const AboutPage: React.FC = () => {
  const stats = [
    { icon: Users, label: 'Happy Guests', value: '50,000+' },
    { icon: MapPin, label: 'Properties Listed', value: '5,000+' },
    { icon: Star, label: 'Average Rating', value: '4.8/5' },
    { icon: Award, label: 'Years of Service', value: '5+' }
  ];

  const values = [
    {
      icon: Heart,
      title: 'Passion for Travel',
      description: 'We believe travel enriches lives and creates lasting memories. Our platform connects travelers with unique accommodations across South Africa.'
    },
    {
      icon: Shield,
      title: 'Trust & Safety',
      description: 'Your safety is our priority. We verify all properties and hosts, ensuring secure transactions and reliable accommodations.'
    },
    {
      icon: Zap,
      title: 'Innovation',
      description: 'We continuously improve our platform with cutting-edge technology to provide the best booking experience.'
    },
    {
      icon: Globe,
      title: 'Local Expertise',
      description: 'As a proudly South African company, we understand local travel needs and showcase the best our country has to offer.'
    }
  ];

  const team = [
    {
      name: '<PERSON>',
      role: 'CEO & Founder',
      image: '/images/team/sarah.jpg',
      bio: 'Travel enthusiast with 15+ years in hospitality industry'
    },
    {
      name: 'Michael Chen',
      role: 'CTO',
      image: '/images/team/michael.jpg',
      bio: 'Tech innovator passionate about creating seamless user experiences'
    },
    {
      name: 'Nomsa Mthembu',
      role: 'Head of Operations',
      image: '/images/team/nomsa.jpg',
      bio: 'Operations expert ensuring quality service across all properties'
    },
    {
      name: 'David Williams',
      role: 'Head of Marketing',
      image: '/images/team/david.jpg',
      bio: 'Marketing strategist connecting travelers with perfect stays'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-sea-green-600 via-ocean-blue-600 to-sea-green-700 text-white py-20">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-5xl font-bold mb-6">About StayFinder</h1>
          <p className="text-xl max-w-3xl mx-auto mb-8 opacity-90">
            We're passionate about connecting travelers with extraordinary accommodations across South Africa. 
            From luxury villas in Cape Town to cozy guesthouses in the Drakensberg, we help you find your perfect stay.
          </p>
          <div className="flex justify-center gap-4">
            <Button size="lg" variant="secondary">
              Start Your Journey
            </Button>
            <Button size="lg" variant="outline" className="text-white border-white hover:bg-white hover:text-sea-green-600">
              List Your Property
            </Button>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 bg-sea-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <stat.icon className="h-8 w-8 text-sea-green-600" />
                </div>
                <div className="text-3xl font-bold text-gray-900 mb-2">{stat.value}</div>
                <div className="text-gray-600">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Story Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-center mb-12">Our Story</h2>
            <div className="grid md:grid-cols-2 gap-12 items-center">
              <div>
                <h3 className="text-2xl font-semibold mb-6">Born from a Love of Travel</h3>
                <p className="text-gray-600 mb-6">
                  StayFinder was founded in 2019 by a group of travel enthusiasts who experienced firsthand 
                  the challenges of finding quality accommodations in South Africa. We noticed a gap in the 
                  market for a platform that truly understood local needs and showcased the incredible 
                  diversity of our country's hospitality offerings.
                </p>
                <p className="text-gray-600 mb-6">
                  Starting with just 50 properties in Cape Town, we've grown to become South Africa's 
                  trusted accommodation booking platform, featuring thousands of verified properties 
                  from the bustling streets of Johannesburg to the pristine beaches of the Garden Route.
                </p>
                <p className="text-gray-600">
                  Today, we're proud to support local hosts and property owners while helping travelers 
                  discover the magic of South African hospitality.
                </p>
              </div>
              <div className="relative">
                <img
                  src="/images/about/story.jpg"
                  alt="StayFinder Story"
                  className="rounded-lg shadow-lg w-full h-80 object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-lg"></div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12">Our Values</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <Card key={index} className="text-center hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="w-16 h-16 bg-sea-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <value.icon className="h-8 w-8 text-sea-green-600" />
                  </div>
                  <h3 className="text-xl font-semibold mb-3">{value.title}</h3>
                  <p className="text-gray-600 text-sm">{value.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12">Meet Our Team</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {team.map((member, index) => (
              <Card key={index} className="text-center hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="w-24 h-24 bg-gray-200 rounded-full mx-auto mb-4 overflow-hidden">
                    <img
                      src={member.image}
                      alt={member.name}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(member.name)}&background=059669&color=fff&size=96`;
                      }}
                    />
                  </div>
                  <h3 className="text-xl font-semibold mb-1">{member.name}</h3>
                  <p className="text-sea-green-600 font-medium mb-3">{member.role}</p>
                  <p className="text-gray-600 text-sm">{member.bio}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Mission Section */}
      <section className="py-16 bg-sea-green-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-8">Our Mission</h2>
          <p className="text-xl max-w-4xl mx-auto mb-8 opacity-90">
            To make travel accessible, safe, and memorable for everyone by connecting travelers with 
            exceptional accommodations and supporting local hosts in sharing the beauty of South Africa 
            with the world.
          </p>
          <div className="grid md:grid-cols-3 gap-8 mt-12">
            <div>
              <h3 className="text-xl font-semibold mb-3">For Travelers</h3>
              <p className="opacity-90">
                Discover unique stays, enjoy seamless booking, and create unforgettable memories 
                across South Africa.
              </p>
            </div>
            <div>
              <h3 className="text-xl font-semibold mb-3">For Hosts</h3>
              <p className="opacity-90">
                Grow your hospitality business with our platform, tools, and support to reach 
                travelers worldwide.
              </p>
            </div>
            <div>
              <h3 className="text-xl font-semibold mb-3">For Communities</h3>
              <p className="opacity-90">
                Support local economies and showcase the diverse beauty and culture of 
                South African destinations.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-6">Ready to Start Your Journey?</h2>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            Join thousands of travelers who have discovered their perfect stays through StayFinder. 
            Your next adventure awaits!
          </p>
          <div className="flex justify-center gap-4">
            <Button size="lg" className="bg-sea-green-600 hover:bg-sea-green-700">
              Find Your Stay
            </Button>
            <Button size="lg" variant="outline">
              Become a Host
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
};
