// StayFinder Brand Guidelines and Design System
export const brandGuidelines = {
  // Brand Identity
  brand: {
    name: 'StayFinder',
    tagline: 'Your perfect stay awaits',
    mission: 'Connecting travelers with unique accommodations across South Africa',
    values: ['Trust', 'Quality', 'Local Experience', 'Accessibility', 'Innovation']
  },

  // Logo Specifications
  logo: {
    primary: {
      svg: '/logo/stayfinder-primary.svg',
      png: '/logo/stayfinder-primary.png',
      usage: 'Primary logo for light backgrounds',
      minWidth: '120px',
      clearSpace: '20px'
    },
    white: {
      svg: '/logo/stayfinder-white.svg',
      png: '/logo/stayfinder-white.png',
      usage: 'White logo for dark backgrounds',
      minWidth: '120px',
      clearSpace: '20px'
    },
    icon: {
      svg: '/logo/stayfinder-icon.svg',
      png: '/logo/stayfinder-icon.png',
      usage: 'Icon only for small spaces',
      minSize: '24px',
      clearSpace: '8px'
    },
    favicon: {
      ico: '/favicon.ico',
      svg: '/favicon.svg',
      sizes: ['16x16', '32x32', '48x48', '64x64']
    }
  },

  // Color Palette
  colors: {
    primary: {
      'sea-green': {
        50: '#f0fdfa',
        100: '#ccfbf1',
        200: '#99f6e4',
        300: '#5eead4',
        400: '#2dd4bf',
        500: '#14b8a6', // Primary brand color
        600: '#0d9488',
        700: '#0f766e',
        800: '#115e59',
        900: '#134e4a'
      },
      'ocean-blue': {
        50: '#eff6ff',
        100: '#dbeafe',
        200: '#bfdbfe',
        300: '#93c5fd',
        400: '#60a5fa',
        500: '#3b82f6', // Secondary brand color
        600: '#2563eb',
        700: '#1d4ed8',
        800: '#1e40af',
        900: '#1e3a8a'
      }
    },
    neutral: {
      white: '#ffffff',
      gray: {
        50: '#f9fafb',
        100: '#f3f4f6',
        200: '#e5e7eb',
        300: '#d1d5db',
        400: '#9ca3af',
        500: '#6b7280',
        600: '#4b5563',
        700: '#374151',
        800: '#1f2937',
        900: '#111827'
      },
      black: '#000000'
    },
    semantic: {
      success: '#10b981',
      warning: '#f59e0b',
      error: '#ef4444',
      info: '#3b82f6'
    }
  },

  // Typography System
  typography: {
    fontFamilies: {
      sans: ['Inter', 'system-ui', 'sans-serif'],
      serif: ['Merriweather', 'Georgia', 'serif'],
      mono: ['JetBrains Mono', 'Consolas', 'monospace']
    },
    fontSizes: {
      xs: '0.75rem',    // 12px
      sm: '0.875rem',   // 14px
      base: '1rem',     // 16px
      lg: '1.125rem',   // 18px
      xl: '1.25rem',    // 20px
      '2xl': '1.5rem',  // 24px
      '3xl': '1.875rem', // 30px
      '4xl': '2.25rem', // 36px
      '5xl': '3rem',    // 48px
      '6xl': '3.75rem', // 60px
      '7xl': '4.5rem',  // 72px
      '8xl': '6rem',    // 96px
      '9xl': '8rem'     // 128px
    },
    fontWeights: {
      thin: '100',
      extralight: '200',
      light: '300',
      normal: '400',
      medium: '500',
      semibold: '600',
      bold: '700',
      extrabold: '800',
      black: '900'
    },
    lineHeights: {
      none: '1',
      tight: '1.25',
      snug: '1.375',
      normal: '1.5',
      relaxed: '1.625',
      loose: '2'
    },
    letterSpacing: {
      tighter: '-0.05em',
      tight: '-0.025em',
      normal: '0em',
      wide: '0.025em',
      wider: '0.05em',
      widest: '0.1em'
    }
  },

  // Spacing System
  spacing: {
    scale: {
      0: '0px',
      1: '0.25rem',  // 4px
      2: '0.5rem',   // 8px
      3: '0.75rem',  // 12px
      4: '1rem',     // 16px
      5: '1.25rem',  // 20px
      6: '1.5rem',   // 24px
      8: '2rem',     // 32px
      10: '2.5rem',  // 40px
      12: '3rem',    // 48px
      16: '4rem',    // 64px
      20: '5rem',    // 80px
      24: '6rem',    // 96px
      32: '8rem',    // 128px
      40: '10rem',   // 160px
      48: '12rem',   // 192px
      56: '14rem',   // 224px
      64: '16rem'    // 256px
    },
    containers: {
      sm: '640px',
      md: '768px',
      lg: '1024px',
      xl: '1280px',
      '2xl': '1536px'
    }
  },

  // Border Radius System
  borderRadius: {
    none: '0px',
    sm: '0.125rem',   // 2px
    base: '0.25rem',  // 4px
    md: '0.375rem',   // 6px
    lg: '0.5rem',     // 8px
    xl: '0.75rem',    // 12px
    '2xl': '1rem',    // 16px
    '3xl': '1.5rem',  // 24px
    full: '9999px'
  },

  // Shadow System
  shadows: {
    sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
    base: '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
    md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
    lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
    xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
    '2xl': '0 25px 50px -12px rgb(0 0 0 / 0.25)',
    inner: 'inset 0 2px 4px 0 rgb(0 0 0 / 0.05)',
    none: '0 0 #0000'
  },

  // Icon System
  icons: {
    sizes: {
      xs: '12px',
      sm: '16px',
      base: '20px',
      lg: '24px',
      xl: '32px',
      '2xl': '48px'
    },
    library: 'Lucide React',
    style: 'Outline',
    strokeWidth: '1.5px'
  },

  // Component Specifications
  components: {
    buttons: {
      sizes: {
        sm: { padding: '8px 12px', fontSize: '14px', borderRadius: '6px' },
        md: { padding: '10px 16px', fontSize: '16px', borderRadius: '8px' },
        lg: { padding: '12px 20px', fontSize: '18px', borderRadius: '10px' },
        xl: { padding: '16px 24px', fontSize: '20px', borderRadius: '12px' }
      },
      variants: {
        primary: { bg: 'sea-green-600', text: 'white', hover: 'sea-green-700' },
        secondary: { bg: 'gray-200', text: 'gray-900', hover: 'gray-300' },
        outline: { bg: 'transparent', text: 'sea-green-600', border: 'sea-green-600' },
        ghost: { bg: 'transparent', text: 'gray-600', hover: 'gray-100' },
        destructive: { bg: 'red-600', text: 'white', hover: 'red-700' }
      }
    },
    cards: {
      padding: '24px',
      borderRadius: '12px',
      shadow: 'md',
      border: '1px solid rgb(229 231 235)'
    },
    inputs: {
      height: '44px',
      padding: '12px 16px',
      borderRadius: '8px',
      border: '1px solid rgb(209 213 219)',
      fontSize: '16px'
    }
  },

  // Animation Guidelines
  animations: {
    durations: {
      fast: '150ms',
      normal: '300ms',
      slow: '500ms'
    },
    easings: {
      easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
      easeOut: 'cubic-bezier(0, 0, 0.2, 1)',
      easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)'
    },
    principles: [
      'Animations should feel natural and purposeful',
      'Use consistent timing and easing',
      'Respect user preferences for reduced motion',
      'Provide feedback for user actions'
    ]
  },

  // Accessibility Guidelines
  accessibility: {
    colorContrast: {
      normal: '4.5:1',
      large: '3:1'
    },
    focusIndicators: {
      color: 'sea-green-500',
      width: '2px',
      style: 'solid',
      offset: '2px'
    },
    touchTargets: {
      minSize: '44px',
      spacing: '8px'
    },
    principles: [
      'Ensure sufficient color contrast',
      'Provide keyboard navigation',
      'Include alt text for images',
      'Use semantic HTML elements',
      'Support screen readers'
    ]
  },

  // Usage Guidelines
  usage: {
    logo: {
      dos: [
        'Use the primary logo on light backgrounds',
        'Maintain minimum clear space',
        'Use approved color variations',
        'Ensure legibility at all sizes'
      ],
      donts: [
        'Stretch or distort the logo',
        'Use unapproved colors',
        'Place on busy backgrounds',
        'Use below minimum size'
      ]
    },
    colors: {
      dos: [
        'Use sea-green as primary brand color',
        'Maintain color hierarchy',
        'Ensure accessibility compliance',
        'Use semantic colors appropriately'
      ],
      donts: [
        'Use colors outside the palette',
        'Ignore contrast requirements',
        'Overuse bright colors',
        'Mix warm and cool tones randomly'
      ]
    },
    typography: {
      dos: [
        'Use Inter for UI text',
        'Maintain consistent hierarchy',
        'Ensure readable line heights',
        'Use appropriate font weights'
      ],
      donts: [
        'Use too many font families',
        'Ignore responsive sizing',
        'Use insufficient contrast',
        'Overcrowd text elements'
      ]
    }
  },

  // Brand Voice & Tone
  voice: {
    personality: ['Friendly', 'Professional', 'Trustworthy', 'Helpful', 'Local'],
    tone: {
      formal: 'Professional but approachable',
      casual: 'Warm and conversational',
      error: 'Helpful and reassuring',
      success: 'Celebratory but humble'
    },
    language: {
      dos: [
        'Use clear, simple language',
        'Be inclusive and welcoming',
        'Highlight local experiences',
        'Focus on benefits to users'
      ],
      donts: [
        'Use jargon or technical terms',
        'Be overly promotional',
        'Make assumptions about users',
        'Use negative language'
      ]
    }
  }
};

// Export individual sections for easier imports
export const { colors, typography, spacing, shadows, components } = brandGuidelines;
export default brandGuidelines;
