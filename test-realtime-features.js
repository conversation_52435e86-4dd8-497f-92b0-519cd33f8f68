#!/usr/bin/env node

/**
 * Supabase Real-time Features Test
 * Tests real-time subscriptions and live updates
 */

import { createClient } from '@supabase/supabase-js';
import { config } from 'dotenv';
config();

// ANSI color codes
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, colors.green);
}

function logError(message) {
  log(`❌ ${message}`, colors.red);
}

function logWarning(message) {
  log(`⚠️  ${message}`, colors.yellow);
}

function logInfo(message) {
  log(`ℹ️  ${message}`, colors.blue);
}

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

// Test results
const testResults = {
  connectionTest: false,
  subscriptionTest: false,
  dataChangeTest: false,
  cleanupTest: false
};

// Test 1: Real-time Connection
async function testRealtimeConnection() {
  log('\n🔄 Testing Real-time Connection...', colors.cyan);
  
  return new Promise((resolve) => {
    const channel = supabase.channel('connection-test');
    
    const timeout = setTimeout(() => {
      logError('Real-time connection timeout (30s)');
      channel.unsubscribe();
      resolve(false);
    }, 30000);

    channel
      .on('presence', { event: 'sync' }, () => {
        logSuccess('Real-time connection established');
        clearTimeout(timeout);
        channel.unsubscribe();
        resolve(true);
      })
      .on('presence', { event: 'join' }, () => {
        logInfo('Presence join event received');
      })
      .on('presence', { event: 'leave' }, () => {
        logInfo('Presence leave event received');
      })
      .subscribe((status) => {
        logInfo(`Subscription status: ${status}`);
        if (status === 'SUBSCRIBED') {
          logSuccess('Successfully subscribed to real-time channel');
          // Track presence to trigger sync event
          channel.track({ user: 'test-user', timestamp: Date.now() });
        } else if (status === 'CHANNEL_ERROR') {
          logError('Channel subscription error');
          clearTimeout(timeout);
          resolve(false);
        }
      });
  });
}

// Test 2: Database Change Subscriptions
async function testDatabaseSubscriptions() {
  log('\n📊 Testing Database Change Subscriptions...', colors.cyan);
  
  // First, check if we have any tables to subscribe to
  const { data: tables, error: tablesError } = await supabase
    .from('information_schema.tables')
    .select('table_name')
    .eq('table_schema', 'public')
    .limit(5);

  if (tablesError || !tables || tables.length === 0) {
    logWarning('No public tables found - creating test table for subscription test');
    
    // Create a temporary test table
    const { error: createError } = await supabase.rpc('create_test_table', {});
    
    if (createError) {
      // Try creating table with raw SQL
      const createTableSQL = `
        CREATE TABLE IF NOT EXISTS realtime_test (
          id SERIAL PRIMARY KEY,
          message TEXT,
          created_at TIMESTAMP DEFAULT NOW()
        );
        
        -- Enable real-time for the table
        ALTER PUBLICATION supabase_realtime ADD TABLE realtime_test;
      `;
      
      logInfo('Creating test table for real-time testing...');
      // Note: This would need to be run in the Supabase dashboard SQL editor
      logWarning('Please run the following SQL in your Supabase dashboard:');
      console.log(createTableSQL);
      return false;
    }
  }

  return new Promise((resolve) => {
    let changeReceived = false;
    const testTableName = 'realtime_test';
    
    const timeout = setTimeout(() => {
      if (!changeReceived) {
        logWarning('No real-time changes detected within 30 seconds');
        logInfo('This might be because:');
        logInfo('1. Real-time is not enabled for tables');
        logInfo('2. Tables don\'t exist yet');
        logInfo('3. Real-time publication is not configured');
      }
      subscription.unsubscribe();
      resolve(changeReceived);
    }, 30000);

    const subscription = supabase
      .channel('db-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: testTableName
        },
        (payload) => {
          logSuccess(`Real-time change detected: ${payload.eventType}`);
          logInfo(`Change details: ${JSON.stringify(payload, null, 2)}`);
          changeReceived = true;
          clearTimeout(timeout);
          subscription.unsubscribe();
          resolve(true);
        }
      )
      .subscribe((status) => {
        logInfo(`Database subscription status: ${status}`);
        
        if (status === 'SUBSCRIBED') {
          logSuccess('Successfully subscribed to database changes');
          
          // Insert a test record to trigger the subscription
          setTimeout(async () => {
            logInfo('Inserting test record to trigger real-time event...');
            
            const { error: insertError } = await supabase
              .from(testTableName)
              .insert({ message: `Test message ${Date.now()}` });
            
            if (insertError) {
              logWarning(`Could not insert test record: ${insertError.message}`);
              logInfo('This is expected if the table doesn\'t exist yet');
            } else {
              logSuccess('Test record inserted');
            }
          }, 2000);
        } else if (status === 'CHANNEL_ERROR') {
          logError('Database subscription error');
          clearTimeout(timeout);
          resolve(false);
        }
      });
  });
}

// Test 3: Real-time Configuration Check
async function testRealtimeConfiguration() {
  log('\n⚙️  Testing Real-time Configuration...', colors.cyan);
  
  try {
    // Check if real-time is enabled in environment
    const realtimeEnabled = process.env.SUPABASE_REALTIME_ENABLED === 'true';
    
    if (realtimeEnabled) {
      logSuccess('Real-time is enabled in environment configuration');
    } else {
      logWarning('Real-time is disabled in environment configuration');
    }

    // Check heartbeat interval
    const heartbeatInterval = process.env.SUPABASE_REALTIME_HEARTBEAT_INTERVAL;
    if (heartbeatInterval) {
      logSuccess(`Heartbeat interval configured: ${heartbeatInterval}ms`);
    } else {
      logInfo('Using default heartbeat interval');
    }

    // Check configured channels
    const channels = [
      process.env.SUPABASE_REALTIME_BOOKINGS_CHANNEL,
      process.env.SUPABASE_REALTIME_MESSAGES_CHANNEL,
      process.env.SUPABASE_REALTIME_PROPERTIES_CHANNEL
    ].filter(Boolean);

    if (channels.length > 0) {
      logSuccess(`Configured channels: ${channels.join(', ')}`);
    } else {
      logWarning('No specific channels configured');
    }

    return true;
  } catch (error) {
    logError(`Configuration check error: ${error.message}`);
    return false;
  }
}

// Test 4: Cleanup Test Data
async function cleanupTestData() {
  log('\n🧹 Cleaning up test data...', colors.cyan);
  
  try {
    // Try to clean up any test records
    const { error: deleteError } = await supabase
      .from('realtime_test')
      .delete()
      .like('message', 'Test message%');
    
    if (deleteError) {
      if (deleteError.message.includes('relation "realtime_test" does not exist')) {
        logInfo('No test table to clean up');
      } else {
        logWarning(`Cleanup warning: ${deleteError.message}`);
      }
    } else {
      logSuccess('Test data cleaned up');
    }

    return true;
  } catch (error) {
    logWarning(`Cleanup error: ${error.message}`);
    return false;
  }
}

// Main test runner
async function runRealtimeTests() {
  log('🔄 Starting Real-time Features Tests...', colors.bright);
  log('='.repeat(50), colors.cyan);

  const startTime = Date.now();

  // Run tests
  testResults.connectionTest = await testRealtimeConnection();
  await testRealtimeConfiguration();
  testResults.subscriptionTest = await testDatabaseSubscriptions();
  testResults.cleanupTest = await cleanupTestData();

  const endTime = Date.now();
  const duration = ((endTime - startTime) / 1000).toFixed(2);

  // Print summary
  log('\n' + '='.repeat(50), colors.cyan);
  log('📊 Real-time Test Summary', colors.bright);
  log('='.repeat(50), colors.cyan);
  
  const passed = Object.values(testResults).filter(Boolean).length;
  const total = Object.keys(testResults).length;
  
  if (testResults.connectionTest) {
    logSuccess('Real-time connection: PASSED');
  } else {
    logError('Real-time connection: FAILED');
  }
  
  if (testResults.subscriptionTest) {
    logSuccess('Database subscriptions: PASSED');
  } else {
    logWarning('Database subscriptions: NEEDS SETUP');
  }
  
  logInfo(`Duration: ${duration}s`);
  logInfo(`Tests passed: ${passed}/${total}`);

  // Provide setup instructions if needed
  if (!testResults.subscriptionTest) {
    log('\n📋 Setup Instructions for Real-time:', colors.yellow);
    log('1. Go to your Supabase dashboard');
    log('2. Navigate to Database > Replication');
    log('3. Enable real-time for your tables');
    log('4. Or run this SQL in the SQL editor:');
    log('   ALTER PUBLICATION supabase_realtime ADD TABLE your_table_name;');
  }

  if (testResults.connectionTest) {
    log('\n✅ Real-time connection is working!', colors.green);
  } else {
    log('\n❌ Real-time connection failed. Check your Supabase configuration.', colors.red);
  }
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runRealtimeTests().catch(error => {
    logError(`Real-time test error: ${error.message}`);
    console.error(error);
    process.exit(1);
  });
}

export {
  runRealtimeTests,
  testResults
};
