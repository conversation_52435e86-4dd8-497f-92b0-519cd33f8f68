import React, { useState, useEffect } from 'react';
import { Header } from '../components/Header';
import { BookingCard } from '../components/BookingCard';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useBooking } from '../contexts/BookingContext';
import { useAuth } from '../contexts/AuthContext';
import { 
  Calendar, 
  Clock, 
  CheckCircle, 
  XCircle, 
  Loader2,
  AlertCircle,
  Filter,
  Search
} from 'lucide-react';

export const Bookings: React.FC = () => {
  const [filter, setFilter] = useState<'all' | 'upcoming' | 'past' | 'pending' | 'confirmed' | 'cancelled'>('all');
  const [loading, setLoading] = useState(true);
  
  const { isAuthenticated, user } = useAuth();
  const { 
    userBookings, 
    upcomingBookings, 
    getUserBookings, 
    getUpcomingBookings,
    loading: bookingLoading,
    error 
  } = useBooking();

  useEffect(() => {
    if (isAuthenticated) {
      const fetchBookings = async () => {
        setLoading(true);
        try {
          await Promise.all([
            getUserBookings(),
            getUpcomingBookings()
          ]);
        } catch (err) {
          console.error('Failed to fetch bookings:', err);
        } finally {
          setLoading(false);
        }
      };
      fetchBookings();
    }
  }, [isAuthenticated, getUserBookings, getUpcomingBookings]);

  const handleBookingUpdate = async () => {
    // Refresh bookings after an update
    await getUserBookings();
    await getUpcomingBookings();
  };

  const getFilteredBookings = () => {
    const now = new Date();
    
    switch (filter) {
      case 'upcoming':
        return userBookings.filter(booking => 
          new Date(booking.checkInDate) > now && 
          booking.bookingStatus !== 'cancelled'
        );
      case 'past':
        return userBookings.filter(booking => 
          new Date(booking.checkOutDate) < now
        );
      case 'pending':
        return userBookings.filter(booking => booking.bookingStatus === 'pending');
      case 'confirmed':
        return userBookings.filter(booking => booking.bookingStatus === 'confirmed');
      case 'cancelled':
        return userBookings.filter(booking => booking.bookingStatus === 'cancelled');
      default:
        return userBookings;
    }
  };

  const getBookingStats = () => {
    const now = new Date();
    const upcoming = userBookings.filter(booking => 
      new Date(booking.checkInDate) > now && 
      booking.bookingStatus !== 'cancelled'
    ).length;
    const pending = userBookings.filter(booking => booking.bookingStatus === 'pending').length;
    const confirmed = userBookings.filter(booking => booking.bookingStatus === 'confirmed').length;
    const cancelled = userBookings.filter(booking => booking.bookingStatus === 'cancelled').length;
    
    return { upcoming, pending, confirmed, cancelled, total: userBookings.length };
  };

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="container mx-auto px-4 py-8">
          <Card className="max-w-md mx-auto">
            <CardContent className="text-center py-12">
              <AlertCircle className="h-12 w-12 mx-auto mb-4 text-gray-400" />
              <h2 className="text-xl font-semibold text-gray-900 mb-2">Login Required</h2>
              <p className="text-gray-600 mb-4">
                Please log in to view your bookings.
              </p>
              <Button onClick={() => window.location.href = '/'}>
                Go to Homepage
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  const filteredBookings = getFilteredBookings();
  const stats = getBookingStats();

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            My Bookings
          </h1>
          <p className="text-gray-600">
            Manage your property bookings and reservations
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Bookings</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
                </div>
                <Calendar className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Upcoming</p>
                  <p className="text-2xl font-bold text-green-600">{stats.upcoming}</p>
                </div>
                <Clock className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Pending</p>
                  <p className="text-2xl font-bold text-yellow-600">{stats.pending}</p>
                </div>
                <Clock className="h-8 w-8 text-yellow-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Confirmed</p>
                  <p className="text-2xl font-bold text-blue-600">{stats.confirmed}</p>
                </div>
                <CheckCircle className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Filter Bookings
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {[
                { key: 'all', label: 'All Bookings', count: stats.total },
                { key: 'upcoming', label: 'Upcoming', count: stats.upcoming },
                { key: 'pending', label: 'Pending', count: stats.pending },
                { key: 'confirmed', label: 'Confirmed', count: stats.confirmed },
                { key: 'cancelled', label: 'Cancelled', count: stats.cancelled },
                { key: 'past', label: 'Past', count: stats.total - stats.upcoming - stats.pending }
              ].map((filterOption) => (
                <Button
                  key={filterOption.key}
                  variant={filter === filterOption.key ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setFilter(filterOption.key as any)}
                  className="flex items-center gap-2"
                >
                  {filterOption.label}
                  {filterOption.count > 0 && (
                    <Badge variant="secondary" className="ml-1">
                      {filterOption.count}
                    </Badge>
                  )}
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Loading State */}
        {(loading || bookingLoading) && (
          <div className="text-center py-12">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-sea-green-600" />
            <p className="text-gray-600">Loading your bookings...</p>
          </div>
        )}

        {/* Error State */}
        {error && (
          <Card>
            <CardContent className="text-center py-12">
              <AlertCircle className="h-12 w-12 mx-auto mb-4 text-red-500" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading Bookings</h3>
              <p className="text-gray-600 mb-4">{error}</p>
              <Button onClick={() => handleBookingUpdate()}>
                Try Again
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Bookings List */}
        {!loading && !bookingLoading && !error && (
          <>
            {filteredBookings.length === 0 ? (
              <Card>
                <CardContent className="text-center py-12">
                  <Search className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    {filter === 'all' ? 'No Bookings Found' : `No ${filter} Bookings`}
                  </h3>
                  <p className="text-gray-600 mb-4">
                    {filter === 'all' 
                      ? "You haven't made any bookings yet. Start exploring properties!"
                      : `You don't have any ${filter} bookings at the moment.`
                    }
                  </p>
                  <Button onClick={() => window.location.href = '/'}>
                    Browse Properties
                  </Button>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-6">
                <div className="flex justify-between items-center">
                  <h2 className="text-xl font-semibold text-gray-900">
                    {filter === 'all' ? 'All Bookings' : `${filter.charAt(0).toUpperCase() + filter.slice(1)} Bookings`}
                  </h2>
                  <p className="text-gray-600">
                    {filteredBookings.length} booking{filteredBookings.length !== 1 ? 's' : ''}
                  </p>
                </div>

                <div className="grid gap-6">
                  {filteredBookings.map((booking) => (
                    <BookingCard
                      key={booking.id}
                      booking={booking}
                      onUpdate={handleBookingUpdate}
                    />
                  ))}
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default Bookings;
