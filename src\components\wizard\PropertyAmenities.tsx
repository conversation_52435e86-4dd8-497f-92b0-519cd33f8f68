import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { 
  Wifi, 
  Car, 
  Waves, 
  Utensils, 
  Tv, 
  Wind, 
  Flame,
  Snowflake,
  Shirt,
  Coffee,
  Dumb<PERSON>,
  Baby,
  Paw<PERSON><PERSON>t,
  Cigarette,
  Users,
  Shield,
  Camera,
  Music,
  Gamepad2,
  Book,
  Laptop,
  Phone,
  MapPin,
  TreePine
} from 'lucide-react';

interface PropertyAmenitiesProps {
  data: {
    amenities: string[];
  };
  errors: Record<string, string>;
  onChange: (updates: any) => void;
}

const AMENITY_CATEGORIES = [
  {
    title: 'Essential',
    amenities: [
      { id: 'wifi', label: 'WiFi', icon: Wifi },
      { id: 'parking', label: 'Free Parking', icon: Car },
      { id: 'kitchen', label: 'Kitchen', icon: Utensils },
      { id: 'tv', label: 'TV', icon: Tv },
      { id: 'air_conditioning', label: 'Air Conditioning', icon: Snowflake },
      { id: 'heating', label: 'Heating', icon: Flame },
    ]
  },
  {
    title: 'Features',
    amenities: [
      { id: 'pool', label: 'Swimming Pool', icon: Waves },
      { id: 'hot_tub', label: 'Hot Tub', icon: Waves },
      { id: 'garden', label: 'Garden', icon: TreePine },
      { id: 'balcony', label: 'Balcony/Patio', icon: Wind },
      { id: 'fireplace', label: 'Fireplace', icon: Flame },
      { id: 'gym', label: 'Gym/Fitness', icon: Dumbbell },
    ]
  },
  {
    title: 'Appliances',
    amenities: [
      { id: 'washing_machine', label: 'Washing Machine', icon: Shirt },
      { id: 'dryer', label: 'Dryer', icon: Wind },
      { id: 'dishwasher', label: 'Dishwasher', icon: Utensils },
      { id: 'coffee_maker', label: 'Coffee Maker', icon: Coffee },
      { id: 'microwave', label: 'Microwave', icon: Utensils },
      { id: 'refrigerator', label: 'Refrigerator', icon: Snowflake },
    ]
  },
  {
    title: 'Entertainment',
    amenities: [
      { id: 'netflix', label: 'Netflix', icon: Tv },
      { id: 'sound_system', label: 'Sound System', icon: Music },
      { id: 'games', label: 'Board Games', icon: Gamepad2 },
      { id: 'books', label: 'Books', icon: Book },
      { id: 'workspace', label: 'Dedicated Workspace', icon: Laptop },
      { id: 'phone', label: 'Phone', icon: Phone },
    ]
  },
  {
    title: 'Family',
    amenities: [
      { id: 'baby_friendly', label: 'Baby Friendly', icon: Baby },
      { id: 'high_chair', label: 'High Chair', icon: Baby },
      { id: 'crib', label: 'Crib', icon: Baby },
      { id: 'toys', label: 'Children\'s Toys', icon: Gamepad2 },
      { id: 'child_safety', label: 'Child Safety Features', icon: Shield },
      { id: 'family_friendly', label: 'Family Friendly', icon: Users },
    ]
  },
  {
    title: 'Policies',
    amenities: [
      { id: 'pets_allowed', label: 'Pets Allowed', icon: PawPrint },
      { id: 'smoking_allowed', label: 'Smoking Allowed', icon: Cigarette },
      { id: 'events_allowed', label: 'Events Allowed', icon: Users },
      { id: 'long_term_stays', label: 'Long-term Stays', icon: MapPin },
    ]
  },
  {
    title: 'Safety & Security',
    amenities: [
      { id: 'security_cameras', label: 'Security Cameras (exterior)', icon: Camera },
      { id: 'smoke_detector', label: 'Smoke Detector', icon: Shield },
      { id: 'carbon_monoxide', label: 'Carbon Monoxide Detector', icon: Shield },
      { id: 'first_aid', label: 'First Aid Kit', icon: Shield },
      { id: 'fire_extinguisher', label: 'Fire Extinguisher', icon: Shield },
      { id: 'security_system', label: 'Security System', icon: Shield },
    ]
  }
];

export const PropertyAmenities: React.FC<PropertyAmenitiesProps> = ({
  data,
  errors,
  onChange
}) => {
  const toggleAmenity = (amenityId: string) => {
    const currentAmenities = data.amenities || [];
    const updatedAmenities = currentAmenities.includes(amenityId)
      ? currentAmenities.filter(id => id !== amenityId)
      : [...currentAmenities, amenityId];
    
    onChange({ amenities: updatedAmenities });
  };

  const isSelected = (amenityId: string) => {
    return (data.amenities || []).includes(amenityId);
  };

  const getSelectedCount = () => {
    return (data.amenities || []).length;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          What amenities do you offer?
        </h2>
        <p className="text-gray-600">
          Select all the amenities and features available at your property
        </p>
        <p className="text-sm text-sea-green-600 mt-2">
          {getSelectedCount()} amenities selected
        </p>
      </div>

      {/* Amenities by Category */}
      <div className="space-y-6">
        {AMENITY_CATEGORIES.map((category) => (
          <Card key={category.title}>
            <CardHeader>
              <CardTitle className="text-lg">{category.title}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                {category.amenities.map((amenity) => {
                  const IconComponent = amenity.icon;
                  const selected = isSelected(amenity.id);
                  
                  return (
                    <div
                      key={amenity.id}
                      onClick={() => toggleAmenity(amenity.id)}
                      className={`
                        cursor-pointer p-4 rounded-lg border-2 transition-all hover:shadow-md
                        ${selected 
                          ? 'border-sea-green-500 bg-sea-green-50 text-sea-green-900' 
                          : 'border-gray-200 hover:border-gray-300 text-gray-700'
                        }
                      `}
                    >
                      <div className="flex items-center gap-3">
                        <IconComponent className={`h-5 w-5 ${
                          selected ? 'text-sea-green-600' : 'text-gray-600'
                        }`} />
                        <span className="font-medium">{amenity.label}</span>
                        {selected && (
                          <div className="ml-auto">
                            <div className="w-5 h-5 bg-sea-green-600 rounded-full flex items-center justify-center">
                              <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                              </svg>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Quick Actions */}
      <div className="flex flex-wrap gap-3 justify-center">
        <button
          type="button"
          onClick={() => onChange({ amenities: [] })}
          className="px-4 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50"
        >
          Clear All
        </button>
        <button
          type="button"
          onClick={() => {
            // Select all essential amenities
            const essentialAmenities = AMENITY_CATEGORIES[0].amenities.map(a => a.id);
            const currentAmenities = data.amenities || [];
            const allEssential = essentialAmenities.every(id => currentAmenities.includes(id));
            
            if (allEssential) {
              // Remove all essential
              onChange({ 
                amenities: currentAmenities.filter(id => !essentialAmenities.includes(id))
              });
            } else {
              // Add all essential
              const newAmenities = [...new Set([...currentAmenities, ...essentialAmenities])];
              onChange({ amenities: newAmenities });
            }
          }}
          className="px-4 py-2 text-sm bg-sea-green-100 text-sea-green-700 border border-sea-green-300 rounded-lg hover:bg-sea-green-200"
        >
          Toggle Essential Amenities
        </button>
      </div>

      {/* Tips */}
      <Card className="bg-blue-50 border-blue-200">
        <CardContent className="p-4">
          <h4 className="font-medium text-blue-900 mb-2">💡 Amenity Tips</h4>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• Essential amenities like WiFi and parking are highly valued by guests</li>
            <li>• Unique features like pools or gardens can help your listing stand out</li>
            <li>• Be honest about what's available - accuracy builds trust</li>
            <li>• Safety amenities like smoke detectors are important for guest confidence</li>
            <li>• Family-friendly amenities attract guests traveling with children</li>
          </ul>
        </CardContent>
      </Card>

      {/* Selected Amenities Summary */}
      {getSelectedCount() > 0 && (
        <Card className="bg-green-50 border-green-200">
          <CardContent className="p-4">
            <h4 className="font-medium text-green-900 mb-2">
              Selected Amenities ({getSelectedCount()})
            </h4>
            <div className="flex flex-wrap gap-2">
              {(data.amenities || []).map((amenityId) => {
                const amenity = AMENITY_CATEGORIES
                  .flatMap(cat => cat.amenities)
                  .find(a => a.id === amenityId);
                
                if (!amenity) return null;
                
                return (
                  <span
                    key={amenityId}
                    className="inline-flex items-center gap-1 px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full"
                  >
                    <amenity.icon className="h-3 w-3" />
                    {amenity.label}
                  </span>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
