import React, { Component, ErrorInfo, ReactNode } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>freshCw, Home, Bug } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, <PERSON><PERSON>ontent, CardH<PERSON>er, CardTitle } from '@/components/ui/card';
import { captureError } from '@/lib/errorTracking';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  showDetails?: boolean;
  level?: 'page' | 'component' | 'critical';
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string | null;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error,
      errorId: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({ errorInfo });

    // Track the error
    captureError({
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      errorBoundary: this.constructor.name,
      errorInfo,
      severity: this.props.level === 'critical' ? 'critical' : 'high',
      category: 'ui',
      context: {
        errorBoundaryLevel: this.props.level,
        errorId: this.state.errorId
      }
    });

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    console.error('Error Boundary caught an error:', error, errorInfo);
  }

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null
    });
  };

  handleReload = () => {
    window.location.reload();
  };

  handleGoHome = () => {
    window.location.href = '/';
  };

  render() {
    if (this.state.hasError) {
      // Use custom fallback if provided
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Different error UIs based on level
      if (this.props.level === 'critical') {
        return <CriticalErrorFallback 
          error={this.state.error}
          errorInfo={this.state.errorInfo}
          errorId={this.state.errorId}
          onRetry={this.handleRetry}
          onReload={this.handleReload}
          onGoHome={this.handleGoHome}
          showDetails={this.props.showDetails}
        />;
      }

      if (this.props.level === 'component') {
        return <ComponentErrorFallback 
          error={this.state.error}
          onRetry={this.handleRetry}
          showDetails={this.props.showDetails}
        />;
      }

      // Default page-level error
      return <PageErrorFallback 
        error={this.state.error}
        errorInfo={this.state.errorInfo}
        errorId={this.state.errorId}
        onRetry={this.handleRetry}
        onReload={this.handleReload}
        onGoHome={this.handleGoHome}
        showDetails={this.props.showDetails}
      />;
    }

    return this.props.children;
  }
}

// Critical error fallback (full page)
const CriticalErrorFallback: React.FC<{
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string | null;
  onRetry: () => void;
  onReload: () => void;
  onGoHome: () => void;
  showDetails?: boolean;
}> = ({ error, errorInfo, errorId, onRetry, onReload, onGoHome, showDetails }) => (
  <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
    <Card className="w-full max-w-2xl">
      <CardHeader className="text-center">
        <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
          <AlertTriangle className="h-8 w-8 text-red-600" />
        </div>
        <CardTitle className="text-2xl text-red-600">Something went wrong</CardTitle>
        <p className="text-gray-600 mt-2">
          We're sorry, but something unexpected happened. Our team has been notified.
        </p>
        {errorId && (
          <p className="text-sm text-gray-500 mt-2">
            Error ID: <code className="bg-gray-100 px-2 py-1 rounded">{errorId}</code>
          </p>
        )}
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          <Button onClick={onRetry} className="flex items-center gap-2">
            <RefreshCw className="h-4 w-4" />
            Try Again
          </Button>
          <Button onClick={onReload} variant="outline" className="flex items-center gap-2">
            <RefreshCw className="h-4 w-4" />
            Reload Page
          </Button>
          <Button onClick={onGoHome} variant="outline" className="flex items-center gap-2">
            <Home className="h-4 w-4" />
            Go Home
          </Button>
        </div>

        {showDetails && error && (
          <details className="mt-6">
            <summary className="cursor-pointer text-sm font-medium text-gray-700 hover:text-gray-900">
              Technical Details
            </summary>
            <div className="mt-3 p-4 bg-gray-100 rounded-lg text-sm">
              <div className="mb-3">
                <strong>Error:</strong>
                <pre className="mt-1 text-red-600 whitespace-pre-wrap">{error.message}</pre>
              </div>
              {error.stack && (
                <div className="mb-3">
                  <strong>Stack Trace:</strong>
                  <pre className="mt-1 text-xs text-gray-600 whitespace-pre-wrap overflow-x-auto">
                    {error.stack}
                  </pre>
                </div>
              )}
              {errorInfo?.componentStack && (
                <div>
                  <strong>Component Stack:</strong>
                  <pre className="mt-1 text-xs text-gray-600 whitespace-pre-wrap overflow-x-auto">
                    {errorInfo.componentStack}
                  </pre>
                </div>
              )}
            </div>
          </details>
        )}
      </CardContent>
    </Card>
  </div>
);

// Page-level error fallback
const PageErrorFallback: React.FC<{
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string | null;
  onRetry: () => void;
  onReload: () => void;
  onGoHome: () => void;
  showDetails?: boolean;
}> = ({ error, errorInfo, errorId, onRetry, onReload, onGoHome, showDetails }) => (
  <div className="min-h-[400px] flex items-center justify-center p-4">
    <Card className="w-full max-w-lg">
      <CardHeader className="text-center">
        <div className="mx-auto w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mb-3">
          <Bug className="h-6 w-6 text-orange-600" />
        </div>
        <CardTitle className="text-xl text-orange-600">Oops! Something went wrong</CardTitle>
        <p className="text-gray-600 text-sm mt-2">
          This page encountered an error. Please try refreshing or go back to the homepage.
        </p>
        {errorId && (
          <p className="text-xs text-gray-500 mt-2">
            Error ID: <code className="bg-gray-100 px-1 py-0.5 rounded text-xs">{errorId}</code>
          </p>
        )}
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="flex flex-col gap-2">
          <Button onClick={onRetry} size="sm" className="flex items-center gap-2">
            <RefreshCw className="h-4 w-4" />
            Try Again
          </Button>
          <div className="flex gap-2">
            <Button onClick={onReload} variant="outline" size="sm" className="flex-1">
              Reload
            </Button>
            <Button onClick={onGoHome} variant="outline" size="sm" className="flex-1">
              Home
            </Button>
          </div>
        </div>

        {showDetails && error && (
          <details className="mt-4">
            <summary className="cursor-pointer text-xs font-medium text-gray-600 hover:text-gray-800">
              Show Details
            </summary>
            <div className="mt-2 p-3 bg-gray-50 rounded text-xs">
              <div className="text-red-600 font-mono">{error.message}</div>
              {error.stack && (
                <pre className="mt-2 text-gray-600 text-xs overflow-x-auto whitespace-pre-wrap">
                  {error.stack.split('\n').slice(0, 5).join('\n')}
                </pre>
              )}
            </div>
          </details>
        )}
      </CardContent>
    </Card>
  </div>
);

// Component-level error fallback (inline)
const ComponentErrorFallback: React.FC<{
  error: Error | null;
  onRetry: () => void;
  showDetails?: boolean;
}> = ({ error, onRetry, showDetails }) => (
  <div className="p-4 border border-orange-200 bg-orange-50 rounded-lg">
    <div className="flex items-center gap-3">
      <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center flex-shrink-0">
        <Bug className="h-4 w-4 text-orange-600" />
      </div>
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium text-orange-800">Component Error</p>
        <p className="text-xs text-orange-600 mt-1">
          This component failed to load properly.
        </p>
      </div>
      <Button onClick={onRetry} size="sm" variant="outline" className="flex-shrink-0">
        <RefreshCw className="h-3 w-3 mr-1" />
        Retry
      </Button>
    </div>

    {showDetails && error && (
      <details className="mt-3">
        <summary className="cursor-pointer text-xs font-medium text-orange-700 hover:text-orange-900">
          Error Details
        </summary>
        <div className="mt-2 p-2 bg-orange-100 rounded text-xs">
          <code className="text-red-600">{error.message}</code>
        </div>
      </details>
    )}
  </div>
);

// Convenience wrapper components
export const PageErrorBoundary: React.FC<{ children: ReactNode }> = ({ children }) => (
  <ErrorBoundary level="page" showDetails={process.env.NODE_ENV === 'development'}>
    {children}
  </ErrorBoundary>
);

export const ComponentErrorBoundary: React.FC<{ children: ReactNode }> = ({ children }) => (
  <ErrorBoundary level="component" showDetails={process.env.NODE_ENV === 'development'}>
    {children}
  </ErrorBoundary>
);

export const CriticalErrorBoundary: React.FC<{ children: ReactNode }> = ({ children }) => (
  <ErrorBoundary level="critical" showDetails={process.env.NODE_ENV === 'development'}>
    {children}
  </ErrorBoundary>
);
