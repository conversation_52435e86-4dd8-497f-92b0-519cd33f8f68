import { supabase } from '@/lib/supabase';

export interface HostMetrics {
  id: string;
  host_id: string;
  metric_date: string;
  total_views: number;
  total_inquiries: number;
  total_bookings: number;
  total_revenue: number;
  average_rating: number;
  response_rate: number;
  acceptance_rate: number;
  created_at: string;
  updated_at: string;
}

export interface PropertyMetrics {
  id: string;
  property_id: string;
  metric_date: string;
  views_count: number;
  inquiries_count: number;
  bookings_count: number;
  revenue: number;
  occupancy_rate: number;
  created_at: string;
  updated_at: string;
  // Joined data
  property?: {
    id: string;
    title: string;
    city: string;
    province: string;
  };
}

export interface AnalyticsSummary {
  totalViews: number;
  totalBookings: number;
  totalRevenue: number;
  averageRating: number;
  responseRate: number;
  acceptanceRate: number;
  occupancyRate: number;
  viewsGrowth: number;
  bookingsGrowth: number;
  revenueGrowth: number;
}

export class HostAnalyticsService {
  /**
   * Get host metrics for a date range
   */
  static async getHostMetrics(
    hostId: string,
    startDate: string,
    endDate: string
  ): Promise<HostMetrics[]> {
    try {
      const { data, error } = await supabase
        .from('host_metrics')
        .select('*')
        .eq('host_id', hostId)
        .gte('metric_date', startDate)
        .lte('metric_date', endDate)
        .order('metric_date', { ascending: true });

      if (error) {
        console.error('Error fetching host metrics:', error);
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Failed to fetch host metrics:', error);
      throw error;
    }
  }

  /**
   * Get property metrics for a host
   */
  static async getPropertyMetrics(
    hostId: string,
    startDate: string,
    endDate: string
  ): Promise<PropertyMetrics[]> {
    try {
      const { data, error } = await supabase
        .from('property_metrics')
        .select(`
          *,
          property:properties (
            id,
            title,
            city,
            province
          )
        `)
        .eq('properties.host_id', hostId)
        .gte('metric_date', startDate)
        .lte('metric_date', endDate)
        .order('metric_date', { ascending: true });

      if (error) {
        console.error('Error fetching property metrics:', error);
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Failed to fetch property metrics:', error);
      throw error;
    }
  }

  /**
   * Get analytics summary for a host
   */
  static async getAnalyticsSummary(
    hostId: string,
    currentPeriodStart: string,
    currentPeriodEnd: string,
    previousPeriodStart: string,
    previousPeriodEnd: string
  ): Promise<AnalyticsSummary> {
    try {
      // Get current period metrics
      const currentMetrics = await this.getHostMetrics(
        hostId,
        currentPeriodStart,
        currentPeriodEnd
      );

      // Get previous period metrics for comparison
      const previousMetrics = await this.getHostMetrics(
        hostId,
        previousPeriodStart,
        previousPeriodEnd
      );

      // Calculate current period totals
      const currentTotals = currentMetrics.reduce(
        (acc, metric) => ({
          totalViews: acc.totalViews + metric.total_views,
          totalBookings: acc.totalBookings + metric.total_bookings,
          totalRevenue: acc.totalRevenue + metric.total_revenue,
          totalRating: acc.totalRating + metric.average_rating,
          totalResponse: acc.totalResponse + metric.response_rate,
          totalAcceptance: acc.totalAcceptance + metric.acceptance_rate,
          count: acc.count + 1,
        }),
        {
          totalViews: 0,
          totalBookings: 0,
          totalRevenue: 0,
          totalRating: 0,
          totalResponse: 0,
          totalAcceptance: 0,
          count: 0,
        }
      );

      // Calculate previous period totals
      const previousTotals = previousMetrics.reduce(
        (acc, metric) => ({
          totalViews: acc.totalViews + metric.total_views,
          totalBookings: acc.totalBookings + metric.total_bookings,
          totalRevenue: acc.totalRevenue + metric.total_revenue,
        }),
        {
          totalViews: 0,
          totalBookings: 0,
          totalRevenue: 0,
        }
      );

      // Calculate growth percentages
      const viewsGrowth = previousTotals.totalViews > 0 
        ? ((currentTotals.totalViews - previousTotals.totalViews) / previousTotals.totalViews) * 100
        : 0;

      const bookingsGrowth = previousTotals.totalBookings > 0
        ? ((currentTotals.totalBookings - previousTotals.totalBookings) / previousTotals.totalBookings) * 100
        : 0;

      const revenueGrowth = previousTotals.totalRevenue > 0
        ? ((currentTotals.totalRevenue - previousTotals.totalRevenue) / previousTotals.totalRevenue) * 100
        : 0;

      // Get occupancy rate from property metrics
      const propertyMetrics = await this.getPropertyMetrics(
        hostId,
        currentPeriodStart,
        currentPeriodEnd
      );

      const averageOccupancyRate = propertyMetrics.length > 0
        ? propertyMetrics.reduce((acc, pm) => acc + pm.occupancy_rate, 0) / propertyMetrics.length
        : 0;

      return {
        totalViews: currentTotals.totalViews,
        totalBookings: currentTotals.totalBookings,
        totalRevenue: currentTotals.totalRevenue,
        averageRating: currentTotals.count > 0 ? currentTotals.totalRating / currentTotals.count : 0,
        responseRate: currentTotals.count > 0 ? currentTotals.totalResponse / currentTotals.count : 0,
        acceptanceRate: currentTotals.count > 0 ? currentTotals.totalAcceptance / currentTotals.count : 0,
        occupancyRate: averageOccupancyRate,
        viewsGrowth,
        bookingsGrowth,
        revenueGrowth,
      };
    } catch (error) {
      console.error('Failed to get analytics summary:', error);
      throw error;
    }
  }

  /**
   * Get top performing properties for a host
   */
  static async getTopPerformingProperties(
    hostId: string,
    startDate: string,
    endDate: string,
    limit: number = 5
  ): Promise<PropertyMetrics[]> {
    try {
      const { data, error } = await supabase
        .from('property_metrics')
        .select(`
          *,
          property:properties (
            id,
            title,
            city,
            province
          )
        `)
        .eq('properties.host_id', hostId)
        .gte('metric_date', startDate)
        .lte('metric_date', endDate)
        .order('revenue', { ascending: false })
        .limit(limit);

      if (error) {
        console.error('Error fetching top performing properties:', error);
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Failed to fetch top performing properties:', error);
      throw error;
    }
  }

  /**
   * Update or create host metrics for a specific date
   */
  static async updateHostMetrics(
    hostId: string,
    date: string,
    metrics: Partial<Omit<HostMetrics, 'id' | 'host_id' | 'metric_date' | 'created_at' | 'updated_at'>>
  ): Promise<HostMetrics> {
    try {
      const { data, error } = await supabase
        .from('host_metrics')
        .upsert({
          host_id: hostId,
          metric_date: date,
          ...metrics,
          updated_at: new Date().toISOString(),
        })
        .select()
        .single();

      if (error) {
        console.error('Error updating host metrics:', error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Failed to update host metrics:', error);
      throw error;
    }
  }

  /**
   * Update or create property metrics for a specific date
   */
  static async updatePropertyMetrics(
    propertyId: string,
    date: string,
    metrics: Partial<Omit<PropertyMetrics, 'id' | 'property_id' | 'metric_date' | 'created_at' | 'updated_at'>>
  ): Promise<PropertyMetrics> {
    try {
      const { data, error } = await supabase
        .from('property_metrics')
        .upsert({
          property_id: propertyId,
          metric_date: date,
          ...metrics,
          updated_at: new Date().toISOString(),
        })
        .select()
        .single();

      if (error) {
        console.error('Error updating property metrics:', error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Failed to update property metrics:', error);
      throw error;
    }
  }

  /**
   * Calculate and update daily metrics for a host
   */
  static async calculateDailyMetrics(hostId: string, date: string): Promise<void> {
    try {
      // This would typically be run as a background job
      // For now, we'll create a placeholder implementation
      
      // Get property views for the date
      const { data: views } = await supabase
        .from('property_views')
        .select('property_id')
        .gte('viewed_at', `${date}T00:00:00Z`)
        .lt('viewed_at', `${date}T23:59:59Z`)
        .in('property_id', 
          supabase
            .from('properties')
            .select('id')
            .eq('host_id', hostId)
        );

      // Get bookings for the date
      const { data: bookings } = await supabase
        .from('bookings')
        .select('total_amount, property_id')
        .gte('created_at', `${date}T00:00:00Z`)
        .lt('created_at', `${date}T23:59:59Z`)
        .in('property_id',
          supabase
            .from('properties')
            .select('id')
            .eq('host_id', hostId)
        );

      const totalViews = views?.length || 0;
      const totalBookings = bookings?.length || 0;
      const totalRevenue = bookings?.reduce((sum, booking) => sum + (booking.total_amount || 0), 0) || 0;

      // Update host metrics
      await this.updateHostMetrics(hostId, date, {
        total_views: totalViews,
        total_bookings: totalBookings,
        total_revenue: totalRevenue,
      });

    } catch (error) {
      console.error('Failed to calculate daily metrics:', error);
      throw error;
    }
  }
}
