import React, { useState, useEffect, useRef } from 'react';
import { Search, Brain, Sparkles, TrendingUp, MapPin, Calendar, Users, DollarSign, Zap, Mic, MicOff } from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';

interface SmartSearchResult {
  id: string;
  query: string;
  intent: 'location' | 'price' | 'amenity' | 'date' | 'experience' | 'mixed';
  confidence: number;
  suggestions: Array<{
    type: 'property' | 'location' | 'filter' | 'experience';
    title: string;
    description: string;
    value: any;
    score: number;
  }>;
  interpretation: string;
  refinements: string[];
}

interface SmartSearchAIProps {
  onSearch: (query: string, filters: any) => void;
  onSuggestionSelect: (suggestion: any) => void;
  placeholder?: string;
  enableVoiceSearch?: boolean;
  className?: string;
}

export const SmartSearchAI: React.FC<SmartSearchAIProps> = ({
  onSearch,
  onSuggestionSelect,
  placeholder = "Tell me what kind of stay you're looking for...",
  enableVoiceSearch = true,
  className
}) => {
  const [query, setQuery] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [results, setResults] = useState<SmartSearchResult | null>(null);
  const [isListening, setIsListening] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const recognitionRef = useRef<any>(null);

  // Initialize speech recognition
  useEffect(() => {
    if (enableVoiceSearch && 'webkitSpeechRecognition' in window) {
      const SpeechRecognition = (window as any).webkitSpeechRecognition;
      recognitionRef.current = new SpeechRecognition();
      recognitionRef.current.continuous = false;
      recognitionRef.current.interimResults = false;
      recognitionRef.current.lang = 'en-ZA';

      recognitionRef.current.onresult = (event: any) => {
        const transcript = event.results[0][0].transcript;
        setQuery(transcript);
        handleSmartSearch(transcript);
        setIsListening(false);
      };

      recognitionRef.current.onerror = () => {
        setIsListening(false);
      };

      recognitionRef.current.onend = () => {
        setIsListening(false);
      };
    }
  }, [enableVoiceSearch]);

  const handleSmartSearch = async (searchQuery: string) => {
    if (!searchQuery.trim()) {
      setResults(null);
      setShowSuggestions(false);
      return;
    }

    setIsProcessing(true);
    setShowSuggestions(true);

    try {
      // Simulate AI processing delay
      await new Promise(resolve => setTimeout(resolve, 800));

      // Mock AI-powered search analysis
      const mockResult: SmartSearchResult = analyzeQuery(searchQuery);
      setResults(mockResult);
    } catch (error) {
      console.error('Smart search error:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  const analyzeQuery = (searchQuery: string): SmartSearchResult => {
    const query = searchQuery.toLowerCase();
    
    // Intent detection
    let intent: SmartSearchResult['intent'] = 'mixed';
    let confidence = 0.8;

    if (query.includes('cape town') || query.includes('johannesburg') || query.includes('durban')) {
      intent = 'location';
      confidence = 0.95;
    } else if (query.includes('cheap') || query.includes('budget') || query.includes('expensive') || query.includes('luxury')) {
      intent = 'price';
      confidence = 0.9;
    } else if (query.includes('pool') || query.includes('wifi') || query.includes('parking') || query.includes('kitchen')) {
      intent = 'amenity';
      confidence = 0.85;
    } else if (query.includes('weekend') || query.includes('holiday') || query.includes('vacation')) {
      intent = 'date';
      confidence = 0.8;
    } else if (query.includes('romantic') || query.includes('family') || query.includes('business') || query.includes('adventure')) {
      intent = 'experience';
      confidence = 0.85;
    }

    // Generate suggestions based on intent
    const suggestions = generateSuggestions(query, intent);
    
    // Create interpretation
    const interpretation = generateInterpretation(query, intent);
    
    // Generate refinements
    const refinements = generateRefinements(query, intent);

    return {
      id: Date.now().toString(),
      query: searchQuery,
      intent,
      confidence,
      suggestions,
      interpretation,
      refinements
    };
  };

  const generateSuggestions = (query: string, intent: string) => {
    const suggestions = [];

    // Location-based suggestions
    if (intent === 'location' || intent === 'mixed') {
      if (query.includes('cape town')) {
        suggestions.push({
          type: 'location' as const,
          title: 'Cape Town City Center',
          description: 'Close to V&A Waterfront and Table Mountain',
          value: { location: 'cape-town-city', lat: -33.9249, lon: 18.4241 },
          score: 0.95
        });
        suggestions.push({
          type: 'location' as const,
          title: 'Camps Bay, Cape Town',
          description: 'Beachfront properties with ocean views',
          value: { location: 'camps-bay', lat: -33.9553, lon: 18.3756 },
          score: 0.9
        });
      }
    }

    // Price-based suggestions
    if (intent === 'price' || intent === 'mixed') {
      if (query.includes('budget') || query.includes('cheap')) {
        suggestions.push({
          type: 'filter' as const,
          title: 'Budget-Friendly Options',
          description: 'Properties under R500 per night',
          value: { priceMax: 500 },
          score: 0.9
        });
      } else if (query.includes('luxury') || query.includes('expensive')) {
        suggestions.push({
          type: 'filter' as const,
          title: 'Luxury Properties',
          description: 'Premium accommodations R2000+ per night',
          value: { priceMin: 2000, amenities: ['pool', 'spa', 'concierge'] },
          score: 0.9
        });
      }
    }

    // Amenity-based suggestions
    if (intent === 'amenity' || intent === 'mixed') {
      if (query.includes('pool')) {
        suggestions.push({
          type: 'filter' as const,
          title: 'Properties with Swimming Pool',
          description: 'Accommodations featuring swimming pools',
          value: { amenities: ['pool'] },
          score: 0.85
        });
      }
      if (query.includes('wifi')) {
        suggestions.push({
          type: 'filter' as const,
          title: 'High-Speed WiFi Available',
          description: 'Properties with reliable internet connection',
          value: { amenities: ['wifi'] },
          score: 0.8
        });
      }
    }

    // Experience-based suggestions
    if (intent === 'experience' || intent === 'mixed') {
      if (query.includes('romantic')) {
        suggestions.push({
          type: 'experience' as const,
          title: 'Romantic Getaway Package',
          description: 'Intimate properties perfect for couples',
          value: { tags: ['romantic', 'couples'], amenities: ['jacuzzi', 'fireplace'] },
          score: 0.9
        });
      } else if (query.includes('family')) {
        suggestions.push({
          type: 'experience' as const,
          title: 'Family-Friendly Stays',
          description: 'Properties suitable for families with children',
          value: { tags: ['family'], amenities: ['kitchen', 'playground', 'pool'] },
          score: 0.85
        });
      }
    }

    // Property suggestions (mock data)
    suggestions.push({
      type: 'property' as const,
      title: 'Oceanview Villa in Camps Bay',
      description: 'Luxury 3-bedroom villa with stunning ocean views',
      value: { propertyId: 'villa-001', price: 2500 },
      score: 0.8
    });

    return suggestions.sort((a, b) => b.score - a.score).slice(0, 6);
  };

  const generateInterpretation = (query: string, intent: string): string => {
    switch (intent) {
      case 'location':
        return `I understand you're looking for accommodations in a specific location. I've found properties and areas that match your search.`;
      case 'price':
        return `I can see you have budget preferences. I've filtered properties based on your price range requirements.`;
      case 'amenity':
        return `You're looking for specific amenities. I've found properties that offer the features you mentioned.`;
      case 'date':
        return `I understand you have specific dates in mind. Let me show you available properties for your travel period.`;
      case 'experience':
        return `You're looking for a particular type of experience. I've curated properties that match your travel style.`;
      default:
        return `I've analyzed your search and found several relevant options based on your preferences.`;
    }
  };

  const generateRefinements = (query: string, intent: string): string[] => {
    const refinements = [];

    if (!query.includes('date')) {
      refinements.push('Add specific dates for better availability');
    }
    if (!query.includes('guest')) {
      refinements.push('Specify number of guests');
    }
    if (intent !== 'price') {
      refinements.push('Set a budget range');
    }
    if (!query.includes('amenity')) {
      refinements.push('Add preferred amenities');
    }

    return refinements.slice(0, 3);
  };

  const handleVoiceSearch = () => {
    if (!recognitionRef.current) return;

    if (isListening) {
      recognitionRef.current.stop();
      setIsListening(false);
    } else {
      recognitionRef.current.start();
      setIsListening(true);
    }
  };

  const handleSuggestionClick = (suggestion: any) => {
    onSuggestionSelect(suggestion);
    setShowSuggestions(false);
  };

  const handleSearch = () => {
    if (results) {
      onSearch(query, results.suggestions[0]?.value || {});
    }
    setShowSuggestions(false);
  };

  return (
    <div className={cn("relative w-full", className)}>
      {/* Search Input */}
      <div className="relative">
        <div className="absolute left-3 top-1/2 transform -translate-y-1/2 flex items-center gap-2">
          <Brain className="h-5 w-5 text-purple-500" />
          {isProcessing && <Sparkles className="h-4 w-4 text-purple-500 animate-pulse" />}
        </div>
        
        <Input
          value={query}
          onChange={(e) => {
            setQuery(e.target.value);
            handleSmartSearch(e.target.value);
          }}
          placeholder={placeholder}
          className="pl-12 pr-20 py-3 text-lg border-2 border-purple-200 focus:border-purple-500 rounded-xl"
        />
        
        <div className="absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center gap-2">
          {enableVoiceSearch && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleVoiceSearch}
              className={cn(
                "p-2 rounded-lg",
                isListening ? "bg-red-100 text-red-600" : "hover:bg-purple-100"
              )}
            >
              {isListening ? <MicOff className="h-4 w-4" /> : <Mic className="h-4 w-4" />}
            </Button>
          )}
          
          <Button
            onClick={handleSearch}
            disabled={!query.trim()}
            className="bg-purple-600 hover:bg-purple-700"
          >
            <Search className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* AI Results */}
      {showSuggestions && (
        <Card className="absolute top-full mt-2 w-full z-50 shadow-xl border-purple-200">
          <CardContent className="p-0">
            {isProcessing ? (
              <div className="p-6 text-center">
                <div className="flex items-center justify-center gap-2 mb-2">
                  <Brain className="h-5 w-5 text-purple-500 animate-pulse" />
                  <span className="text-purple-600 font-medium">AI is analyzing your request...</span>
                </div>
                <div className="w-full bg-purple-100 rounded-full h-2">
                  <div className="bg-purple-500 h-2 rounded-full animate-pulse" style={{ width: '60%' }}></div>
                </div>
              </div>
            ) : results ? (
              <div className="p-4">
                {/* AI Interpretation */}
                <div className="mb-4 p-3 bg-purple-50 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <Sparkles className="h-4 w-4 text-purple-600" />
                    <span className="font-medium text-purple-800">AI Understanding</span>
                    <Badge variant="secondary" className="text-xs">
                      {Math.round(results.confidence * 100)}% confident
                    </Badge>
                  </div>
                  <p className="text-sm text-purple-700">{results.interpretation}</p>
                </div>

                {/* Suggestions */}
                <div className="space-y-2 mb-4">
                  <h4 className="font-medium text-gray-800 flex items-center gap-2">
                    <TrendingUp className="h-4 w-4" />
                    Smart Suggestions
                  </h4>
                  {results.suggestions.map((suggestion, index) => (
                    <div
                      key={index}
                      onClick={() => handleSuggestionClick(suggestion)}
                      className="flex items-center gap-3 p-3 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors"
                    >
                      <div className="flex-shrink-0">
                        {suggestion.type === 'location' && <MapPin className="h-4 w-4 text-blue-500" />}
                        {suggestion.type === 'filter' && <DollarSign className="h-4 w-4 text-green-500" />}
                        {suggestion.type === 'experience' && <Sparkles className="h-4 w-4 text-purple-500" />}
                        {suggestion.type === 'property' && <Search className="h-4 w-4 text-orange-500" />}
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="font-medium text-gray-900">{suggestion.title}</div>
                        <div className="text-sm text-gray-600">{suggestion.description}</div>
                      </div>
                      
                      <div className="flex-shrink-0">
                        <div className="w-2 h-2 rounded-full bg-green-400" 
                             style={{ opacity: suggestion.score }} />
                      </div>
                    </div>
                  ))}
                </div>

                {/* Refinements */}
                {results.refinements.length > 0 && (
                  <div>
                    <Separator className="mb-3" />
                    <h4 className="font-medium text-gray-800 mb-2 flex items-center gap-2">
                      <Zap className="h-4 w-4" />
                      Improve Your Search
                    </h4>
                    <div className="flex flex-wrap gap-2">
                      {results.refinements.map((refinement, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {refinement}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ) : null}
          </CardContent>
        </Card>
      )}

      {/* Voice Search Indicator */}
      {isListening && (
        <div className="absolute top-full mt-2 w-full z-40">
          <Card className="border-red-200 bg-red-50">
            <CardContent className="p-4 text-center">
              <div className="flex items-center justify-center gap-2 text-red-600">
                <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse" />
                <span className="font-medium">Listening... Speak now</span>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};
