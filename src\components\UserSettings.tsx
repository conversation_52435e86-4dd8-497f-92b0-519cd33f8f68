import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { userProfileService, UserProfile, UserProfileUpdate } from '../services/userProfileService';
import { AccountSecurity } from './AccountSecurity';
import {
  Settings,
  Bell,
  Globe,
  DollarSign,
  Clock,
  Mail,
  Smartphone,
  Save,
  AlertCircle,
  Shield
} from 'lucide-react';

interface UserSettingsProps {
  className?: string;
  onSettingsUpdate?: (profile: UserProfile) => void;
}

export const UserSettings: React.FC<UserSettingsProps> = ({
  className,
  onSettingsUpdate
}) => {
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeSection, setActiveSection] = useState<'preferences' | 'notifications' | 'security'>('preferences');
  const [preferences, setPreferences] = useState({
    currency: 'ZAR',
    language: 'en',
    timezone: 'Africa/Johannesburg',
    marketing_emails: true,
    booking_notifications: true,
    review_notifications: true,
  });
  const [notificationSettings, setNotificationSettings] = useState({
    email_notifications: true,
    sms_notifications: false,
    push_notifications: true,
    marketing_communications: false,
    booking_updates: true,
    review_reminders: true,
    price_alerts: false,
  });

  useEffect(() => {
    fetchProfile();
  }, []);

  const fetchProfile = async () => {
    try {
      setLoading(true);
      setError(null);
      const profileData = await userProfileService.getUserProfile();
      setProfile(profileData);
      
      if (profileData.preferences) {
        setPreferences({ ...preferences, ...profileData.preferences });
      }
      
      if (profileData.notification_settings) {
        setNotificationSettings({ ...notificationSettings, ...profileData.notification_settings });
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load settings');
    } finally {
      setLoading(false);
    }
  };

  const handleSaveSettings = async () => {
    try {
      setSaving(true);
      setError(null);

      const updates: UserProfileUpdate = {
        preferences,
        notification_settings: notificationSettings,
      };

      const updatedProfile = await userProfileService.updateUserProfile(updates);
      setProfile(updatedProfile);

      if (onSettingsUpdate) {
        onSettingsUpdate(updatedProfile);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save settings');
    } finally {
      setSaving(false);
    }
  };

  const handlePreferenceChange = (key: string, value: any) => {
    setPreferences(prev => ({ ...prev, [key]: value }));
  };

  const handleNotificationChange = (key: string, value: boolean) => {
    setNotificationSettings(prev => ({ ...prev, [key]: value }));
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="animate-pulse space-y-4">
            <div className="h-6 bg-gray-200 rounded w-1/3"></div>
            <div className="space-y-3">
              <div className="h-4 bg-gray-200 rounded"></div>
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={className ? `space-y-6 ${className}` : 'space-y-6'}>
      {/* Settings Navigation */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveSection('preferences')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeSection === 'preferences'
                ? 'border-sea-green-500 text-sea-green-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <Settings className="h-4 w-4 inline mr-2" />
            Preferences
          </button>
          <button
            onClick={() => setActiveSection('notifications')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeSection === 'notifications'
                ? 'border-sea-green-500 text-sea-green-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <Bell className="h-4 w-4 inline mr-2" />
            Notifications
          </button>
          <button
            onClick={() => setActiveSection('security')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeSection === 'security'
                ? 'border-sea-green-500 text-sea-green-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <Shield className="h-4 w-4 inline mr-2" />
            Security
          </button>
        </nav>
      </div>

      {/* Preferences Section */}
      {activeSection === 'preferences' && (
        <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            General Preferences
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Currency */}
            <div>
              <Label className="flex items-center gap-2 mb-2">
                <DollarSign className="h-4 w-4" />
                Currency
              </Label>
              <select
                value={preferences.currency}
                onChange={(e) => handlePreferenceChange('currency', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
              >
                <option value="ZAR">South African Rand (ZAR)</option>
                <option value="USD">US Dollar (USD)</option>
                <option value="EUR">Euro (EUR)</option>
                <option value="GBP">British Pound (GBP)</option>
              </select>
            </div>

            {/* Language */}
            <div>
              <Label className="flex items-center gap-2 mb-2">
                <Globe className="h-4 w-4" />
                Language
              </Label>
              <select
                value={preferences.language}
                onChange={(e) => handlePreferenceChange('language', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
              >
                <option value="en">English</option>
                <option value="af">Afrikaans</option>
                <option value="zu">Zulu</option>
                <option value="xh">Xhosa</option>
              </select>
            </div>

            {/* Timezone */}
            <div>
              <Label className="flex items-center gap-2 mb-2">
                <Clock className="h-4 w-4" />
                Timezone
              </Label>
              <select
                value={preferences.timezone}
                onChange={(e) => handlePreferenceChange('timezone', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
              >
                <option value="Africa/Johannesburg">South Africa Standard Time</option>
                <option value="UTC">UTC</option>
                <option value="America/New_York">Eastern Time</option>
                <option value="Europe/London">Greenwich Mean Time</option>
              </select>
            </div>
          </div>

          {/* Communication Preferences */}
          <div className="border-t pt-6">
            <h4 className="font-medium text-gray-900 mb-4">Communication Preferences</h4>
            <div className="space-y-3">
              <label className="flex items-center justify-between cursor-pointer">
                <div>
                  <p className="font-medium">Marketing Emails</p>
                  <p className="text-sm text-gray-600">Receive promotional offers and updates</p>
                </div>
                <input
                  type="checkbox"
                  checked={preferences.marketing_emails}
                  onChange={(e) => handlePreferenceChange('marketing_emails', e.target.checked)}
                  className="w-4 h-4 text-sea-green-600 border-gray-300 rounded focus:ring-sea-green-500"
                />
              </label>

              <label className="flex items-center justify-between cursor-pointer">
                <div>
                  <p className="font-medium">Booking Notifications</p>
                  <p className="text-sm text-gray-600">Get notified about booking confirmations and updates</p>
                </div>
                <input
                  type="checkbox"
                  checked={preferences.booking_notifications}
                  onChange={(e) => handlePreferenceChange('booking_notifications', e.target.checked)}
                  className="w-4 h-4 text-sea-green-600 border-gray-300 rounded focus:ring-sea-green-500"
                />
              </label>

              <label className="flex items-center justify-between cursor-pointer">
                <div>
                  <p className="font-medium">Review Notifications</p>
                  <p className="text-sm text-gray-600">Get reminded to leave reviews after your stay</p>
                </div>
                <input
                  type="checkbox"
                  checked={preferences.review_notifications}
                  onChange={(e) => handlePreferenceChange('review_notifications', e.target.checked)}
                  className="w-4 h-4 text-sea-green-600 border-gray-300 rounded focus:ring-sea-green-500"
                />
              </label>
            </div>
          </div>
        </CardContent>
      </Card>
      )}

      {/* Notifications Section */}
      {activeSection === 'notifications' && (
        <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Notification Settings
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Email Notifications */}
          <div className="space-y-3">
            <div className="flex items-center gap-2 mb-3">
              <Mail className="h-4 w-4 text-gray-500" />
              <h4 className="font-medium text-gray-900">Email Notifications</h4>
            </div>

            <label className="flex items-center justify-between cursor-pointer">
              <div>
                <p className="font-medium">Email Notifications</p>
                <p className="text-sm text-gray-600">Receive notifications via email</p>
              </div>
              <input
                type="checkbox"
                checked={notificationSettings.email_notifications}
                onChange={(e) => handleNotificationChange('email_notifications', e.target.checked)}
                className="w-4 h-4 text-sea-green-600 border-gray-300 rounded focus:ring-sea-green-500"
              />
            </label>

            <label className="flex items-center justify-between cursor-pointer">
              <div>
                <p className="font-medium">Booking Updates</p>
                <p className="text-sm text-gray-600">Important booking information and changes</p>
              </div>
              <input
                type="checkbox"
                checked={notificationSettings.booking_updates}
                onChange={(e) => handleNotificationChange('booking_updates', e.target.checked)}
                className="w-4 h-4 text-sea-green-600 border-gray-300 rounded focus:ring-sea-green-500"
              />
            </label>

            <label className="flex items-center justify-between cursor-pointer">
              <div>
                <p className="font-medium">Review Reminders</p>
                <p className="text-sm text-gray-600">Reminders to leave reviews after your stay</p>
              </div>
              <input
                type="checkbox"
                checked={notificationSettings.review_reminders}
                onChange={(e) => handleNotificationChange('review_reminders', e.target.checked)}
                className="w-4 h-4 text-sea-green-600 border-gray-300 rounded focus:ring-sea-green-500"
              />
            </label>

            <label className="flex items-center justify-between cursor-pointer">
              <div>
                <p className="font-medium">Price Alerts</p>
                <p className="text-sm text-gray-600">Get notified when prices drop for saved properties</p>
              </div>
              <input
                type="checkbox"
                checked={notificationSettings.price_alerts}
                onChange={(e) => handleNotificationChange('price_alerts', e.target.checked)}
                className="w-4 h-4 text-sea-green-600 border-gray-300 rounded focus:ring-sea-green-500"
              />
            </label>
          </div>

          {/* SMS Notifications */}
          <div className="border-t pt-4">
            <div className="flex items-center gap-2 mb-3">
              <Smartphone className="h-4 w-4 text-gray-500" />
              <h4 className="font-medium text-gray-900">SMS Notifications</h4>
            </div>

            <label className="flex items-center justify-between cursor-pointer">
              <div>
                <p className="font-medium">SMS Notifications</p>
                <p className="text-sm text-gray-600">Receive notifications via SMS</p>
              </div>
              <input
                type="checkbox"
                checked={notificationSettings.sms_notifications}
                onChange={(e) => handleNotificationChange('sms_notifications', e.target.checked)}
                className="w-4 h-4 text-sea-green-600 border-gray-300 rounded focus:ring-sea-green-500"
              />
            </label>

            <label className="flex items-center justify-between cursor-pointer">
              <div>
                <p className="font-medium">Push Notifications</p>
                <p className="text-sm text-gray-600">Receive push notifications in your browser</p>
              </div>
              <input
                type="checkbox"
                checked={notificationSettings.push_notifications}
                onChange={(e) => handleNotificationChange('push_notifications', e.target.checked)}
                className="w-4 h-4 text-sea-green-600 border-gray-300 rounded focus:ring-sea-green-500"
              />
            </label>
          </div>

          {/* Marketing Communications */}
          <div className="border-t pt-4">
            <label className="flex items-center justify-between cursor-pointer">
              <div>
                <p className="font-medium">Marketing Communications</p>
                <p className="text-sm text-gray-600">Receive promotional offers and special deals</p>
              </div>
              <input
                type="checkbox"
                checked={notificationSettings.marketing_communications}
                onChange={(e) => handleNotificationChange('marketing_communications', e.target.checked)}
                className="w-4 h-4 text-sea-green-600 border-gray-300 rounded focus:ring-sea-green-500"
              />
            </label>
          </div>
        </CardContent>
      </Card>
      )}

      {/* Save Button for Preferences and Notifications */}
      {(activeSection === 'preferences' || activeSection === 'notifications') && (
        <div className="flex justify-end">
          <Button
            onClick={handleSaveSettings}
            disabled={saving}
            className="bg-sea-green-500 hover:bg-sea-green-600"
          >
            {saving ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            ) : (
              <Save className="h-4 w-4 mr-2" />
            )}
            Save Settings
          </Button>
        </div>
      )}

      {/* Security Section */}
      {activeSection === 'security' && (
        <AccountSecurity />
      )}

      {/* Error Message */}
      {error && (
        <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-center gap-2 text-red-700">
            <AlertCircle className="h-4 w-4" />
            <span className="text-sm">{error}</span>
          </div>
        </div>
      )}
    </div>
  );
};
