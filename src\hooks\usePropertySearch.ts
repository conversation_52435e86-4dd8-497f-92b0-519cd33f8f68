import { useState, useEffect, useCallback } from 'react';
import propertiesService from '../services/properties';

interface SearchFilters {
  location?: string;
  minPrice?: number;
  maxPrice?: number;
  guests?: number;
  propertyType?: string;
  page?: number;
  limit?: number;
}

interface Property {
  id: string;
  title: string;
  location: string;
  price: number;
  images: string[];
  description: string;
  available: boolean;
  propertyType?: string;
  maxGuests?: number;
  bedrooms?: number;
  bathrooms?: number;
  amenities?: string[];
  averageRating?: number;
  reviewCount?: number;
  owner?: {
    firstName: string;
    lastName: string;
  };
  coordinates?: {
    latitude: number;
    longitude: number;
  };
  cleaningFee?: number;
}

interface SearchResult {
  properties: Property[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

interface UsePropertySearchReturn {
  properties: Property[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  loading: boolean;
  error: string | null;
  searchProperties: (filters: SearchFilters) => Promise<void>;
  clearSearch: () => void;
  loadMore: () => Promise<void>;
  hasMore: boolean;
}

export const usePropertySearch = (initialFilters: SearchFilters = {}): UsePropertySearchReturn => {
  const [properties, setProperties] = useState<Property[]>([]);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 12,
    total: 0,
    totalPages: 0
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentFilters, setCurrentFilters] = useState<SearchFilters>(initialFilters);

  const searchProperties = useCallback(async (filters: SearchFilters) => {
    setLoading(true);
    setError(null);
    
    try {
      // Reset to page 1 for new search
      const searchFilters = { ...filters, page: 1 };
      setCurrentFilters(searchFilters);
      
      const result = await propertiesService.searchProperties(searchFilters);
      
      setProperties(result.properties || []);
      setPagination(result.pagination || {
        page: 1,
        limit: 12,
        total: 0,
        totalPages: 0
      });
    } catch (err: any) {
      setError(err.message || 'Failed to search properties');
      setProperties([]);
      setPagination({
        page: 1,
        limit: 12,
        total: 0,
        totalPages: 0
      });
    } finally {
      setLoading(false);
    }
  }, []);

  const loadMore = useCallback(async () => {
    if (loading || pagination.page >= pagination.totalPages) {
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const nextPage = pagination.page + 1;
      const result = await propertiesService.searchProperties({
        ...currentFilters,
        page: nextPage
      });

      setProperties(prev => [...prev, ...(result.properties || [])]);
      setPagination(result.pagination || pagination);
    } catch (err: any) {
      setError(err.message || 'Failed to load more properties');
    } finally {
      setLoading(false);
    }
  }, [loading, pagination, currentFilters]);

  const clearSearch = useCallback(() => {
    setProperties([]);
    setPagination({
      page: 1,
      limit: 12,
      total: 0,
      totalPages: 0
    });
    setError(null);
    setCurrentFilters({});
  }, []);

  const hasMore = pagination.page < pagination.totalPages;

  // Initial search if filters are provided
  useEffect(() => {
    if (Object.keys(initialFilters).length > 0) {
      searchProperties(initialFilters);
    }
  }, []); // Only run on mount

  return {
    properties,
    pagination,
    loading,
    error,
    searchProperties,
    clearSearch,
    loadMore,
    hasMore
  };
};
