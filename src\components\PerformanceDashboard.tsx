import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  performanceService, 
  getPerformanceMetrics, 
  getPerformanceScore, 
  getPerformanceRecommendations 
} from '@/services/performanceService';
import { 
  Zap, 
  TrendingUp, 
  TrendingDown, 
  Clock, 
  Eye, 
  Gauge, 
  AlertTriangle, 
  CheckCircle, 
  Download, 
  RefreshCw,
  Monitor,
  Wifi,
  HardDrive,
  Cpu,
  BarChart3,
  Activity,
  Target,
  Award
} from 'lucide-react';
import { 
  HoverAnimation, 
  SlideIn, 
  StaggeredAnimation,
  ScaleIn 
} from '@/components/ui/page-transitions';
import { QuickTooltip } from '@/components/ui/enhanced-tooltip';

interface PerformanceDashboardProps {
  className?: string;
  showDetailedMetrics?: boolean;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

export const PerformanceDashboard: React.FC<PerformanceDashboardProps> = ({
  className,
  showDetailedMetrics = true,
  autoRefresh = true,
  refreshInterval = 30000
}) => {
  const [metrics, setMetrics] = useState(getPerformanceMetrics());
  const [score, setScore] = useState(getPerformanceScore());
  const [recommendations, setRecommendations] = useState(getPerformanceRecommendations());
  const [isRefreshing, setIsRefreshing] = useState(false);

  const refreshMetrics = async () => {
    setIsRefreshing(true);
    
    // Simulate refresh delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    setMetrics(getPerformanceMetrics());
    setScore(getPerformanceScore());
    setRecommendations(getPerformanceRecommendations());
    setIsRefreshing(false);
  };

  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(refreshMetrics, refreshInterval);
    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval]);

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBadgeColor = (score: number) => {
    if (score >= 90) return 'bg-green-100 text-green-800';
    if (score >= 70) return 'bg-yellow-100 text-yellow-800';
    return 'bg-red-100 text-red-800';
  };

  const formatTime = (time?: number) => {
    if (!time) return 'N/A';
    return `${time.toFixed(0)}ms`;
  };

  const formatBytes = (bytes?: number) => {
    if (!bytes) return 'N/A';
    const mb = bytes / (1024 * 1024);
    return `${mb.toFixed(1)}MB`;
  };

  const coreWebVitals = [
    {
      name: 'LCP',
      label: 'Largest Contentful Paint',
      value: metrics.lcp,
      threshold: { good: 2500, poor: 4000 },
      icon: Eye,
      description: 'Time to render the largest content element'
    },
    {
      name: 'FID',
      label: 'First Input Delay',
      value: metrics.fid,
      threshold: { good: 100, poor: 300 },
      icon: Clock,
      description: 'Time from first user interaction to browser response'
    },
    {
      name: 'CLS',
      label: 'Cumulative Layout Shift',
      value: metrics.cls,
      threshold: { good: 0.1, poor: 0.25 },
      icon: Activity,
      description: 'Visual stability of the page during loading'
    }
  ];

  const additionalMetrics = [
    {
      name: 'FCP',
      label: 'First Contentful Paint',
      value: metrics.fcp,
      icon: Zap,
      format: formatTime
    },
    {
      name: 'TTFB',
      label: 'Time to First Byte',
      value: metrics.ttfb,
      icon: Wifi,
      format: formatTime
    },
    {
      name: 'Page Load',
      label: 'Page Load Time',
      value: metrics.pageLoadTime,
      icon: Monitor,
      format: formatTime
    },
    {
      name: 'Memory',
      label: 'JS Heap Used',
      value: metrics.usedJSHeapSize,
      icon: HardDrive,
      format: formatBytes
    }
  ];

  const getVitalStatus = (value?: number, threshold?: { good: number; poor: number }) => {
    if (!value || !threshold) return 'unknown';
    if (value <= threshold.good) return 'good';
    if (value <= threshold.poor) return 'needs-improvement';
    return 'poor';
  };

  const getVitalColor = (status: string) => {
    switch (status) {
      case 'good': return 'text-green-600';
      case 'needs-improvement': return 'text-yellow-600';
      case 'poor': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const exportData = () => {
    const data = performanceService.exportData();
    const blob = new Blob([data], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `performance-report-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <SlideIn direction="up" delay={100}>
      <div className={`space-y-6 ${className}`}>
        {/* Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          <div>
            <h2 className="text-3xl font-bold text-gray-900 flex items-center gap-2">
              <Gauge className="h-8 w-8 text-blue-600" />
              Performance Dashboard
            </h2>
            <p className="text-gray-600 mt-1">Real-time performance monitoring and optimization insights</p>
          </div>
          
          <div className="flex items-center gap-3">
            <QuickTooltip text="Refresh Metrics">
              <Button
                variant="outline"
                size="sm"
                onClick={refreshMetrics}
                disabled={isRefreshing}
              >
                <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
              </Button>
            </QuickTooltip>
            
            <QuickTooltip text="Export Report">
              <Button
                variant="outline"
                size="sm"
                onClick={exportData}
              >
                <Download className="h-4 w-4" />
              </Button>
            </QuickTooltip>
          </div>
        </div>

        {/* Performance Score */}
        <Card className="border-0 shadow-lg">
          <CardContent className="p-8">
            <div className="text-center">
              <div className="flex items-center justify-center mb-4">
                <div className="relative">
                  <div className="w-32 h-32 rounded-full border-8 border-gray-200 flex items-center justify-center">
                    <div className="text-center">
                      <div className={`text-4xl font-bold ${getScoreColor(score)}`}>
                        {score}
                      </div>
                      <div className="text-sm text-gray-600">Score</div>
                    </div>
                  </div>
                  <div className="absolute inset-0 rounded-full border-8 border-transparent">
                    <div 
                      className={`absolute inset-0 rounded-full border-8 border-t-transparent border-r-transparent border-b-transparent ${
                        score >= 90 ? 'border-l-green-500' : score >= 70 ? 'border-l-yellow-500' : 'border-l-red-500'
                      }`}
                      style={{ 
                        transform: `rotate(${(score / 100) * 360}deg)`,
                        transition: 'transform 1s ease-in-out'
                      }}
                    />
                  </div>
                </div>
              </div>
              
              <Badge className={getScoreBadgeColor(score)}>
                {score >= 90 ? 'Excellent' : score >= 70 ? 'Good' : 'Needs Improvement'}
              </Badge>
              
              <p className="text-gray-600 mt-2">
                Overall performance score based on Core Web Vitals and custom metrics
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Core Web Vitals */}
        <Card className="border-0 shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5 text-blue-600" />
              Core Web Vitals
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <StaggeredAnimation delay={100}>
                {coreWebVitals.map((vital) => {
                  const Icon = vital.icon;
                  const status = getVitalStatus(vital.value, vital.threshold);
                  const color = getVitalColor(status);
                  
                  return (
                    <HoverAnimation key={vital.name} type="lift">
                      <Card className="border border-gray-200">
                        <CardContent className="p-6">
                          <div className="flex items-center justify-between mb-4">
                            <Icon className={`h-6 w-6 ${color}`} />
                            <Badge 
                              className={
                                status === 'good' ? 'bg-green-100 text-green-800' :
                                status === 'needs-improvement' ? 'bg-yellow-100 text-yellow-800' :
                                'bg-red-100 text-red-800'
                              }
                            >
                              {status === 'good' ? 'Good' : 
                               status === 'needs-improvement' ? 'Needs Work' : 'Poor'}
                            </Badge>
                          </div>
                          
                          <div className="text-2xl font-bold text-gray-900 mb-1">
                            {vital.name === 'CLS' 
                              ? vital.value?.toFixed(3) || 'N/A'
                              : formatTime(vital.value)
                            }
                          </div>
                          
                          <div className="text-sm font-medium text-gray-700 mb-2">
                            {vital.label}
                          </div>
                          
                          <div className="text-xs text-gray-500">
                            {vital.description}
                          </div>
                          
                          {vital.threshold && vital.value && (
                            <div className="mt-4">
                              <Progress 
                                value={Math.min((vital.value / vital.threshold.poor) * 100, 100)}
                                className="h-2"
                              />
                              <div className="flex justify-between text-xs text-gray-500 mt-1">
                                <span>0</span>
                                <span>{vital.threshold.good}</span>
                                <span>{vital.threshold.poor}+</span>
                              </div>
                            </div>
                          )}
                        </CardContent>
                      </Card>
                    </HoverAnimation>
                  );
                })}
              </StaggeredAnimation>
            </div>
          </CardContent>
        </Card>

        {/* Additional Metrics */}
        {showDetailedMetrics && (
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5 text-purple-600" />
                Additional Metrics
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <StaggeredAnimation delay={200}>
                  {additionalMetrics.map((metric) => {
                    const Icon = metric.icon;
                    
                    return (
                      <HoverAnimation key={metric.name} type="scale">
                        <div className="text-center p-4 bg-gray-50 rounded-lg">
                          <Icon className="h-8 w-8 text-gray-600 mx-auto mb-2" />
                          <div className="text-lg font-bold text-gray-900">
                            {metric.format ? metric.format(metric.value) : (metric.value || 'N/A')}
                          </div>
                          <div className="text-sm text-gray-600">{metric.label}</div>
                        </div>
                      </HoverAnimation>
                    );
                  })}
                </StaggeredAnimation>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Recommendations */}
        {recommendations.length > 0 && (
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Award className="h-5 w-5 text-orange-600" />
                Performance Recommendations
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {recommendations.map((recommendation, index) => (
                  <div key={index} className="flex items-start gap-3 p-3 bg-orange-50 rounded-lg">
                    <AlertTriangle className="h-5 w-5 text-orange-600 mt-0.5 flex-shrink-0" />
                    <p className="text-sm text-orange-800">{recommendation}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Network Information */}
        {metrics.connectionType && (
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Wifi className="h-5 w-5 text-green-600" />
                Network Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-lg font-bold text-gray-900">{metrics.connectionType}</div>
                  <div className="text-sm text-gray-600">Connection Type</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-gray-900">{metrics.effectiveType}</div>
                  <div className="text-sm text-gray-600">Effective Type</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-gray-900">{metrics.downlink}Mbps</div>
                  <div className="text-sm text-gray-600">Downlink</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-gray-900">{metrics.rtt}ms</div>
                  <div className="text-sm text-gray-600">RTT</div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </SlideIn>
  );
};
