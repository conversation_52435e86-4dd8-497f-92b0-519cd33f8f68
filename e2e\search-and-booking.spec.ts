import { test, expect } from '@playwright/test';

test.describe('Search and Booking Flow', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
  });

  test('should complete full search to booking flow', async ({ page }) => {
    // Step 1: Search for properties
    await page.getByPlaceholder(/where are you going/i).fill('Cape Town');
    
    // Select dates (mock date picker interaction)
    await page.getByText(/check-in/i).click();
    await page.getByRole('button', { name: '15' }).first().click(); // Select 15th
    
    await page.getByText(/check-out/i).click();
    await page.getByRole('button', { name: '20' }).first().click(); // Select 20th
    
    // Select guests
    await page.getByText(/guests/i).click();
    await page.getByRole('button', { name: /\+/i }).first().click(); // 2 guests
    
    // Submit search
    await page.getByRole('button', { name: /search/i }).click();
    
    // Step 2: Verify search results page
    await expect(page).toHaveURL(/\/search/);
    await expect(page.getByText(/search results/i)).toBeVisible();
    
    // Wait for properties to load
    await page.waitForSelector('[data-testid="property-card"]', { timeout: 10000 });
    
    // Step 3: Select a property
    const firstProperty = page.locator('[data-testid="property-card"]').first();
    await firstProperty.click();
    
    // Step 4: Verify property detail page
    await expect(page).toHaveURL(/\/property\/\d+/);
    await expect(page.getByRole('heading', { level: 1 })).toBeVisible();
    
    // Check property details are displayed
    await expect(page.getByText(/per night/i)).toBeVisible();
    await expect(page.getByText(/guests/i)).toBeVisible();
    await expect(page.getByText(/bedrooms/i)).toBeVisible();
    
    // Step 5: Initiate booking
    await page.getByRole('button', { name: /book now/i }).click();
    
    // Step 6: Handle authentication (if required)
    const loginForm = page.locator('form').filter({ hasText: /sign in/i });
    if (await loginForm.isVisible()) {
      await page.getByLabel(/email/i).fill('<EMAIL>');
      await page.getByLabel(/password/i).fill('password123');
      await page.getByRole('button', { name: /sign in/i }).click();
    }
    
    // Step 7: Complete booking form
    await expect(page.getByText(/booking details/i)).toBeVisible();
    
    // Fill guest information
    await page.getByLabel(/first name/i).fill('John');
    await page.getByLabel(/last name/i).fill('Doe');
    await page.getByLabel(/phone/i).fill('+27123456789');
    
    // Add special requests (if available)
    const specialRequests = page.getByLabel(/special requests/i);
    if (await specialRequests.isVisible()) {
      await specialRequests.fill('Late check-in please');
    }
    
    // Step 8: Review booking summary
    await expect(page.getByText(/booking summary/i)).toBeVisible();
    await expect(page.getByText(/total amount/i)).toBeVisible();
    
    // Step 9: Proceed to payment
    await page.getByRole('button', { name: /proceed to payment/i }).click();
    
    // Step 10: Complete payment (mock payment)
    await expect(page.getByText(/payment details/i)).toBeVisible();
    
    // Fill payment form
    await page.getByLabel(/card number/i).fill('****************');
    await page.getByLabel(/expiry/i).fill('12/25');
    await page.getByLabel(/cvv/i).fill('123');
    await page.getByLabel(/cardholder name/i).fill('John Doe');
    
    // Submit payment
    await page.getByRole('button', { name: /complete booking/i }).click();
    
    // Step 11: Verify booking confirmation
    await expect(page).toHaveURL(/\/booking\/confirmation/);
    await expect(page.getByText(/booking confirmed/i)).toBeVisible();
    await expect(page.getByText(/booking reference/i)).toBeVisible();
  });

  test('should filter search results', async ({ page }) => {
    // Navigate to search page
    await page.goto('/search?location=Cape Town');
    
    // Wait for results to load
    await page.waitForSelector('[data-testid="property-card"]', { timeout: 10000 });
    
    // Apply price filter
    await page.getByLabel(/min price/i).fill('1000');
    await page.getByLabel(/max price/i).fill('3000');
    
    // Apply property type filter
    await page.getByLabel(/villa/i).check();
    
    // Apply amenities filter
    await page.getByLabel(/pool/i).check();
    await page.getByLabel(/wifi/i).check();
    
    // Apply filters
    await page.getByRole('button', { name: /apply filters/i }).click();
    
    // Verify filtered results
    await page.waitForSelector('[data-testid="property-card"]', { timeout: 10000 });
    
    // Check that results match filters
    const propertyCards = page.locator('[data-testid="property-card"]');
    const firstCard = propertyCards.first();
    
    // Verify price is within range
    const priceText = await firstCard.getByText(/R\d+/).textContent();
    const price = parseInt(priceText?.replace(/[^\d]/g, '') || '0');
    expect(price).toBeGreaterThanOrEqual(1000);
    expect(price).toBeLessThanOrEqual(3000);
  });

  test('should handle search with no results', async ({ page }) => {
    // Search for non-existent location
    await page.getByPlaceholder(/where are you going/i).fill('Nonexistent City');
    await page.getByRole('button', { name: /search/i }).click();
    
    // Verify no results message
    await expect(page.getByText(/no properties found/i)).toBeVisible();
    await expect(page.getByText(/try adjusting your search/i)).toBeVisible();
    
    // Check suggestions are provided
    await expect(page.getByText(/popular destinations/i)).toBeVisible();
  });

  test('should save property to favorites', async ({ page }) => {
    // Navigate to property detail page
    await page.goto('/property/1');
    
    // Click favorite button
    await page.getByRole('button', { name: /add to favorites/i }).click();
    
    // Handle authentication if required
    const loginForm = page.locator('form').filter({ hasText: /sign in/i });
    if (await loginForm.isVisible()) {
      await page.getByLabel(/email/i).fill('<EMAIL>');
      await page.getByLabel(/password/i).fill('password123');
      await page.getByRole('button', { name: /sign in/i }).click();
    }
    
    // Verify property is added to favorites
    await expect(page.getByText(/added to favorites/i)).toBeVisible();
    
    // Navigate to favorites page
    await page.getByRole('link', { name: /favorites/i }).click();
    
    // Verify property appears in favorites
    await expect(page.locator('[data-testid="property-card"]')).toBeVisible();
  });

  test('should share property', async ({ page }) => {
    // Navigate to property detail page
    await page.goto('/property/1');
    
    // Click share button
    await page.getByRole('button', { name: /share/i }).click();
    
    // Verify share modal opens
    await expect(page.getByText(/share this property/i)).toBeVisible();
    
    // Test copy link functionality
    await page.getByRole('button', { name: /copy link/i }).click();
    
    // Verify success message
    await expect(page.getByText(/link copied/i)).toBeVisible();
  });

  test('should handle booking cancellation', async ({ page }) => {
    // Mock authenticated user with existing booking
    await page.addInitScript(() => {
      localStorage.setItem('auth_token', 'mock-token');
    });
    
    // Navigate to bookings page
    await page.goto('/bookings');
    
    // Find a booking to cancel
    const booking = page.locator('[data-testid="booking-card"]').first();
    await booking.getByRole('button', { name: /cancel/i }).click();
    
    // Confirm cancellation
    await page.getByRole('button', { name: /confirm cancellation/i }).click();
    
    // Verify cancellation success
    await expect(page.getByText(/booking cancelled/i)).toBeVisible();
  });

  test('should display booking history', async ({ page }) => {
    // Mock authenticated user
    await page.addInitScript(() => {
      localStorage.setItem('auth_token', 'mock-token');
    });
    
    // Navigate to bookings page
    await page.goto('/bookings');
    
    // Verify booking history is displayed
    await expect(page.getByText(/your bookings/i)).toBeVisible();
    
    // Check booking cards are present
    await expect(page.locator('[data-testid="booking-card"]')).toBeVisible();
    
    // Verify booking details
    const firstBooking = page.locator('[data-testid="booking-card"]').first();
    await expect(firstBooking.getByText(/check-in/i)).toBeVisible();
    await expect(firstBooking.getByText(/check-out/i)).toBeVisible();
    await expect(firstBooking.getByText(/total/i)).toBeVisible();
  });

  test('should handle search pagination', async ({ page }) => {
    // Navigate to search with many results
    await page.goto('/search?location=Cape Town');
    
    // Wait for results to load
    await page.waitForSelector('[data-testid="property-card"]', { timeout: 10000 });
    
    // Check pagination controls
    const nextButton = page.getByRole('button', { name: /next/i });
    if (await nextButton.isVisible()) {
      await nextButton.click();
      
      // Verify page 2 loads
      await expect(page).toHaveURL(/page=2/);
      await page.waitForSelector('[data-testid="property-card"]', { timeout: 10000 });
    }
  });

  test('should handle map view toggle', async ({ page }) => {
    // Navigate to search page
    await page.goto('/search?location=Cape Town');
    
    // Toggle map view
    const mapToggle = page.getByRole('button', { name: /map view/i });
    if (await mapToggle.isVisible()) {
      await mapToggle.click();
      
      // Verify map is displayed
      await expect(page.locator('[data-testid="map-container"]')).toBeVisible();
      
      // Toggle back to list view
      await page.getByRole('button', { name: /list view/i }).click();
      await expect(page.locator('[data-testid="property-list"]')).toBeVisible();
    }
  });
});
