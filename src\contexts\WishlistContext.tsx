import React, { createContext, useContext, useState, useCallback, useEffect } from 'react';
import { Property } from './ComparisonContext';

interface WishlistItem extends Property {
  addedAt: Date;
  notes?: string;
}

interface WishlistContextType {
  wishlistItems: WishlistItem[];
  addToWishlist: (property: Property, notes?: string) => void;
  removeFromWishlist: (propertyId: string) => void;
  clearWishlist: () => void;
  isInWishlist: (propertyId: string) => boolean;
  updateNotes: (propertyId: string, notes: string) => void;
  getWishlistItem: (propertyId: string) => WishlistItem | undefined;
  wishlistCount: number;
}

const WishlistContext = createContext<WishlistContextType | undefined>(undefined);

export const useWishlist = () => {
  const context = useContext(WishlistContext);
  if (context === undefined) {
    throw new Error('useWishlist must be used within a WishlistProvider');
  }
  return context;
};

interface WishlistProviderProps {
  children: React.ReactNode;
  storageKey?: string;
}

export const WishlistProvider: React.FC<WishlistProviderProps> = ({
  children,
  storageKey = 'stayfinder-wishlist'
}) => {
  const [wishlistItems, setWishlistItems] = useState<WishlistItem[]>([]);

  // Load wishlist from localStorage on mount
  useEffect(() => {
    try {
      const stored = localStorage.getItem(storageKey);
      if (stored) {
        const parsed = JSON.parse(stored);
        // Convert date strings back to Date objects
        const itemsWithDates = parsed.map((item: any) => ({
          ...item,
          addedAt: new Date(item.addedAt)
        }));
        setWishlistItems(itemsWithDates);
      }
    } catch (error) {
      console.warn('Failed to load wishlist from localStorage:', error);
    }
  }, [storageKey]);

  // Save wishlist to localStorage whenever it changes
  const saveWishlist = useCallback((items: WishlistItem[]) => {
    try {
      localStorage.setItem(storageKey, JSON.stringify(items));
    } catch (error) {
      console.warn('Failed to save wishlist to localStorage:', error);
    }
  }, [storageKey]);

  const addToWishlist = useCallback((property: Property, notes?: string) => {
    setWishlistItems(prev => {
      // Check if property is already in wishlist
      if (prev.some(item => item.id === property.id)) {
        return prev;
      }
      
      const newItem: WishlistItem = {
        ...property,
        addedAt: new Date(),
        notes
      };
      
      const newItems = [newItem, ...prev]; // Add to beginning
      saveWishlist(newItems);
      return newItems;
    });
  }, [saveWishlist]);

  const removeFromWishlist = useCallback((propertyId: string) => {
    setWishlistItems(prev => {
      const newItems = prev.filter(item => item.id !== propertyId);
      saveWishlist(newItems);
      return newItems;
    });
  }, [saveWishlist]);

  const clearWishlist = useCallback(() => {
    setWishlistItems([]);
    saveWishlist([]);
  }, [saveWishlist]);

  const isInWishlist = useCallback((propertyId: string) => {
    return wishlistItems.some(item => item.id === propertyId);
  }, [wishlistItems]);

  const updateNotes = useCallback((propertyId: string, notes: string) => {
    setWishlistItems(prev => {
      const newItems = prev.map(item =>
        item.id === propertyId ? { ...item, notes } : item
      );
      saveWishlist(newItems);
      return newItems;
    });
  }, [saveWishlist]);

  const getWishlistItem = useCallback((propertyId: string) => {
    return wishlistItems.find(item => item.id === propertyId);
  }, [wishlistItems]);

  const value: WishlistContextType = {
    wishlistItems,
    addToWishlist,
    removeFromWishlist,
    clearWishlist,
    isInWishlist,
    updateNotes,
    getWishlistItem,
    wishlistCount: wishlistItems.length
  };

  return (
    <WishlistContext.Provider value={value}>
      {children}
    </WishlistContext.Provider>
  );
};
