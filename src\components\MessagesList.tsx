import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { messagesService, Conversation } from '../services/messagesService';
import { 
  MessageSquare, 
  User,
  Clock,
  Home,
  Search,
  Plus,
  MoreVertical
} from 'lucide-react';

interface MessagesListProps {
  onConversationSelect: (conversation: Conversation) => void;
  selectedConversationId?: string;
  className?: string;
}

// Chat Interface Component
interface ChatInterfaceProps {
  conversation: Conversation;
  onBack?: () => void;
  className?: string;
}

const ChatInterface: React.FC<ChatInterfaceProps> = ({
  conversation,
  onBack,
  className
}) => {
  const [messages, setMessages] = useState<any[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);

  useEffect(() => {
    fetchMessages();
    markAsRead();
  }, [conversation.id]);

  const fetchMessages = async () => {
    try {
      setLoading(true);
      const data = await messagesService.getMessages(conversation.id);
      setMessages(data);
    } catch (error) {
      console.error('Error fetching messages:', error);
    } finally {
      setLoading(false);
    }
  };

  const markAsRead = async () => {
    try {
      await messagesService.markMessagesAsRead(conversation.id);
    } catch (error) {
      console.error('Error marking messages as read:', error);
    }
  };

  const handleSendMessage = async () => {
    if (!newMessage.trim() || sending) return;

    try {
      setSending(true);
      await messagesService.sendMessage({
        conversation_id: conversation.id,
        content: newMessage.trim(),
        message_type: 'text'
      });

      setNewMessage('');
      await fetchMessages(); // Refresh messages
    } catch (error) {
      console.error('Error sending message:', error);
    } finally {
      setSending(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <Card className={className}>
      {/* Header */}
      <CardHeader className="pb-3 border-b">
        <div className="flex items-center gap-3">
          {onBack && (
            <Button variant="ghost" size="sm" onClick={onBack}>
              ←
            </Button>
          )}
          <div className="w-10 h-10 rounded-full overflow-hidden bg-gray-200 flex items-center justify-center">
            {conversation.other_user_avatar ? (
              <img
                src={conversation.other_user_avatar}
                alt={conversation.other_user_name}
                className="w-full h-full object-cover"
              />
            ) : (
              <User className="h-5 w-5 text-gray-400" />
            )}
          </div>
          <div className="flex-1">
            <h3 className="font-semibold text-gray-900">{conversation.other_user_name}</h3>
            <p className="text-sm text-gray-600">{conversation.property_title}</p>
          </div>
        </div>
      </CardHeader>

      {/* Messages */}
      <CardContent className="p-0">
        <div className="h-96 overflow-y-auto p-4 space-y-4">
          {loading ? (
            <div className="flex justify-center items-center h-full">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-sea-green-500"></div>
            </div>
          ) : messages.length === 0 ? (
            <div className="text-center text-gray-500 py-8">
              <MessageSquare className="h-12 w-12 mx-auto mb-2 opacity-50" />
              <p>No messages yet. Start the conversation!</p>
            </div>
          ) : (
            messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.is_own_message ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                    message.is_own_message
                      ? 'bg-sea-green-500 text-white'
                      : 'bg-gray-100 text-gray-900'
                  }`}
                >
                  <p className="text-sm">{message.content}</p>
                  <p className={`text-xs mt-1 ${
                    message.is_own_message ? 'text-sea-green-100' : 'text-gray-500'
                  }`}>
                    {messagesService.formatMessageTime(message.created_at)}
                  </p>
                </div>
              </div>
            ))
          )}
        </div>

        {/* Message Input */}
        <div className="border-t p-4">
          <div className="flex items-center gap-2">
            <input
              type="text"
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Type a message..."
              disabled={sending}
              className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-sea-green-500 focus:border-transparent"
            />
            <Button
              onClick={handleSendMessage}
              disabled={!newMessage.trim() || sending}
              className="bg-sea-green-500 hover:bg-sea-green-600"
            >
              {sending ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              ) : (
                'Send'
              )}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export const MessagesList: React.FC<MessagesListProps> = ({
  onConversationSelect,
  selectedConversationId,
  className
}) => {
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    fetchConversations();
  }, []);

  const fetchConversations = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const data = await messagesService.getConversations();
      setConversations(data);
    } catch (err) {
      console.error('Error fetching conversations:', err);
      setError('Failed to load conversations');
    } finally {
      setLoading(false);
    }
  };

  const filteredConversations = conversations.filter(conversation =>
    conversation.other_user_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    conversation.property_title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    conversation.last_message?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const renderConversationItem = (conversation: Conversation) => {
    const isSelected = conversation.id === selectedConversationId;
    const hasUnread = conversation.unread_count > 0;
    
    return (
      <div
        key={conversation.id}
        onClick={() => onConversationSelect(conversation)}
        className={`
          p-4 border-b border-gray-200 cursor-pointer transition-colors hover:bg-gray-50
          ${isSelected ? 'bg-sea-green-50 border-sea-green-200' : ''}
        `}
      >
        <div className="flex items-start gap-3">
          {/* Avatar */}
          <div className="w-12 h-12 rounded-full overflow-hidden bg-gray-200 flex items-center justify-center flex-shrink-0">
            {conversation.other_user_avatar ? (
              <img
                src={conversation.other_user_avatar}
                alt={conversation.other_user_name}
                className="w-full h-full object-cover"
              />
            ) : (
              <User className="h-6 w-6 text-gray-400" />
            )}
          </div>

          {/* Content */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between mb-1">
              <h4 className={`font-medium text-gray-900 truncate ${hasUnread ? 'font-semibold' : ''}`}>
                {conversation.other_user_name}
              </h4>
              <div className="flex items-center gap-2">
                {hasUnread && (
                  <Badge variant="default" className="bg-sea-green-500 text-white text-xs">
                    {conversation.unread_count}
                  </Badge>
                )}
                <span className="text-xs text-gray-500">
                  {conversation.last_message_at && 
                    messagesService.formatLastMessageTime(conversation.last_message_at)
                  }
                </span>
              </div>
            </div>

            {/* Property info */}
            <div className="flex items-center gap-1 mb-2">
              <Home className="h-3 w-3 text-gray-400" />
              <span className="text-xs text-gray-600 truncate">
                {conversation.property_title}
              </span>
            </div>

            {/* Last message */}
            <div className="flex items-center gap-1">
              {conversation.last_message_type && conversation.last_message_type !== 'text' && (
                <span className="text-sm">
                  {messagesService.getMessageTypeIcon(conversation.last_message_type)}
                </span>
              )}
              <p className={`text-sm text-gray-600 truncate ${hasUnread ? 'font-medium' : ''}`}>
                {conversation.last_message ? 
                  messagesService.truncateMessage(conversation.last_message, 60) : 
                  'No messages yet'
                }
              </p>
            </div>

            {/* Status indicators */}
            <div className="flex items-center gap-2 mt-2">
              {conversation.is_host && (
                <Badge variant="outline" className="text-xs">
                  Host
                </Badge>
              )}
              {conversation.booking_id && (
                <Badge variant="outline" className="text-xs">
                  Booking
                </Badge>
              )}
              {conversation.status === 'archived' && (
                <Badge variant="secondary" className="text-xs">
                  Archived
                </Badge>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="animate-pulse space-y-4">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
              <div className="flex-1 space-y-2">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
            </div>
            {[...Array(4)].map((_, i) => (
              <div key={i} className="flex items-center gap-3">
                <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-2/3"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/3"></div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="p-6 text-center">
          <MessageSquare className="h-12 w-12 mx-auto mb-4 text-gray-400" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Unable to Load Messages
          </h3>
          <p className="text-gray-600 mb-4">{error}</p>
          <Button onClick={fetchConversations}>
            Try Again
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            Messages
          </CardTitle>
          <Button size="sm" variant="outline">
            <Plus className="h-4 w-4" />
          </Button>
        </div>
        
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search conversations..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-sea-green-500 focus:border-transparent"
          />
        </div>
      </CardHeader>

      <CardContent className="p-0">
        {filteredConversations.length === 0 ? (
          <div className="p-6 text-center">
            <MessageSquare className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {searchQuery ? 'No matching conversations' : 'No conversations yet'}
            </h3>
            <p className="text-gray-600">
              {searchQuery 
                ? 'Try adjusting your search terms'
                : 'Start a conversation with a host or guest'
              }
            </p>
          </div>
        ) : (
          <div className="max-h-96 overflow-y-auto">
            {filteredConversations.map(renderConversationItem)}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export { ChatInterface };
