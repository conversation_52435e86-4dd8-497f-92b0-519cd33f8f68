import { supabase } from '@/integrations/supabase/client';

export interface UserFavorite {
  id: string;
  user_id: string;
  property_id: string;
  created_at: string;
}

export class FavoritesService {
  /**
   * Get all favorites for the current user
   */
  static async getUserFavorites(userId: string): Promise<UserFavorite[]> {
    try {
      const { data, error } = await supabase
        .from('user_favorites')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching user favorites:', error);
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Failed to fetch user favorites:', error);
      throw error;
    }
  }

  /**
   * Check if a property is favorited by the user
   */
  static async isFavorited(userId: string, propertyId: string): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from('user_favorites')
        .select('id')
        .eq('user_id', userId)
        .eq('property_id', propertyId)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
        console.error('Error checking favorite status:', error);
        throw error;
      }

      return !!data;
    } catch (error) {
      console.error('Failed to check favorite status:', error);
      return false;
    }
  }

  /**
   * Add a property to user's favorites
   */
  static async addFavorite(userId: string, propertyId: string): Promise<UserFavorite> {
    try {
      const { data, error } = await supabase
        .from('user_favorites')
        .insert({
          user_id: userId,
          property_id: propertyId
        })
        .select()
        .single();

      if (error) {
        console.error('Error adding favorite:', error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Failed to add favorite:', error);
      throw error;
    }
  }

  /**
   * Remove a property from user's favorites
   */
  static async removeFavorite(userId: string, propertyId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('user_favorites')
        .delete()
        .eq('user_id', userId)
        .eq('property_id', propertyId);

      if (error) {
        console.error('Error removing favorite:', error);
        throw error;
      }
    } catch (error) {
      console.error('Failed to remove favorite:', error);
      throw error;
    }
  }

  /**
   * Toggle favorite status for a property
   */
  static async toggleFavorite(userId: string, propertyId: string): Promise<boolean> {
    try {
      const isFav = await this.isFavorited(userId, propertyId);
      
      if (isFav) {
        await this.removeFavorite(userId, propertyId);
        return false;
      } else {
        await this.addFavorite(userId, propertyId);
        return true;
      }
    } catch (error) {
      console.error('Failed to toggle favorite:', error);
      throw error;
    }
  }

  /**
   * Get favorite property IDs for the user (for quick lookups)
   */
  static async getFavoritePropertyIds(userId: string): Promise<string[]> {
    try {
      const { data, error } = await supabase
        .from('user_favorites')
        .select('property_id')
        .eq('user_id', userId);

      if (error) {
        console.error('Error fetching favorite property IDs:', error);
        throw error;
      }

      return data?.map(item => item.property_id) || [];
    } catch (error) {
      console.error('Failed to fetch favorite property IDs:', error);
      return [];
    }
  }

  /**
   * Get favorite properties with full property details
   */
  static async getFavoriteProperties(userId: string) {
    try {
      const { data, error } = await supabase
        .from('user_favorites')
        .select(`
          id,
          created_at,
          properties (
            id,
            title,
            description,
            city,
            province,
            price_per_night,
            max_guests,
            bedrooms,
            bathrooms,
            property_type,
            average_rating,
            total_reviews,
            featured,
            property_images (
              image_url,
              alt_text,
              is_primary
            )
          )
        `)
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching favorite properties:', error);
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Failed to fetch favorite properties:', error);
      throw error;
    }
  }
}
