// Recommendation Engine for StayFinder
// Provides personalized property recommendations based on user behavior and preferences

interface UserPreferences {
  priceRange: [number, number];
  preferredPropertyTypes: string[];
  preferredAmenities: string[];
  preferredLocations: string[];
  groupSize: number;
  travelPurpose: 'business' | 'leisure' | 'family' | 'romantic' | 'adventure';
  accessibility: string[];
}

interface UserBehavior {
  viewedProperties: string[];
  savedProperties: string[];
  bookedProperties: string[];
  searchHistory: {
    location: string;
    checkIn: string;
    checkOut: string;
    guests: number;
    timestamp: Date;
  }[];
  clickedAmenities: string[];
  timeSpentOnProperties: { [propertyId: string]: number };
}

interface Property {
  id: string;
  name: string;
  location: string;
  city: string;
  province: string;
  propertyType: string;
  pricePerNight: number;
  rating: number;
  reviewCount: number;
  amenities: string[];
  capacity: number;
  images: string[];
  description: string;
  host: {
    id: string;
    name: string;
    isSuperhost: boolean;
    responseRate: number;
  };
  features: string[];
  accessibility: string[];
  coordinates: [number, number];
}

interface RecommendationScore {
  propertyId: string;
  score: number;
  reasons: string[];
  category: 'trending' | 'similar' | 'personalized' | 'location' | 'price';
}

export class RecommendationEngine {
  private userPreferences: UserPreferences;
  private userBehavior: UserBehavior;
  private properties: Property[];

  constructor(
    userPreferences: UserPreferences,
    userBehavior: UserBehavior,
    properties: Property[]
  ) {
    this.userPreferences = userPreferences;
    this.userBehavior = userBehavior;
    this.properties = properties;
  }

  // Main recommendation method
  public getRecommendations(limit: number = 10): RecommendationScore[] {
    const scores: RecommendationScore[] = [];

    for (const property of this.properties) {
      // Skip already viewed or booked properties
      if (this.userBehavior.viewedProperties.includes(property.id) ||
          this.userBehavior.bookedProperties.includes(property.id)) {
        continue;
      }

      const score = this.calculatePropertyScore(property);
      if (score.score > 0) {
        scores.push(score);
      }
    }

    // Sort by score and return top recommendations
    return scores
      .sort((a, b) => b.score - a.score)
      .slice(0, limit);
  }

  // Calculate comprehensive score for a property
  private calculatePropertyScore(property: Property): RecommendationScore {
    let totalScore = 0;
    const reasons: string[] = [];
    let category: RecommendationScore['category'] = 'personalized';

    // 1. Price compatibility (25% weight)
    const priceScore = this.calculatePriceScore(property);
    totalScore += priceScore * 0.25;
    if (priceScore > 0.7) {
      reasons.push('Within your budget');
    }

    // 2. Property type preference (20% weight)
    const typeScore = this.calculatePropertyTypeScore(property);
    totalScore += typeScore * 0.20;
    if (typeScore > 0.8) {
      reasons.push(`Matches your preference for ${property.propertyType}s`);
    }

    // 3. Amenities match (20% weight)
    const amenitiesScore = this.calculateAmenitiesScore(property);
    totalScore += amenitiesScore * 0.20;
    if (amenitiesScore > 0.6) {
      reasons.push('Has amenities you typically look for');
    }

    // 4. Location preference (15% weight)
    const locationScore = this.calculateLocationScore(property);
    totalScore += locationScore * 0.15;
    if (locationScore > 0.7) {
      reasons.push('In an area you\'ve shown interest in');
      category = 'location';
    }

    // 5. Capacity match (10% weight)
    const capacityScore = this.calculateCapacityScore(property);
    totalScore += capacityScore * 0.10;
    if (capacityScore === 1) {
      reasons.push('Perfect size for your group');
    }

    // 6. Quality indicators (10% weight)
    const qualityScore = this.calculateQualityScore(property);
    totalScore += qualityScore * 0.10;
    if (qualityScore > 0.8) {
      reasons.push('Highly rated property');
    }

    // Bonus factors
    const bonusScore = this.calculateBonusScore(property);
    totalScore += bonusScore;

    // Determine category based on highest contributing factor
    if (priceScore > 0.8 && property.pricePerNight < this.userPreferences.priceRange[1] * 0.7) {
      category = 'price';
      reasons.unshift('Great value for money');
    }

    if (this.isTrendingProperty(property)) {
      category = 'trending';
      reasons.unshift('Trending in your area');
    }

    return {
      propertyId: property.id,
      score: Math.min(totalScore, 1), // Cap at 1.0
      reasons: reasons.slice(0, 3), // Limit to top 3 reasons
      category
    };
  }

  private calculatePriceScore(property: Property): number {
    const [minPrice, maxPrice] = this.userPreferences.priceRange;
    const price = property.pricePerNight;

    if (price < minPrice || price > maxPrice) {
      return 0;
    }

    // Prefer properties in the middle of the range
    const midPoint = (minPrice + maxPrice) / 2;
    const distance = Math.abs(price - midPoint);
    const maxDistance = (maxPrice - minPrice) / 2;
    
    return 1 - (distance / maxDistance);
  }

  private calculatePropertyTypeScore(property: Property): number {
    if (this.userPreferences.preferredPropertyTypes.length === 0) {
      return 0.5; // Neutral if no preference
    }

    return this.userPreferences.preferredPropertyTypes.includes(property.propertyType) ? 1 : 0;
  }

  private calculateAmenitiesScore(property: Property): number {
    if (this.userPreferences.preferredAmenities.length === 0) {
      return 0.5; // Neutral if no preference
    }

    const matchingAmenities = property.amenities.filter(amenity =>
      this.userPreferences.preferredAmenities.includes(amenity)
    );

    const baseScore = matchingAmenities.length / this.userPreferences.preferredAmenities.length;

    // Bonus for amenities user has clicked on before
    const clickedAmenities = property.amenities.filter(amenity =>
      this.userBehavior.clickedAmenities.includes(amenity)
    );
    const clickBonus = clickedAmenities.length * 0.1;

    return Math.min(baseScore + clickBonus, 1);
  }

  private calculateLocationScore(property: Property): number {
    let score = 0;

    // Check if location matches preferences
    if (this.userPreferences.preferredLocations.includes(property.city) ||
        this.userPreferences.preferredLocations.includes(property.province)) {
      score += 0.5;
    }

    // Check search history for location patterns
    const locationSearches = this.userBehavior.searchHistory.filter(search =>
      search.location.toLowerCase().includes(property.city.toLowerCase()) ||
      search.location.toLowerCase().includes(property.province.toLowerCase())
    );

    if (locationSearches.length > 0) {
      score += 0.3;
    }

    // Check if similar to saved properties
    const savedInSameArea = this.userBehavior.savedProperties.filter(savedId => {
      const savedProperty = this.properties.find(p => p.id === savedId);
      return savedProperty && (
        savedProperty.city === property.city ||
        savedProperty.province === property.province
      );
    });

    if (savedInSameArea.length > 0) {
      score += 0.2;
    }

    return Math.min(score, 1);
  }

  private calculateCapacityScore(property: Property): number {
    const userGroupSize = this.userPreferences.groupSize;
    
    if (property.capacity < userGroupSize) {
      return 0; // Too small
    }

    if (property.capacity === userGroupSize || property.capacity === userGroupSize + 1) {
      return 1; // Perfect fit
    }

    if (property.capacity <= userGroupSize + 2) {
      return 0.8; // Good fit
    }

    // Penalty for being too large
    const excess = property.capacity - userGroupSize;
    return Math.max(0.3, 1 - (excess * 0.1));
  }

  private calculateQualityScore(property: Property): number {
    let score = 0;

    // Rating score (40% of quality)
    if (property.rating >= 4.5) score += 0.4;
    else if (property.rating >= 4.0) score += 0.3;
    else if (property.rating >= 3.5) score += 0.2;
    else if (property.rating >= 3.0) score += 0.1;

    // Review count (30% of quality)
    if (property.reviewCount >= 100) score += 0.3;
    else if (property.reviewCount >= 50) score += 0.2;
    else if (property.reviewCount >= 20) score += 0.15;
    else if (property.reviewCount >= 10) score += 0.1;

    // Superhost bonus (30% of quality)
    if (property.host.isSuperhost) score += 0.3;
    else if (property.host.responseRate >= 90) score += 0.2;
    else if (property.host.responseRate >= 80) score += 0.1;

    return score;
  }

  private calculateBonusScore(property: Property): number {
    let bonus = 0;

    // Travel purpose alignment
    if (this.userPreferences.travelPurpose === 'business' && 
        property.amenities.includes('wifi') && 
        property.amenities.includes('workspace')) {
      bonus += 0.1;
    }

    if (this.userPreferences.travelPurpose === 'family' && 
        property.amenities.includes('family-friendly')) {
      bonus += 0.1;
    }

    if (this.userPreferences.travelPurpose === 'romantic' && 
        property.features.includes('romantic')) {
      bonus += 0.1;
    }

    // Accessibility match
    if (this.userPreferences.accessibility.length > 0) {
      const accessibilityMatch = property.accessibility.some(feature =>
        this.userPreferences.accessibility.includes(feature)
      );
      if (accessibilityMatch) bonus += 0.1;
    }

    // Recently popular (trending)
    if (this.isTrendingProperty(property)) {
      bonus += 0.05;
    }

    return bonus;
  }

  private isTrendingProperty(property: Property): boolean {
    // Simple trending logic - in real implementation, this would use actual data
    return property.reviewCount > 50 && property.rating >= 4.3;
  }

  // Get recommendations by category
  public getRecommendationsByCategory(category: RecommendationScore['category'], limit: number = 5): Property[] {
    const recommendations = this.getRecommendations(50);
    const categoryRecommendations = recommendations
      .filter(rec => rec.category === category)
      .slice(0, limit);

    return categoryRecommendations.map(rec => 
      this.properties.find(p => p.id === rec.propertyId)!
    );
  }

  // Get similar properties to a given property
  public getSimilarProperties(propertyId: string, limit: number = 5): Property[] {
    const targetProperty = this.properties.find(p => p.id === propertyId);
    if (!targetProperty) return [];

    const similarities: { property: Property; score: number }[] = [];

    for (const property of this.properties) {
      if (property.id === propertyId) continue;

      let similarityScore = 0;

      // Location similarity (30%)
      if (property.city === targetProperty.city) similarityScore += 0.3;
      else if (property.province === targetProperty.province) similarityScore += 0.15;

      // Property type similarity (25%)
      if (property.propertyType === targetProperty.propertyType) similarityScore += 0.25;

      // Price similarity (20%)
      const priceDiff = Math.abs(property.pricePerNight - targetProperty.pricePerNight);
      const priceScore = Math.max(0, 1 - (priceDiff / targetProperty.pricePerNight));
      similarityScore += priceScore * 0.2;

      // Amenities similarity (15%)
      const commonAmenities = property.amenities.filter(amenity =>
        targetProperty.amenities.includes(amenity)
      );
      const amenitiesScore = commonAmenities.length / Math.max(property.amenities.length, targetProperty.amenities.length);
      similarityScore += amenitiesScore * 0.15;

      // Capacity similarity (10%)
      const capacityDiff = Math.abs(property.capacity - targetProperty.capacity);
      const capacityScore = Math.max(0, 1 - (capacityDiff / targetProperty.capacity));
      similarityScore += capacityScore * 0.1;

      if (similarityScore > 0.3) {
        similarities.push({ property, score: similarityScore });
      }
    }

    return similarities
      .sort((a, b) => b.score - a.score)
      .slice(0, limit)
      .map(item => item.property);
  }

  // Update user behavior (call this when user interacts with properties)
  public updateUserBehavior(action: 'view' | 'save' | 'book' | 'search' | 'click_amenity', data: any): void {
    switch (action) {
      case 'view':
        if (!this.userBehavior.viewedProperties.includes(data.propertyId)) {
          this.userBehavior.viewedProperties.push(data.propertyId);
        }
        if (data.timeSpent) {
          this.userBehavior.timeSpentOnProperties[data.propertyId] = data.timeSpent;
        }
        break;

      case 'save':
        if (!this.userBehavior.savedProperties.includes(data.propertyId)) {
          this.userBehavior.savedProperties.push(data.propertyId);
        }
        break;

      case 'book':
        if (!this.userBehavior.bookedProperties.includes(data.propertyId)) {
          this.userBehavior.bookedProperties.push(data.propertyId);
        }
        break;

      case 'search':
        this.userBehavior.searchHistory.push({
          location: data.location,
          checkIn: data.checkIn,
          checkOut: data.checkOut,
          guests: data.guests,
          timestamp: new Date()
        });
        // Keep only last 50 searches
        if (this.userBehavior.searchHistory.length > 50) {
          this.userBehavior.searchHistory = this.userBehavior.searchHistory.slice(-50);
        }
        break;

      case 'click_amenity':
        if (!this.userBehavior.clickedAmenities.includes(data.amenity)) {
          this.userBehavior.clickedAmenities.push(data.amenity);
        }
        break;
    }
  }
}

// Utility functions for creating recommendation engine
export const createRecommendationEngine = (
  userPreferences: UserPreferences,
  userBehavior: UserBehavior,
  properties: Property[]
): RecommendationEngine => {
  return new RecommendationEngine(userPreferences, userBehavior, properties);
};

export const getDefaultUserPreferences = (): UserPreferences => ({
  priceRange: [500, 3000],
  preferredPropertyTypes: [],
  preferredAmenities: [],
  preferredLocations: [],
  groupSize: 2,
  travelPurpose: 'leisure',
  accessibility: []
});

export const getDefaultUserBehavior = (): UserBehavior => ({
  viewedProperties: [],
  savedProperties: [],
  bookedProperties: [],
  searchHistory: [],
  clickedAmenities: [],
  timeSpentOnProperties: {}
});
