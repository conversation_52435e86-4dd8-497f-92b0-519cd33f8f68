import React from 'react';
import { cn } from '@/lib/utils';
import { Loader2, RefreshCw, RotateCcw } from 'lucide-react';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  variant?: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger';
  type?: 'spinner' | 'dots' | 'pulse' | 'bounce' | 'icon';
  className?: string;
  text?: string;
  fullScreen?: boolean;
}

const sizeClasses = {
  sm: 'h-4 w-4',
  md: 'h-6 w-6',
  lg: 'h-8 w-8',
  xl: 'h-12 w-12'
};

const variantClasses = {
  default: 'text-gray-600',
  primary: 'text-sea-green-600',
  secondary: 'text-ocean-blue-600',
  success: 'text-green-600',
  warning: 'text-yellow-600',
  danger: 'text-red-600'
};

const textSizeClasses = {
  sm: 'text-sm',
  md: 'text-base',
  lg: 'text-lg',
  xl: 'text-xl'
};

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  variant = 'default',
  type = 'spinner',
  className,
  text,
  fullScreen = false
}) => {
  const renderSpinner = () => {
    const baseClasses = cn(
      'animate-spin',
      sizeClasses[size],
      variantClasses[variant],
      className
    );

    switch (type) {
      case 'icon':
        return <Loader2 className={baseClasses} />;
      
      case 'dots':
        return (
          <div className={cn('flex space-x-1', className)}>
            {[0, 1, 2].map((i) => (
              <div
                key={i}
                className={cn(
                  'rounded-full animate-bounce',
                  size === 'sm' ? 'h-2 w-2' : size === 'md' ? 'h-3 w-3' : size === 'lg' ? 'h-4 w-4' : 'h-6 w-6',
                  variantClasses[variant].replace('text-', 'bg-')
                )}
                style={{
                  animationDelay: `${i * 0.1}s`,
                  animationDuration: '0.6s'
                }}
              />
            ))}
          </div>
        );
      
      case 'pulse':
        return (
          <div
            className={cn(
              'rounded-full animate-pulse',
              sizeClasses[size],
              variantClasses[variant].replace('text-', 'bg-'),
              className
            )}
          />
        );
      
      case 'bounce':
        return (
          <div
            className={cn(
              'rounded-full animate-bounce',
              sizeClasses[size],
              variantClasses[variant].replace('text-', 'bg-'),
              className
            )}
          />
        );
      
      default:
        return (
          <div
            className={cn(
              'animate-spin rounded-full border-2 border-current border-t-transparent',
              sizeClasses[size],
              variantClasses[variant],
              className
            )}
          />
        );
    }
  };

  const content = (
    <div className="flex flex-col items-center justify-center space-y-2">
      {renderSpinner()}
      {text && (
        <p className={cn(
          'font-medium',
          textSizeClasses[size],
          variantClasses[variant]
        )}>
          {text}
        </p>
      )}
    </div>
  );

  if (fullScreen) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-white/80 backdrop-blur-sm">
        {content}
      </div>
    );
  }

  return content;
};

// Branded StayFinder Loading Spinner
export const StayFinderLoader: React.FC<{
  size?: 'sm' | 'md' | 'lg';
  text?: string;
  fullScreen?: boolean;
}> = ({ size = 'md', text = 'Loading...', fullScreen = false }) => {
  const logoSize = size === 'sm' ? 'h-8 w-8' : size === 'md' ? 'h-12 w-12' : 'h-16 w-16';
  
  const content = (
    <div className="flex flex-col items-center justify-center space-y-4">
      <div className={cn(
        'relative',
        logoSize
      )}>
        {/* Animated ring */}
        <div className={cn(
          'absolute inset-0 rounded-full border-2 border-sea-green-200 border-t-sea-green-600 animate-spin',
          logoSize
        )} />
        
        {/* StayFinder logo */}
        <div className={cn(
          'absolute inset-0 bg-gradient-to-br from-sea-green-500 via-ocean-blue-500 to-sea-green-600 rounded-full flex items-center justify-center shadow-lg',
          logoSize
        )}>
          <span className={cn(
            'text-white font-bold',
            size === 'sm' ? 'text-sm' : size === 'md' ? 'text-lg' : 'text-2xl'
          )}>
            S
          </span>
        </div>
      </div>
      
      {text && (
        <p className={cn(
          'font-medium text-gray-600',
          size === 'sm' ? 'text-sm' : size === 'md' ? 'text-base' : 'text-lg'
        )}>
          {text}
        </p>
      )}
    </div>
  );

  if (fullScreen) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-white/95 backdrop-blur-sm">
        {content}
      </div>
    );
  }

  return content;
};

// Button Loading State
export const ButtonLoader: React.FC<{
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}> = ({ size = 'md', className }) => {
  return (
    <Loader2 className={cn(
      'animate-spin',
      sizeClasses[size],
      className
    )} />
  );
};

// Inline Loading Text
export const InlineLoader: React.FC<{
  text?: string;
  size?: 'sm' | 'md' | 'lg';
}> = ({ text = 'Loading', size = 'md' }) => {
  return (
    <div className="flex items-center space-x-2">
      <LoadingSpinner size={size} type="dots" variant="primary" />
      <span className={cn(
        'text-gray-600',
        textSizeClasses[size]
      )}>
        {text}...
      </span>
    </div>
  );
};

// Page Transition Loader
export const PageTransitionLoader: React.FC<{
  isLoading: boolean;
}> = ({ isLoading }) => {
  if (!isLoading) return null;

  return (
    <div className="fixed top-0 left-0 right-0 z-50">
      <div className="h-1 bg-gray-200">
        <div className="h-full bg-gradient-to-r from-sea-green-500 to-ocean-blue-500 animate-pulse" />
      </div>
    </div>
  );
};

// Content Loading Overlay
export const ContentLoadingOverlay: React.FC<{
  isLoading: boolean;
  children: React.ReactNode;
  text?: string;
}> = ({ isLoading, children, text = 'Loading...' }) => {
  return (
    <div className="relative">
      {children}
      {isLoading && (
        <div className="absolute inset-0 bg-white/80 backdrop-blur-sm flex items-center justify-center z-10">
          <StayFinderLoader text={text} />
        </div>
      )}
    </div>
  );
};

export default LoadingSpinner;
