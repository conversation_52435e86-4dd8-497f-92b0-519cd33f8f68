import { useState, useEffect, useCallback } from 'react';
import { ConversationsService, Conversation, ConversationMessage } from '@/services/conversationsService';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';

export const useConversations = () => {
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [loading, setLoading] = useState(false);
  const [unreadCount, setUnreadCount] = useState(0);
  const { user } = useAuth();
  const { toast } = useToast();

  // Load user's conversations
  const loadConversations = useCallback(async () => {
    if (!user?.id) return;

    try {
      setLoading(true);
      const data = await ConversationsService.getUserConversations(user.id);
      setConversations(data);
    } catch (error) {
      console.error('Failed to load conversations:', error);
      toast({
        title: "Error",
        description: "Failed to load conversations",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, [user?.id, toast]);

  // Load unread message count
  const loadUnreadCount = useCallback(async () => {
    if (!user?.id) return;

    try {
      const count = await ConversationsService.getUnreadMessageCount(user.id);
      setUnreadCount(count);
    } catch (error) {
      console.error('Failed to load unread count:', error);
    }
  }, [user?.id]);

  // Load data on mount
  useEffect(() => {
    if (user?.id) {
      loadConversations();
      loadUnreadCount();
    }
  }, [user?.id, loadConversations, loadUnreadCount]);

  // Get or create conversation
  const getOrCreateConversation = async (
    otherUserId: string,
    propertyId?: string,
    bookingId?: string
  ): Promise<Conversation | null> => {
    if (!user?.id) {
      toast({
        title: "Sign in required",
        description: "Please sign in to start a conversation",
        variant: "destructive",
      });
      return null;
    }

    try {
      const conversation = await ConversationsService.getOrCreateConversation(
        user.id,
        otherUserId,
        propertyId,
        bookingId
      );

      // Update local state if this is a new conversation
      setConversations(prev => {
        const exists = prev.find(c => c.id === conversation.id);
        if (!exists) {
          return [conversation, ...prev];
        }
        return prev;
      });

      return conversation;
    } catch (error) {
      console.error('Failed to get or create conversation:', error);
      toast({
        title: "Error",
        description: "Failed to start conversation",
        variant: "destructive",
      });
      return null;
    }
  };

  // Archive conversation
  const archiveConversation = async (conversationId: string): Promise<boolean> => {
    try {
      await ConversationsService.archiveConversation(conversationId);
      
      // Remove from local state
      setConversations(prev => prev.filter(c => c.id !== conversationId));
      
      toast({
        title: "Conversation archived",
        description: "The conversation has been archived",
      });
      
      return true;
    } catch (error) {
      console.error('Failed to archive conversation:', error);
      toast({
        title: "Error",
        description: "Failed to archive conversation",
        variant: "destructive",
      });
      return false;
    }
  };

  // Mark messages as read
  const markAsRead = async (conversationId: string): Promise<void> => {
    if (!user?.id) return;

    try {
      await ConversationsService.markMessagesAsRead(conversationId, user.id);
      
      // Update unread count
      await loadUnreadCount();
    } catch (error) {
      console.error('Failed to mark messages as read:', error);
    }
  };

  return {
    conversations,
    loading,
    unreadCount,
    loadConversations,
    getOrCreateConversation,
    archiveConversation,
    markAsRead,
    refreshUnreadCount: loadUnreadCount,
  };
};

export const useConversationMessages = (conversationId: string) => {
  const [messages, setMessages] = useState<ConversationMessage[]>([]);
  const [loading, setLoading] = useState(false);
  const [sending, setSending] = useState(false);
  const { user } = useAuth();
  const { toast } = useToast();

  // Load messages
  const loadMessages = useCallback(async () => {
    if (!conversationId) return;

    try {
      setLoading(true);
      const data = await ConversationsService.getConversationMessages(conversationId);
      setMessages(data);
    } catch (error) {
      console.error('Failed to load messages:', error);
      toast({
        title: "Error",
        description: "Failed to load messages",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, [conversationId, toast]);

  // Send message
  const sendMessage = async (content: string, recipientId: string): Promise<boolean> => {
    if (!user?.id || !conversationId) return false;

    try {
      setSending(true);
      const message = await ConversationsService.sendMessage(
        conversationId,
        user.id,
        recipientId,
        content
      );

      // Add to local state
      setMessages(prev => [...prev, message]);
      
      return true;
    } catch (error) {
      console.error('Failed to send message:', error);
      toast({
        title: "Error",
        description: "Failed to send message",
        variant: "destructive",
      });
      return false;
    } finally {
      setSending(false);
    }
  };

  // Subscribe to real-time updates
  useEffect(() => {
    if (!conversationId) return;

    const subscription = ConversationsService.subscribeToConversation(
      conversationId,
      (newMessage) => {
        setMessages(prev => {
          // Avoid duplicates
          const exists = prev.find(m => m.id === newMessage.id);
          if (!exists) {
            return [...prev, newMessage];
          }
          return prev;
        });
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, [conversationId]);

  // Load messages on mount
  useEffect(() => {
    if (conversationId) {
      loadMessages();
    }
  }, [conversationId, loadMessages]);

  return {
    messages,
    loading,
    sending,
    sendMessage,
    loadMessages,
  };
};
