// Database setup script for StayFinder
const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

async function setupDatabase() {
  console.log('🔧 Setting up StayFinder database...\n');

  try {
    // First, connect without specifying a database to create it
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      multipleStatements: true
    });

    console.log('✅ Connected to MySQL server');

    // Create database
    await connection.execute('CREATE DATABASE IF NOT EXISTS stayfinder_dev CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci');
    console.log('✅ Database "stayfinder_dev" created');

    // Use the database
    await connection.execute('USE stayfinder_dev');
    console.log('✅ Using stayfinder_dev database');

    // Read and execute the SQL file
    const sqlFile = path.join(__dirname, '..', 'database_init.sql');
    if (fs.existsSync(sqlFile)) {
      const sql = fs.readFileSync(sqlFile, 'utf8');
      
      // Split SQL into individual statements and execute them
      const statements = sql.split(';').filter(stmt => stmt.trim() && !stmt.trim().startsWith('--'));
      
      for (const statement of statements) {
        const trimmed = statement.trim();
        if (trimmed && !trimmed.startsWith('CREATE DATABASE') && !trimmed.startsWith('USE')) {
          try {
            await connection.execute(trimmed);
          } catch (error) {
            // Skip errors for statements that might already exist
            if (!error.message.includes('already exists') && !error.message.includes('Duplicate entry')) {
              console.warn(`Warning: ${error.message}`);
            }
          }
        }
      }
      
      console.log('✅ Database tables and sample data created');
    } else {
      console.error('❌ database_init.sql file not found');
      return;
    }

    // Verify tables were created
    const [tables] = await connection.execute('SHOW TABLES');
    console.log('\n📊 Tables created:');
    tables.forEach(table => {
      console.log(`  • ${Object.values(table)[0]}`);
    });

    // Show sample data counts
    const tableNames = ['users', 'properties', 'bookings', 'reviews'];
    console.log('\n📈 Sample data loaded:');
    
    for (const tableName of tableNames) {
      try {
        const [result] = await connection.execute(`SELECT COUNT(*) as count FROM ${tableName}`);
        console.log(`  • ${tableName}: ${result[0].count} records`);
      } catch (error) {
        console.log(`  • ${tableName}: table not found`);
      }
    }

    await connection.end();
    console.log('\n🎉 Database setup completed successfully!');
    console.log('\n📝 Test user accounts created:');
    console.log('  • <EMAIL> (password: password123)');
    console.log('  • <EMAIL> (password: password123)');
    console.log('  • <EMAIL> (password: password123)');

  } catch (error) {
    console.error('❌ Database setup failed:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Solution: Please start XAMPP MySQL service first');
      console.log('   1. Open XAMPP Control Panel');
      console.log('   2. Click "Start" next to MySQL');
      console.log('   3. Wait for it to turn green');
      console.log('   4. Run this script again');
    }
    
    process.exit(1);
  }
}

// Run the setup
setupDatabase();
