#!/usr/bin/env node

/**
 * Comprehensive JSX Error Fixer for StayFinder
 * Systematically fixes all JSX syntax errors
 */

import fs from 'fs';

const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

// Fix Hero.tsx - Remove problematic JSX structure
function fixHeroTsx() {
  const filePath = 'src/components/Hero.tsx';
  log(`🔧 Fixing ${filePath}...`, colors.blue);
  
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    
    // Fix the problematic ternary operator structure
    const problematicSection = `                    ) : (
                          // Fallback destinations if API fails
                          ['Cape Town', 'Johannesburg', 'Durban', 'Stellenbosch', 'Port Elizabeth', 'Knysna'].map((location) => (
                            <HoverAnimation key={location} type="scale">
                              <button
                                onClick={() => setSearchQuery(location)}
                                className="px-5 py-3 text-sm bg-gradient-to-r from-white to-gray-50 hover:from-sea-green-50 hover:to-ocean-blue-50 text-gray-700 hover:text-sea-green-700 rounded-full transition-all duration-300 border border-gray-200 hover:border-sea-green-300 hover:shadow-lg font-medium transform hover:-translate-y-0.5"
                              >
                                {location}
                              </button>
                            </HoverAnimation>
                          ))
                    )}`;
    
    const fixedSection = `                    ) : (
                          ['Cape Town', 'Johannesburg', 'Durban', 'Stellenbosch', 'Port Elizabeth', 'Knysna'].map((location) => (
                            <HoverAnimation key={location} type="scale">
                              <button
                                onClick={() => setSearchQuery(location)}
                                className="px-5 py-3 text-sm bg-gradient-to-r from-white to-gray-50 hover:from-sea-green-50 hover:to-ocean-blue-50 text-gray-700 hover:text-sea-green-700 rounded-full transition-all duration-300 border border-gray-200 hover:border-sea-green-300 hover:shadow-lg font-medium transform hover:-translate-y-0.5"
                              >
                                {location}
                              </button>
                            </HoverAnimation>
                          ))
                    )}`;
    
    content = content.replace(problematicSection, fixedSection);
    
    fs.writeFileSync(filePath, content, 'utf8');
    log(`✅ Fixed ${filePath}`, colors.green);
    return true;
  } catch (error) {
    log(`❌ Failed to fix ${filePath}: ${error.message}`, colors.red);
    return false;
  }
}

// Fix MapSearch.tsx - Ensure proper tag closure
function fixMapSearchTsx() {
  const filePath = 'src/components/MapSearch.tsx';
  log(`🔧 Fixing ${filePath}...`, colors.blue);
  
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    
    // Ensure the file ends with proper SlideIn closure
    if (!content.includes('</SlideIn>')) {
      content = content.replace(
        /(\s*<\/div>\s*)\);(\s*)$/,
        '$1</SlideIn>\n  );\n$2'
      );
    }
    
    // Fix any malformed JSX structure
    content = content.replace(/\)\)\}/g, '))}');
    
    fs.writeFileSync(filePath, content, 'utf8');
    log(`✅ Fixed ${filePath}`, colors.green);
    return true;
  } catch (error) {
    log(`❌ Failed to fix ${filePath}: ${error.message}`, colors.red);
    return false;
  }
}

// Fix PropertyAnalytics.tsx - Ensure proper tag closure
function fixPropertyAnalyticsTsx() {
  const filePath = 'src/components/PropertyAnalytics.tsx';
  log(`🔧 Fixing ${filePath}...`, colors.blue);
  
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    
    // Ensure the file ends with proper SlideIn closure
    if (!content.includes('</SlideIn>')) {
      content = content.replace(
        /(\s*<\/div>\s*)\);(\s*)$/,
        '$1</SlideIn>\n  );\n$2'
      );
    }
    
    fs.writeFileSync(filePath, content, 'utf8');
    log(`✅ Fixed ${filePath}`, colors.green);
    return true;
  } catch (error) {
    log(`❌ Failed to fix ${filePath}: ${error.message}`, colors.red);
    return false;
  }
}

// Fix UserDashboard.tsx - Fix conditional structure
function fixUserDashboardTsx() {
  const filePath = 'src/pages/UserDashboard.tsx';
  log(`🔧 Fixing ${filePath}...`, colors.blue);
  
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    
    // Read the entire file and reconstruct the problematic section
    const lines = content.split('\n');
    let fixedLines = [];
    let inOverviewTab = false;
    let braceCount = 0;
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      
      // Track when we enter the overview tab
      if (line.includes("activeTab === 'overview'")) {
        inOverviewTab = true;
        braceCount = 0;
      }
      
      // Count braces to track nesting
      if (inOverviewTab) {
        braceCount += (line.match(/\{/g) || []).length;
        braceCount -= (line.match(/\}/g) || []).length;
      }
      
      // Fix the problematic closing structure
      if (line.includes('        )}') && inOverviewTab && braceCount <= 0) {
        fixedLines.push('          </div>');
        fixedLines.push('        )}');
        inOverviewTab = false;
      } else {
        fixedLines.push(line);
      }
    }
    
    content = fixedLines.join('\n');
    
    fs.writeFileSync(filePath, content, 'utf8');
    log(`✅ Fixed ${filePath}`, colors.green);
    return true;
  } catch (error) {
    log(`❌ Failed to fix ${filePath}: ${error.message}`, colors.red);
    return false;
  }
}

// Main fix function
async function fixAllJSXErrors() {
  log('🚀 COMPREHENSIVE JSX ERROR FIXING', colors.cyan);
  log('='.repeat(50), colors.cyan);
  
  const fixes = [
    { name: 'Hero.tsx', fix: fixHeroTsx },
    { name: 'MapSearch.tsx', fix: fixMapSearchTsx },
    { name: 'PropertyAnalytics.tsx', fix: fixPropertyAnalyticsTsx },
    { name: 'UserDashboard.tsx', fix: fixUserDashboardTsx }
  ];
  
  let successCount = 0;
  
  for (const { name, fix } of fixes) {
    if (fix()) {
      successCount++;
    }
  }
  
  log('\n' + '='.repeat(50), colors.cyan);
  log(`📊 Fixed ${successCount}/${fixes.length} files`, colors.blue);
  
  if (successCount === fixes.length) {
    log('✅ All JSX errors should be fixed!', colors.green);
    log('\n🎯 Next: Try starting the dev server again', colors.blue);
  } else {
    log('⚠️  Some files may need manual attention', colors.yellow);
  }
  
  return successCount === fixes.length;
}

// Run the fixes
fixAllJSXErrors().then(success => {
  process.exit(success ? 0 : 1);
}).catch(error => {
  log(`❌ Fix script error: ${error.message}`, colors.red);
  process.exit(1);
});
