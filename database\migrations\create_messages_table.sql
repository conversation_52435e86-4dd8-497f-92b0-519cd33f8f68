-- Create messages and conversations tables for the real-time messaging system
CREATE TABLE IF NOT EXISTS conversations (
    id VARCHAR(36) PRIMARY KEY,
    property_id VARCHAR(36) NOT NULL,
    host_id VARCHAR(36) NOT NULL,
    guest_id VARCHAR(36) NOT NULL,
    booking_id VARCHAR(36) NULL,
    status ENUM('active', 'archived', 'blocked') DEFAULT 'active',
    last_message_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (property_id) REFERENCES properties(id) ON DELETE CASCADE,
    FOREIGN KEY (host_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (guest_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (booking_id) REFERENCES bookings(id) ON DELETE SET NULL,
    
    UNIQUE KEY unique_conversation (property_id, host_id, guest_id, booking_id)
);

CREATE TABLE IF NOT EXISTS messages (
    id VARCHAR(36) PRIMARY KEY,
    conversation_id VARCHAR(36) NOT NULL,
    sender_id VARCHAR(36) NOT NULL,
    message_type ENUM('text', 'image', 'file', 'system', 'booking_update') DEFAULT 'text',
    content TEXT NOT NULL,
    attachment_url VARCHAR(500) NULL,
    attachment_name VARCHAR(255) NULL,
    attachment_size INT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    read_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (conversation_id) REFERENCES conversations(id) ON DELETE CASCADE,
    FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_conversations_host_id ON conversations(host_id);
CREATE INDEX IF NOT EXISTS idx_conversations_guest_id ON conversations(guest_id);
CREATE INDEX IF NOT EXISTS idx_conversations_property_id ON conversations(property_id);
CREATE INDEX IF NOT EXISTS idx_conversations_booking_id ON conversations(booking_id);
CREATE INDEX IF NOT EXISTS idx_conversations_last_message_at ON conversations(last_message_at);

CREATE INDEX IF NOT EXISTS idx_messages_conversation_id ON messages(conversation_id);
CREATE INDEX IF NOT EXISTS idx_messages_sender_id ON messages(sender_id);
CREATE INDEX IF NOT EXISTS idx_messages_created_at ON messages(created_at);
CREATE INDEX IF NOT EXISTS idx_messages_is_read ON messages(is_read);

-- Insert sample conversation and messages for testing
INSERT IGNORE INTO conversations (
    id, property_id, host_id, guest_id, booking_id, 
    status, last_message_at, created_at, updated_at
) VALUES 
(
    'conv-1',
    'prop-1',
    (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1),
    (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1),
    NULL,
    'active',
    '2024-06-21 10:30:00',
    '2024-06-20 09:00:00',
    '2024-06-21 10:30:00'
),
(
    'conv-2',
    'prop-2',
    (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1),
    (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1),
    (SELECT id FROM bookings WHERE property_id = 'prop-2' LIMIT 1),
    'active',
    '2024-06-21 14:15:00',
    '2024-06-19 16:30:00',
    '2024-06-21 14:15:00'
);

-- Insert sample messages
INSERT IGNORE INTO messages (
    id, conversation_id, sender_id, message_type, content, 
    is_read, read_at, created_at, updated_at
) VALUES 
(
    'msg-1',
    'conv-1',
    (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1),
    'text',
    'Hi! I\'m interested in booking your beautiful villa for next weekend. Is it available?',
    TRUE,
    '2024-06-20 09:15:00',
    '2024-06-20 09:00:00',
    '2024-06-20 09:00:00'
),
(
    'msg-2',
    'conv-1',
    (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1),
    'text',
    'Hello! Thank you for your interest. Yes, the villa is available for next weekend. I\'d be happy to help you with the booking. How many guests will be staying?',
    TRUE,
    '2024-06-20 10:00:00',
    '2024-06-20 09:30:00',
    '2024-06-20 09:30:00'
),
(
    'msg-3',
    'conv-1',
    (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1),
    'text',
    'Perfect! We\'ll be 4 adults. Could you tell me more about the amenities and check-in process?',
    TRUE,
    '2024-06-20 11:00:00',
    '2024-06-20 10:30:00',
    '2024-06-20 10:30:00'
),
(
    'msg-4',
    'conv-1',
    (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1),
    'text',
    'Great! The villa has a private pool, full kitchen, WiFi, and parking. Check-in is at 3 PM and I\'ll meet you personally to show you around. The beach is just a 2-minute walk away!',
    FALSE,
    NULL,
    '2024-06-21 10:30:00',
    '2024-06-21 10:30:00'
),
(
    'msg-5',
    'conv-2',
    (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1),
    'system',
    'Booking confirmed for Mountain Retreat Cabin from June 25-28, 2024. Total: R2,800',
    TRUE,
    '2024-06-21 14:00:00',
    '2024-06-21 13:45:00',
    '2024-06-21 13:45:00'
),
(
    'msg-6',
    'conv-2',
    (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1),
    'text',
    'Welcome! I\'m excited to host you at the mountain cabin. I\'ve sent you the check-in details via email. Let me know if you have any questions!',
    FALSE,
    NULL,
    '2024-06-21 14:15:00',
    '2024-06-21 14:15:00'
);

-- Update conversations with last message timestamp
UPDATE conversations c SET 
    last_message_at = (
        SELECT MAX(m.created_at) 
        FROM messages m 
        WHERE m.conversation_id = c.id
    )
WHERE EXISTS (
    SELECT 1 FROM messages m WHERE m.conversation_id = c.id
);
