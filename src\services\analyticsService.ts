import { supabase } from '@/integrations/supabase/client';

export interface PropertyView {
  id: string;
  property_id: string;
  user_id?: string;
  session_id?: string;
  ip_address?: string;
  user_agent?: string;
  referrer?: string;
  viewed_at: string;
}

export interface SearchHistoryEntry {
  id: string;
  user_id?: string;
  search_query: string;
  filters: Record<string, any>;
  results_count: number;
  location?: string;
  check_in_date?: string;
  check_out_date?: string;
  guests?: number;
  created_at: string;
}

export class AnalyticsService {
  /**
   * Track a property view
   */
  static async trackPropertyView(
    propertyId: string,
    userId?: string,
    additionalData?: {
      referrer?: string;
      sessionId?: string;
    }
  ): Promise<void> {
    try {
      const viewData: any = {
        property_id: propertyId,
        user_id: userId || null,
        session_id: additionalData?.sessionId || this.getSessionId(),
        user_agent: navigator.userAgent,
        referrer: additionalData?.referrer || document.referrer || null,
      };

      const { error } = await supabase
        .from('property_views')
        .insert(viewData);

      if (error) {
        console.error('Error tracking property view:', error);
        // Don't throw error for analytics - fail silently
      }
    } catch (error) {
      console.error('Failed to track property view:', error);
      // Don't throw error for analytics - fail silently
    }
  }

  /**
   * Track a search query
   */
  static async trackSearch(
    searchQuery: string,
    filters: Record<string, any> = {},
    resultsCount: number = 0,
    userId?: string
  ): Promise<void> {
    try {
      const searchData: any = {
        user_id: userId || null,
        search_query: searchQuery,
        filters: filters,
        results_count: resultsCount,
        location: filters.location || null,
        check_in_date: filters.checkIn || null,
        check_out_date: filters.checkOut || null,
        guests: filters.guests ? parseInt(filters.guests) : null,
      };

      const { error } = await supabase
        .from('search_history')
        .insert(searchData);

      if (error) {
        console.error('Error tracking search:', error);
        // Don't throw error for analytics - fail silently
      }
    } catch (error) {
      console.error('Failed to track search:', error);
      // Don't throw error for analytics - fail silently
    }
  }

  /**
   * Get property view analytics
   */
  static async getPropertyViews(
    propertyId: string,
    startDate?: string,
    endDate?: string
  ): Promise<PropertyView[]> {
    try {
      let query = supabase
        .from('property_views')
        .select('*')
        .eq('property_id', propertyId)
        .order('viewed_at', { ascending: false });

      if (startDate) {
        query = query.gte('viewed_at', startDate);
      }

      if (endDate) {
        query = query.lte('viewed_at', endDate);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching property views:', error);
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Failed to fetch property views:', error);
      throw error;
    }
  }

  /**
   * Get user's search history
   */
  static async getUserSearchHistory(userId: string, limit: number = 50): Promise<SearchHistoryEntry[]> {
    try {
      const { data, error } = await supabase
        .from('search_history')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) {
        console.error('Error fetching search history:', error);
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Failed to fetch search history:', error);
      throw error;
    }
  }

  /**
   * Get popular search terms
   */
  static async getPopularSearchTerms(limit: number = 10): Promise<Array<{ term: string; count: number }>> {
    try {
      const { data, error } = await supabase
        .rpc('get_popular_search_terms', { result_limit: limit });

      if (error) {
        console.error('Error fetching popular search terms:', error);
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Failed to fetch popular search terms:', error);
      return [];
    }
  }

  /**
   * Get property view count
   */
  static async getPropertyViewCount(propertyId: string): Promise<number> {
    try {
      const { count, error } = await supabase
        .from('property_views')
        .select('*', { count: 'exact', head: true })
        .eq('property_id', propertyId);

      if (error) {
        console.error('Error fetching property view count:', error);
        return 0;
      }

      return count || 0;
    } catch (error) {
      console.error('Failed to fetch property view count:', error);
      return 0;
    }
  }

  /**
   * Get trending properties based on recent views
   */
  static async getTrendingProperties(limit: number = 10): Promise<Array<{ property_id: string; view_count: number }>> {
    try {
      const { data, error } = await supabase
        .rpc('get_trending_properties', { result_limit: limit });

      if (error) {
        console.error('Error fetching trending properties:', error);
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Failed to fetch trending properties:', error);
      return [];
    }
  }

  /**
   * Generate or get session ID
   */
  private static getSessionId(): string {
    let sessionId = sessionStorage.getItem('stayfinder_session_id');
    
    if (!sessionId) {
      sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      sessionStorage.setItem('stayfinder_session_id', sessionId);
    }
    
    return sessionId;
  }

  /**
   * Get user's recently viewed properties
   */
  static async getRecentlyViewedProperties(userId: string, limit: number = 10) {
    try {
      const { data, error } = await supabase
        .from('property_views')
        .select(`
          property_id,
          viewed_at,
          properties (
            id,
            title,
            description,
            city,
            province,
            price_per_night,
            max_guests,
            bedrooms,
            bathrooms,
            property_type,
            average_rating,
            total_reviews,
            property_images (
              image_url,
              alt_text,
              is_primary
            )
          )
        `)
        .eq('user_id', userId)
        .order('viewed_at', { ascending: false })
        .limit(limit);

      if (error) {
        console.error('Error fetching recently viewed properties:', error);
        throw error;
      }

      // Remove duplicates (same property viewed multiple times)
      const uniqueProperties = [];
      const seenPropertyIds = new Set();

      for (const item of data || []) {
        if (!seenPropertyIds.has(item.property_id)) {
          seenPropertyIds.add(item.property_id);
          uniqueProperties.push(item);
        }
      }

      return uniqueProperties;
    } catch (error) {
      console.error('Failed to fetch recently viewed properties:', error);
      throw error;
    }
  }
}
