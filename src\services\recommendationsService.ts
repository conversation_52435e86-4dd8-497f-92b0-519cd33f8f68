interface Property {
  id: string;
  title: string;
  location: string;
  price: number;
  images: string[];
  averageRating?: number;
  reviewCount?: number;
  propertyType?: string;
  maxGuests?: number;
  bedrooms?: number;
  bathrooms?: number;
  amenities?: string[];
  available?: boolean;
  owner?: {
    firstName: string;
    lastName: string;
  };
}

interface RecommendationsResponse {
  success: boolean;
  data: Property[];
  type: string;
  count: number;
  error?: string;
}

class RecommendationsService {
  private baseUrl = 'http://localhost:3001/api/recommendations';

  private async makeRequest(url: string, options: RequestInit = {}): Promise<RecommendationsResponse> {
    try {
      const token = localStorage.getItem('auth_token');
      const headers: HeadersInit = {
        'Content-Type': 'application/json',
        ...options.headers,
      };

      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const response = await fetch(url, {
        ...options,
        headers,
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Recommendations API error:', error);
      throw error;
    }
  }

  async getSimilarProperties(propertyId: string, limit: number = 4): Promise<Property[]> {
    try {
      const url = `${this.baseUrl}?type=similar&property_id=${propertyId}&limit=${limit}`;
      const response = await this.makeRequest(url);
      
      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to fetch similar properties');
      }
    } catch (error) {
      console.error('Error fetching similar properties:', error);
      return [];
    }
  }

  async getRecentlyViewedProperties(limit: number = 6): Promise<Property[]> {
    try {
      const url = `${this.baseUrl}?type=recently_viewed&limit=${limit}`;
      const response = await this.makeRequest(url);
      
      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to fetch recently viewed properties');
      }
    } catch (error) {
      console.error('Error fetching recently viewed properties:', error);
      return [];
    }
  }

  async getPersonalizedRecommendations(limit: number = 8): Promise<Property[]> {
    try {
      const url = `${this.baseUrl}?type=personalized&limit=${limit}`;
      const response = await this.makeRequest(url);
      
      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to fetch personalized recommendations');
      }
    } catch (error) {
      console.error('Error fetching personalized recommendations:', error);
      return [];
    }
  }

  async getTrendingProperties(limit: number = 6): Promise<Property[]> {
    try {
      const url = `${this.baseUrl}?type=trending&limit=${limit}`;
      const response = await this.makeRequest(url);
      
      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to fetch trending properties');
      }
    } catch (error) {
      console.error('Error fetching trending properties:', error);
      return [];
    }
  }

  async logPropertyView(propertyId: string): Promise<boolean> {
    try {
      const response = await this.makeRequest(this.baseUrl, {
        method: 'POST',
        body: JSON.stringify({ property_id: propertyId }),
      });
      
      return response.success;
    } catch (error) {
      console.error('Error logging property view:', error);
      return false;
    }
  }

  async getAllRecommendations(propertyId?: string): Promise<{
    similar: Property[];
    recentlyViewed: Property[];
    personalized: Property[];
    trending: Property[];
  }> {
    try {
      const [similar, recentlyViewed, personalized, trending] = await Promise.allSettled([
        propertyId ? this.getSimilarProperties(propertyId, 4) : Promise.resolve([]),
        this.getRecentlyViewedProperties(6),
        this.getPersonalizedRecommendations(8),
        this.getTrendingProperties(6),
      ]);

      return {
        similar: similar.status === 'fulfilled' ? similar.value : [],
        recentlyViewed: recentlyViewed.status === 'fulfilled' ? recentlyViewed.value : [],
        personalized: personalized.status === 'fulfilled' ? personalized.value : [],
        trending: trending.status === 'fulfilled' ? trending.value : [],
      };
    } catch (error) {
      console.error('Error fetching all recommendations:', error);
      return {
        similar: [],
        recentlyViewed: [],
        personalized: [],
        trending: [],
      };
    }
  }
}

export const recommendationsService = new RecommendationsService();
export type { Property, RecommendationsResponse };
