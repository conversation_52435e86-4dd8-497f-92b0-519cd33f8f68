import React, { createContext, useContext, useState, useCallback } from 'react';
import bookingsService from '../services/bookings';
import propertiesService from '../services/properties';
import { useAuth } from './AuthContext';

interface BookingData {
  id?: string;
  propertyId: string;
  propertyTitle?: string;
  propertyLocation?: string;
  checkInDate: string;
  checkOutDate: string;
  guestCount: number;
  totalAmount?: number;
  bookingStatus?: string;
  paymentStatus?: string;
  specialRequests?: string;
  costBreakdown?: {
    nights: number;
    accommodationCost: number;
    cleaningFee: number;
    totalAmount: number;
    pricePerNight: number;
  };
  guest?: {
    firstName: string;
    lastName: string;
    email: string;
    phone?: string;
  };
  owner?: {
    firstName: string;
    lastName: string;
    email: string;
    phone?: string;
  };
  createdAt?: string;
  updatedAt?: string;
}

interface BookingContextType {
  // Current booking state
  currentBooking: BookingData | null;
  userBookings: BookingData[];
  upcomingBookings: BookingData[];
  
  // Loading states
  loading: boolean;
  submitting: boolean;
  
  // Error state
  error: string | null;
  
  // Booking actions
  createBooking: (bookingData: Omit<BookingData, 'id'>) => Promise<BookingData>;
  calculateCost: (propertyId: string, checkInDate: string, checkOutDate: string) => Promise<any>;
  checkAvailability: (propertyId: string, checkInDate: string, checkOutDate: string) => Promise<boolean>;
  
  // Booking management
  getUserBookings: () => Promise<void>;
  getUpcomingBookings: () => Promise<void>;
  updateBookingStatus: (bookingId: string, status: string) => Promise<void>;
  cancelBooking: (bookingId: string) => Promise<void>;
  
  // Utility functions
  clearCurrentBooking: () => void;
  clearError: () => void;
  setCurrentBooking: (booking: BookingData | null) => void;
}

const BookingContext = createContext<BookingContextType | undefined>(undefined);

export const useBooking = (): BookingContextType => {
  const context = useContext(BookingContext);
  if (!context) {
    throw new Error('useBooking must be used within a BookingProvider');
  }
  return context;
};

interface BookingProviderProps {
  children: React.ReactNode;
}

export const BookingProvider: React.FC<BookingProviderProps> = ({ children }) => {
  const [currentBooking, setCurrentBooking] = useState<BookingData | null>(null);
  const [userBookings, setUserBookings] = useState<BookingData[]>([]);
  const [upcomingBookings, setUpcomingBookings] = useState<BookingData[]>([]);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const { isAuthenticated } = useAuth();

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const clearCurrentBooking = useCallback(() => {
    setCurrentBooking(null);
  }, []);

  const createBooking = useCallback(async (bookingData: Omit<BookingData, 'id'>): Promise<BookingData> => {
    if (!isAuthenticated) {
      throw new Error('You must be logged in to make a booking');
    }

    setSubmitting(true);
    setError(null);

    try {
      const response = await bookingsService.createBooking({
        propertyId: bookingData.propertyId,
        checkInDate: bookingData.checkInDate,
        checkOutDate: bookingData.checkOutDate,
        guestCount: bookingData.guestCount,
        specialRequests: bookingData.specialRequests
      });

      const newBooking = bookingsService.transformBooking(response.booking);
      setCurrentBooking(newBooking);
      
      // Refresh user bookings
      await getUserBookings();
      
      return newBooking;
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to create booking';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setSubmitting(false);
    }
  }, [isAuthenticated]);

  const calculateCost = useCallback(async (propertyId: string, checkInDate: string, checkOutDate: string) => {
    setLoading(true);
    setError(null);

    try {
      const response = await bookingsService.calculateBookingCost(propertyId, checkInDate, checkOutDate);
      return response.costBreakdown;
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to calculate cost';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  const checkAvailability = useCallback(async (propertyId: string, checkInDate: string, checkOutDate: string): Promise<boolean> => {
    setLoading(true);
    setError(null);

    try {
      const response = await propertiesService.checkAvailability(propertyId, checkInDate, checkOutDate);
      return response.available;
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to check availability';
      setError(errorMessage);
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  const getUserBookings = useCallback(async () => {
    if (!isAuthenticated) return;

    setLoading(true);
    setError(null);

    try {
      const response = await bookingsService.getUserBookings();
      const transformedBookings = response.bookings.map((booking: any) => 
        bookingsService.transformBooking(booking)
      );
      setUserBookings(transformedBookings);
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to fetch bookings';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [isAuthenticated]);

  const getUpcomingBookings = useCallback(async () => {
    if (!isAuthenticated) return;

    setLoading(true);
    setError(null);

    try {
      const response = await bookingsService.getUpcomingBookings();
      const transformedBookings = response.bookings.map((booking: any) => 
        bookingsService.transformBooking(booking)
      );
      setUpcomingBookings(transformedBookings);
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to fetch upcoming bookings';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [isAuthenticated]);

  const updateBookingStatus = useCallback(async (bookingId: string, status: string) => {
    setSubmitting(true);
    setError(null);

    try {
      await bookingsService.updateBookingStatus(bookingId, status);
      
      // Refresh bookings
      await getUserBookings();
      await getUpcomingBookings();
      
      // Update current booking if it matches
      if (currentBooking?.id === bookingId) {
        setCurrentBooking(prev => prev ? { ...prev, bookingStatus: status } : null);
      }
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to update booking status';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setSubmitting(false);
    }
  }, [currentBooking, getUserBookings, getUpcomingBookings]);

  const cancelBooking = useCallback(async (bookingId: string) => {
    await updateBookingStatus(bookingId, 'cancelled');
  }, [updateBookingStatus]);

  const value: BookingContextType = {
    // State
    currentBooking,
    userBookings,
    upcomingBookings,
    loading,
    submitting,
    error,
    
    // Actions
    createBooking,
    calculateCost,
    checkAvailability,
    getUserBookings,
    getUpcomingBookings,
    updateBookingStatus,
    cancelBooking,
    
    // Utilities
    clearCurrentBooking,
    clearError,
    setCurrentBooking
  };

  return (
    <BookingContext.Provider value={value}>
      {children}
    </BookingContext.Provider>
  );
};
