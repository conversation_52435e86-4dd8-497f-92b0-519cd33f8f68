import React from 'react';
import { Plus, Check, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useComparison, Property } from '@/contexts/ComparisonContext';
import { cn } from '@/lib/utils';
import { toast } from '@/components/ui/use-toast';

interface ComparisonButtonProps {
  property: Property;
  variant?: 'default' | 'icon' | 'compact';
  className?: string;
}

export const ComparisonButton: React.FC<ComparisonButtonProps> = ({
  property,
  variant = 'default',
  className
}) => {
  const { 
    addToComparison, 
    removeFromComparison, 
    isInComparison, 
    canAddMore, 
    maxProperties,
    comparedProperties 
  } = useComparison();

  const isComparing = isInComparison(property.id);

  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (isComparing) {
      removeFromComparison(property.id);
      toast({
        title: "Removed from comparison",
        description: `${property.title} has been removed from comparison.`,
      });
    } else {
      if (!canAddMore) {
        toast({
          title: "Comparison limit reached",
          description: `You can only compare up to ${maxProperties} properties at once. Remove one to add another.`,
          variant: "destructive",
        });
        return;
      }

      addToComparison(property);
      toast({
        title: "Added to comparison",
        description: `${property.title} has been added to comparison.`,
      });
    }
  };

  if (variant === 'icon') {
    return (
      <Button
        variant={isComparing ? "default" : "outline"}
        size="sm"
        onClick={handleClick}
        className={cn(
          "w-8 h-8 p-0 rounded-full transition-all duration-200",
          isComparing 
            ? "bg-sea-green-500 hover:bg-sea-green-600 text-white" 
            : "border-gray-300 hover:border-sea-green-500 hover:text-sea-green-600",
          !canAddMore && !isComparing && "opacity-50 cursor-not-allowed",
          className
        )}
        disabled={!canAddMore && !isComparing}
        title={
          isComparing 
            ? "Remove from comparison" 
            : canAddMore 
              ? "Add to comparison" 
              : "Comparison limit reached"
        }
      >
        {isComparing ? (
          <Check className="h-4 w-4" />
        ) : (
          <Plus className="h-4 w-4" />
        )}
      </Button>
    );
  }

  if (variant === 'compact') {
    return (
      <Button
        variant={isComparing ? "default" : "outline"}
        size="sm"
        onClick={handleClick}
        className={cn(
          "text-xs transition-all duration-200",
          isComparing 
            ? "bg-sea-green-500 hover:bg-sea-green-600 text-white" 
            : "border-gray-300 hover:border-sea-green-500 hover:text-sea-green-600",
          !canAddMore && !isComparing && "opacity-50 cursor-not-allowed",
          className
        )}
        disabled={!canAddMore && !isComparing}
      >
        {isComparing ? (
          <>
            <Check className="h-3 w-3 mr-1" />
            Added
          </>
        ) : (
          <>
            <Plus className="h-3 w-3 mr-1" />
            Compare
          </>
        )}
      </Button>
    );
  }

  return (
    <Button
      variant={isComparing ? "default" : "outline"}
      size="sm"
      onClick={handleClick}
      className={cn(
        "transition-all duration-200",
        isComparing 
          ? "bg-sea-green-500 hover:bg-sea-green-600 text-white" 
          : "border-gray-300 hover:border-sea-green-500 hover:text-sea-green-600",
        !canAddMore && !isComparing && "opacity-50 cursor-not-allowed",
        className
      )}
      disabled={!canAddMore && !isComparing}
    >
      {isComparing ? (
        <>
          <Check className="h-4 w-4 mr-2" />
          Added to Compare
        </>
      ) : (
        <>
          <Plus className="h-4 w-4 mr-2" />
          Add to Compare
        </>
      )}
    </Button>
  );
};

// Floating comparison bar that shows when properties are selected
export const ComparisonBar: React.FC<{ onOpenComparison: () => void }> = ({ 
  onOpenComparison 
}) => {
  const { comparedProperties, clearComparison } = useComparison();

  if (comparedProperties.length === 0) {
    return null;
  }

  return (
    <div className="fixed bottom-6 left-1/2 transform -translate-x-1/2 z-40">
      <div className="bg-white rounded-full shadow-lg border border-gray-200 px-6 py-3 flex items-center gap-4">
        <div className="flex items-center gap-2">
          <div className="flex -space-x-2">
            {comparedProperties.slice(0, 3).map((property, index) => (
              <div
                key={property.id}
                className="w-8 h-8 rounded-full bg-gray-200 border-2 border-white overflow-hidden"
                style={{ zIndex: 3 - index }}
              >
                <img
                  src={property.images[0] || '/placeholder-property.jpg'}
                  alt={property.title}
                  className="w-full h-full object-cover"
                />
              </div>
            ))}
          </div>
          <span className="text-sm font-medium">
            {comparedProperties.length} properties selected
          </span>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={clearComparison}
            className="text-red-600 border-red-200 hover:bg-red-50"
          >
            <X className="h-4 w-4 mr-1" />
            Clear
          </Button>
          <Button
            onClick={onOpenComparison}
            className="bg-sea-green-500 hover:bg-sea-green-600 text-white"
            size="sm"
          >
            Compare Properties
          </Button>
        </div>
      </div>
    </div>
  );
};
