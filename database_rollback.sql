-- ============================================================
-- STAYFINDER DATABASE ROLLBACK SCRIPT
-- Use this to undo migrations if needed
-- Execute in REVERSE order if rolling back partially
-- ============================================================

-- ============================================================
-- ROLLBACK MIGRATION 004: Host Performance Metrics
-- ============================================================

DROP TABLE IF EXISTS public.property_metrics CASCADE;
DROP TABLE IF EXISTS public.host_metrics CASCADE;

-- ============================================================
-- ROLLBACK MIGRATION 003: Conversation Management
-- ============================================================

-- Remove conversation_id column from messages table
ALTER TABLE public.messages DROP COLUMN IF EXISTS conversation_id;

-- Drop conversations table
DROP TABLE IF EXISTS public.conversations CASCADE;

-- ============================================================
-- ROLLBACK MIGRATION 002: Search History & Analytics
-- ============================================================

DROP TABLE IF EXISTS public.property_views CASCADE;
DROP TABLE IF EXISTS public.search_history CASCADE;

-- ============================================================
-- ROLLBACK MIGRATION 001: User Favorites System
-- ============================================================

DROP TABLE IF EXISTS public.user_favorites CASCADE;

-- ============================================================
-- ROLLBACK PERFORMANCE INDEXES
-- ============================================================

-- Drop performance indexes (if they were created by the migration)
DROP INDEX IF EXISTS public.idx_properties_location_search;
DROP INDEX IF EXISTS public.idx_properties_price_range;
DROP INDEX IF EXISTS public.idx_properties_availability;
DROP INDEX IF EXISTS public.idx_bookings_date_range;
DROP INDEX IF EXISTS public.idx_bookings_status_date;
DROP INDEX IF EXISTS public.idx_reviews_property_rating;

-- ============================================================
-- VERIFICATION AFTER ROLLBACK
-- ============================================================

-- Verify tables are removed
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('user_favorites', 'search_history', 'property_views', 'conversations', 'host_metrics', 'property_metrics');

-- Should return no rows if rollback was successful
