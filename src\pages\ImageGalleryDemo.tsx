import React from 'react';
import { PropertyImageGallery } from '@/components/PropertyImageGallery';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Header } from '@/components/Header';
import { 
  PageTransition, 
  SlideIn, 
  StaggeredAnimation 
} from '@/components/ui/page-transitions';
import { EnhancedBreadcrumb } from '@/components/ui/enhanced-breadcrumb';
import { Home } from 'lucide-react';

export const ImageGalleryDemo: React.FC = () => {
  // Sample images for demonstration
  const sampleImages = [
    {
      id: '1',
      url: 'https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=800&h=600&fit=crop',
      alt: 'Modern beachfront villa exterior',
      isPrimary: true,
      caption: 'Stunning beachfront villa with panoramic ocean views',
      photographer: '<PERSON>',
      tags: ['exterior', 'beachfront', 'luxury']
    },
    {
      id: '2',
      url: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&h=600&fit=crop',
      alt: 'Spacious living room with ocean view',
      caption: 'Open-plan living area with floor-to-ceiling windows',
      photographer: '<PERSON> Doe',
      tags: ['interior', 'living-room', 'ocean-view']
    },
    {
      id: '3',
      url: 'https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=800&h=600&fit=crop',
      alt: 'Master bedroom with king-size bed',
      caption: 'Luxurious master bedroom with premium furnishings',
      photographer: 'Mike Johnson',
      tags: ['bedroom', 'luxury', 'interior']
    },
    {
      id: '4',
      url: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=800&h=600&fit=crop',
      alt: 'Modern kitchen with island',
      caption: 'Fully equipped gourmet kitchen with marble countertops',
      photographer: 'Sarah Wilson',
      tags: ['kitchen', 'modern', 'gourmet']
    },
    {
      id: '5',
      url: 'https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=800&h=600&fit=crop',
      alt: 'Private pool and deck area',
      caption: 'Infinity pool overlooking the ocean',
      photographer: 'David Brown',
      tags: ['pool', 'outdoor', 'luxury']
    },
    {
      id: '6',
      url: 'https://images.unsplash.com/photo-1582268611958-ebfd161ef9cf?w=800&h=600&fit=crop',
      alt: 'Elegant bathroom with bathtub',
      caption: 'Spa-like bathroom with ocean views',
      photographer: 'Lisa Garcia',
      tags: ['bathroom', 'spa', 'luxury']
    }
  ];

  return (
    <PageTransition>
      <div className="min-h-screen bg-gray-50">
        <Header />
        
        <div className="container mx-auto px-4 py-8">
          <SlideIn direction="up" delay={100}>
            <div className="mb-8">
              <EnhancedBreadcrumb 
                items={[
                  { label: 'Home', href: '/', icon: <Home className="h-4 w-4" /> },
                  { label: 'Image Gallery Demo', current: true }
                ]}
              />
              <h1 className="text-4xl font-bold text-gray-900 mt-4 mb-2">
                Enhanced Property Image Gallery
              </h1>
              <p className="text-lg text-gray-600">
                Showcase of the advanced image gallery features with zoom, slideshow, and interactive controls
              </p>
            </div>
          </SlideIn>

          <div className="space-y-12">
            {/* Main Gallery Demo */}
            <SlideIn direction="up" delay={200}>
              <Card className="border-0 shadow-lg">
                <CardHeader>
                  <CardTitle className="flex items-center">
                    🖼️ Full-Featured Gallery
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <PropertyImageGallery
                    images={sampleImages}
                    propertyTitle="Luxury Beachfront Villa"
                    showThumbnails={true}
                    showControls={true}
                    enableSlideshow={true}
                    enableZoom={true}
                    enableRotation={true}
                    autoPlay={false}
                    slideshowInterval={4000}
                    onImageChange={(index) => console.log('Image changed to:', index)}
                    onImageLoad={(index) => console.log('Image loaded:', index)}
                    onImageError={(index, error) => console.log('Image error:', index, error)}
                  />
                </CardContent>
              </Card>
            </SlideIn>

            {/* Feature Showcase */}
            <SlideIn direction="up" delay={300}>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <Card className="border-0 shadow-lg">
                  <CardHeader>
                    <CardTitle className="text-lg">🔍 Zoom & Pan</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2 text-sm text-gray-600">
                      <li>• Zoom in/out with mouse wheel or controls</li>
                      <li>• Pan zoomed images by dragging</li>
                      <li>• Zoom level indicator</li>
                      <li>• Reset zoom functionality</li>
                      <li>• Keyboard shortcuts (+/-)</li>
                    </ul>
                  </CardContent>
                </Card>

                <Card className="border-0 shadow-lg">
                  <CardHeader>
                    <CardTitle className="text-lg">🎬 Slideshow Mode</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2 text-sm text-gray-600">
                      <li>• Auto-play slideshow with timer</li>
                      <li>• Play/pause controls</li>
                      <li>• Progress bar indicator</li>
                      <li>• Skip to first/last image</li>
                      <li>• Keyboard control (spacebar)</li>
                    </ul>
                  </CardContent>
                </Card>

                <Card className="border-0 shadow-lg">
                  <CardHeader>
                    <CardTitle className="text-lg">🖥️ Fullscreen Experience</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2 text-sm text-gray-600">
                      <li>• Immersive fullscreen viewing</li>
                      <li>• All controls available in fullscreen</li>
                      <li>• Image rotation (90° increments)</li>
                      <li>• Keyboard navigation</li>
                      <li>• ESC to exit fullscreen</li>
                    </ul>
                  </CardContent>
                </Card>

                <Card className="border-0 shadow-lg">
                  <CardHeader>
                    <CardTitle className="text-lg">⚡ Loading & Error States</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2 text-sm text-gray-600">
                      <li>• Branded loading spinners</li>
                      <li>• Graceful error handling</li>
                      <li>• Individual image loading states</li>
                      <li>• Retry functionality</li>
                      <li>• Skeleton loading for thumbnails</li>
                    </ul>
                  </CardContent>
                </Card>

                <Card className="border-0 shadow-lg">
                  <CardHeader>
                    <CardTitle className="text-lg">🎨 Enhanced Thumbnails</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2 text-sm text-gray-600">
                      <li>• Animated hover effects</li>
                      <li>• Active image highlighting</li>
                      <li>• Primary photo indicators</li>
                      <li>• Smooth transitions</li>
                      <li>• Responsive grid layout</li>
                    </ul>
                  </CardContent>
                </Card>

                <Card className="border-0 shadow-lg">
                  <CardHeader>
                    <CardTitle className="text-lg">📝 Rich Metadata</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2 text-sm text-gray-600">
                      <li>• Image captions and descriptions</li>
                      <li>• Photographer credits</li>
                      <li>• Tag system for categorization</li>
                      <li>• Primary photo designation</li>
                      <li>• Image counter and navigation</li>
                    </ul>
                  </CardContent>
                </Card>
              </div>
            </SlideIn>

            {/* Keyboard Shortcuts */}
            <SlideIn direction="up" delay={400}>
              <Card className="border-0 shadow-lg">
                <CardHeader>
                  <CardTitle>⌨️ Keyboard Shortcuts (Fullscreen Mode)</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div className="space-y-2">
                      <div className="font-medium text-gray-900">Navigation</div>
                      <div className="text-gray-600">
                        <div>← → Arrow keys</div>
                        <div>ESC - Exit fullscreen</div>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="font-medium text-gray-900">Zoom</div>
                      <div className="text-gray-600">
                        <div>+ - Zoom in</div>
                        <div>- - Zoom out</div>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="font-medium text-gray-900">Slideshow</div>
                      <div className="text-gray-600">
                        <div>Space - Play/Pause</div>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="font-medium text-gray-900">Rotation</div>
                      <div className="text-gray-600">
                        <div>R - Rotate 90°</div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </SlideIn>

            {/* Minimal Gallery Example */}
            <SlideIn direction="up" delay={500}>
              <Card className="border-0 shadow-lg">
                <CardHeader>
                  <CardTitle>🎯 Minimal Gallery (No Controls)</CardTitle>
                </CardHeader>
                <CardContent>
                  <PropertyImageGallery
                    images={sampleImages.slice(0, 3)}
                    propertyTitle="Simple Gallery"
                    showThumbnails={false}
                    showControls={false}
                    enableSlideshow={false}
                    enableZoom={false}
                    className="max-w-md mx-auto"
                  />
                </CardContent>
              </Card>
            </SlideIn>
          </div>
        </div>
      </div>
    </PageTransition>
  );
};
