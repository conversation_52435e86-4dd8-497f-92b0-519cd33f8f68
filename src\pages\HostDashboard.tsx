import React, { useState, useEffect } from 'react';
import { Header } from '../components/Header';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '../contexts/AuthContext';
import { PropertyAnalyticsComponent } from '../components/PropertyAnalytics';
import { propertyManagementService, HostProperty, HostDashboardStats } from '../services/propertyManagementService';
import { useBooking } from '../contexts/BookingContext';
import { 
  Home, 
  Calendar, 
  DollarSign, 
  Users,
  TrendingUp,
  Plus,
  Eye,
  Edit,
  BarChart3,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle
} from 'lucide-react';

export const HostDashboard: React.FC = () => {
  const [timeRange, setTimeRange] = useState<'week' | 'month' | 'year'>('month');
  const [hostProperties, setHostProperties] = useState<HostProperty[]>([]);
  const [dashboardStats, setDashboardStats] = useState<HostDashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedPropertyForAnalytics, setSelectedPropertyForAnalytics] = useState<string | null>(null);

  const { user, isAuthenticated } = useAuth();

  useEffect(() => {
    if (isAuthenticated && user) {
      fetchDashboardData();
    }
  }, [isAuthenticated, user]);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch properties and dashboard stats in parallel
      const [properties, stats] = await Promise.all([
        propertyManagementService.getHostProperties({ limit: 10 }),
        propertyManagementService.getHostDashboardStats()
      ]);

      setHostProperties(properties);
      setDashboardStats(stats);
    } catch (err) {
      console.error('Error fetching dashboard data:', err);
      setError('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const handlePropertyStatusUpdate = async (propertyId: string, newStatus: string) => {
    try {
      await propertyManagementService.updatePropertyStatus(propertyId, newStatus);

      // Update local state
      setHostProperties(prev =>
        prev.map(property =>
          property.id === propertyId
            ? { ...property, status: newStatus as any }
            : property
        )
      );
    } catch (error) {
      console.error('Error updating property status:', error);
      alert('Failed to update property status');
    }
  };

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="container mx-auto px-4 py-8">
          <Card className="max-w-md mx-auto">
            <CardContent className="text-center py-12">
              <AlertCircle className="h-12 w-12 mx-auto mb-4 text-gray-400" />
              <h2 className="text-xl font-semibold text-gray-900 mb-2">Login Required</h2>
              <p className="text-gray-600 mb-4">
                Please log in to access your host dashboard.
              </p>
              <Button onClick={() => window.location.href = '/'}>
                Go to Homepage
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="container mx-auto px-4 py-8">
          <Card className="max-w-md mx-auto">
            <CardContent className="text-center py-12">
              <AlertCircle className="h-12 w-12 mx-auto mb-4 text-red-400" />
              <h2 className="text-xl font-semibold text-gray-900 mb-2">Error Loading Dashboard</h2>
              <p className="text-gray-600 mb-4">{error}</p>
              <Button onClick={fetchDashboardData}>
                Try Again
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  const getTotalRevenue = () => {
    return dashboardStats?.overview.total_revenue || 0;
  };

  const getTotalBookings = () => {
    return dashboardStats?.overview.total_bookings || 0;
  };

  const getAverageRating = () => {
    return dashboardStats?.overview.average_rating || 0;
  };

  const formatCurrency = (amount: number) => {
    return propertyManagementService.formatCurrency(amount);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'confirmed':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-600" />;
      case 'cancelled':
        return <XCircle className="h-4 w-4 text-red-600" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'cancelled':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                Host Dashboard
              </h1>
              <p className="text-gray-600">
                Manage your properties, bookings, and earnings
              </p>
            </div>
            <Button
              onClick={() => window.location.href = '/host/property/new'}
              className="bg-sea-green-500 hover:bg-sea-green-600 text-white"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add New Property
            </Button>
          </div>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Revenue</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatCurrency(getTotalRevenue())}
                  </p>
                  <p className="text-sm text-gray-600 mt-1">
                    {dashboardStats?.overview.confirmed_bookings || 0} confirmed bookings
                  </p>
                </div>
                <DollarSign className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Bookings</p>
                  <p className="text-2xl font-bold text-gray-900">{getTotalBookings()}</p>
                  <p className="text-sm text-gray-600 mt-1">
                    {dashboardStats?.overview.confirmed_bookings || 0} confirmed
                  </p>
                </div>
                <Calendar className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Properties</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {dashboardStats?.overview.total_properties || 0}
                  </p>
                  <p className="text-sm text-gray-600 mt-1">
                    {dashboardStats?.overview.active_properties || 0} active
                  </p>
                </div>
                <Home className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Average Rating</p>
                  <p className="text-2xl font-bold text-gray-900">{getAverageRating()}</p>
                  <p className="text-sm text-gray-600 mt-1">
                    {dashboardStats?.overview.total_reviews || 0} reviews
                  </p>
                </div>
                <Users className="h-8 w-8 text-orange-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Properties Management */}
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle className="flex items-center gap-2">
                  <Home className="h-5 w-5" />
                  Your Properties
                </CardTitle>
                <Button variant="outline" size="sm">
                  <Eye className="h-4 w-4 mr-2" />
                  View All
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-sea-green-600 mx-auto mb-4"></div>
                  <p className="text-gray-600">Loading properties...</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {hostProperties.map((property) => (
                    <div key={property.id} className="border rounded-lg p-4 hover:bg-gray-50">
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <h4 className="font-medium text-gray-900">{property.title}</h4>
                          <p className="text-sm text-gray-600">{property.location}</p>
                        </div>
                        <Badge
                          variant="outline"
                          className={propertyManagementService.getStatusColor(property.status)}
                        >
                          {property.status}
                        </Badge>
                      </div>

                      <div className="grid grid-cols-3 gap-4 text-sm">
                        <div>
                          <p className="text-gray-600">Price/night</p>
                          <p className="font-medium">{formatCurrency(property.price_per_night)}</p>
                        </div>
                        <div>
                          <p className="text-gray-600">Bookings</p>
                          <p className="font-medium">{property.total_bookings}</p>
                        </div>
                        <div>
                          <p className="text-gray-600">Revenue</p>
                          <p className="font-medium">{formatCurrency(property.total_revenue)}</p>
                        </div>
                      </div>

                      <div className="flex justify-between items-center mt-4">
                        <div className="flex items-center gap-1">
                          <span className="text-yellow-400">★</span>
                          <span className="text-sm font-medium">{property.average_rating}</span>
                          <span className="text-sm text-gray-600">({property.review_count})</span>
                        </div>
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setSelectedPropertyForAnalytics(property.id)}
                          >
                            <BarChart3 className="h-3 w-3 mr-1" />
                            Analytics
                          </Button>
                          <Button variant="outline" size="sm">
                            <Edit className="h-3 w-3 mr-1" />
                            Edit
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Recent Bookings */}
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="h-5 w-5" />
                  Recent Bookings
                </CardTitle>
                <Button variant="outline" size="sm">
                  <Eye className="h-4 w-4 mr-2" />
                  View All
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-sea-green-600 mx-auto mb-4"></div>
                  <p className="text-gray-600">Loading bookings...</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {dashboardStats?.recent_bookings.map((booking) => (
                    <div key={booking.id} className="border rounded-lg p-4 hover:bg-gray-50">
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <h4 className="font-medium text-gray-900">{booking.property_title}</h4>
                          <p className="text-sm text-gray-600">Guest: {booking.guest_name}</p>
                        </div>
                        <Badge
                          variant="outline"
                          className={propertyManagementService.getBookingStatusColor(booking.booking_status)}
                        >
                          <span className="flex items-center gap-1">
                            {getStatusIcon(booking.booking_status)}
                            {booking.booking_status}
                          </span>
                        </Badge>
                      </div>

                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <p className="text-gray-600">Check-in</p>
                          <p className="font-medium">{propertyManagementService.formatDate(booking.check_in_date)}</p>
                        </div>
                        <div>
                          <p className="text-gray-600">Check-out</p>
                          <p className="font-medium">{propertyManagementService.formatDate(booking.check_out_date)}</p>
                        </div>
                      </div>

                      <div className="flex justify-between items-center mt-4">
                        <div>
                          <p className="text-sm text-gray-600">Total Amount</p>
                          <p className="font-medium text-lg">{formatCurrency(booking.total_amount)}</p>
                        </div>
                        {booking.booking_status === 'pending' && (
                          <div className="flex gap-2">
                            <Button size="sm" className="bg-green-600 hover:bg-green-700 text-white">
                              Accept
                            </Button>
                            <Button size="sm" variant="outline">
                              Decline
                            </Button>
                          </div>
                        )}
                      </div>
                    </div>
                  )) || (
                    <div className="text-center py-8">
                      <Calendar className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                      <p className="text-gray-600">No recent bookings</p>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Property Analytics */}
        {selectedPropertyForAnalytics && (
          <div className="mt-8">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-bold text-gray-900">Property Analytics</h2>
              <Button
                variant="outline"
                onClick={() => setSelectedPropertyForAnalytics(null)}
              >
                Close Analytics
              </Button>
            </div>
            <PropertyAnalyticsComponent
              propertyId={selectedPropertyForAnalytics}
              propertyTitle={
                hostProperties.find(p => p.id === selectedPropertyForAnalytics)?.title || 'Property'
              }
            />
          </div>
        )}

        {/* Monthly Revenue Chart */}
        {dashboardStats?.monthly_revenue && dashboardStats.monthly_revenue.length > 0 && (
          <Card className="mt-8">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Monthly Revenue Trend
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-64 flex items-end justify-between gap-2 p-4 bg-gray-50 rounded-lg">
                {dashboardStats.monthly_revenue.map((month, index) => {
                  const maxRevenue = Math.max(...dashboardStats.monthly_revenue.map(m => m.revenue));
                  const height = maxRevenue > 0 ? (month.revenue / maxRevenue) * 200 : 0;

                  return (
                    <div key={month.month} className="flex flex-col items-center gap-2 flex-1">
                      <div className="text-xs text-gray-600 text-center">
                        {formatCurrency(month.revenue)}
                      </div>
                      <div
                        className="bg-sea-green-500 rounded-t w-full min-h-[4px] transition-all duration-300 hover:bg-sea-green-600"
                        style={{ height: `${height}px` }}
                        title={`${month.month}: ${formatCurrency(month.revenue)} (${month.bookings} bookings)`}
                      />
                      <div className="text-xs text-gray-500 text-center">
                        {new Date(month.month + '-01').toLocaleDateString('en-US', {
                          month: 'short'
                        })}
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default HostDashboard;
