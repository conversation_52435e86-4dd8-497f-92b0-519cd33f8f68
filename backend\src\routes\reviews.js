const express = require('express');
const { body, validationResult } = require('express-validator');
const { v4: uuidv4 } = require('uuid');
const Review = require('../models/Review');
const { authenticateToken, requireRole } = require('../middleware/auth');
const logger = require('../utils/logger');

const router = express.Router();

// Create new review
router.post('/', [
  authenticateToken,
  body('bookingId').notEmpty().withMessage('Booking ID required'),
  body('rating').isInt({ min: 1, max: 5 }).withMessage('Rating must be between 1 and 5'),
  body('comment').trim().isLength({ min: 10, max: 1000 }).withMessage('Comment must be between 10 and 1000 characters')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { bookingId, rating, comment } = req.body;
    const reviewerId = req.user.id;

    // Verify booking exists and belongs to the user
    const booking = await Review.validateBookingForReview(bookingId, reviewerId);

    if (!booking) {
      return res.status(404).json({
        error: 'Booking not found or you are not authorized to review this booking'
      });
    }

    // Check if booking is completed
    if (booking.booking_status !== 'completed') {
      return res.status(400).json({
        error: 'You can only review completed bookings'
      });
    }

    // Check if checkout date has passed
    const checkoutDate = new Date(booking.check_out_date);
    const today = new Date();
    if (checkoutDate > today) {
      return res.status(400).json({
        error: 'You can only review after your stay is completed'
      });
    }

    // Check if review already exists
    const existingReview = await Review.checkExistingReview(bookingId);

    if (existingReview) {
      return res.status(409).json({
        error: 'Review already exists for this booking'
      });
    }

    // Create review using Review model
    const reviewId = uuidv4();
    const reviewData = {
      id: reviewId,
      booking_id: bookingId,
      reviewer_id: reviewerId,
      property_id: booking.property_id,
      rating,
      comment
    };

    await Review.create(reviewData);

    // Get complete review details for response
    const newReview = await Review.findById(reviewId);

    res.status(201).json({
      message: 'Review created successfully',
      review: {
        id: newReview.id,
        bookingId: newReview.booking_id,
        propertyId: newReview.property_id,
        propertyTitle: newReview.property_title,
        rating: newReview.rating,
        comment: newReview.comment,
        response: newReview.response,
        reviewer: {
          firstName: newReview.reviewer_first_name,
          lastName: newReview.reviewer_last_name
        },
        createdAt: newReview.created_at
      }
    });

    logger.info(`Review created: ${reviewId} for property ${booking.property_id} by user ${reviewerId}`);

  } catch (error) {
    logger.error('Review creation error:', error);
    res.status(500).json({
      error: 'Failed to create review',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
});

// Get reviews for a property
router.get('/property/:propertyId', async (req, res) => {
  try {
    const { propertyId } = req.params;
    const { page = 1, limit = 10 } = req.query;

    const offset = (page - 1) * limit;

    // Get reviews using Review model
    const reviews = await Review.findByPropertyId(propertyId, page, limit);

    // Get property stats
    const stats = await Review.getPropertyStats(propertyId);
    const total = stats.total_reviews;
    const averageRating = parseFloat(stats.average_rating || 0);

    const formattedReviews = reviews.map(review => ({
      id: review.id,
      rating: review.rating,
      comment: review.comment,
      response: review.response,
      reviewer: {
        firstName: review.reviewer_first_name,
        lastName: review.reviewer_last_name
      },
      createdAt: review.created_at,
      updatedAt: review.updated_at
    }));

    res.json({
      reviews: formattedReviews,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages: Math.ceil(total / limit)
      },
      averageRating,
      totalReviews: total,
      ratingBreakdown: {
        fiveStar: stats.five_star,
        fourStar: stats.four_star,
        threeStar: stats.three_star,
        twoStar: stats.two_star,
        oneStar: stats.one_star
      }
    });

    logger.info(`Reviews fetched for property ${propertyId}: ${total} reviews`);

  } catch (error) {
    logger.error('Reviews fetch error:', error);
    res.status(500).json({
      error: 'Failed to fetch reviews',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
});

// Get user's reviews
router.get('/my-reviews', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;

    const reviews = await Review.findByReviewerId(userId);

    const formattedReviews = reviews.map(review => ({
      id: review.id,
      propertyId: review.property_id,
      propertyTitle: review.property_title,
      propertyLocation: review.property_location,
      rating: review.rating,
      comment: review.comment,
      response: review.response,
      createdAt: review.created_at,
      updatedAt: review.updated_at
    }));

    res.json({
      reviews: formattedReviews
    });

    logger.info(`User reviews fetched for user ${userId}: ${formattedReviews.length} reviews`);

  } catch (error) {
    logger.error('User reviews fetch error:', error);
    res.status(500).json({
      error: 'Failed to fetch your reviews',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
});

// Add host response to review
router.patch('/:id/response', [
  authenticateToken,
  body('response').trim().isLength({ min: 1, max: 500 }).withMessage('Response must be between 1 and 500 characters')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { id } = req.params;
    const { response } = req.body;
    const userId = req.user.id;

    // Verify user is the property owner
    const review = await Review.getPropertyOwner(id);

    if (!review) {
      return res.status(404).json({
        error: 'Review not found'
      });
    }

    if (review.owner_id !== userId && req.user.role !== 'admin') {
      return res.status(403).json({
        error: 'You can only respond to reviews for your own properties'
      });
    }

    // Update review with response
    const result = await Review.addResponse(id, response);

    if (result.affectedRows === 0) {
      return res.status(404).json({
        error: 'Review not found'
      });
    }

    res.json({
      message: 'Response added successfully',
      response
    });

    logger.info(`Review response added: ${id} by user ${userId}`);

  } catch (error) {
    logger.error('Review response error:', error);
    res.status(500).json({
      error: 'Failed to add response',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
});

// Update review (only by reviewer within 24 hours)
router.put('/:id', [
  authenticateToken,
  body('rating').isInt({ min: 1, max: 5 }).withMessage('Rating must be between 1 and 5'),
  body('comment').trim().isLength({ min: 10, max: 1000 }).withMessage('Comment must be between 10 and 1000 characters')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { id } = req.params;
    const { rating, comment } = req.body;
    const userId = req.user.id;

    // Check if user can edit the review
    const editCheck = await Review.canEditReview(id, userId);

    if (!editCheck.canEdit) {
      return res.status(editCheck.reason.includes('not found') ? 404 : 400).json({
        error: editCheck.reason
      });
    }

    // Update review
    const result = await Review.updateById(id, { rating, comment });

    if (result.affectedRows === 0) {
      return res.status(404).json({
        error: 'Review not found'
      });
    }

    res.json({
      message: 'Review updated successfully',
      rating,
      comment
    });

    logger.info(`Review updated: ${id} by user ${userId}`);

  } catch (error) {
    logger.error('Review update error:', error);
    res.status(500).json({
      error: 'Failed to update review',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
});

// Get recent reviews (for admin dashboard or homepage)
router.get('/recent', async (req, res) => {
  try {
    const { limit = 10 } = req.query;

    const recentReviews = await Review.getRecentReviews(parseInt(limit));

    const formattedReviews = recentReviews.map(review => ({
      id: review.id,
      propertyId: review.property_id,
      propertyTitle: review.property_title,
      propertyLocation: review.property_location,
      rating: review.rating,
      comment: review.comment,
      reviewer: {
        firstName: review.reviewer_first_name,
        lastName: review.reviewer_last_name
      },
      createdAt: review.created_at
    }));

    res.json({
      recentReviews: formattedReviews
    });

    logger.info(`Recent reviews fetched: ${formattedReviews.length} reviews`);

  } catch (error) {
    logger.error('Recent reviews fetch error:', error);
    res.status(500).json({
      error: 'Failed to fetch recent reviews',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
});

// Get reviewer statistics
router.get('/stats/reviewer/:userId', authenticateToken, async (req, res) => {
  try {
    const { userId } = req.params;
    const requesterId = req.user.id;

    // Users can only see their own stats unless they're admin
    if (userId !== requesterId && req.user.role !== 'admin') {
      return res.status(403).json({
        error: 'You can only view your own review statistics'
      });
    }

    const stats = await Review.getReviewerStats(userId);

    res.json({
      reviewerStats: {
        totalReviews: parseInt(stats.total_reviews),
        averageRatingGiven: parseFloat(stats.average_rating_given || 0),
        ratingBreakdown: {
          fiveStar: parseInt(stats.five_star_given),
          fourStar: parseInt(stats.four_star_given),
          threeStar: parseInt(stats.three_star_given),
          twoStar: parseInt(stats.two_star_given),
          oneStar: parseInt(stats.one_star_given)
        }
      }
    });

    logger.info(`Reviewer stats fetched for user ${userId}`);

  } catch (error) {
    logger.error('Reviewer stats fetch error:', error);
    res.status(500).json({
      error: 'Failed to fetch reviewer statistics',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
});

// Search reviews
router.get('/search', async (req, res) => {
  try {
    const { q: searchTerm, limit = 20 } = req.query;

    if (!searchTerm || searchTerm.trim().length < 3) {
      return res.status(400).json({
        error: 'Search term must be at least 3 characters long'
      });
    }

    const searchResults = await Review.searchReviews(searchTerm.trim(), parseInt(limit));

    const formattedResults = searchResults.map(review => ({
      id: review.id,
      propertyId: review.property_id,
      propertyTitle: review.property_title,
      propertyLocation: review.property_location,
      rating: review.rating,
      comment: review.comment,
      reviewer: {
        firstName: review.reviewer_first_name,
        lastName: review.reviewer_last_name
      },
      createdAt: review.created_at
    }));

    res.json({
      searchResults: formattedResults,
      searchTerm,
      totalResults: formattedResults.length
    });

    logger.info(`Review search performed: "${searchTerm}" - ${formattedResults.length} results`);

  } catch (error) {
    logger.error('Review search error:', error);
    res.status(500).json({
      error: 'Failed to search reviews',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
});

// Get top-rated properties
router.get('/top-rated-properties', async (req, res) => {
  try {
    const { limit = 10 } = req.query;

    const topRatedProperties = await Review.getTopRatedProperties(parseInt(limit));

    const formattedProperties = topRatedProperties.map(property => ({
      id: property.id,
      title: property.title,
      location: property.location,
      reviewCount: parseInt(property.review_count),
      averageRating: parseFloat(property.average_rating)
    }));

    res.json({
      topRatedProperties: formattedProperties
    });

    logger.info(`Top-rated properties fetched: ${formattedProperties.length} properties`);

  } catch (error) {
    logger.error('Top-rated properties fetch error:', error);
    res.status(500).json({
      error: 'Failed to fetch top-rated properties',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
});

module.exports = router;
