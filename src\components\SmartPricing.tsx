import React, { useState, useEffect } from 'react';
import { TrendingUp, TrendingDown, Calendar, DollarSign, BarChart3, Zap, AlertCircle, Target, Settings } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Slider } from '@/components/ui/slider';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { toast } from '@/components/ui/use-toast';
import { cn } from '@/lib/utils';

interface PricingRule {
  id: string;
  name: string;
  type: 'seasonal' | 'demand' | 'event' | 'weekday' | 'length_of_stay' | 'last_minute';
  adjustment: number; // percentage
  isActive: boolean;
  conditions: {
    startDate?: string;
    endDate?: string;
    daysOfWeek?: number[];
    minStay?: number;
    maxStay?: number;
    advanceBooking?: number;
  };
}

interface MarketData {
  averagePrice: number;
  occupancyRate: number;
  competitorPrices: number[];
  demandLevel: 'low' | 'medium' | 'high' | 'very_high';
  seasonality: 'low' | 'shoulder' | 'peak';
}

interface SmartPricingProps {
  propertyId: string;
  basePrice: number;
  onPriceUpdate: (newPrice: number) => void;
  onRulesUpdate: (rules: PricingRule[]) => void;
  className?: string;
}

export const SmartPricing: React.FC<SmartPricingProps> = ({
  propertyId,
  basePrice,
  onPriceUpdate,
  onRulesUpdate,
  className
}) => {
  const [isEnabled, setIsEnabled] = useState(false);
  const [currentPrice, setCurrentPrice] = useState(basePrice);
  const [priceRange, setPriceRange] = useState([basePrice * 0.7, basePrice * 1.5]);
  const [aggressiveness, setAggressiveness] = useState(50); // 0-100
  const [rules, setRules] = useState<PricingRule[]>([
    {
      id: '1',
      name: 'Weekend Premium',
      type: 'weekday',
      adjustment: 20,
      isActive: true,
      conditions: { daysOfWeek: [5, 6] } // Friday, Saturday
    },
    {
      id: '2',
      name: 'High Season',
      type: 'seasonal',
      adjustment: 30,
      isActive: true,
      conditions: { startDate: '2024-12-15', endDate: '2025-01-15' }
    },
    {
      id: '3',
      name: 'Long Stay Discount',
      type: 'length_of_stay',
      adjustment: -15,
      isActive: true,
      conditions: { minStay: 7 }
    },
    {
      id: '4',
      name: 'Last Minute Boost',
      type: 'last_minute',
      adjustment: 25,
      isActive: true,
      conditions: { advanceBooking: 3 }
    }
  ]);

  // Mock market data - in real implementation, this would come from API
  const [marketData] = useState<MarketData>({
    averagePrice: basePrice * 1.1,
    occupancyRate: 78,
    competitorPrices: [basePrice * 0.9, basePrice * 1.2, basePrice * 1.05, basePrice * 0.95],
    demandLevel: 'high',
    seasonality: 'peak'
  });

  const [priceHistory] = useState([
    { date: '2024-12-20', price: basePrice * 0.9, occupancy: 65 },
    { date: '2024-12-21', price: basePrice * 1.1, occupancy: 82 },
    { date: '2024-12-22', price: basePrice * 1.3, occupancy: 95 },
    { date: '2024-12-23', price: basePrice * 1.4, occupancy: 100 },
    { date: '2024-12-24', price: basePrice * 1.5, occupancy: 100 },
    { date: '2024-12-25', price: basePrice * 1.2, occupancy: 88 },
    { date: '2024-12-26', price: basePrice * 1.0, occupancy: 72 }
  ]);

  useEffect(() => {
    if (isEnabled) {
      calculateSmartPrice();
    }
  }, [isEnabled, aggressiveness, rules, marketData]);

  const calculateSmartPrice = () => {
    let adjustedPrice = basePrice;
    
    // Apply market-based adjustments
    const marketMultiplier = getMarketMultiplier();
    adjustedPrice *= marketMultiplier;
    
    // Apply active rules
    const activeRules = rules.filter(rule => rule.isActive);
    activeRules.forEach(rule => {
      const ruleMultiplier = 1 + (rule.adjustment / 100);
      adjustedPrice *= ruleMultiplier;
    });
    
    // Apply aggressiveness factor
    const aggressivenessFactor = (aggressiveness - 50) / 100; // -0.5 to 0.5
    const marketDiff = marketData.averagePrice - basePrice;
    adjustedPrice += marketDiff * aggressivenessFactor;
    
    // Ensure price stays within bounds
    adjustedPrice = Math.max(priceRange[0], Math.min(priceRange[1], adjustedPrice));
    
    setCurrentPrice(Math.round(adjustedPrice));
  };

  const getMarketMultiplier = (): number => {
    let multiplier = 1;
    
    // Demand-based adjustment
    switch (marketData.demandLevel) {
      case 'very_high': multiplier *= 1.3; break;
      case 'high': multiplier *= 1.15; break;
      case 'medium': multiplier *= 1.0; break;
      case 'low': multiplier *= 0.9; break;
    }
    
    // Seasonality adjustment
    switch (marketData.seasonality) {
      case 'peak': multiplier *= 1.2; break;
      case 'shoulder': multiplier *= 1.0; break;
      case 'low': multiplier *= 0.85; break;
    }
    
    // Occupancy adjustment
    if (marketData.occupancyRate > 90) multiplier *= 1.1;
    else if (marketData.occupancyRate < 60) multiplier *= 0.95;
    
    return multiplier;
  };

  const getDemandColor = (level: string) => {
    switch (level) {
      case 'very_high': return 'text-red-600 bg-red-100';
      case 'high': return 'text-orange-600 bg-orange-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'low': return 'text-green-600 bg-green-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getSeasonalityColor = (season: string) => {
    switch (season) {
      case 'peak': return 'text-red-600 bg-red-100';
      case 'shoulder': return 'text-yellow-600 bg-yellow-100';
      case 'low': return 'text-green-600 bg-green-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const toggleRule = (ruleId: string) => {
    const updatedRules = rules.map(rule =>
      rule.id === ruleId ? { ...rule, isActive: !rule.isActive } : rule
    );
    setRules(updatedRules);
    onRulesUpdate(updatedRules);
  };

  const applyPrice = () => {
    onPriceUpdate(currentPrice);
    toast({
      title: "Price updated",
      description: `Your property price has been set to R${currentPrice.toLocaleString()}`,
    });
  };

  const priceChange = currentPrice - basePrice;
  const priceChangePercent = ((priceChange / basePrice) * 100).toFixed(1);

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Zap className="h-5 w-5" />
              <span>Smart Pricing</span>
              {isEnabled && <Badge variant="default" className="bg-green-600">Active</Badge>}
            </div>
            <Switch checked={isEnabled} onCheckedChange={setIsEnabled} />
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-600">
            Automatically optimize your pricing based on market demand, seasonality, and competitor analysis.
          </p>
        </CardContent>
      </Card>

      {/* Current Price Display */}
      <Card>
        <CardContent className="p-6">
          <div className="text-center">
            <div className="text-3xl font-bold mb-2">
              R{currentPrice.toLocaleString()}
              <span className="text-sm text-gray-500 font-normal ml-2">per night</span>
            </div>
            
            <div className="flex items-center justify-center gap-2 mb-4">
              {priceChange > 0 ? (
                <TrendingUp className="h-4 w-4 text-green-600" />
              ) : priceChange < 0 ? (
                <TrendingDown className="h-4 w-4 text-red-600" />
              ) : null}
              
              <span className={cn(
                "text-sm font-medium",
                priceChange > 0 ? "text-green-600" : priceChange < 0 ? "text-red-600" : "text-gray-600"
              )}>
                {priceChange > 0 ? '+' : ''}R{priceChange.toLocaleString()} ({priceChangePercent}%)
              </span>
              <span className="text-sm text-gray-500">from base price</span>
            </div>
            
            <div className="flex justify-center gap-4 text-sm text-gray-600">
              <div>Base: R{basePrice.toLocaleString()}</div>
              <div>Market Avg: R{marketData.averagePrice.toLocaleString()}</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Market Insights */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Market Insights
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold">{marketData.occupancyRate}%</div>
              <div className="text-sm text-gray-600">Occupancy Rate</div>
            </div>
            
            <div className="text-center">
              <Badge className={getDemandColor(marketData.demandLevel)}>
                {marketData.demandLevel.replace('_', ' ')}
              </Badge>
              <div className="text-sm text-gray-600 mt-1">Demand Level</div>
            </div>
            
            <div className="text-center">
              <Badge className={getSeasonalityColor(marketData.seasonality)}>
                {marketData.seasonality}
              </Badge>
              <div className="text-sm text-gray-600 mt-1">Season</div>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold">
                R{Math.min(...marketData.competitorPrices).toLocaleString()}
              </div>
              <div className="text-sm text-gray-600">Lowest Competitor</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Pricing Settings */}
      {isEnabled && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Pricing Settings
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Price Range */}
            <div>
              <Label className="text-base font-medium mb-4 block">Price Range</Label>
              <div className="px-3">
                <Slider
                  value={priceRange}
                  onValueChange={setPriceRange}
                  max={basePrice * 2}
                  min={basePrice * 0.5}
                  step={50}
                  className="mb-4"
                />
                <div className="flex justify-between text-sm text-gray-600">
                  <span>Min: R{priceRange[0].toLocaleString()}</span>
                  <span>Max: R{priceRange[1].toLocaleString()}</span>
                </div>
              </div>
            </div>

            <Separator />

            {/* Aggressiveness */}
            <div>
              <Label className="text-base font-medium mb-4 block">
                Pricing Aggressiveness: {aggressiveness}%
              </Label>
              <div className="px-3">
                <Slider
                  value={[aggressiveness]}
                  onValueChange={(value) => setAggressiveness(value[0])}
                  max={100}
                  min={0}
                  step={5}
                  className="mb-2"
                />
                <div className="flex justify-between text-xs text-gray-500">
                  <span>Conservative</span>
                  <span>Balanced</span>
                  <span>Aggressive</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Pricing Rules */}
      {isEnabled && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5" />
              Pricing Rules
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {rules.map((rule) => (
                <div key={rule.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <Switch
                      checked={rule.isActive}
                      onCheckedChange={() => toggleRule(rule.id)}
                    />
                    <div>
                      <div className="font-medium">{rule.name}</div>
                      <div className="text-sm text-gray-600">
                        {rule.adjustment > 0 ? '+' : ''}{rule.adjustment}% adjustment
                      </div>
                    </div>
                  </div>
                  <Badge variant="outline" className="capitalize">
                    {rule.type.replace('_', ' ')}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Apply Button */}
      {isEnabled && (
        <div className="flex gap-3">
          <Button variant="outline" onClick={() => setCurrentPrice(basePrice)} className="flex-1">
            Reset to Base Price
          </Button>
          <Button onClick={applyPrice} className="flex-1 bg-sea-green-600 hover:bg-sea-green-700">
            <DollarSign className="h-4 w-4 mr-2" />
            Apply Price (R{currentPrice.toLocaleString()})
          </Button>
        </div>
      )}

      {/* Disabled State */}
      {!isEnabled && (
        <Card className="border-dashed">
          <CardContent className="p-8 text-center">
            <Zap className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <h3 className="text-lg font-medium mb-2">Smart Pricing Disabled</h3>
            <p className="text-gray-600 mb-4">
              Enable smart pricing to automatically optimize your rates based on market conditions.
            </p>
            <Button onClick={() => setIsEnabled(true)}>
              Enable Smart Pricing
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
