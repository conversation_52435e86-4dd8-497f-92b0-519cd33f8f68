const express = require('express');
const { body, query, validationResult } = require('express-validator');
const { v4: uuidv4 } = require('uuid');
const Property = require('../models/Property');
const { authenticateToken } = require('../middleware/auth');
const logger = require('../utils/logger');

const router = express.Router();

// Get all properties with filtering and search
router.get('/', [
  query('location').optional().trim(),
  query('minPrice').optional().isNumeric(),
  query('maxPrice').optional().isNumeric(),
  query('guests').optional().isInt({ min: 1 }),
  query('propertyType').optional().isIn(['apartment', 'house', 'villa', 'guesthouse', 'cottage']),
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 50 })
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const filters = {
      location: req.query.location,
      minPrice: req.query.minPrice,
      maxPrice: req.query.maxPrice,
      guests: req.query.guests,
      propertyType: req.query.propertyType,
      page: parseInt(req.query.page) || 1,
      limit: parseInt(req.query.limit) || 12
    };

    const properties = await Property.search(filters);
    const total = await Property.getCount(filters);

    // Format response
    const formattedProperties = properties.map(property => ({
      id: property.id,
      title: property.title,
      description: property.description,
      propertyType: property.property_type,
      location: property.location,
      coordinates: {
        latitude: property.latitude,
        longitude: property.longitude
      },
      maxGuests: property.max_guests,
      bedrooms: property.bedrooms,
      bathrooms: property.bathrooms,
      pricePerNight: parseFloat(property.price_per_night),
      cleaningFee: parseFloat(property.cleaning_fee || 0),
      amenities: property.amenities ? JSON.parse(property.amenities) : [],
      checkInTime: property.check_in_time,
      checkOutTime: property.check_out_time,
      primaryImage: property.primary_image,
      averageRating: parseFloat(property.average_rating),
      reviewCount: parseInt(property.review_count),
      owner: {
        firstName: property.owner_first_name,
        lastName: property.owner_last_name
      },
      createdAt: property.created_at
    }));

    res.json({
      properties: formattedProperties,
      pagination: {
        page: filters.page,
        limit: filters.limit,
        total,
        totalPages: Math.ceil(total / filters.limit)
      }
    });

    logger.info(`Properties search: ${formattedProperties.length} results for filters: ${JSON.stringify(filters)}`);

  } catch (error) {
    logger.error('Properties fetch error:', error);
    res.status(500).json({
      error: 'Failed to fetch properties',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
});

// Get single property by ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const property = await Property.findById(id);

    if (!property) {
      return res.status(404).json({
        error: 'Property not found'
      });
    }

    // Get property images
    const { findMany } = require('../utils/database');
    const images = await findMany(
      'SELECT image_url, alt_text, is_primary, sort_order FROM property_images WHERE property_id = ? ORDER BY sort_order',
      [id]
    );

    // Get property reviews with reviewer info
    const reviews = await findMany(`
      SELECT
        r.id,
        r.rating,
        r.comment,
        r.response,
        r.created_at,
        u.first_name as reviewer_first_name,
        u.last_name as reviewer_last_name
      FROM reviews r
      LEFT JOIN users u ON r.reviewer_id = u.id
      WHERE r.property_id = ?
      ORDER BY r.created_at DESC
      LIMIT 10
    `, [id]);

    // Format response
    const formattedProperty = {
      id: property.id,
      title: property.title,
      description: property.description,
      propertyType: property.property_type,
      location: property.location,
      coordinates: {
        latitude: property.latitude,
        longitude: property.longitude
      },
      maxGuests: property.max_guests,
      bedrooms: property.bedrooms,
      bathrooms: property.bathrooms,
      pricePerNight: parseFloat(property.price_per_night),
      cleaningFee: parseFloat(property.cleaning_fee || 0),
      amenities: property.amenities ? JSON.parse(property.amenities) : [],
      houseRules: property.house_rules,
      checkInTime: property.check_in_time,
      checkOutTime: property.check_out_time,
      images: images.map(img => ({
        url: img.image_url,
        altText: img.alt_text,
        isPrimary: img.is_primary,
        sortOrder: img.sort_order
      })),
      owner: {
        firstName: property.owner_first_name,
        lastName: property.owner_last_name,
        email: property.owner_email,
        phone: property.owner_phone
      },
      reviews: reviews.map(review => ({
        id: review.id,
        rating: review.rating,
        comment: review.comment,
        response: review.response,
        createdAt: review.created_at,
        reviewer: {
          firstName: review.reviewer_first_name,
          lastName: review.reviewer_last_name
        }
      })),
      createdAt: property.created_at,
      updatedAt: property.updated_at
    };

    res.json({ property: formattedProperty });

    logger.info(`Property details fetched: ${id}`);

  } catch (error) {
    logger.error('Property fetch error:', error);
    res.status(500).json({
      error: 'Failed to fetch property',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
});

// Check property availability
router.get('/:id/availability', async (req, res) => {
  try {
    const { id } = req.params;
    const { startDate, endDate } = req.query;

    if (!startDate || !endDate) {
      return res.status(400).json({
        error: 'Start date and end date are required'
      });
    }

    // Check if property exists
    const { findOne, findMany } = require('../utils/database');
    const property = await findOne(
      'SELECT id FROM properties WHERE id = ? AND status = "active"',
      [id]
    );

    if (!property) {
      return res.status(404).json({
        error: 'Property not found'
      });
    }

    // Check for conflicting bookings
    const conflictingBookings = await findMany(`
      SELECT id, check_in_date, check_out_date, booking_status
      FROM bookings 
      WHERE property_id = ? 
        AND booking_status IN ('confirmed', 'pending')
        AND (
          (check_in_date <= ? AND check_out_date > ?) OR
          (check_in_date < ? AND check_out_date >= ?) OR
          (check_in_date >= ? AND check_out_date <= ?)
        )
    `, [id, startDate, startDate, endDate, endDate, startDate, endDate]);

    const isAvailable = conflictingBookings.length === 0;

    res.json({
      available: isAvailable,
      conflictingBookings: conflictingBookings.map(booking => ({
        id: booking.id,
        checkIn: booking.check_in_date,
        checkOut: booking.check_out_date,
        status: booking.booking_status
      }))
    });

  } catch (error) {
    console.error('Availability check error:', error);
    res.status(500).json({
      error: 'Failed to check availability',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
});

module.exports = router;
