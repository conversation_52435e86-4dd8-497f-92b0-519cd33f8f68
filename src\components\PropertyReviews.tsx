import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { StarRating } from './StarRating';
import { reviewsService, Review, ReviewStats, ReviewsData } from '../services/reviewsService';
import { 
  Star, 
  MessageSquare, 
  User,
  Calendar,
  ChevronDown,
  ChevronUp,
  Filter,
  SortAsc,
  SortDesc,
  MoreHorizontal
} from 'lucide-react';

interface PropertyReviewsProps {
  propertyId: string;
  className?: string;
  showWriteReview?: boolean;
  onWriteReview?: () => void;
}

// Write Review Component
interface WriteReviewProps {
  propertyId: string;
  bookingId: string;
  onSubmit: (reviewData: { rating: number; comment: string }) => void;
  onCancel: () => void;
  loading?: boolean;
}

const WriteReview: React.FC<WriteReviewProps> = ({
  propertyId,
  bookingId,
  onSubmit,
  onCancel,
  loading = false
}) => {
  const [rating, setRating] = useState(0);
  const [comment, setComment] = useState('');
  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleSubmit = () => {
    const validation = reviewsService.validateReviewData({
      property_id: propertyId,
      booking_id: bookingId,
      rating,
      comment
    });

    if (!validation.isValid) {
      setErrors(validation.errors);
      return;
    }

    setErrors({});
    onSubmit({ rating, comment });
  };

  return (
    <Card className="max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Write a Review</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Rating */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Overall Rating *
          </label>
          <StarRating
            rating={rating}
            onRatingChange={setRating}
            size="lg"
          />
          {errors.rating && (
            <p className="text-sm text-red-600 mt-1">{errors.rating}</p>
          )}
        </div>

        {/* Comment */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Your Review *
          </label>
          <textarea
            value={comment}
            onChange={(e) => setComment(e.target.value)}
            placeholder="Share your experience with future guests..."
            rows={6}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-sea-green-500 focus:border-transparent"
          />
          <div className="flex justify-between items-center mt-1">
            {errors.comment && (
              <p className="text-sm text-red-600">{errors.comment}</p>
            )}
            <p className="text-sm text-gray-500 ml-auto">
              {comment.length}/1000 characters
            </p>
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center gap-3 pt-4">
          <Button
            onClick={handleSubmit}
            disabled={loading || rating === 0 || comment.trim().length === 0}
            className="bg-sea-green-500 hover:bg-sea-green-600"
          >
            {loading ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            ) : null}
            Submit Review
          </Button>
          <Button
            variant="outline"
            onClick={onCancel}
            disabled={loading}
          >
            Cancel
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export const PropertyReviews: React.FC<PropertyReviewsProps> = ({
  propertyId,
  className,
  showWriteReview = false,
  onWriteReview
}) => {
  const [reviewsData, setReviewsData] = useState<ReviewsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sortBy, setSortBy] = useState<'created_at' | 'rating'>('created_at');
  const [sortOrder, setSortOrder] = useState<'ASC' | 'DESC'>('DESC');
  const [showAllReviews, setShowAllReviews] = useState(false);
  const [expandedReviews, setExpandedReviews] = useState<Set<string>>(new Set());

  useEffect(() => {
    fetchReviews();
  }, [propertyId, sortBy, sortOrder]);

  const fetchReviews = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const data = await reviewsService.getPropertyReviews(propertyId, {
        limit: showAllReviews ? undefined : 6,
        sortBy,
        sortOrder
      });
      
      setReviewsData(data);
    } catch (err) {
      console.error('Error fetching reviews:', err);
      setError('Failed to load reviews');
    } finally {
      setLoading(false);
    }
  };

  const handleSortChange = (newSortBy: 'created_at' | 'rating') => {
    if (sortBy === newSortBy) {
      setSortOrder(sortOrder === 'DESC' ? 'ASC' : 'DESC');
    } else {
      setSortBy(newSortBy);
      setSortOrder('DESC');
    }
  };

  const toggleReviewExpansion = (reviewId: string) => {
    const newExpanded = new Set(expandedReviews);
    if (newExpanded.has(reviewId)) {
      newExpanded.delete(reviewId);
    } else {
      newExpanded.add(reviewId);
    }
    setExpandedReviews(newExpanded);
  };

  const renderStars = (rating: number) => {
    return (
      <div className="flex items-center gap-1">
        {[...Array(5)].map((_, index) => (
          <Star
            key={index}
            className={`h-4 w-4 ${
              index < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
            }`}
          />
        ))}
      </div>
    );
  };

  const renderRatingBreakdown = (stats: ReviewStats) => {
    const ratings = [
      { stars: 5, count: stats.five_star, percent: stats.five_star_percent },
      { stars: 4, count: stats.four_star, percent: stats.four_star_percent },
      { stars: 3, count: stats.three_star, percent: stats.three_star_percent },
      { stars: 2, count: stats.two_star, percent: stats.two_star_percent },
      { stars: 1, count: stats.one_star, percent: stats.one_star_percent },
    ];

    return (
      <div className="space-y-2">
        {ratings.map((rating) => (
          <div key={rating.stars} className="flex items-center gap-3">
            <div className="flex items-center gap-1 w-12">
              <span className="text-sm">{rating.stars}</span>
              <Star className="h-3 w-3 text-yellow-400 fill-current" />
            </div>
            <div className="flex-1 bg-gray-200 rounded-full h-2">
              <div
                className="bg-yellow-400 h-2 rounded-full transition-all duration-300"
                style={{ width: `${rating.percent}%` }}
              ></div>
            </div>
            <span className="text-sm text-gray-600 w-8">{rating.count}</span>
          </div>
        ))}
      </div>
    );
  };

  const renderReview = (review: Review) => {
    const isExpanded = expandedReviews.has(review.id);
    const shouldTruncate = review.comment.length > 200;
    const displayComment = shouldTruncate && !isExpanded 
      ? review.comment.substring(0, 200) + '...' 
      : review.comment;

    return (
      <div key={review.id} className="border-b border-gray-200 pb-6 last:border-b-0">
        <div className="flex items-start gap-4">
          {/* Reviewer Avatar */}
          <div className="w-12 h-12 rounded-full overflow-hidden bg-gray-200 flex items-center justify-center flex-shrink-0">
            {review.reviewer_profile_picture ? (
              <img
                src={review.reviewer_profile_picture}
                alt={review.reviewer_name}
                className="w-full h-full object-cover"
              />
            ) : (
              <User className="h-6 w-6 text-gray-400" />
            )}
          </div>

          {/* Review Content */}
          <div className="flex-1">
            <div className="flex items-center justify-between mb-2">
              <div>
                <h4 className="font-semibold text-gray-900">{review.reviewer_name}</h4>
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <Calendar className="h-3 w-3" />
                  <span>Stayed {reviewsService.formatReviewDate(review.stay_date)}</span>
                </div>
              </div>
              <div className="text-right">
                {renderStars(review.rating)}
                <div className="text-xs text-gray-500 mt-1">
                  {reviewsService.getTimeAgo(review.created_at)}
                </div>
              </div>
            </div>

            {/* Review Comment */}
            <p className="text-gray-700 mb-3 leading-relaxed">
              {displayComment}
            </p>

            {shouldTruncate && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => toggleReviewExpansion(review.id)}
                className="text-sea-green-600 hover:text-sea-green-700 p-0 h-auto"
              >
                {isExpanded ? (
                  <>
                    <ChevronUp className="h-4 w-4 mr-1" />
                    Show less
                  </>
                ) : (
                  <>
                    <ChevronDown className="h-4 w-4 mr-1" />
                    Read more
                  </>
                )}
              </Button>
            )}

            {/* Host Response */}
            {review.has_response && review.response && (
              <div className="mt-4 bg-gray-50 p-4 rounded-lg border-l-4 border-sea-green-500">
                <div className="flex items-center gap-2 mb-2">
                  <MessageSquare className="h-4 w-4 text-sea-green-500" />
                  <span className="text-sm font-medium text-gray-900">
                    Response from {review.host_name}
                  </span>
                  {review.response_date && (
                    <span className="text-xs text-gray-500">
                      {reviewsService.getTimeAgo(review.response_date)}
                    </span>
                  )}
                </div>
                <p className="text-gray-700 text-sm leading-relaxed">
                  {review.response}
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="animate-pulse space-y-6">
            <div className="flex items-center gap-4">
              <div className="h-8 bg-gray-200 rounded w-32"></div>
              <div className="h-6 bg-gray-200 rounded w-24"></div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-3">
                <div className="h-4 bg-gray-200 rounded"></div>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              </div>
              <div className="space-y-2">
                {[...Array(5)].map((_, i) => (
                  <div key={i} className="h-3 bg-gray-200 rounded"></div>
                ))}
              </div>
            </div>
            {[...Array(3)].map((_, i) => (
              <div key={i} className="flex gap-4">
                <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-1/3"></div>
                  <div className="h-4 bg-gray-200 rounded"></div>
                  <div className="h-4 bg-gray-200 rounded w-2/3"></div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error || !reviewsData) {
    return (
      <Card className={className}>
        <CardContent className="p-6 text-center">
          <MessageSquare className="h-12 w-12 mx-auto mb-4 text-gray-400" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Unable to Load Reviews
          </h3>
          <p className="text-gray-600 mb-4">
            {error || 'There was a problem loading the reviews.'}
          </p>
          <Button onClick={fetchReviews}>
            Try Again
          </Button>
        </CardContent>
      </Card>
    );
  }

  const { reviews, stats } = reviewsData;

  if (stats.total_reviews === 0) {
    return (
      <Card className={className}>
        <CardContent className="p-6 text-center">
          <MessageSquare className="h-12 w-12 mx-auto mb-4 text-gray-400" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No Reviews Yet
          </h3>
          <p className="text-gray-600 mb-4">
            Be the first to share your experience at this property!
          </p>
          {showWriteReview && onWriteReview && (
            <Button
              onClick={onWriteReview}
              className="bg-sea-green-500 hover:bg-sea-green-600"
            >
              Write a Review
            </Button>
          )}
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-3">
            <div className="flex items-center gap-2">
              <Star className="h-6 w-6 text-yellow-400 fill-current" />
              <span className="text-2xl font-bold">{reviewsService.formatRating(stats.average_rating)}</span>
            </div>
            <div>
              <div className="text-lg font-semibold">
                {stats.total_reviews} review{stats.total_reviews !== 1 ? 's' : ''}
              </div>
            </div>
          </CardTitle>
          
          {showWriteReview && onWriteReview && (
            <Button
              onClick={onWriteReview}
              className="bg-sea-green-500 hover:bg-sea-green-600"
            >
              Write a Review
            </Button>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Rating Breakdown */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="font-medium text-gray-900 mb-3">Overall Rating</h4>
            <div className="flex items-center gap-3 mb-4">
              <div className="text-3xl font-bold text-gray-900">
                {reviewsService.formatRating(stats.average_rating)}
              </div>
              <div>
                {renderStars(Math.round(stats.average_rating))}
                <div className="text-sm text-gray-600">
                  Based on {stats.total_reviews} reviews
                </div>
              </div>
            </div>
          </div>
          
          <div>
            <h4 className="font-medium text-gray-900 mb-3">Rating Breakdown</h4>
            {renderRatingBreakdown(stats)}
          </div>
        </div>

        {/* Sort Controls */}
        <div className="flex items-center gap-4 pt-4 border-t">
          <span className="text-sm font-medium text-gray-700">Sort by:</span>
          <div className="flex items-center gap-2">
            <Button
              variant={sortBy === 'created_at' ? 'default' : 'outline'}
              size="sm"
              onClick={() => handleSortChange('created_at')}
              className="flex items-center gap-1"
            >
              Most Recent
              {sortBy === 'created_at' && (
                sortOrder === 'DESC' ? <SortDesc className="h-3 w-3" /> : <SortAsc className="h-3 w-3" />
              )}
            </Button>
            <Button
              variant={sortBy === 'rating' ? 'default' : 'outline'}
              size="sm"
              onClick={() => handleSortChange('rating')}
              className="flex items-center gap-1"
            >
              Highest Rated
              {sortBy === 'rating' && (
                sortOrder === 'DESC' ? <SortDesc className="h-3 w-3" /> : <SortAsc className="h-3 w-3" />
              )}
            </Button>
          </div>
        </div>

        {/* Reviews List */}
        <div className="space-y-6">
          {reviews.map(renderReview)}
        </div>

        {/* Show More Button */}
        {!showAllReviews && stats.total_reviews > 6 && (
          <div className="text-center pt-4 border-t">
            <Button
              variant="outline"
              onClick={() => setShowAllReviews(true)}
            >
              Show all {stats.total_reviews} reviews
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export { WriteReview };
