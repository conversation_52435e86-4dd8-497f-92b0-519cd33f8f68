import React, { useEffect, useRef, useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  MapPin, 
  Maximize2, 
  Minimize2, 
  RotateCcw,
  Search,
  Filter,
  Star,
  DollarSign
} from 'lucide-react';

// Simple map implementation without external dependencies for now
interface Property {
  id: string;
  title: string;
  location: string;
  price: number;
  images: string[];
  coordinates?: {
    latitude: number;
    longitude: number;
  };
  averageRating?: number;
  reviewCount?: number;
  propertyType?: string;
}

interface PropertyMapProps {
  properties: Property[];
  onPropertySelect?: (property: Property) => void;
  onMapBoundsChange?: (bounds: any) => void;
  selectedProperty?: Property | null;
  className?: string;
  height?: string;
  showControls?: boolean;
  showSearch?: boolean;
}

// Mock coordinates for South African coastal properties
const MOCK_COORDINATES = {
  'Margate': { lat: -30.8647, lng: 30.3707 },
  'Scottburgh': { lat: -30.2867, lng: 30.7533 },
  'Hibberdene': { lat: -30.5833, lng: 30.6167 },
  'Port Shepstone': { lat: -30.7417, lng: 30.4550 },
  'Ramsgate': { lat: -30.8833, lng: 30.3500 },
  'Uvongo': { lat: -30.8333, lng: 30.3833 },
  'Shelly Beach': { lat: -30.8167, lng: 30.4000 },
  'Pennington': { lat: -30.2167, lng: 30.7833 }
};

export const PropertyMap: React.FC<PropertyMapProps> = ({
  properties,
  onPropertySelect,
  onMapBoundsChange,
  selectedProperty,
  className,
  height = '400px',
  showControls = true,
  showSearch = false
}) => {
  const mapRef = useRef<HTMLDivElement>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [mapCenter, setMapCenter] = useState({ lat: -30.5, lng: 30.5 });
  const [zoom, setZoom] = useState(10);
  const [hoveredProperty, setHoveredProperty] = useState<Property | null>(null);

  // Add coordinates to properties if missing
  const propertiesWithCoords = properties.map(property => {
    if (!property.coordinates) {
      const coords = MOCK_COORDINATES[property.location as keyof typeof MOCK_COORDINATES];
      if (coords) {
        return {
          ...property,
          coordinates: { latitude: coords.lat, longitude: coords.lng }
        };
      }
    }
    return property;
  }).filter(p => p.coordinates);

  const handleMarkerClick = (property: Property) => {
    if (onPropertySelect) {
      onPropertySelect(property);
    }
  };

  const handleMapReset = () => {
    setMapCenter({ lat: -30.5, lng: 30.5 });
    setZoom(10);
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  const getMarkerColor = (property: Property) => {
    if (selectedProperty?.id === property.id) return '#059669'; // sea-green-600
    if (hoveredProperty?.id === property.id) return '#0891b2'; // ocean-blue-600
    return '#6b7280'; // gray-500
  };

  const renderPropertyMarker = (property: Property, index: number) => {
    if (!property.coordinates) return null;

    const { latitude, longitude } = property.coordinates;
    
    // Calculate position on the mock map (simplified projection)
    const mapWidth = mapRef.current?.clientWidth || 800;
    const mapHeight = mapRef.current?.clientHeight || 400;
    
    // Simple mercator-like projection for South African coast
    const x = ((longitude - 29.5) / 2) * mapWidth;
    const y = ((latitude + 32) / 2) * mapHeight;

    return (
      <div
        key={property.id}
        className="absolute transform -translate-x-1/2 -translate-y-1/2 cursor-pointer z-10"
        style={{ left: `${x}px`, top: `${y}px` }}
        onClick={() => handleMarkerClick(property)}
        onMouseEnter={() => setHoveredProperty(property)}
        onMouseLeave={() => setHoveredProperty(null)}
      >
        {/* Marker Pin */}
        <div
          className="relative flex items-center justify-center w-8 h-8 rounded-full border-2 border-white shadow-lg transition-all duration-200 hover:scale-110"
          style={{ backgroundColor: getMarkerColor(property) }}
        >
          <MapPin className="h-4 w-4 text-white" />
          
          {/* Price Badge */}
          <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-white rounded-full px-2 py-1 shadow-md border">
            <span className="text-xs font-semibold text-gray-900">
              R{property.price.toLocaleString()}
            </span>
          </div>
        </div>

        {/* Property Preview Card */}
        {(hoveredProperty?.id === property.id || selectedProperty?.id === property.id) && (
          <div className="absolute top-10 left-1/2 transform -translate-x-1/2 w-64 bg-white rounded-lg shadow-xl border z-20 p-3">
            <div className="flex gap-3">
              <img
                src={property.images[0] || "https://images.unsplash.com/photo-1721322800607-8c38375eef04?w=80&h=60&fit=crop"}
                alt={property.title}
                className="w-16 h-12 object-cover rounded"
              />
              <div className="flex-1 min-w-0">
                <h4 className="font-semibold text-sm text-gray-900 truncate">
                  {property.title}
                </h4>
                <p className="text-xs text-gray-600">{property.location}</p>
                <div className="flex items-center justify-between mt-1">
                  <div className="flex items-center gap-1">
                    {property.averageRating && (
                      <>
                        <Star className="h-3 w-3 text-yellow-400 fill-current" />
                        <span className="text-xs text-gray-600">
                          {property.averageRating.toFixed(1)}
                        </span>
                      </>
                    )}
                  </div>
                  <span className="text-sm font-semibold text-sea-green-600">
                    R{property.price.toLocaleString()}
                  </span>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <Card className={`${className} ${isFullscreen ? 'fixed inset-4 z-50' : ''}`}>
      {showControls && (
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5" />
              Property Map
              <Badge variant="secondary">{propertiesWithCoords.length} properties</Badge>
            </CardTitle>
            <div className="flex items-center gap-2">
              {showSearch && (
                <Button variant="outline" size="sm">
                  <Search className="h-4 w-4 mr-1" />
                  Search Area
                </Button>
              )}
              <Button variant="outline" size="sm" onClick={handleMapReset}>
                <RotateCcw className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="sm" onClick={toggleFullscreen}>
                {isFullscreen ? (
                  <Minimize2 className="h-4 w-4" />
                ) : (
                  <Maximize2 className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>
        </CardHeader>
      )}

      <CardContent className="p-0">
        <div
          ref={mapRef}
          className="relative bg-gradient-to-br from-blue-100 to-green-100 overflow-hidden"
          style={{ height: isFullscreen ? 'calc(100vh - 120px)' : height }}
        >
          {/* Mock Map Background */}
          <div className="absolute inset-0 bg-gradient-to-br from-blue-200 via-blue-100 to-green-100">
            {/* Coastline representation */}
            <svg className="absolute inset-0 w-full h-full" viewBox="0 0 800 400">
              <path
                d="M 0 200 Q 200 180 400 190 T 800 200 L 800 400 L 0 400 Z"
                fill="#3b82f6"
                fillOpacity="0.3"
                stroke="#2563eb"
                strokeWidth="2"
              />
              <path
                d="M 0 220 Q 200 200 400 210 T 800 220 L 800 400 L 0 400 Z"
                fill="#059669"
                fillOpacity="0.2"
              />
            </svg>
            
            {/* Grid lines */}
            <div className="absolute inset-0 opacity-20">
              {Array.from({ length: 10 }).map((_, i) => (
                <div key={`v-${i}`} className="absolute bg-gray-400" style={{
                  left: `${i * 10}%`,
                  top: 0,
                  width: '1px',
                  height: '100%'
                }} />
              ))}
              {Array.from({ length: 5 }).map((_, i) => (
                <div key={`h-${i}`} className="absolute bg-gray-400" style={{
                  top: `${i * 20}%`,
                  left: 0,
                  height: '1px',
                  width: '100%'
                }} />
              ))}
            </div>
          </div>

          {/* Property Markers */}
          {propertiesWithCoords.map((property, index) => 
            renderPropertyMarker(property, index)
          )}

          {/* Map Legend */}
          <div className="absolute bottom-4 left-4 bg-white rounded-lg shadow-md p-3 text-xs">
            <div className="font-semibold mb-2">Legend</div>
            <div className="flex items-center gap-2 mb-1">
              <div className="w-3 h-3 rounded-full bg-gray-500"></div>
              <span>Available Property</span>
            </div>
            <div className="flex items-center gap-2 mb-1">
              <div className="w-3 h-3 rounded-full bg-ocean-blue-600"></div>
              <span>Hovered Property</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 rounded-full bg-sea-green-600"></div>
              <span>Selected Property</span>
            </div>
          </div>

          {/* Zoom Controls */}
          <div className="absolute top-4 right-4 flex flex-col gap-1">
            <Button
              variant="outline"
              size="sm"
              className="w-8 h-8 p-0 bg-white"
              onClick={() => setZoom(Math.min(zoom + 1, 15))}
            >
              +
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="w-8 h-8 p-0 bg-white"
              onClick={() => setZoom(Math.max(zoom - 1, 5))}
            >
              -
            </Button>
          </div>

          {/* No Properties Message */}
          {propertiesWithCoords.length === 0 && (
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="text-center text-gray-500">
                <MapPin className="h-12 w-12 mx-auto mb-2 opacity-50" />
                <p>No properties to display on map</p>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
