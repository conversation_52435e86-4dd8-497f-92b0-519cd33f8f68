// Environment configuration for StayFinder
export type Environment = 'development' | 'staging' | 'production';

export interface EnvironmentConfig {
  NODE_ENV: Environment;
  API_BASE_URL: string;
  APP_URL: string;
  DATABASE_URL?: string;
  
  // Feature flags
  FEATURES: {
    ENABLE_ANALYTICS: boolean;
    ENABLE_ERROR_TRACKING: boolean;
    ENABLE_PERFORMANCE_MONITORING: boolean;
    ENABLE_A_B_TESTING: boolean;
    ENABLE_CHAT_SUPPORT: boolean;
    ENABLE_PUSH_NOTIFICATIONS: boolean;
    ENABLE_OFFLINE_MODE: boolean;
    ENABLE_DARK_MODE: boolean;
    ENABLE_MULTI_LANGUAGE: boolean;
    ENABLE_SMART_PRICING: boolean;
  };
  
  // API Keys (use environment variables in production)
  API_KEYS: {
    GOOGLE_ANALYTICS?: string;
    GOOGLE_MAPS?: string;
    SENTRY_DSN?: string;
    STRIPE_PUBLISHABLE?: string;
    FIREBASE_CONFIG?: string;
    MAPBOX_TOKEN?: string;
  };
  
  // Performance settings
  PERFORMANCE: {
    ENABLE_CODE_SPLITTING: boolean;
    ENABLE_IMAGE_OPTIMIZATION: boolean;
    ENABLE_SERVICE_WORKER: boolean;
    CACHE_DURATION: number; // in seconds
    MAX_BUNDLE_SIZE: number; // in KB
    LAZY_LOAD_THRESHOLD: number; // in pixels
  };
  
  // Security settings
  SECURITY: {
    ENABLE_CSP: boolean;
    ENABLE_HTTPS_ONLY: boolean;
    ENABLE_RATE_LIMITING: boolean;
    SESSION_TIMEOUT: number; // in minutes
    MAX_LOGIN_ATTEMPTS: number;
    ENABLE_2FA: boolean;
  };
  
  // Logging and monitoring
  LOGGING: {
    LEVEL: 'debug' | 'info' | 'warn' | 'error';
    ENABLE_CONSOLE_LOGS: boolean;
    ENABLE_REMOTE_LOGGING: boolean;
    LOG_RETENTION_DAYS: number;
  };
}

// Development configuration
const developmentConfig: EnvironmentConfig = {
  NODE_ENV: 'development',
  API_BASE_URL: 'http://localhost:3001/api',
  APP_URL: 'http://localhost:5173',
  
  FEATURES: {
    ENABLE_ANALYTICS: false,
    ENABLE_ERROR_TRACKING: true,
    ENABLE_PERFORMANCE_MONITORING: true,
    ENABLE_A_B_TESTING: false,
    ENABLE_CHAT_SUPPORT: false,
    ENABLE_PUSH_NOTIFICATIONS: false,
    ENABLE_OFFLINE_MODE: true,
    ENABLE_DARK_MODE: true,
    ENABLE_MULTI_LANGUAGE: true,
    ENABLE_SMART_PRICING: true,
  },
  
  API_KEYS: {
    GOOGLE_ANALYTICS: undefined,
    GOOGLE_MAPS: process.env.VITE_GOOGLE_MAPS_API_KEY,
    SENTRY_DSN: undefined,
    STRIPE_PUBLISHABLE: process.env.VITE_STRIPE_PUBLISHABLE_KEY,
    MAPBOX_TOKEN: process.env.VITE_MAPBOX_TOKEN,
  },
  
  PERFORMANCE: {
    ENABLE_CODE_SPLITTING: true,
    ENABLE_IMAGE_OPTIMIZATION: true,
    ENABLE_SERVICE_WORKER: false, // Disabled in dev for easier debugging
    CACHE_DURATION: 300, // 5 minutes
    MAX_BUNDLE_SIZE: 2048, // 2MB
    LAZY_LOAD_THRESHOLD: 100,
  },
  
  SECURITY: {
    ENABLE_CSP: false, // Disabled in dev for easier debugging
    ENABLE_HTTPS_ONLY: false,
    ENABLE_RATE_LIMITING: false,
    SESSION_TIMEOUT: 480, // 8 hours
    MAX_LOGIN_ATTEMPTS: 10,
    ENABLE_2FA: false,
  },
  
  LOGGING: {
    LEVEL: 'debug',
    ENABLE_CONSOLE_LOGS: true,
    ENABLE_REMOTE_LOGGING: false,
    LOG_RETENTION_DAYS: 7,
  },
};

// Staging configuration
const stagingConfig: EnvironmentConfig = {
  NODE_ENV: 'staging',
  API_BASE_URL: 'https://api-staging.stayfinder.co.za/api',
  APP_URL: 'https://staging.stayfinder.co.za',
  
  FEATURES: {
    ENABLE_ANALYTICS: true,
    ENABLE_ERROR_TRACKING: true,
    ENABLE_PERFORMANCE_MONITORING: true,
    ENABLE_A_B_TESTING: true,
    ENABLE_CHAT_SUPPORT: true,
    ENABLE_PUSH_NOTIFICATIONS: true,
    ENABLE_OFFLINE_MODE: true,
    ENABLE_DARK_MODE: true,
    ENABLE_MULTI_LANGUAGE: true,
    ENABLE_SMART_PRICING: true,
  },
  
  API_KEYS: {
    GOOGLE_ANALYTICS: process.env.VITE_GA_STAGING_ID,
    GOOGLE_MAPS: process.env.VITE_GOOGLE_MAPS_API_KEY,
    SENTRY_DSN: process.env.VITE_SENTRY_DSN_STAGING,
    STRIPE_PUBLISHABLE: process.env.VITE_STRIPE_PUBLISHABLE_KEY_STAGING,
    MAPBOX_TOKEN: process.env.VITE_MAPBOX_TOKEN,
  },
  
  PERFORMANCE: {
    ENABLE_CODE_SPLITTING: true,
    ENABLE_IMAGE_OPTIMIZATION: true,
    ENABLE_SERVICE_WORKER: true,
    CACHE_DURATION: 1800, // 30 minutes
    MAX_BUNDLE_SIZE: 1024, // 1MB
    LAZY_LOAD_THRESHOLD: 200,
  },
  
  SECURITY: {
    ENABLE_CSP: true,
    ENABLE_HTTPS_ONLY: true,
    ENABLE_RATE_LIMITING: true,
    SESSION_TIMEOUT: 240, // 4 hours
    MAX_LOGIN_ATTEMPTS: 5,
    ENABLE_2FA: true,
  },
  
  LOGGING: {
    LEVEL: 'info',
    ENABLE_CONSOLE_LOGS: false,
    ENABLE_REMOTE_LOGGING: true,
    LOG_RETENTION_DAYS: 30,
  },
};

// Production configuration
const productionConfig: EnvironmentConfig = {
  NODE_ENV: 'production',
  API_BASE_URL: 'https://api.stayfinder.co.za/api',
  APP_URL: 'https://stayfinder.co.za',
  
  FEATURES: {
    ENABLE_ANALYTICS: true,
    ENABLE_ERROR_TRACKING: true,
    ENABLE_PERFORMANCE_MONITORING: true,
    ENABLE_A_B_TESTING: true,
    ENABLE_CHAT_SUPPORT: true,
    ENABLE_PUSH_NOTIFICATIONS: true,
    ENABLE_OFFLINE_MODE: true,
    ENABLE_DARK_MODE: true,
    ENABLE_MULTI_LANGUAGE: true,
    ENABLE_SMART_PRICING: true,
  },
  
  API_KEYS: {
    GOOGLE_ANALYTICS: process.env.VITE_GA_PRODUCTION_ID,
    GOOGLE_MAPS: process.env.VITE_GOOGLE_MAPS_API_KEY,
    SENTRY_DSN: process.env.VITE_SENTRY_DSN_PRODUCTION,
    STRIPE_PUBLISHABLE: process.env.VITE_STRIPE_PUBLISHABLE_KEY_PRODUCTION,
    MAPBOX_TOKEN: process.env.VITE_MAPBOX_TOKEN,
  },
  
  PERFORMANCE: {
    ENABLE_CODE_SPLITTING: true,
    ENABLE_IMAGE_OPTIMIZATION: true,
    ENABLE_SERVICE_WORKER: true,
    CACHE_DURATION: 3600, // 1 hour
    MAX_BUNDLE_SIZE: 512, // 512KB
    LAZY_LOAD_THRESHOLD: 300,
  },
  
  SECURITY: {
    ENABLE_CSP: true,
    ENABLE_HTTPS_ONLY: true,
    ENABLE_RATE_LIMITING: true,
    SESSION_TIMEOUT: 120, // 2 hours
    MAX_LOGIN_ATTEMPTS: 3,
    ENABLE_2FA: true,
  },
  
  LOGGING: {
    LEVEL: 'warn',
    ENABLE_CONSOLE_LOGS: false,
    ENABLE_REMOTE_LOGGING: true,
    LOG_RETENTION_DAYS: 90,
  },
};

// Get current environment
const getCurrentEnvironment = (): Environment => {
  const env = import.meta.env.MODE as Environment;
  if (['development', 'staging', 'production'].includes(env)) {
    return env;
  }
  return 'development'; // Default fallback
};

// Get configuration for current environment
export const getConfig = (): EnvironmentConfig => {
  const env = getCurrentEnvironment();
  
  switch (env) {
    case 'staging':
      return stagingConfig;
    case 'production':
      return productionConfig;
    default:
      return developmentConfig;
  }
};

// Export current configuration
export const config = getConfig();

// Environment utilities
export const isDevelopment = () => config.NODE_ENV === 'development';
export const isStaging = () => config.NODE_ENV === 'staging';
export const isProduction = () => config.NODE_ENV === 'production';

// Feature flag utilities
export const isFeatureEnabled = (feature: keyof EnvironmentConfig['FEATURES']): boolean => {
  return config.FEATURES[feature];
};

// API utilities
export const getApiUrl = (endpoint: string): string => {
  return `${config.API_BASE_URL}${endpoint.startsWith('/') ? '' : '/'}${endpoint}`;
};

// Asset utilities
export const getAssetUrl = (path: string): string => {
  const baseUrl = isProduction() ? config.APP_URL : '';
  return `${baseUrl}${path.startsWith('/') ? '' : '/'}${path}`;
};

// Logging utility
export const shouldLog = (level: EnvironmentConfig['LOGGING']['LEVEL']): boolean => {
  const levels = ['debug', 'info', 'warn', 'error'];
  const currentLevelIndex = levels.indexOf(config.LOGGING.LEVEL);
  const requestedLevelIndex = levels.indexOf(level);
  
  return requestedLevelIndex >= currentLevelIndex;
};

// Performance utilities
export const shouldEnableFeature = (feature: string): boolean => {
  // Check if feature should be enabled based on environment and performance settings
  if (isDevelopment()) {
    return true; // Enable all features in development
  }
  
  // Add specific logic for different features
  switch (feature) {
    case 'heavy-animations':
      return !config.PERFORMANCE.ENABLE_SERVICE_WORKER; // Disable on slow connections
    case 'auto-play-videos':
      return isProduction(); // Only in production
    default:
      return true;
  }
};

// Security utilities
export const getCSPDirectives = (): string => {
  if (!config.SECURITY.ENABLE_CSP) {
    return '';
  }
  
  const directives = [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.google-analytics.com https://www.googletagmanager.com",
    "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
    "font-src 'self' https://fonts.gstatic.com",
    "img-src 'self' data: https: blob:",
    "connect-src 'self' https://api.stayfinder.co.za https://www.google-analytics.com",
    "frame-src 'self' https://www.google.com",
  ];
  
  return directives.join('; ');
};

// Environment validation
export const validateEnvironment = (): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];
  
  // Check required API keys for production
  if (isProduction()) {
    if (!config.API_KEYS.GOOGLE_ANALYTICS) {
      errors.push('Google Analytics ID is required in production');
    }
    if (!config.API_KEYS.SENTRY_DSN) {
      errors.push('Sentry DSN is required in production');
    }
  }
  
  // Check performance settings
  if (config.PERFORMANCE.MAX_BUNDLE_SIZE < 100) {
    errors.push('Max bundle size is too small');
  }
  
  // Check security settings
  if (isProduction() && !config.SECURITY.ENABLE_HTTPS_ONLY) {
    errors.push('HTTPS should be enforced in production');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

// Initialize environment
export const initializeEnvironment = () => {
  const validation = validateEnvironment();
  
  if (!validation.isValid) {
    console.warn('Environment validation failed:', validation.errors);
  }
  
  // Set global configuration
  if (typeof window !== 'undefined') {
    (window as any).__STAYFINDER_CONFIG__ = config;
  }
  
  // Log environment info
  if (shouldLog('info')) {
    console.log(`StayFinder initialized in ${config.NODE_ENV} mode`);
    console.log('Features enabled:', Object.entries(config.FEATURES)
      .filter(([, enabled]) => enabled)
      .map(([feature]) => feature)
    );
  }
};

// Export environment info for debugging
export const getEnvironmentInfo = () => ({
  environment: config.NODE_ENV,
  features: config.FEATURES,
  performance: config.PERFORMANCE,
  security: {
    ...config.SECURITY,
    // Don't expose sensitive settings
  },
  buildTime: new Date().toISOString(),
  version: '1.0.0'
});
