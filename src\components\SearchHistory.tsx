import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { advancedSearchService, SearchHistory, SearchCriteria } from '../services/advancedSearchService';
import { 
  Clock, 
  Search,
  MapPin,
  Users,
  Home,
  DollarSign,
  Calendar,
  RotateCcw,
  Trash2
} from 'lucide-react';

interface SearchHistoryProps {
  onSearchSelect?: (criteria: SearchCriteria) => void;
  className?: string;
  maxItems?: number;
}

export const SearchHistoryComponent: React.FC<SearchHistoryProps> = ({
  onSearchSelect,
  className,
  maxItems = 10
}) => {
  const [searchHistory, setSearchHistory] = useState<SearchHistory[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchSearchHistory();
  }, []);

  const fetchSearchHistory = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const data = await advancedSearchService.getSearchHistory(maxItems);
      setSearchHistory(data);
    } catch (err) {
      console.error('Error fetching search history:', err);
      setError('Failed to load search history');
    } finally {
      setLoading(false);
    }
  };

  const formatTimeAgo = (dateString: string): string => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMs = now.getTime() - date.getTime();
    const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
    const diffInHours = Math.floor(diffInMinutes / 60);
    const diffInDays = Math.floor(diffInHours / 24);
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInHours < 24) return `${diffInHours}h ago`;
    if (diffInDays < 7) return `${diffInDays}d ago`;
    
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    });
  };

  const renderSearchCriteria = (criteria: SearchCriteria) => {
    const items = [];
    
    if (criteria.location) {
      items.push(
        <div key="location" className="flex items-center gap-1 text-xs text-gray-600">
          <MapPin className="h-3 w-3" />
          <span>{criteria.location}</span>
        </div>
      );
    }
    
    if (criteria.propertyType) {
      items.push(
        <div key="type" className="flex items-center gap-1 text-xs text-gray-600">
          <Home className="h-3 w-3" />
          <span>{criteria.propertyType}</span>
        </div>
      );
    }
    
    if (criteria.guests) {
      items.push(
        <div key="guests" className="flex items-center gap-1 text-xs text-gray-600">
          <Users className="h-3 w-3" />
          <span>{criteria.guests} guests</span>
        </div>
      );
    }
    
    if (criteria.minPrice || criteria.maxPrice) {
      const priceText = criteria.minPrice && criteria.maxPrice 
        ? `R${criteria.minPrice} - R${criteria.maxPrice}`
        : criteria.minPrice 
        ? `From R${criteria.minPrice}`
        : `Up to R${criteria.maxPrice}`;
      
      items.push(
        <div key="price" className="flex items-center gap-1 text-xs text-gray-600">
          <DollarSign className="h-3 w-3" />
          <span>{priceText}</span>
        </div>
      );
    }
    
    if (criteria.checkIn && criteria.checkOut) {
      items.push(
        <div key="dates" className="flex items-center gap-1 text-xs text-gray-600">
          <Calendar className="h-3 w-3" />
          <span>{new Date(criteria.checkIn).toLocaleDateString()} - {new Date(criteria.checkOut).toLocaleDateString()}</span>
        </div>
      );
    }
    
    return items;
  };

  const renderHistoryItem = (item: SearchHistory) => {
    const criteriaItems = renderSearchCriteria(item.search_criteria);
    const searchText = advancedSearchService.formatSearchCriteria(item.search_criteria);
    
    return (
      <div 
        key={item.id} 
        className="p-3 border border-gray-200 rounded-lg hover:border-sea-green-300 transition-colors cursor-pointer"
        onClick={() => onSearchSelect?.(item.search_criteria)}
      >
        <div className="flex items-start justify-between mb-2">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-1">
              <Clock className="h-4 w-4 text-gray-400" />
              <span className="text-sm font-medium text-gray-900 truncate">
                {searchText}
              </span>
            </div>
            
            <div className="flex flex-wrap gap-2">
              {criteriaItems.slice(0, 3)}
              {criteriaItems.length > 3 && (
                <Badge variant="outline" className="text-xs">
                  +{criteriaItems.length - 3} more
                </Badge>
              )}
            </div>
          </div>
          
          <div className="flex items-center gap-2 ml-2">
            <Badge variant="secondary" className="text-xs">
              {item.results_count} results
            </Badge>
            <span className="text-xs text-gray-500 whitespace-nowrap">
              {formatTimeAgo(item.search_timestamp)}
            </span>
          </div>
        </div>
        
        <div className="flex items-center justify-between pt-2 border-t border-gray-100">
          <div className="flex items-center gap-1 text-xs text-gray-500">
            <Search className="h-3 w-3" />
            <span>Click to search again</span>
          </div>
          
          <Button
            size="sm"
            variant="ghost"
            onClick={(e) => {
              e.stopPropagation();
              onSearchSelect?.(item.search_criteria);
            }}
            className="text-xs p-1 h-auto"
          >
            <RotateCcw className="h-3 w-3" />
          </Button>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="animate-pulse space-y-3">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="border border-gray-200 rounded-lg p-3">
                <div className="flex items-center gap-2 mb-2">
                  <div className="h-4 w-4 bg-gray-200 rounded"></div>
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                </div>
                <div className="flex gap-2 mb-2">
                  <div className="h-5 bg-gray-200 rounded w-16"></div>
                  <div className="h-5 bg-gray-200 rounded w-20"></div>
                </div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="p-6 text-center">
          <Clock className="h-12 w-12 mx-auto mb-4 text-gray-400" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Unable to Load Search History
          </h3>
          <p className="text-gray-600 mb-4">{error}</p>
          <Button onClick={fetchSearchHistory}>
            Try Again
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2">
          <Clock className="h-5 w-5" />
          Recent Searches
        </CardTitle>
      </CardHeader>

      <CardContent>
        {searchHistory.length === 0 ? (
          <div className="text-center py-8">
            <Clock className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No Search History
            </h3>
            <p className="text-gray-600">
              Your recent searches will appear here for quick access
            </p>
          </div>
        ) : (
          <div className="space-y-3">
            {searchHistory.map(renderHistoryItem)}
          </div>
        )}
      </CardContent>
    </Card>
  );
};
