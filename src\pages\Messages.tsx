import React, { useState } from 'react';
import { Header } from '../components/Header';
import { MessagesList, ChatInterface } from '../components/MessagesList';
import { Conversation } from '../services/messagesService';
import { Card, CardContent } from '@/components/ui/card';
import { MessageSquare, Users, Clock } from 'lucide-react';

export const Messages: React.FC = () => {
  const [selectedConversation, setSelectedConversation] = useState<Conversation | null>(null);
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);

  React.useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const handleConversationSelect = (conversation: Conversation) => {
    setSelectedConversation(conversation);
  };

  const handleBackToList = () => {
    setSelectedConversation(null);
  };

  // Mobile view: show either list or chat
  if (isMobile) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="container mx-auto px-4 py-6">
          {selectedConversation ? (
            <ChatInterface
              conversation={selectedConversation}
              onBack={handleBackToList}
              className="h-[calc(100vh-200px)]"
            />
          ) : (
            <MessagesList
              onConversationSelect={handleConversationSelect}
              selectedConversationId={selectedConversation?.id}
              className="h-[calc(100vh-200px)]"
            />
          )}
        </div>
      </div>
    );
  }

  // Desktop view: show both list and chat side by side
  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      <div className="container mx-auto px-4 py-6">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Messages</h1>
          <p className="text-gray-600">
            Communicate with hosts and guests about your bookings
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-[calc(100vh-250px)]">
          {/* Messages List */}
          <div className="lg:col-span-1">
            <MessagesList
              onConversationSelect={handleConversationSelect}
              selectedConversationId={selectedConversation?.id}
              className="h-full"
            />
          </div>

          {/* Chat Interface */}
          <div className="lg:col-span-2">
            {selectedConversation ? (
              <ChatInterface
                conversation={selectedConversation}
                className="h-full"
              />
            ) : (
              <Card className="h-full">
                <CardContent className="h-full flex items-center justify-center">
                  <div className="text-center">
                    <MessageSquare className="h-16 w-16 mx-auto mb-4 text-gray-400" />
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">
                      Select a conversation
                    </h3>
                    <p className="text-gray-600 mb-6">
                      Choose a conversation from the list to start messaging
                    </p>
                    
                    {/* Quick stats */}
                    <div className="grid grid-cols-2 gap-4 max-w-sm mx-auto">
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <Users className="h-6 w-6 mx-auto mb-2 text-sea-green-500" />
                        <p className="text-sm font-medium text-gray-900">Connect</p>
                        <p className="text-xs text-gray-600">with hosts & guests</p>
                      </div>
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <Clock className="h-6 w-6 mx-auto mb-2 text-sea-green-500" />
                        <p className="text-sm font-medium text-gray-900">Real-time</p>
                        <p className="text-xs text-gray-600">messaging</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
