-- ============================================================
-- STAYFINDER DATABASE MIGRATIONS
-- Execute these SQL commands in your Supabase SQL Editor
-- ============================================================

-- ============================================================
-- MIGRATION 001: User Favorites System
-- ============================================================

-- Create user_favorites table
CREATE TABLE public.user_favorites (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    property_id UUID NOT NULL REFERENCES public.properties(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ DEFAULT now(),
    UNIQUE(user_id, property_id)
);

-- Create indexes for performance
CREATE INDEX idx_user_favorites_user_id ON public.user_favorites(user_id);
CREATE INDEX idx_user_favorites_property_id ON public.user_favorites(property_id);
CREATE INDEX idx_user_favorites_created_at ON public.user_favorites(created_at DESC);

-- Enable RLS
ALTER TABLE public.user_favorites ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can view their own favorites" ON public.user_favorites
    FOR SELECT USING (auth.uid()::uuid = user_id);

CREATE POLICY "Users can manage their own favorites" ON public.user_favorites
    FOR ALL USING (auth.uid()::uuid = user_id);

-- ============================================================
-- MIGRATION 002: Search History & Analytics
-- ============================================================

-- Create search_history table
CREATE TABLE public.search_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    search_query TEXT NOT NULL,
    filters JSONB DEFAULT '{}',
    results_count INTEGER DEFAULT 0,
    location TEXT,
    check_in_date DATE,
    check_out_date DATE,
    guests INTEGER,
    created_at TIMESTAMPTZ DEFAULT now()
);

-- Create property_views table
CREATE TABLE public.property_views (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    property_id UUID NOT NULL REFERENCES public.properties(id) ON DELETE CASCADE,
    user_id UUID REFERENCES public.users(id) ON DELETE SET NULL,
    session_id TEXT,
    ip_address INET,
    user_agent TEXT,
    referrer TEXT,
    viewed_at TIMESTAMPTZ DEFAULT now()
);

-- Indexes for performance
CREATE INDEX idx_search_history_user_id ON public.search_history(user_id);
CREATE INDEX idx_search_history_created_at ON public.search_history(created_at DESC);
CREATE INDEX idx_property_views_property_id ON public.property_views(property_id);
CREATE INDEX idx_property_views_user_id ON public.property_views(user_id);
CREATE INDEX idx_property_views_viewed_at ON public.property_views(viewed_at DESC);

-- Enable RLS
ALTER TABLE public.search_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.property_views ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can view their own search history" ON public.search_history
    FOR SELECT USING (auth.uid()::uuid = user_id OR user_id IS NULL);

CREATE POLICY "Anyone can insert search history" ON public.search_history
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Anyone can insert property views" ON public.property_views
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Property views are readable by property owners" ON public.property_views
    FOR SELECT USING (
        property_id IN (
            SELECT id FROM public.properties 
            WHERE host_id = auth.uid()::uuid
        )
    );

-- ============================================================
-- MIGRATION 003: Conversation Management
-- ============================================================

-- Create conversations table
CREATE TABLE public.conversations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    property_id UUID REFERENCES public.properties(id) ON DELETE CASCADE,
    booking_id UUID REFERENCES public.bookings(id) ON DELETE SET NULL,
    participant_1_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    participant_2_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    last_message_at TIMESTAMPTZ DEFAULT now(),
    is_archived BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now(),
    UNIQUE(participant_1_id, participant_2_id, property_id)
);

-- Update messages table to reference conversations
ALTER TABLE public.messages 
ADD COLUMN IF NOT EXISTS conversation_id UUID REFERENCES public.conversations(id) ON DELETE CASCADE;

-- Create indexes
CREATE INDEX idx_conversations_participant_1 ON public.conversations(participant_1_id);
CREATE INDEX idx_conversations_participant_2 ON public.conversations(participant_2_id);
CREATE INDEX idx_conversations_property_id ON public.conversations(property_id);
CREATE INDEX idx_conversations_last_message_at ON public.conversations(last_message_at DESC);

-- Enable RLS
ALTER TABLE public.conversations ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can view their conversations" ON public.conversations
    FOR SELECT USING (
        auth.uid()::uuid = participant_1_id OR 
        auth.uid()::uuid = participant_2_id
    );

CREATE POLICY "Users can create conversations" ON public.conversations
    FOR INSERT WITH CHECK (
        auth.uid()::uuid = participant_1_id OR 
        auth.uid()::uuid = participant_2_id
    );

-- ============================================================
-- MIGRATION 004: Host Performance Metrics
-- ============================================================

-- Create host_metrics table
CREATE TABLE public.host_metrics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    host_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    metric_date DATE NOT NULL,
    total_views INTEGER DEFAULT 0,
    total_inquiries INTEGER DEFAULT 0,
    total_bookings INTEGER DEFAULT 0,
    total_revenue DECIMAL(10,2) DEFAULT 0,
    average_rating DECIMAL(3,2) DEFAULT 0,
    response_rate DECIMAL(5,2) DEFAULT 0,
    acceptance_rate DECIMAL(5,2) DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now(),
    UNIQUE(host_id, metric_date)
);

-- Create property_metrics table
CREATE TABLE public.property_metrics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    property_id UUID NOT NULL REFERENCES public.properties(id) ON DELETE CASCADE,
    metric_date DATE NOT NULL,
    views_count INTEGER DEFAULT 0,
    inquiries_count INTEGER DEFAULT 0,
    bookings_count INTEGER DEFAULT 0,
    revenue DECIMAL(10,2) DEFAULT 0,
    occupancy_rate DECIMAL(5,2) DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now(),
    UNIQUE(property_id, metric_date)
);

-- Indexes
CREATE INDEX idx_host_metrics_host_id ON public.host_metrics(host_id);
CREATE INDEX idx_host_metrics_date ON public.host_metrics(metric_date DESC);
CREATE INDEX idx_property_metrics_property_id ON public.property_metrics(property_id);
CREATE INDEX idx_property_metrics_date ON public.property_metrics(metric_date DESC);

-- Enable RLS
ALTER TABLE public.host_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.property_metrics ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Hosts can view their own metrics" ON public.host_metrics
    FOR SELECT USING (auth.uid()::uuid = host_id);

CREATE POLICY "Property owners can view property metrics" ON public.property_metrics
    FOR SELECT USING (
        property_id IN (
            SELECT id FROM public.properties 
            WHERE host_id = auth.uid()::uuid
        )
    );

-- ============================================================
-- PERFORMANCE OPTIMIZATIONS
-- ============================================================

-- Properties table optimizations
CREATE INDEX IF NOT EXISTS idx_properties_location_search 
ON public.properties USING GIN(to_tsvector('english', city || ' ' || province));

CREATE INDEX IF NOT EXISTS idx_properties_price_range 
ON public.properties(price_per_night) WHERE status = 'active';

CREATE INDEX IF NOT EXISTS idx_properties_availability 
ON public.properties(status, featured, created_at DESC) WHERE status = 'active';

-- Bookings table optimizations
CREATE INDEX IF NOT EXISTS idx_bookings_date_range 
ON public.bookings(check_in_date, check_out_date);

CREATE INDEX IF NOT EXISTS idx_bookings_status_date 
ON public.bookings(status, created_at DESC);

-- Reviews table optimizations
CREATE INDEX IF NOT EXISTS idx_reviews_property_rating
ON public.reviews(property_id, overall_rating DESC) WHERE status IS NOT NULL;

-- ============================================================
-- VERIFICATION QUERIES (Run after migrations)
-- ============================================================

-- Verify new tables exist
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('user_favorites', 'search_history', 'property_views', 'conversations', 'host_metrics', 'property_metrics');

-- Verify RLS policies
SELECT schemaname, tablename, policyname, permissive, roles, cmd 
FROM pg_policies 
WHERE schemaname = 'public' 
AND tablename IN ('user_favorites', 'search_history', 'property_views', 'conversations', 'host_metrics', 'property_metrics');
