import React, { useState, useEffect, useRef } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  ChevronLeft,
  ChevronRight,
  X,
  ZoomIn,
  ZoomOut,
  Download,
  Share2,
  Heart,
  Grid3X3,
  RotateCw,
  Maximize2,
  Play,
  Pause,
  SkipBack,
  SkipForward,
  AlertCircle,
  Loader2
} from 'lucide-react';
import {
  SlideIn,
  ScaleIn,
  HoverAnimation
} from '@/components/ui/page-transitions';
import { StayFinderLoader } from '@/components/ui/loading-spinner';

interface PropertyImage {
  id: string;
  url: string;
  alt: string;
  isPrimary?: boolean;
  caption?: string;
  photographer?: string;
  tags?: string[];
}

interface PropertyImageGalleryProps {
  images: PropertyImage[];
  propertyTitle?: string;
  showThumbnails?: boolean;
  showControls?: boolean;
  enableSlideshow?: boolean;
  enableZoom?: boolean;
  enableRotation?: boolean;
  autoPlay?: boolean;
  slideshowInterval?: number;
  className?: string;
  onImageChange?: (index: number) => void;
  onImageLoad?: (index: number) => void;
  onImageError?: (index: number, error: string) => void;
}

export const PropertyImageGallery: React.FC<PropertyImageGalleryProps> = ({
  images,
  propertyTitle = 'Property',
  showThumbnails = true,
  showControls = true,
  enableSlideshow = true,
  enableZoom = true,
  enableRotation = false,
  autoPlay = false,
  slideshowInterval = 5000,
  className,
  onImageChange,
  onImageLoad,
  onImageError
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isLiked, setIsLiked] = useState(false);
  const [isZoomed, setIsZoomed] = useState(false);
  const [zoomLevel, setZoomLevel] = useState(1);
  const [rotation, setRotation] = useState(0);
  const [isPlaying, setIsPlaying] = useState(autoPlay);
  const [loadingStates, setLoadingStates] = useState<Record<number, boolean>>({});
  const [errorStates, setErrorStates] = useState<Record<number, string>>({});
  const [imagePositions, setImagePositions] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);

  const intervalRef = useRef<NodeJS.Timeout>();
  const imageRef = useRef<HTMLImageElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Slideshow effect
  useEffect(() => {
    if (isPlaying && images.length > 1 && !isFullscreen) {
      intervalRef.current = setInterval(() => {
        setCurrentIndex((prev) => (prev === images.length - 1 ? 0 : prev + 1));
      }, slideshowInterval);
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isPlaying, images.length, isFullscreen, slideshowInterval]);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (!isFullscreen) return;

      switch (e.key) {
        case 'ArrowLeft':
          goToPrevious();
          break;
        case 'ArrowRight':
          goToNext();
          break;
        case 'Escape':
          setIsFullscreen(false);
          break;
        case ' ':
          e.preventDefault();
          toggleSlideshow();
          break;
        case '+':
        case '=':
          zoomIn();
          break;
        case '-':
          zoomOut();
          break;
        case 'r':
          if (enableRotation) rotateImage();
          break;
      }
    };

    if (isFullscreen) {
      document.addEventListener('keydown', handleKeyPress);
      return () => document.removeEventListener('keydown', handleKeyPress);
    }
  }, [isFullscreen, enableRotation]);

  // Reset zoom and rotation when changing images
  useEffect(() => {
    setZoomLevel(1);
    setRotation(0);
    setImagePositions({ x: 0, y: 0 });
    setIsZoomed(false);
    onImageChange?.(currentIndex);
  }, [currentIndex, onImageChange]);

  if (!images || images.length === 0) {
    return (
      <SlideIn direction="up" delay={100}>
        <Card className={className}>
          <CardContent className="p-8">
            <div className="text-center">
              <div className="w-24 h-24 mx-auto mb-4 bg-gray-200 rounded-lg flex items-center justify-center">
                <Grid3X3 className="h-8 w-8 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Images Available</h3>
              <p className="text-gray-600">Images for this property will appear here</p>
            </div>
          </CardContent>
        </Card>
      </SlideIn>
    );
  }

  const currentImage = images[currentIndex];

  const goToPrevious = () => {
    setCurrentIndex((prev) => (prev === 0 ? images.length - 1 : prev - 1));
  };

  const goToNext = () => {
    setCurrentIndex((prev) => (prev === images.length - 1 ? 0 : prev + 1));
  };

  const goToImage = (index: number) => {
    setCurrentIndex(index);
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
    if (!isFullscreen) {
      setIsPlaying(false); // Pause slideshow in fullscreen
    }
  };

  const toggleSlideshow = () => {
    setIsPlaying(!isPlaying);
  };

  const zoomIn = () => {
    if (!enableZoom) return;
    setZoomLevel(prev => Math.min(prev + 0.5, 3));
    setIsZoomed(true);
  };

  const zoomOut = () => {
    if (!enableZoom) return;
    setZoomLevel(prev => {
      const newLevel = Math.max(prev - 0.5, 1);
      if (newLevel === 1) {
        setIsZoomed(false);
        setImagePositions({ x: 0, y: 0 });
      }
      return newLevel;
    });
  };

  const resetZoom = () => {
    setZoomLevel(1);
    setIsZoomed(false);
    setImagePositions({ x: 0, y: 0 });
  };

  const rotateImage = () => {
    if (!enableRotation) return;
    setRotation(prev => (prev + 90) % 360);
  };

  const handleImageLoad = (index: number) => {
    setLoadingStates(prev => ({ ...prev, [index]: false }));
    onImageLoad?.(index);
  };

  const handleImageError = (index: number, error: string) => {
    setLoadingStates(prev => ({ ...prev, [index]: false }));
    setErrorStates(prev => ({ ...prev, [index]: error }));
    onImageError?.(index, error);
  };

  const handleImageLoadStart = (index: number) => {
    setLoadingStates(prev => ({ ...prev, [index]: true }));
    setErrorStates(prev => ({ ...prev, [index]: '' }));
  };

  const handleDownload = async () => {
    try {
      const response = await fetch(currentImage.url);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${propertyTitle}-image-${currentIndex + 1}.jpg`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error downloading image:', error);
    }
  };

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: propertyTitle,
          text: `Check out this property: ${propertyTitle}`,
          url: window.location.href
        });
      } catch (error) {
        console.error('Error sharing:', error);
      }
    } else {
      // Fallback: copy URL to clipboard
      navigator.clipboard.writeText(window.location.href);
      alert('Property URL copied to clipboard!');
    }
  };

  const toggleLike = () => {
    setIsLiked(!isLiked);
    // TODO: Implement actual like functionality
  };

  // Fullscreen Modal
  if (isFullscreen) {
    return (
      <div className="fixed inset-0 z-50 bg-black bg-opacity-95 flex items-center justify-center">
        <div className="relative w-full h-full flex items-center justify-center p-4" ref={containerRef}>
          {/* Top Controls Bar */}
          <div className="absolute top-4 left-4 right-4 z-10 flex items-center justify-between">
            <div className="flex items-center gap-2">
              {/* Image Info */}
              <div className="bg-black bg-opacity-50 text-white px-3 py-2 rounded-lg">
                <span className="text-sm font-medium">
                  {currentIndex + 1} of {images.length}
                </span>
                {currentImage.caption && (
                  <span className="text-xs text-gray-300 ml-2">
                    {currentImage.caption}
                  </span>
                )}
              </div>
            </div>

            <div className="flex items-center gap-2">
              {/* Slideshow Controls */}
              {enableSlideshow && images.length > 1 && (
                <>
                  <Button
                    onClick={() => setCurrentIndex(0)}
                    className="bg-black bg-opacity-50 hover:bg-opacity-70 text-white border-none"
                    size="sm"
                  >
                    <SkipBack className="h-4 w-4" />
                  </Button>
                  <Button
                    onClick={toggleSlideshow}
                    className="bg-black bg-opacity-50 hover:bg-opacity-70 text-white border-none"
                    size="sm"
                  >
                    {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                  </Button>
                  <Button
                    onClick={() => setCurrentIndex(images.length - 1)}
                    className="bg-black bg-opacity-50 hover:bg-opacity-70 text-white border-none"
                    size="sm"
                  >
                    <SkipForward className="h-4 w-4" />
                  </Button>
                </>
              )}

              {/* Close Button */}
              <Button
                onClick={toggleFullscreen}
                className="bg-black bg-opacity-50 hover:bg-opacity-70 text-white border-none"
                size="sm"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Bottom Controls Bar */}
          <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 z-10">
            <div className="flex items-center gap-2 bg-black bg-opacity-50 rounded-lg p-2">
              {enableZoom && (
                <>
                  <Button
                    onClick={zoomOut}
                    disabled={zoomLevel <= 1}
                    className="bg-transparent hover:bg-white hover:bg-opacity-20 text-white border-none disabled:opacity-50"
                    size="sm"
                  >
                    <ZoomOut className="h-4 w-4" />
                  </Button>
                  <span className="text-white text-sm px-2">
                    {Math.round(zoomLevel * 100)}%
                  </span>
                  <Button
                    onClick={zoomIn}
                    disabled={zoomLevel >= 3}
                    className="bg-transparent hover:bg-white hover:bg-opacity-20 text-white border-none disabled:opacity-50"
                    size="sm"
                  >
                    <ZoomIn className="h-4 w-4" />
                  </Button>
                </>
              )}

              {enableRotation && (
                <Button
                  onClick={rotateImage}
                  className="bg-transparent hover:bg-white hover:bg-opacity-20 text-white border-none"
                  size="sm"
                >
                  <RotateCw className="h-4 w-4" />
                </Button>
              )}

              <Button
                onClick={resetZoom}
                className="bg-transparent hover:bg-white hover:bg-opacity-20 text-white border-none"
                size="sm"
              >
                <Maximize2 className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Navigation Buttons */}
          {images.length > 1 && (
            <>
              <Button
                onClick={goToPrevious}
                className="absolute left-4 top-1/2 transform -translate-y-1/2 z-10 bg-black bg-opacity-50 hover:bg-opacity-70 text-white border-none"
                size="sm"
              >
                <ChevronLeft className="h-6 w-6" />
              </Button>
              <Button
                onClick={goToNext}
                className="absolute right-4 top-1/2 transform -translate-y-1/2 z-10 bg-black bg-opacity-50 hover:bg-opacity-70 text-white border-none"
                size="sm"
              >
                <ChevronRight className="h-6 w-6" />
              </Button>
            </>
          )}

          {/* Enhanced Image with Zoom and Rotation */}
          <div className="relative overflow-hidden w-full h-full flex items-center justify-center">
            {loadingStates[currentIndex] && (
              <div className="absolute inset-0 flex items-center justify-center z-20">
                <StayFinderLoader size="lg" text="Loading image..." />
              </div>
            )}

            {errorStates[currentIndex] ? (
              <div className="flex flex-col items-center justify-center text-white">
                <AlertCircle className="h-16 w-16 mb-4 text-red-400" />
                <p className="text-lg font-medium mb-2">Failed to load image</p>
                <p className="text-sm text-gray-300">{errorStates[currentIndex]}</p>
              </div>
            ) : (
              <img
                ref={imageRef}
                src={currentImage.url}
                alt={currentImage.alt}
                onLoad={() => handleImageLoad(currentIndex)}
                onError={() => handleImageError(currentIndex, 'Image failed to load')}
                onLoadStart={() => handleImageLoadStart(currentIndex)}
                className="max-w-full max-h-full object-contain transition-transform duration-300 cursor-move"
                style={{
                  transform: `scale(${zoomLevel}) rotate(${rotation}deg) translate(${imagePositions.x}px, ${imagePositions.y}px)`,
                  transformOrigin: 'center center'
                }}
                draggable={false}
              />
            )}
          </div>

          {/* Image Counter */}
          <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-50 text-white px-3 py-1 rounded text-sm">
            {currentIndex + 1} of {images.length}
          </div>
        </div>
      </div>
    );
  }

  return (
    <SlideIn direction="up" delay={100}>
      <div className={className}>
        {/* Main Image */}
        <HoverAnimation type="lift">
          <Card className="overflow-hidden shadow-lg border-0">
            <div className="relative">
              <div className="aspect-[4/3] relative overflow-hidden">
                {/* Loading State */}
                {loadingStates[currentIndex] && (
                  <div className="absolute inset-0 flex items-center justify-center bg-gray-100 z-10">
                    <StayFinderLoader size="md" text="Loading image..." />
                  </div>
                )}

                {/* Error State */}
                {errorStates[currentIndex] ? (
                  <div className="absolute inset-0 flex flex-col items-center justify-center bg-gray-100">
                    <AlertCircle className="h-12 w-12 text-red-400 mb-2" />
                    <p className="text-sm text-gray-600">Failed to load image</p>
                  </div>
                ) : (
                  <img
                    src={currentImage.url}
                    alt={currentImage.alt}
                    onLoad={() => handleImageLoad(currentIndex)}
                    onError={() => handleImageError(currentIndex, 'Image failed to load')}
                    onLoadStart={() => handleImageLoadStart(currentIndex)}
                    className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
                  />
                )}

                {/* Slideshow Progress Bar */}
                {isPlaying && (
                  <div className="absolute bottom-0 left-0 right-0 h-1 bg-black bg-opacity-30">
                    <div
                      className="h-full bg-sea-green-500 transition-all duration-100"
                      style={{
                        width: `${((Date.now() % slideshowInterval) / slideshowInterval) * 100}%`
                      }}
                    />
                  </div>
                )}

                {/* Image Overlay Controls */}
                {showControls && (
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300">
                    {/* Top Controls */}
                    <div className="absolute top-4 left-4 right-4 flex justify-between">
                      {/* Slideshow Controls */}
                      {enableSlideshow && images.length > 1 && (
                        <div className="flex gap-2">
                          <Button
                            onClick={toggleSlideshow}
                            size="sm"
                            variant="secondary"
                            className="bg-white/90 hover:bg-white"
                          >
                            {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                          </Button>
                        </div>
                      )}

                      {/* Action Controls */}
                        <Button
                          onClick={toggleLike}
                          size="sm"
                          variant="secondary"
                          className="bg-white/90 hover:bg-white"
                        >
                          <Heart className={`h-4 w-4 ${isLiked ? 'fill-red-500 text-red-500' : ''}`} />
                        </Button>
                        <Button
                          onClick={handleShare}
                          size="sm"
                          variant="secondary"
                          className="bg-white/90 hover:bg-white"
                        >
                          <Share2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>

                    {/* Bottom Controls */}
                    <div className="absolute bottom-4 right-4 flex gap-2">
                      <Button
                        onClick={handleDownload}
                        size="sm"
                        variant="secondary"
                        className="bg-white/90 hover:bg-white"
                      >
                        <Download className="h-4 w-4" />
                      </Button>
                      {enableZoom && (
                        <Button
                          onClick={zoomIn}
                          size="sm"
                          variant="secondary"
                          className="bg-white/90 hover:bg-white"
                        >
                          <ZoomIn className="h-4 w-4" />
                        </Button>
                      )}
                      <Button
                        onClick={toggleFullscreen}
                        size="sm"
                        variant="secondary"
                        className="bg-white/90 hover:bg-white"
                      >
                        <Maximize2 className="h-4 w-4" />
                      </Button>
                    </div>

                {/* Image Counter */}
                {images.length > 1 && (
                  <div className="absolute bottom-4 left-4 bg-black/50 text-white px-2 py-1 rounded text-sm">
                    {currentIndex + 1} / {images.length}
                  </div>
                )}
              </div>
            )}

            {/* Navigation Arrows */}
            {images.length > 1 && (
              <>
                <Button
                  onClick={goToPrevious}
                  className="absolute left-2 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white text-gray-800 border-none shadow-md"
                  size="sm"
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <Button
                  onClick={goToNext}
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white text-gray-800 border-none shadow-md"
                  size="sm"
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </>
            )}
          </div>
        </div>
      </Card>

        {/* Enhanced Thumbnail Gallery */}
        {showThumbnails && images.length > 1 && (
          <SlideIn direction="up" delay={200}>
            <div className="mt-6">
              <div className="flex items-center justify-between mb-3">
                <h4 className="text-sm font-medium text-gray-700">
                  Gallery ({images.length} photos)
                </h4>
                {enableSlideshow && (
                  <Button
                    onClick={toggleSlideshow}
                    size="xs"
                    variant="outline"
                    className="text-xs"
                  >
                    {isPlaying ? (
                      <>
                        <Pause className="h-3 w-3 mr-1" />
                        Pause
                      </>
                    ) : (
                      <>
                        <Play className="h-3 w-3 mr-1" />
                        Play
                      </>
                    )}
                  </Button>
                )}
              </div>

              <div className="flex gap-3 overflow-x-auto pb-2 scrollbar-hide">
                {images.map((image, index) => (
                  <HoverAnimation key={image.id} type="scale">
                    <button
                      onClick={() => goToImage(index)}
                      className={`relative flex-shrink-0 w-24 h-24 rounded-xl overflow-hidden border-2 transition-all duration-200 ${
                        index === currentIndex
                          ? 'border-sea-green-500 ring-2 ring-sea-green-200 shadow-lg'
                          : 'border-gray-200 hover:border-sea-green-300 hover:shadow-md'
                      }`}
                    >
                      {loadingStates[index] && (
                        <div className="absolute inset-0 flex items-center justify-center bg-gray-100 z-10">
                          <Loader2 className="h-4 w-4 animate-spin text-gray-400" />
                        </div>
                      )}

                      {errorStates[index] ? (
                        <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
                          <AlertCircle className="h-4 w-4 text-red-400" />
                        </div>
                      ) : (
                        <img
                          src={image.url}
                          alt={image.alt}
                          onLoad={() => handleImageLoad(index)}
                          onError={() => handleImageError(index, 'Thumbnail failed to load')}
                          onLoadStart={() => handleImageLoadStart(index)}
                          className="w-full h-full object-cover transition-transform duration-200 hover:scale-110"
                        />
                      )}

                      {image.isPrimary && (
                        <div className="absolute top-1 right-1 bg-yellow-500 text-white text-xs px-1 py-0.5 rounded">
                          ★
                        </div>
                      )}

                      {index === currentIndex && (
                        <div className="absolute inset-0 bg-sea-green-500 bg-opacity-20 flex items-center justify-center">
                          <div className="w-3 h-3 bg-white rounded-full shadow-md"></div>
                        </div>
                      )}
                    </button>
                  </HoverAnimation>
                ))}
              </div>
            </div>
          </SlideIn>
        )}

        {/* Enhanced Image Info */}
        {showControls && (
          <SlideIn direction="up" delay={300}>
            <div className="mt-6 space-y-3">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <h3 className="text-sm font-medium text-gray-900">
                    {currentImage.caption || currentImage.alt || `Image ${currentIndex + 1}`}
                  </h3>
                  {currentImage.photographer && (
                    <p className="text-xs text-gray-500 mt-1">
                      Photo by {currentImage.photographer}
                    </p>
                  )}
                </div>

                <div className="flex items-center gap-2">
                  {currentImage.isPrimary && (
                    <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs font-medium">
                      Primary Photo
                    </span>
                  )}
                  <span className="bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs">
                    {currentIndex + 1} of {images.length}
                  </span>
                </div>
              </div>

              {currentImage.tags && currentImage.tags.length > 0 && (
                <div className="flex flex-wrap gap-1">
                  {currentImage.tags.map((tag, index) => (
                    <span
                      key={index}
                      className="bg-sea-green-100 text-sea-green-700 px-2 py-1 rounded text-xs"
                    >
                      #{tag}
                    </span>
                  ))}
                </div>
              )}
            </div>
          </SlideIn>
        )}
        </div>
      </HoverAnimation>
    </SlideIn>
  );
};
