import { useState, useEffect, useRef, useCallback } from 'react';

interface UseLazyLoadingOptions {
  threshold?: number;
  rootMargin?: string;
  triggerOnce?: boolean;
  enabled?: boolean;
}

interface LazyLoadingState {
  isInView: boolean;
  hasBeenInView: boolean;
  isLoading: boolean;
  error: string | null;
}

// Hook for lazy loading with Intersection Observer
export const useLazyLoading = (options: UseLazyLoadingOptions = {}) => {
  const {
    threshold = 0.1,
    rootMargin = '50px',
    triggerOnce = true,
    enabled = true
  } = options;

  const [state, setState] = useState<LazyLoadingState>({
    isInView: false,
    hasBeenInView: false,
    isLoading: false,
    error: null
  });

  const elementRef = useRef<HTMLElement | null>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);

  const setRef = useCallback((element: HTMLElement | null) => {
    elementRef.current = element;
  }, []);

  useEffect(() => {
    if (!enabled || !elementRef.current) return;

    const element = elementRef.current;

    observerRef.current = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        const isInView = entry.isIntersecting;

        setState(prev => ({
          ...prev,
          isInView,
          hasBeenInView: prev.hasBeenInView || isInView
        }));

        if (isInView && triggerOnce) {
          observerRef.current?.disconnect();
        }
      },
      {
        threshold,
        rootMargin
      }
    );

    observerRef.current.observe(element);

    return () => {
      observerRef.current?.disconnect();
    };
  }, [enabled, threshold, rootMargin, triggerOnce]);

  const setLoading = useCallback((loading: boolean) => {
    setState(prev => ({ ...prev, isLoading: loading }));
  }, []);

  const setError = useCallback((error: string | null) => {
    setState(prev => ({ ...prev, error }));
  }, []);

  return {
    ref: setRef,
    isInView: state.isInView,
    hasBeenInView: state.hasBeenInView,
    isLoading: state.isLoading,
    error: state.error,
    setLoading,
    setError
  };
};

// Hook for lazy loading images
export const useLazyImage = (src: string, options: UseLazyLoadingOptions = {}) => {
  const [imageState, setImageState] = useState({
    loaded: false,
    error: false,
    src: ''
  });

  const { ref, isInView, hasBeenInView } = useLazyLoading(options);

  useEffect(() => {
    if (!isInView && !hasBeenInView) return;

    const img = new Image();
    
    img.onload = () => {
      setImageState({
        loaded: true,
        error: false,
        src
      });
    };

    img.onerror = () => {
      setImageState({
        loaded: false,
        error: true,
        src: ''
      });
    };

    img.src = src;
  }, [src, isInView, hasBeenInView]);

  return {
    ref,
    ...imageState,
    isInView,
    hasBeenInView
  };
};

// Hook for lazy loading with retry logic
export const useLazyLoadingWithRetry = (
  loadFunction: () => Promise<any>,
  options: UseLazyLoadingOptions & { maxRetries?: number; retryDelay?: number } = {}
) => {
  const { maxRetries = 3, retryDelay = 1000, ...lazyOptions } = options;
  
  const [state, setState] = useState({
    data: null,
    loading: false,
    error: null,
    retryCount: 0
  });

  const { ref, isInView, hasBeenInView } = useLazyLoading(lazyOptions);

  const load = useCallback(async (retryCount = 0) => {
    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const data = await loadFunction();
      setState(prev => ({ ...prev, data, loading: false, retryCount }));
    } catch (error) {
      if (retryCount < maxRetries) {
        setTimeout(() => {
          load(retryCount + 1);
        }, retryDelay * Math.pow(2, retryCount)); // Exponential backoff
      } else {
        setState(prev => ({ 
          ...prev, 
          loading: false, 
          error: error instanceof Error ? error.message : 'Failed to load',
          retryCount 
        }));
      }
    }
  }, [loadFunction, maxRetries, retryDelay]);

  useEffect(() => {
    if (isInView || hasBeenInView) {
      load();
    }
  }, [isInView, hasBeenInView, load]);

  const retry = useCallback(() => {
    load(0);
  }, [load]);

  return {
    ref,
    ...state,
    retry,
    isInView,
    hasBeenInView
  };
};

// Hook for lazy loading lists/grids
export const useLazyList = <T>(
  items: T[],
  batchSize: number = 10,
  options: UseLazyLoadingOptions = {}
) => {
  const [visibleCount, setVisibleCount] = useState(batchSize);
  const { ref, isInView } = useLazyLoading(options);

  useEffect(() => {
    if (isInView && visibleCount < items.length) {
      setVisibleCount(prev => Math.min(prev + batchSize, items.length));
    }
  }, [isInView, visibleCount, items.length, batchSize]);

  const visibleItems = items.slice(0, visibleCount);
  const hasMore = visibleCount < items.length;

  return {
    ref,
    visibleItems,
    hasMore,
    loadMore: () => setVisibleCount(prev => Math.min(prev + batchSize, items.length)),
    reset: () => setVisibleCount(batchSize)
  };
};

// Hook for preloading resources
export const usePreloader = () => {
  const [preloadedResources, setPreloadedResources] = useState<Set<string>>(new Set());

  const preloadImage = useCallback((src: string): Promise<void> => {
    if (preloadedResources.has(src)) {
      return Promise.resolve();
    }

    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        setPreloadedResources(prev => new Set(prev).add(src));
        resolve();
      };
      img.onerror = reject;
      img.src = src;
    });
  }, [preloadedResources]);

  const preloadImages = useCallback((sources: string[]): Promise<void[]> => {
    return Promise.all(sources.map(preloadImage));
  }, [preloadImage]);

  const preloadResource = useCallback((url: string, type: 'script' | 'style' | 'font' = 'script'): Promise<void> => {
    if (preloadedResources.has(url)) {
      return Promise.resolve();
    }

    return new Promise((resolve, reject) => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.href = url;
      
      switch (type) {
        case 'script':
          link.as = 'script';
          break;
        case 'style':
          link.as = 'style';
          break;
        case 'font':
          link.as = 'font';
          link.crossOrigin = 'anonymous';
          break;
      }

      link.onload = () => {
        setPreloadedResources(prev => new Set(prev).add(url));
        resolve();
      };
      link.onerror = reject;

      document.head.appendChild(link);
    });
  }, [preloadedResources]);

  const isPreloaded = useCallback((resource: string): boolean => {
    return preloadedResources.has(resource);
  }, [preloadedResources]);

  return {
    preloadImage,
    preloadImages,
    preloadResource,
    isPreloaded,
    preloadedCount: preloadedResources.size
  };
};

// Hook for virtual scrolling (for large lists)
export const useVirtualScrolling = <T>(
  items: T[],
  itemHeight: number,
  containerHeight: number,
  overscan: number = 5
) => {
  const [scrollTop, setScrollTop] = useState(0);

  const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
  const endIndex = Math.min(
    items.length - 1,
    Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
  );

  const visibleItems = items.slice(startIndex, endIndex + 1).map((item, index) => ({
    item,
    index: startIndex + index,
    style: {
      position: 'absolute' as const,
      top: (startIndex + index) * itemHeight,
      height: itemHeight,
      width: '100%'
    }
  }));

  const totalHeight = items.length * itemHeight;

  const handleScroll = useCallback((event: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(event.currentTarget.scrollTop);
  }, []);

  return {
    visibleItems,
    totalHeight,
    handleScroll,
    startIndex,
    endIndex
  };
};

// Hook for measuring element performance
export const usePerformanceMeasure = (name: string) => {
  const startTime = useRef<number>(0);

  const start = useCallback(() => {
    startTime.current = performance.now();
    performance.mark(`${name}-start`);
  }, [name]);

  const end = useCallback(() => {
    const endTime = performance.now();
    performance.mark(`${name}-end`);
    performance.measure(name, `${name}-start`, `${name}-end`);
    
    const duration = endTime - startTime.current;
    
    if (process.env.NODE_ENV === 'development') {
      console.log(`[Performance] ${name}: ${duration.toFixed(2)}ms`);
    }
    
    return duration;
  }, [name]);

  return { start, end };
};
