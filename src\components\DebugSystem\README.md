# StayFinder Debug System

A comprehensive debugging system for the StayFinder application that provides real-time error capture, display, and management capabilities.

## Features

### Core Functionality
- **Error Detection & Display**: Intercepts and captures all JavaScript console errors, warnings, and logs
- **Individual Error Controls**: Copy, dismiss, and manage individual error entries
- **Bulk Actions**: Copy all, clear all, and export functionality
- **Admin Control System**: Role-based access control and admin-only visibility
- **Real-time Capture**: Automatic error capturing with configurable options

### Technical Capabilities
- React Error Boundaries for component error catching
- Console method overriding for comprehensive logging
- Global error and unhandled promise rejection handling
- Draggable and resizable debug panel
- Keyboard shortcuts for quick access
- Local storage persistence for settings and panel state

## Components

### 1. DebugSystemProvider
Main provider component that wraps the application and provides debug system context.

```tsx
import { DebugSystemProvider } from '@/components/DebugSystem/DebugSystemProvider';

<DebugSystemProvider userRole="admin" autoStart={true}>
  <App />
</DebugSystemProvider>
```

### 2. DebugPanel
The main debug panel UI component with error display and management.

### 3. ErrorCapture
React Error Boundary component for catching component errors.

### 4. DebugToggleButton
Floating action button to toggle the debug panel.

```tsx
import { DebugToggleButton } from '@/components/DebugSystem/DebugSystemProvider';

<DebugToggleButton position="bottom-right" />
```

### 5. AdminDebugControls
Admin interface for configuring debug system settings.

## Hooks

### useErrorCapture
Core hook for error capture functionality.

```tsx
import { useErrorCapture } from '@/hooks/useErrorCapture';

const {
  errors,
  isCapturing,
  clearErrors,
  dismissError,
  copyError,
  exportErrors
} = useErrorCapture();
```

### useDebugSystem
Hook for accessing debug system context.

```tsx
import { useDebugSystem } from '@/components/DebugSystem/DebugSystemProvider';

const {
  isEnabled,
  isVisible,
  togglePanel,
  config,
  updateConfig
} = useDebugSystem();
```

## Configuration

### Debug System Config
```typescript
interface DebugSystemConfig {
  enabled: boolean;           // Enable/disable debug system
  adminOnly: boolean;         // Restrict to admin users only
  maxErrors: number;          // Maximum number of errors to store
  autoCapture: boolean;       // Automatically start capturing on load
  showInProduction: boolean;  // Show in production environment
  keyboardShortcuts: boolean; // Enable keyboard shortcuts
}
```

### Default Configuration
```typescript
const DEFAULT_CONFIG = {
  enabled: false,
  adminOnly: true,
  maxErrors: 100,
  autoCapture: true,
  showInProduction: false,
  keyboardShortcuts: true
};
```

## Usage

### Basic Setup
1. Wrap your app with `DebugSystemProvider`
2. Add `DebugToggleButton` for easy access
3. Configure admin controls in your admin dashboard

### Keyboard Shortcuts
- `Ctrl+Shift+D`: Toggle debug panel
- `Ctrl+Shift+C`: Clear all errors
- `Escape`: Close debug panel (when open)

### Error Types
- **Error**: JavaScript errors and exceptions
- **Warning**: Console warnings and validation issues
- **Info**: Informational messages and logs
- **Debug**: Debug-level logging information

### Manual Error Reporting
```tsx
import { useErrorReporting } from '@/components/DebugSystem/ErrorCapture';

const { reportError, reportWarning, reportInfo } = useErrorReporting();

// Report custom errors
reportError(new Error('Custom error'), 'user-action');
reportWarning('Validation warning', 'form-validation');
reportInfo('User action completed', 'user-tracking');
```

## Admin Controls

### Accessing Admin Controls
Navigate to Admin Dashboard → Debug System tab to access configuration options:

- Enable/disable debug system
- Configure admin-only access
- Set maximum error count
- Toggle production visibility
- Enable/disable keyboard shortcuts

### Security Considerations
- Debug system is hidden from non-admin users by default
- Production visibility is disabled by default
- All settings are stored locally and don't affect other users
- Error data is not transmitted to external services

## Error Information

Each captured error includes:
- **Message**: Error message text
- **Type**: Error severity level
- **Timestamp**: When the error occurred
- **Source**: Where the error originated (console, global, react, etc.)
- **Stack Trace**: Full JavaScript stack trace
- **File Location**: Filename, line, and column numbers
- **Component Stack**: React component hierarchy (for React errors)
- **User Agent**: Browser information
- **URL**: Page where error occurred

## Export Functionality

### JSON Export Format
```json
{
  "timestamp": "2024-01-15T10:30:00.000Z",
  "userAgent": "Mozilla/5.0...",
  "url": "https://stayfinder.com/properties",
  "totalErrors": 5,
  "errors": [
    {
      "id": "error_1705312200000_abc123",
      "type": "error",
      "message": "Cannot read property 'name' of undefined",
      "timestamp": "2024-01-15T10:30:00.000Z",
      "source": "react",
      "filename": "PropertyCard.tsx",
      "line": 45,
      "column": 12,
      "stack": "Error: Cannot read property...",
      "componentStack": "    in PropertyCard..."
    }
  ]
}
```

## Performance Considerations

- Error capture has minimal performance impact
- Maximum error count prevents memory issues
- Panel rendering is optimized with virtual scrolling
- Local storage is used for persistence
- Console overrides are lightweight

## Browser Compatibility

- Modern browsers with ES6+ support
- Chrome, Firefox, Safari, Edge
- Mobile browsers supported
- Graceful degradation for older browsers

## Development vs Production

### Development Mode
- Debug system is more permissive
- Additional development warnings shown
- More detailed error information
- Auto-enabled for easier debugging

### Production Mode
- Disabled by default for security
- Can be enabled via admin controls
- Reduced error detail for performance
- Admin-only access enforced

## Troubleshooting

### Debug System Not Visible
1. Check if user has admin role
2. Verify debug system is enabled in admin controls
3. Ensure production visibility is enabled if in production
4. Check browser console for initialization errors

### Errors Not Being Captured
1. Verify auto-capture is enabled
2. Check if error capture is active
3. Ensure error types are not filtered out
4. Verify console override is working

### Performance Issues
1. Reduce maximum error count
2. Clear errors regularly
3. Disable debug system when not needed
4. Check for memory leaks in error objects

## Contributing

When adding new features to the debug system:

1. Follow existing TypeScript patterns
2. Add proper error handling
3. Update documentation
4. Test in both development and production
5. Consider performance implications
6. Maintain backward compatibility

## Security Notes

- Never log sensitive user data
- Sanitize error messages in production
- Respect user privacy settings
- Don't expose internal system details
- Use secure local storage practices
