import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Header } from '../components/Header';
import { PropertyMap } from '../components/PropertyMap';
import { NeighborhoodInfo } from '../components/NeighborhoodInfo';
import { SmartRecommendations } from '../components/SmartRecommendations';
import { BookingCalendar } from '../components/BookingCalendar';
import { PropertyReviews } from '../components/PropertyReviews';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { usePropertyTracking } from '@/hooks/usePropertyTracking';
import { useFavorites } from '@/hooks/useFavorites';
import { 
  MapPin, 
  Star, 
  Users, 
  Bed, 
  Bath, 
  Wifi, 
  Car, 
  Waves,
  ArrowLeft,
  Share,
  Heart,
  Calendar
} from 'lucide-react';

interface Property {
  id: string;
  title: string;
  location: string;
  price: number;
  images: string[];
  description: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
  averageRating?: number;
  reviewCount?: number;
  propertyType?: string;
  maxGuests?: number;
  bedrooms?: number;
  bathrooms?: number;
  amenities?: string[];
  available?: boolean;
  owner?: {
    firstName: string;
    lastName: string;
  };
}

export const PropertyDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [property, setProperty] = useState<Property | null>(null);
  const [loading, setLoading] = useState(true);
  const [showBooking, setShowBooking] = useState(false);

  // Analytics and favorites hooks
  const { useAutoTrackPropertyView } = usePropertyTracking();
  const { isFavorited, toggleFavorite, loading: favoritesLoading } = useFavorites();

  // Auto-track property view when component loads
  useAutoTrackPropertyView(id || '', !!id && !!property);
  const [activeTab, setActiveTab] = useState<'overview' | 'location' | 'reviews'>('overview');

  useEffect(() => {
    // Mock property data - in real app, fetch from API
    const mockProperty: Property = {
      id: id || '1',
      title: 'Luxury Beachfront Villa in Margate',
      location: 'Margate',
      price: 2500,
      images: [
        'https://images.unsplash.com/photo-1721322800607-8c38375eef04?w=800&h=600&fit=crop',
        'https://images.unsplash.com/photo-1566073771259-6a8506099945?w=800&h=600&fit=crop',
        'https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=800&h=600&fit=crop'
      ],
      description: 'Experience luxury living in this stunning beachfront villa located in the heart of Margate. This property offers breathtaking ocean views, modern amenities, and direct beach access. Perfect for families or groups looking for an unforgettable coastal getaway.',
      coordinates: {
        latitude: -30.8647,
        longitude: 30.3707
      },
      averageRating: 4.8,
      reviewCount: 127,
      propertyType: 'Villa',
      maxGuests: 8,
      bedrooms: 4,
      bathrooms: 3,
      amenities: ['wifi', 'parking', 'pool', 'kitchen', 'air_conditioning', 'tv'],
      available: true,
      owner: {
        firstName: 'Sarah',
        lastName: 'Johnson'
      }
    };

    setTimeout(() => {
      setProperty(mockProperty);
      setLoading(false);
    }, 1000);
  }, [id]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="container mx-auto px-4 py-6">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-gray-200 rounded w-1/3"></div>
            <div className="h-96 bg-gray-200 rounded"></div>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className="lg:col-span-2 space-y-4">
                <div className="h-6 bg-gray-200 rounded"></div>
                <div className="h-4 bg-gray-200 rounded w-2/3"></div>
                <div className="h-20 bg-gray-200 rounded"></div>
              </div>
              <div className="h-64 bg-gray-200 rounded"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!property) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="container mx-auto px-4 py-6">
          <div className="text-center py-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Property not found</h2>
            <Button onClick={() => navigate('/search')}>
              Back to Search
            </Button>
          </div>
        </div>
      </div>
    );
  }

  const tabs = [
    { id: 'overview', label: 'Overview' },
    { id: 'location', label: 'Location & Neighborhood' },
    { id: 'reviews', label: 'Reviews' }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="container mx-auto px-4 py-6">
        {/* Back Button */}
        <Button
          variant="ghost"
          onClick={() => navigate(-1)}
          className="mb-4 flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Back
        </Button>

        {/* Property Header */}
        <div className="mb-6">
          <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                {property.title}
              </h1>
              <div className="flex items-center gap-4 text-gray-600">
                <div className="flex items-center gap-1">
                  <MapPin className="h-4 w-4" />
                  {property.location}
                </div>
                {property.averageRating && (
                  <div className="flex items-center gap-1">
                    <Star className="h-4 w-4 text-yellow-400 fill-current" />
                    <span>{property.averageRating}</span>
                    <span>({property.reviewCount} reviews)</span>
                  </div>
                )}
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm">
                <Share className="h-4 w-4 mr-1" />
                Share
              </Button>
              <Button variant="outline" size="sm">
                <Heart className="h-4 w-4 mr-1" />
                Save
              </Button>
            </div>
          </div>
        </div>

        {/* Property Images */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2 mb-6 h-96">
          <div className="lg:col-span-2 lg:row-span-2">
            <img
              src={property.images[0]}
              alt={property.title}
              className="w-full h-full object-cover rounded-lg"
            />
          </div>
          {property.images.slice(1, 5).map((image, index) => (
            <div key={index} className="h-48 lg:h-full">
              <img
                src={image}
                alt={`${property.title} ${index + 2}`}
                className="w-full h-full object-cover rounded-lg"
              />
            </div>
          ))}
        </div>

        {/* Content Tabs */}
        <div className="mb-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-sea-green-500 text-sea-green-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2">
            {activeTab === 'overview' && (
              <div className="space-y-6">
                {/* Property Details */}
                <Card>
                  <CardHeader>
                    <CardTitle>Property Details</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div className="flex items-center gap-2">
                        <Users className="h-4 w-4 text-gray-500" />
                        <span>{property.maxGuests} guests</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Bed className="h-4 w-4 text-gray-500" />
                        <span>{property.bedrooms} bedrooms</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Bath className="h-4 w-4 text-gray-500" />
                        <span>{property.bathrooms} bathrooms</span>
                      </div>
                      <div>
                        <Badge variant="outline">{property.propertyType}</Badge>
                      </div>
                    </div>
                    <p className="text-gray-700">{property.description}</p>
                  </CardContent>
                </Card>

                {/* Amenities */}
                <Card>
                  <CardHeader>
                    <CardTitle>Amenities</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                      {property.amenities?.map((amenity) => (
                        <div key={amenity} className="flex items-center gap-2">
                          <Wifi className="h-4 w-4 text-gray-500" />
                          <span className="capitalize">{amenity.replace('_', ' ')}</span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}

            {activeTab === 'location' && (
              <div className="space-y-6">
                <PropertyMap
                  properties={[property]}
                  selectedProperty={property}
                  height="400px"
                  showControls={true}
                />
                <NeighborhoodInfo location={property.location} />
              </div>
            )}

            {activeTab === 'reviews' && (
              <PropertyReviews
                propertyId={property.id}
                showWriteReview={true}
                onWriteReview={() => {
                  console.log('Write review clicked');
                  // TODO: Implement write review modal
                }}
              />
            )}
          </div>

          {/* Booking Sidebar */}
          <div className="lg:col-span-1">
            <Card className="sticky top-6">
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>R{property.price.toLocaleString()}</span>
                  <span className="text-sm font-normal text-gray-600">/ night</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <Button
                  onClick={() => setShowBooking(!showBooking)}
                  className="w-full bg-sea-green-500 hover:bg-sea-green-600"
                >
                  <Calendar className="h-4 w-4 mr-2" />
                  {showBooking ? 'Hide Calendar' : 'Check Availability'}
                </Button>

                {showBooking && (
                  <BookingCalendar
                    propertyId={property.id}
                    propertyTitle={property.title}
                    propertyPrice={property.price}
                    maxGuests={property.maxGuests}
                    onBookingComplete={() => {
                      console.log('Booking completed');
                    }}
                    onClose={() => setShowBooking(false)}
                  />
                )}

                <div className="text-center text-sm text-gray-600">
                  <p>Free cancellation for 48 hours</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Similar Properties */}
        <div className="mt-12">
          <SmartRecommendations
            type="similar"
            propertyId={property.id}
            title="Similar Properties"
            subtitle="Other properties you might like"
            limit={4}
            onPropertyClick={(property) => {
              navigate(`/property/${property.id}`);
            }}
          />
        </div>
      </div>
    </div>
  );
};
