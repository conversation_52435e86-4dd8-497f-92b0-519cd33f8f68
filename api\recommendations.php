<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: http://localhost:8081');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Credentials: true');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once 'config.php';
require_once 'auth.php';

function getSimilarProperties($propertyId, $limit = 4) {
    global $pdo;
    
    try {
        // Get the current property details
        $stmt = $pdo->prepare("
            SELECT property_type, location, price, max_guests, bedrooms, bathrooms 
            FROM properties 
            WHERE id = ? AND status = 'active'
        ");
        $stmt->execute([$propertyId]);
        $currentProperty = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$currentProperty) {
            return [];
        }
        
        // Find similar properties based on multiple criteria
        $stmt = $pdo->prepare("
            SELECT p.*, 
                   u.first_name as owner_first_name, 
                   u.last_name as owner_last_name,
                   COALESCE(AVG(r.rating), 0) as average_rating,
                   COUNT(r.id) as review_count,
                   (
                       CASE WHEN p.property_type = ? THEN 3 ELSE 0 END +
                       CASE WHEN p.location = ? THEN 2 ELSE 0 END +
                       CASE WHEN ABS(p.price - ?) <= 500 THEN 2 ELSE 0 END +
                       CASE WHEN p.max_guests = ? THEN 1 ELSE 0 END +
                       CASE WHEN p.bedrooms = ? THEN 1 ELSE 0 END
                   ) as similarity_score
            FROM properties p
            LEFT JOIN users u ON p.owner_id = u.id
            LEFT JOIN reviews r ON p.id = r.property_id
            WHERE p.id != ? AND p.status = 'active'
            GROUP BY p.id
            HAVING similarity_score > 0
            ORDER BY similarity_score DESC, average_rating DESC
            LIMIT ?
        ");
        
        $stmt->execute([
            $currentProperty['property_type'],
            $currentProperty['location'],
            $currentProperty['price'],
            $currentProperty['max_guests'],
            $currentProperty['bedrooms'],
            $propertyId,
            $limit
        ]);
        
        $properties = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Format the response
        foreach ($properties as &$property) {
            $property['images'] = json_decode($property['images'] ?? '[]', true);
            $property['amenities'] = json_decode($property['amenities'] ?? '[]', true);
            $property['average_rating'] = (float)$property['average_rating'];
            $property['review_count'] = (int)$property['review_count'];
            $property['similarity_score'] = (int)$property['similarity_score'];
            
            // Remove sensitive information
            unset($property['created_at'], $property['updated_at']);
        }
        
        return $properties;
        
    } catch (Exception $e) {
        error_log("Error getting similar properties: " . $e->getMessage());
        return [];
    }
}

function getRecentlyViewedProperties($userId, $limit = 6) {
    global $pdo;
    
    try {
        // Get recently viewed properties from user activity log
        $stmt = $pdo->prepare("
            SELECT DISTINCT p.*, 
                   u.first_name as owner_first_name, 
                   u.last_name as owner_last_name,
                   COALESCE(AVG(r.rating), 0) as average_rating,
                   COUNT(r.id) as review_count,
                   uv.viewed_at
            FROM user_property_views uv
            JOIN properties p ON uv.property_id = p.id
            LEFT JOIN users u ON p.owner_id = u.id
            LEFT JOIN reviews r ON p.id = r.property_id
            WHERE uv.user_id = ? AND p.status = 'active'
            GROUP BY p.id
            ORDER BY uv.viewed_at DESC
            LIMIT ?
        ");
        
        $stmt->execute([$userId, $limit]);
        $properties = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Format the response
        foreach ($properties as &$property) {
            $property['images'] = json_decode($property['images'] ?? '[]', true);
            $property['amenities'] = json_decode($property['amenities'] ?? '[]', true);
            $property['average_rating'] = (float)$property['average_rating'];
            $property['review_count'] = (int)$property['review_count'];
            
            // Remove sensitive information
            unset($property['created_at'], $property['updated_at']);
        }
        
        return $properties;
        
    } catch (Exception $e) {
        error_log("Error getting recently viewed properties: " . $e->getMessage());
        return [];
    }
}

function getPersonalizedRecommendations($userId, $limit = 8) {
    global $pdo;
    
    try {
        // Get user preferences based on their booking and viewing history
        $stmt = $pdo->prepare("
            SELECT 
                AVG(p.price) as avg_price_preference,
                MODE() WITHIN GROUP (ORDER BY p.property_type) as preferred_type,
                MODE() WITHIN GROUP (ORDER BY p.location) as preferred_location,
                AVG(p.max_guests) as avg_guests_preference
            FROM (
                SELECT DISTINCT property_id FROM bookings WHERE user_id = ?
                UNION
                SELECT DISTINCT property_id FROM user_property_views WHERE user_id = ?
            ) user_activity
            JOIN properties p ON user_activity.property_id = p.id
        ");
        
        $stmt->execute([$userId, $userId]);
        $preferences = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$preferences || !$preferences['avg_price_preference']) {
            // If no preferences, return trending properties
            return getTrendingProperties($limit);
        }
        
        // Get recommendations based on preferences
        $stmt = $pdo->prepare("
            SELECT p.*, 
                   u.first_name as owner_first_name, 
                   u.last_name as owner_last_name,
                   COALESCE(AVG(r.rating), 0) as average_rating,
                   COUNT(r.id) as review_count,
                   (
                       CASE WHEN p.property_type = ? THEN 3 ELSE 0 END +
                       CASE WHEN p.location = ? THEN 2 ELSE 0 END +
                       CASE WHEN ABS(p.price - ?) <= 1000 THEN 2 ELSE 0 END +
                       CASE WHEN ABS(p.max_guests - ?) <= 2 THEN 1 ELSE 0 END
                   ) as recommendation_score
            FROM properties p
            LEFT JOIN users u ON p.owner_id = u.id
            LEFT JOIN reviews r ON p.id = r.property_id
            WHERE p.status = 'active'
            AND p.id NOT IN (
                SELECT DISTINCT property_id FROM bookings WHERE user_id = ?
                UNION
                SELECT DISTINCT property_id FROM user_property_views WHERE user_id = ? AND viewed_at > DATE_SUB(NOW(), INTERVAL 7 DAY)
            )
            GROUP BY p.id
            ORDER BY recommendation_score DESC, average_rating DESC, RAND()
            LIMIT ?
        ");
        
        $stmt->execute([
            $preferences['preferred_type'],
            $preferences['preferred_location'],
            $preferences['avg_price_preference'],
            $preferences['avg_guests_preference'],
            $userId,
            $userId,
            $limit
        ]);
        
        $properties = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Format the response
        foreach ($properties as &$property) {
            $property['images'] = json_decode($property['images'] ?? '[]', true);
            $property['amenities'] = json_decode($property['amenities'] ?? '[]', true);
            $property['average_rating'] = (float)$property['average_rating'];
            $property['review_count'] = (int)$property['review_count'];
            $property['recommendation_score'] = (int)$property['recommendation_score'];
            
            // Remove sensitive information
            unset($property['created_at'], $property['updated_at']);
        }
        
        return $properties;
        
    } catch (Exception $e) {
        error_log("Error getting personalized recommendations: " . $e->getMessage());
        return getTrendingProperties($limit);
    }
}

function getTrendingProperties($limit = 6) {
    global $pdo;
    
    try {
        // Get trending properties based on recent bookings and views
        $stmt = $pdo->prepare("
            SELECT p.*, 
                   u.first_name as owner_first_name, 
                   u.last_name as owner_last_name,
                   COALESCE(AVG(r.rating), 0) as average_rating,
                   COUNT(DISTINCT r.id) as review_count,
                   COUNT(DISTINCT b.id) as recent_bookings,
                   COUNT(DISTINCT uv.id) as recent_views,
                   (COUNT(DISTINCT b.id) * 3 + COUNT(DISTINCT uv.id)) as trending_score
            FROM properties p
            LEFT JOIN users u ON p.owner_id = u.id
            LEFT JOIN reviews r ON p.id = r.property_id
            LEFT JOIN bookings b ON p.id = b.property_id AND b.created_at > DATE_SUB(NOW(), INTERVAL 30 DAY)
            LEFT JOIN user_property_views uv ON p.id = uv.property_id AND uv.viewed_at > DATE_SUB(NOW(), INTERVAL 7 DAY)
            WHERE p.status = 'active'
            GROUP BY p.id
            HAVING trending_score > 0
            ORDER BY trending_score DESC, average_rating DESC
            LIMIT ?
        ");
        
        $stmt->execute([$limit]);
        $properties = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Format the response
        foreach ($properties as &$property) {
            $property['images'] = json_decode($property['images'] ?? '[]', true);
            $property['amenities'] = json_decode($property['amenities'] ?? '[]', true);
            $property['average_rating'] = (float)$property['average_rating'];
            $property['review_count'] = (int)$property['review_count'];
            $property['trending_score'] = (int)$property['trending_score'];
            
            // Remove sensitive information
            unset($property['created_at'], $property['updated_at']);
        }
        
        return $properties;
        
    } catch (Exception $e) {
        error_log("Error getting trending properties: " . $e->getMessage());
        return [];
    }
}

function logPropertyView($userId, $propertyId) {
    global $pdo;
    
    try {
        // Insert or update property view
        $stmt = $pdo->prepare("
            INSERT INTO user_property_views (user_id, property_id, viewed_at) 
            VALUES (?, ?, NOW())
            ON DUPLICATE KEY UPDATE viewed_at = NOW()
        ");
        
        $stmt->execute([$userId, $propertyId]);
        return true;
        
    } catch (Exception $e) {
        error_log("Error logging property view: " . $e->getMessage());
        return false;
    }
}

// Handle the request
$method = $_SERVER['REQUEST_METHOD'];
$path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
$pathParts = explode('/', trim($path, '/'));

try {
    switch ($method) {
        case 'GET':
            if (isset($_GET['type'])) {
                $type = $_GET['type'];
                $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 6;
                
                switch ($type) {
                    case 'similar':
                        $propertyId = $_GET['property_id'] ?? null;
                        if (!$propertyId) {
                            http_response_code(400);
                            echo json_encode(['error' => 'Property ID is required for similar properties']);
                            exit;
                        }
                        
                        $properties = getSimilarProperties($propertyId, $limit);
                        echo json_encode([
                            'success' => true,
                            'data' => $properties,
                            'type' => 'similar',
                            'count' => count($properties)
                        ]);
                        break;
                        
                    case 'recently_viewed':
                        $user = getCurrentUser();
                        if (!$user) {
                            http_response_code(401);
                            echo json_encode(['error' => 'Authentication required']);
                            exit;
                        }
                        
                        $properties = getRecentlyViewedProperties($user['id'], $limit);
                        echo json_encode([
                            'success' => true,
                            'data' => $properties,
                            'type' => 'recently_viewed',
                            'count' => count($properties)
                        ]);
                        break;
                        
                    case 'personalized':
                        $user = getCurrentUser();
                        if (!$user) {
                            http_response_code(401);
                            echo json_encode(['error' => 'Authentication required']);
                            exit;
                        }
                        
                        $properties = getPersonalizedRecommendations($user['id'], $limit);
                        echo json_encode([
                            'success' => true,
                            'data' => $properties,
                            'type' => 'personalized',
                            'count' => count($properties)
                        ]);
                        break;
                        
                    case 'trending':
                        $properties = getTrendingProperties($limit);
                        echo json_encode([
                            'success' => true,
                            'data' => $properties,
                            'type' => 'trending',
                            'count' => count($properties)
                        ]);
                        break;
                        
                    default:
                        http_response_code(400);
                        echo json_encode(['error' => 'Invalid recommendation type']);
                        break;
                }
            } else {
                http_response_code(400);
                echo json_encode(['error' => 'Recommendation type is required']);
            }
            break;
            
        case 'POST':
            // Log property view
            $input = json_decode(file_get_contents('php://input'), true);
            $user = getCurrentUser();
            
            if (!$user) {
                http_response_code(401);
                echo json_encode(['error' => 'Authentication required']);
                exit;
            }
            
            if (!isset($input['property_id'])) {
                http_response_code(400);
                echo json_encode(['error' => 'Property ID is required']);
                exit;
            }
            
            $success = logPropertyView($user['id'], $input['property_id']);
            
            if ($success) {
                echo json_encode(['success' => true, 'message' => 'Property view logged']);
            } else {
                http_response_code(500);
                echo json_encode(['error' => 'Failed to log property view']);
            }
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            break;
    }
    
} catch (Exception $e) {
    error_log("Recommendations API error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error']);
}
?>
