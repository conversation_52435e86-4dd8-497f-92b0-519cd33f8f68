# StayFinder Frontend Environment Configuration (Vite)
# Copy this file to .env.local and fill in your actual values
# Note: Only variables prefixed with VITE_ are exposed to the frontend

# =============================================================================
# SUPABASE FRONTEND CONFIGURATION
# =============================================================================

# Supabase Public Configuration (Safe for frontend)
VITE_SUPABASE_URL=https://your-project-ref.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key-here

# Supabase Feature Flags
VITE_ENABLE_SUPABASE_AUTH=true
VITE_ENABLE_SUPABASE_REALTIME=true
VITE_ENABLE_SUPABASE_STORAGE=true

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================

# Environment
VITE_NODE_ENV=development
VITE_APP_NAME=StayFinder
VITE_APP_VERSION=1.0.0
VITE_APP_DESCRIPTION=Find your perfect stay on the KZN South Coast

# API Configuration
VITE_API_BASE_URL=http://localhost:3001/api
VITE_BACKEND_URL=http://localhost:3001

# =============================================================================
# AUTHENTICATION CONFIGURATION
# =============================================================================

# Auth Redirect URLs
VITE_AUTH_REDIRECT_URL=http://localhost:5173/auth/callback
VITE_AUTH_ERROR_REDIRECT_URL=http://localhost:5173/auth/error
VITE_AUTH_SUCCESS_REDIRECT_URL=http://localhost:5173/dashboard

# Social Authentication (if enabled)
VITE_ENABLE_GOOGLE_AUTH=false
VITE_ENABLE_FACEBOOK_AUTH=false
VITE_ENABLE_GITHUB_AUTH=false

# Session Configuration
VITE_SESSION_TIMEOUT=7200000  # 2 hours in milliseconds
VITE_AUTO_REFRESH_TOKEN=true

# =============================================================================
# THIRD-PARTY INTEGRATIONS
# =============================================================================

# Maps Integration
VITE_GOOGLE_MAPS_API_KEY=your-google-maps-api-key
VITE_MAPBOX_ACCESS_TOKEN=your-mapbox-access-token
VITE_DEFAULT_MAP_CENTER_LAT=-30.3753
VITE_DEFAULT_MAP_CENTER_LNG=30.6653  # KZN South Coast coordinates

# Payment Processing (Stripe Public Key)
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_your-stripe-publishable-key

# Analytics
VITE_GA_MEASUREMENT_ID=G-XXXXXXXXXX
VITE_ENABLE_ANALYTICS=true

# Error Tracking (Sentry Public DSN)
VITE_SENTRY_DSN=https://<EMAIL>/project-id
VITE_SENTRY_ENVIRONMENT=development

# =============================================================================
# FEATURE FLAGS
# =============================================================================

# Core Features
VITE_ENABLE_REAL_TIME_MESSAGING=true
VITE_ENABLE_PROPERTY_RECOMMENDATIONS=true
VITE_ENABLE_ADVANCED_SEARCH=true
VITE_ENABLE_PAYMENT_PROCESSING=true
VITE_ENABLE_REVIEWS_SYSTEM=true
VITE_ENABLE_BOOKING_SYSTEM=true

# UI Features
VITE_ENABLE_DARK_MODE=true
VITE_ENABLE_ANIMATIONS=true
VITE_ENABLE_LAZY_LOADING=true
VITE_ENABLE_IMAGE_OPTIMIZATION=true
VITE_ENABLE_PWA=true

# Advanced Features
VITE_ENABLE_A_B_TESTING=false
VITE_ENABLE_PUSH_NOTIFICATIONS=false
VITE_ENABLE_OFFLINE_MODE=true
VITE_ENABLE_MULTI_LANGUAGE=false

# =============================================================================
# PERFORMANCE CONFIGURATION
# =============================================================================

# Image Optimization
VITE_IMAGE_QUALITY=80
VITE_IMAGE_FORMATS=webp,avif,jpg
VITE_LAZY_LOAD_THRESHOLD=100  # pixels

# Caching
VITE_CACHE_DURATION=300000  # 5 minutes in milliseconds
VITE_ENABLE_SERVICE_WORKER=true

# Bundle Optimization
VITE_ENABLE_CODE_SPLITTING=true
VITE_CHUNK_SIZE_WARNING_LIMIT=1000  # KB

# =============================================================================
# DEVELOPMENT CONFIGURATION
# =============================================================================

# Debug Settings
VITE_DEBUG_MODE=true
VITE_ENABLE_CONSOLE_LOGS=true
VITE_ENABLE_REDUX_DEVTOOLS=true

# Development Tools
VITE_ENABLE_ERROR_OVERLAY=true
VITE_ENABLE_HOT_RELOAD=true
VITE_ENABLE_MOCK_API=false

# Testing
VITE_ENABLE_TEST_IDS=true
VITE_TEST_USER_EMAIL=<EMAIL>
VITE_TEST_USER_PASSWORD=test123

# =============================================================================
# UI/UX CONFIGURATION
# =============================================================================

# Theme Configuration
VITE_DEFAULT_THEME=system  # light, dark, system
VITE_PRIMARY_COLOR=#14B8A6  # Teal
VITE_SECONDARY_COLOR=#F1F5F9  # Slate

# Layout Settings
VITE_HEADER_HEIGHT=64
VITE_SIDEBAR_WIDTH=280
VITE_CONTAINER_MAX_WIDTH=1400

# Animation Settings
VITE_ANIMATION_DURATION=300  # milliseconds
VITE_ENABLE_REDUCED_MOTION=false

# =============================================================================
# SEARCH AND FILTERING
# =============================================================================

# Search Configuration
VITE_SEARCH_DEBOUNCE_DELAY=300  # milliseconds
VITE_SEARCH_MIN_CHARACTERS=2
VITE_SEARCH_MAX_RESULTS=50

# Default Search Filters
VITE_DEFAULT_SEARCH_RADIUS=50  # kilometers
VITE_DEFAULT_PRICE_RANGE_MIN=100
VITE_DEFAULT_PRICE_RANGE_MAX=5000
VITE_DEFAULT_GUEST_COUNT=2

# =============================================================================
# BOOKING CONFIGURATION
# =============================================================================

# Booking Rules
VITE_MIN_BOOKING_DAYS=1
VITE_MAX_BOOKING_DAYS=30
VITE_ADVANCE_BOOKING_DAYS=365  # How far in advance bookings can be made
VITE_CANCELLATION_DEADLINE_HOURS=24

# Payment Configuration
VITE_CURRENCY=ZAR
VITE_CURRENCY_SYMBOL=R
VITE_ENABLE_INSTANT_BOOKING=true
VITE_REQUIRE_PAYMENT_ON_BOOKING=true

# =============================================================================
# NOTIFICATION CONFIGURATION
# =============================================================================

# Toast Notifications
VITE_TOAST_DURATION=5000  # milliseconds
VITE_TOAST_POSITION=top-right
VITE_MAX_TOASTS=3

# Push Notifications (if enabled)
VITE_VAPID_PUBLIC_KEY=your-vapid-public-key
VITE_ENABLE_BROWSER_NOTIFICATIONS=false

# =============================================================================
# STORAGE CONFIGURATION
# =============================================================================

# Local Storage Keys
VITE_STORAGE_PREFIX=stayfinder_
VITE_STORAGE_VERSION=1.0

# File Upload Limits (Frontend Validation)
VITE_MAX_FILE_SIZE=10485760  # 10MB in bytes
VITE_MAX_FILES_PER_UPLOAD=10
VITE_ALLOWED_IMAGE_TYPES=image/jpeg,image/png,image/webp,image/avif

# =============================================================================
# SEO CONFIGURATION
# =============================================================================

# Meta Tags
VITE_SITE_NAME=StayFinder
VITE_SITE_DESCRIPTION=Find your perfect holiday accommodation on the beautiful KZN South Coast
VITE_SITE_KEYWORDS=holiday rental,accommodation,KZN,South Coast,vacation,booking
VITE_SITE_AUTHOR=StayFinder Team

# Social Media
VITE_TWITTER_HANDLE=@stayfinder
VITE_FACEBOOK_PAGE=stayfinder
VITE_INSTAGRAM_HANDLE=stayfinder

# Open Graph
VITE_OG_IMAGE=https://stayfinder.co.za/og-image.jpg
VITE_OG_TYPE=website

# =============================================================================
# ACCESSIBILITY CONFIGURATION
# =============================================================================

# Accessibility Features
VITE_ENABLE_SCREEN_READER_SUPPORT=true
VITE_ENABLE_KEYBOARD_NAVIGATION=true
VITE_ENABLE_HIGH_CONTRAST_MODE=false
VITE_ENABLE_FOCUS_INDICATORS=true

# WCAG Compliance
VITE_WCAG_LEVEL=AA
VITE_MIN_COLOR_CONTRAST_RATIO=4.5

# =============================================================================
# LOCALIZATION CONFIGURATION
# =============================================================================

# Language Settings
VITE_DEFAULT_LOCALE=en-ZA
VITE_SUPPORTED_LOCALES=en-ZA,af-ZA,zu-ZA
VITE_ENABLE_RTL=false

# Date/Time Formatting
VITE_DATE_FORMAT=DD/MM/YYYY
VITE_TIME_FORMAT=HH:mm
VITE_TIMEZONE=Africa/Johannesburg

# =============================================================================
# MONITORING AND ANALYTICS
# =============================================================================

# Performance Monitoring
VITE_ENABLE_PERFORMANCE_MONITORING=true
VITE_PERFORMANCE_SAMPLE_RATE=0.1  # 10%

# User Analytics
VITE_ENABLE_USER_TRACKING=true
VITE_TRACK_PAGE_VIEWS=true
VITE_TRACK_USER_INTERACTIONS=true

# Error Tracking
VITE_ENABLE_ERROR_TRACKING=true
VITE_ERROR_SAMPLE_RATE=1.0  # 100%

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# Content Security Policy
VITE_ENABLE_CSP=false  # Disabled in development
VITE_CSP_REPORT_URI=/api/csp-report

# HTTPS Settings
VITE_FORCE_HTTPS=false  # Set to true in production
VITE_ENABLE_HSTS=false  # Set to true in production

# =============================================================================
# PRODUCTION OVERRIDES
# =============================================================================

# Production-specific settings (override in production .env.local)
# VITE_NODE_ENV=production
# VITE_DEBUG_MODE=false
# VITE_ENABLE_CONSOLE_LOGS=false
# VITE_ENABLE_REDUX_DEVTOOLS=false
# VITE_API_BASE_URL=https://api.yourdomain.com/api
# VITE_BACKEND_URL=https://api.yourdomain.com
# VITE_AUTH_REDIRECT_URL=https://yourdomain.com/auth/callback
# VITE_ENABLE_CSP=true
# VITE_FORCE_HTTPS=true
# VITE_ENABLE_HSTS=true
# VITE_SENTRY_ENVIRONMENT=production

# =============================================================================
# STAGING OVERRIDES
# =============================================================================

# Staging-specific settings (override in staging .env.local)
# VITE_NODE_ENV=staging
# VITE_DEBUG_MODE=true
# VITE_ENABLE_CONSOLE_LOGS=true
# VITE_API_BASE_URL=https://staging-api.yourdomain.com/api
# VITE_BACKEND_URL=https://staging-api.yourdomain.com
# VITE_SENTRY_ENVIRONMENT=staging
