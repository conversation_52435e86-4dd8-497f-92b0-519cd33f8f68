@echo off
echo ========================================
echo   StayFinder Supabase Setup Script
echo ========================================
echo.

echo This script will help you set up Supabase for StayFinder
echo.

echo Step 1: Installing Supabase CLI...
echo.
npm install -g supabase
if %errorlevel% neq 0 (
    echo Failed to install Supabase CLI. Please install manually.
    pause
    exit /b 1
)

echo.
echo Step 2: Installing Supabase dependencies...
echo.

echo Installing frontend dependencies...
npm install @supabase/supabase-js @supabase/auth-helpers-react @supabase/realtime-js

echo Installing backend dependencies...
cd backend
npm install @supabase/supabase-js pg
cd ..

echo.
echo Step 3: Setting up environment files...
echo.

if not exist .env.local (
    copy .env.local.example .env.local
    echo Created .env.local from example
) else (
    echo .env.local already exists
)

if not exist backend\.env (
    copy .env.example backend\.env
    echo Created backend\.env from example
) else (
    echo backend\.env already exists
)

echo.
echo ========================================
echo   Manual Setup Required
echo ========================================
echo.
echo Please complete the following steps:
echo.
echo 1. Create a Supabase project at https://app.supabase.com
echo.
echo 2. Get your project credentials:
echo    - Project URL: https://your-project-ref.supabase.co
echo    - Anon Key: Found in Settings ^> API
echo    - Service Role Key: Found in Settings ^> API
echo    - JWT Secret: Found in Settings ^> API
echo.
echo 3. Update your environment files:
echo    - .env.local (frontend)
echo    - backend\.env (backend)
echo.
echo 4. Set up your database schema:
echo    - Run the SQL commands from supabase-upgrade.md
echo    - Or use the Supabase dashboard
echo.
echo 5. Configure storage buckets:
echo    - property-images
echo    - user-avatars
echo    - documents
echo.
echo 6. Set up Row Level Security policies
echo.
echo 7. Generate TypeScript types:
echo    supabase gen types typescript --project-id your-project-ref ^> src/lib/database.types.ts
echo.
echo ========================================
echo   Next Steps
echo ========================================
echo.
echo 1. Read the supabase-upgrade.md documentation
echo 2. Follow the migration checklist
echo 3. Test your setup with npm run dev
echo.
echo For detailed instructions, see: supabase-upgrade.md
echo.
pause
