import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { StarRating } from './StarRating';
import { useReviews } from '../contexts/ReviewsContext';
import { useAuth } from '../contexts/AuthContext';
import { 
  Loader2, 
  Star, 
  MessageSquare,
  AlertCircle,
  CheckCircle
} from 'lucide-react';

interface ReviewFormProps {
  propertyId: string;
  bookingId: string;
  propertyTitle?: string;
  existingReview?: {
    id: string;
    rating: number;
    comment: string;
  };
  onSuccess?: (review: any) => void;
  onCancel?: () => void;
}

export const ReviewForm: React.FC<ReviewFormProps> = ({
  propertyId,
  bookingId,
  propertyTitle,
  existingReview,
  onSuccess,
  onCancel
}) => {
  const [rating, setRating] = useState(existingReview?.rating || 0);
  const [comment, setComment] = useState(existingReview?.comment || '');
  const [errors, setErrors] = useState<{ rating?: string; comment?: string }>({});
  
  const { createReview, updateReview, submitting, error, clearError } = useReviews();
  const { isAuthenticated } = useAuth();

  const isEditing = !!existingReview;

  useEffect(() => {
    clearError();
  }, [clearError]);

  const validateForm = () => {
    const newErrors: { rating?: string; comment?: string } = {};

    if (rating === 0) {
      newErrors.rating = 'Please select a rating';
    }

    if (!comment.trim()) {
      newErrors.comment = 'Please write a review comment';
    } else if (comment.trim().length < 10) {
      newErrors.comment = 'Review must be at least 10 characters long';
    } else if (comment.trim().length > 1000) {
      newErrors.comment = 'Review must be less than 1000 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!isAuthenticated) {
      alert('Please log in to write a review');
      return;
    }

    if (!validateForm()) {
      return;
    }

    try {
      let review;
      
      if (isEditing && existingReview) {
        review = await updateReview(existingReview.id, {
          rating,
          comment: comment.trim()
        });
      } else {
        review = await createReview({
          propertyId,
          bookingId,
          rating,
          comment: comment.trim()
        });
      }

      if (onSuccess) {
        onSuccess(review);
      }

      // Reset form if creating new review
      if (!isEditing) {
        setRating(0);
        setComment('');
        setErrors({});
      }
    } catch (err) {
      console.error('Failed to submit review:', err);
    }
  };

  const handleCancel = () => {
    if (isEditing) {
      setRating(existingReview?.rating || 0);
      setComment(existingReview?.comment || '');
    } else {
      setRating(0);
      setComment('');
    }
    setErrors({});
    clearError();
    
    if (onCancel) {
      onCancel();
    }
  };

  if (!isAuthenticated) {
    return (
      <Card>
        <CardContent className="text-center py-8">
          <AlertCircle className="h-12 w-12 mx-auto mb-4 text-gray-400" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Login Required</h3>
          <p className="text-gray-600">
            Please log in to write a review for this property.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MessageSquare className="h-5 w-5" />
          {isEditing ? 'Edit Your Review' : 'Write a Review'}
        </CardTitle>
        {propertyTitle && (
          <p className="text-gray-600">{propertyTitle}</p>
        )}
      </CardHeader>

      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Error Display */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-red-600" />
              <p className="text-red-700">{error}</p>
            </div>
          )}

          {/* Rating Section */}
          <div className="space-y-3">
            <Label className="flex items-center gap-2">
              <Star className="h-4 w-4" />
              Overall Rating
            </Label>
            <div className="space-y-2">
              <StarRating
                rating={rating}
                onRatingChange={setRating}
                size="lg"
                className="justify-center md:justify-start"
              />
              {errors.rating && (
                <p className="text-red-600 text-sm">{errors.rating}</p>
              )}
            </div>
          </div>

          {/* Comment Section */}
          <div className="space-y-3">
            <Label htmlFor="comment" className="flex items-center gap-2">
              <MessageSquare className="h-4 w-4" />
              Your Review
            </Label>
            <Textarea
              id="comment"
              placeholder="Share your experience with this property. What did you like? What could be improved?"
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              rows={5}
              className={`resize-none ${errors.comment ? 'border-red-300' : ''}`}
            />
            <div className="flex justify-between items-center">
              {errors.comment ? (
                <p className="text-red-600 text-sm">{errors.comment}</p>
              ) : (
                <p className="text-gray-500 text-sm">
                  {comment.length}/1000 characters
                </p>
              )}
            </div>
          </div>

          {/* Guidelines */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-2">Review Guidelines</h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Be honest and constructive in your feedback</li>
              <li>• Focus on your experience with the property and host</li>
              <li>• Avoid personal attacks or inappropriate language</li>
              <li>• Include specific details that would help future guests</li>
            </ul>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4">
            <Button
              type="submit"
              disabled={submitting || rating === 0 || !comment.trim()}
              className="flex-1 bg-sea-green-500 hover:bg-sea-green-600 text-white"
            >
              {submitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {isEditing ? 'Updating...' : 'Submitting...'}
                </>
              ) : (
                <>
                  <CheckCircle className="mr-2 h-4 w-4" />
                  {isEditing ? 'Update Review' : 'Submit Review'}
                </>
              )}
            </Button>
            
            {(isEditing || onCancel) && (
              <Button
                type="button"
                variant="outline"
                onClick={handleCancel}
                disabled={submitting}
                className="flex-1"
              >
                Cancel
              </Button>
            )}
          </div>
        </form>
      </CardContent>
    </Card>
  );
};
