-- KZN South Coast StayFinder Database Initialization
-- Run this script in phpMyAdmin or MySQL command line

-- Create database
CREATE DATABASE IF NOT EXISTS stayfinder_dev CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE stayfinder_dev;

-- Users table
CREATE TABLE users (
    id VARCHAR(36) PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    role ENUM('guest', 'host', 'admin') DEFAULT 'guest',
    email_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_users_email (email),
    INDEX idx_users_role (role)
);

-- Properties table
CREATE TABLE properties (
    id VARCHAR(36) <PERSON>IMAR<PERSON> KEY,
    owner_id VARCHAR(36) NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    property_type ENUM('apartment', 'house', 'villa', 'guesthouse', 'cottage') NOT NULL,
    location VARCHAR(255) NOT NULL,
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    max_guests INT NOT NULL,
    bedrooms INT NOT NULL,
    bathrooms INT NOT NULL,
    price_per_night DECIMAL(10, 2) NOT NULL,
    cleaning_fee DECIMAL(10, 2) DEFAULT 0,
    amenities JSON,
    house_rules TEXT,
    check_in_time TIME DEFAULT '15:00:00',
    check_out_time TIME DEFAULT '11:00:00',
    status ENUM('active', 'inactive', 'pending') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_properties_location (location),
    INDEX idx_properties_price (price_per_night),
    INDEX idx_properties_status (status),
    INDEX idx_properties_type (property_type),
    INDEX idx_properties_guests (max_guests)
);

-- Property images table
CREATE TABLE property_images (
    id VARCHAR(36) PRIMARY KEY,
    property_id VARCHAR(36) NOT NULL,
    image_url VARCHAR(500) NOT NULL,
    alt_text VARCHAR(255),
    is_primary BOOLEAN DEFAULT FALSE,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (property_id) REFERENCES properties(id) ON DELETE CASCADE,
    INDEX idx_property_images_property (property_id),
    INDEX idx_property_images_primary (is_primary)
);

-- Bookings table
CREATE TABLE bookings (
    id VARCHAR(36) PRIMARY KEY,
    property_id VARCHAR(36) NOT NULL,
    guest_id VARCHAR(36) NOT NULL,
    check_in_date DATE NOT NULL,
    check_out_date DATE NOT NULL,
    total_amount DECIMAL(10, 2) NOT NULL,
    booking_status ENUM('pending', 'confirmed', 'cancelled', 'completed') DEFAULT 'pending',
    payment_status ENUM('pending', 'paid', 'refunded') DEFAULT 'pending',
    guest_count INT NOT NULL,
    special_requests TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (property_id) REFERENCES properties(id) ON DELETE CASCADE,
    FOREIGN KEY (guest_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_bookings_dates (check_in_date, check_out_date),
    INDEX idx_bookings_status (booking_status),
    INDEX idx_bookings_property (property_id),
    INDEX idx_bookings_guest (guest_id)
);

-- Reviews table
CREATE TABLE reviews (
    id VARCHAR(36) PRIMARY KEY,
    booking_id VARCHAR(36) NOT NULL,
    reviewer_id VARCHAR(36) NOT NULL,
    property_id VARCHAR(36) NOT NULL,
    rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
    comment TEXT,
    response TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (booking_id) REFERENCES bookings(id) ON DELETE CASCADE,
    FOREIGN KEY (reviewer_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (property_id) REFERENCES properties(id) ON DELETE CASCADE,
    INDEX idx_reviews_property (property_id),
    INDEX idx_reviews_rating (rating),
    INDEX idx_reviews_reviewer (reviewer_id)
);

-- Insert sample data for testing
-- Test users (password is 'password123' for all)
INSERT INTO users (id, email, password_hash, first_name, last_name, role, email_verified) VALUES
('user-guest-1', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg9S6O', 'John', 'Doe', 'guest', TRUE),
('user-host-1', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg9S6O', 'Jane', 'Smith', 'host', TRUE),
('user-admin-1', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg9S6O', 'Admin', 'User', 'admin', TRUE),
('user-host-2', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg9S6O', 'Mike', 'Johnson', 'host', TRUE);

-- Test properties
INSERT INTO properties (id, owner_id, title, description, property_type, location, latitude, longitude, max_guests, bedrooms, bathrooms, price_per_night, cleaning_fee, amenities, house_rules, status) VALUES
('prop-1', 'user-host-1', 'Luxury Beachfront Villa - Margate', 'Stunning 4-bedroom villa with private beach access, infinity pool, and panoramic ocean views. Perfect for families and groups seeking luxury accommodation on the KZN South Coast.', 'villa', 'Margate', -30.8647, 30.3707, 8, 4, 3, 2500.00, 300.00, '["WiFi", "Pool", "Beach Access", "Air Conditioning", "Parking", "Kitchen", "Braai Area", "Sea View"]', 'No smoking indoors. No parties. Check-in after 3 PM, check-out before 11 AM.', 'active'),

('prop-2', 'user-host-1', 'Cozy Beach Cottage - Scottburgh', 'Charming 2-bedroom cottage just 100m from the beach. Ideal for couples or small families. Features a lovely garden and outdoor braai area.', 'cottage', 'Scottburgh', -30.2867, 30.7533, 4, 2, 1, 800.00, 150.00, '["WiFi", "Garden", "Braai Area", "Parking", "Kitchen", "Near Beach"]', 'No pets allowed. Quiet hours after 10 PM.', 'active'),

('prop-3', 'user-host-2', 'Modern Apartment with Sea Views - Hibberdene', 'Contemporary 3-bedroom apartment with stunning sea views from every room. Located in a secure complex with pool and direct beach access.', 'apartment', 'Hibberdene', -30.5833, 30.6167, 6, 3, 2, 1200.00, 200.00, '["WiFi", "Sea View", "Pool", "Security", "Beach Access", "Air Conditioning", "Parking", "Kitchen"]', 'No smoking. Maximum 6 guests. Pool hours: 6 AM - 10 PM.', 'active'),

('prop-4', 'user-host-2', 'Family Guesthouse - Port Shepstone', 'Spacious guesthouse perfect for large families. Features 5 bedrooms, large communal areas, and beautiful garden with playground equipment.', 'guesthouse', 'Port Shepstone', -30.7417, 30.4550, 12, 5, 3, 1800.00, 250.00, '["WiFi", "Garden", "Playground", "Braai Area", "Parking", "Kitchen", "Lounge", "Dining Room"]', 'Child-friendly. No smoking indoors. Quiet hours after 9 PM.', 'active'),

('prop-5', 'user-host-1', 'Beachfront Apartment - Amanzimtoti', 'Modern 2-bedroom apartment right on the beachfront. Wake up to the sound of waves and enjoy spectacular sunrises from your private balcony.', 'apartment', 'Amanzimtoti', -30.0333, 30.8833, 4, 2, 2, 1000.00, 180.00, '["WiFi", "Sea View", "Balcony", "Beach Access", "Parking", "Kitchen", "Air Conditioning"]', 'No parties. Balcony safety - children must be supervised.', 'active');

-- Property images
INSERT INTO property_images (id, property_id, image_url, alt_text, is_primary, sort_order) VALUES
('img-1', 'prop-1', 'https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=800&h=600&fit=crop', 'Luxury beachfront villa exterior', TRUE, 1),
('img-2', 'prop-1', 'https://images.unsplash.com/photo-1582268611958-ebfd161ef9cf?w=800&h=600&fit=crop', 'Villa living room with ocean view', FALSE, 2),
('img-3', 'prop-1', 'https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=800&h=600&fit=crop', 'Villa master bedroom', FALSE, 3),

('img-4', 'prop-2', 'https://images.unsplash.com/photo-1520637836862-4d197d17c90a?w=800&h=600&fit=crop', 'Cozy beach cottage exterior', TRUE, 1),
('img-5', 'prop-2', 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&h=600&fit=crop', 'Cottage garden and braai area', FALSE, 2),

('img-6', 'prop-3', 'https://images.unsplash.com/photo-1512917774080-9991f1c4c750?w=800&h=600&fit=crop', 'Modern apartment with sea view', TRUE, 1),
('img-7', 'prop-3', 'https://images.unsplash.com/photo-1560185127-6ed189bf02f4?w=800&h=600&fit=crop', 'Apartment living area', FALSE, 2),

('img-8', 'prop-4', 'https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=800&h=600&fit=crop', 'Family guesthouse exterior', TRUE, 1),
('img-9', 'prop-4', 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&h=600&fit=crop', 'Guesthouse garden with playground', FALSE, 2),

('img-10', 'prop-5', 'https://images.unsplash.com/photo-1502672260266-1c1ef2d93688?w=800&h=600&fit=crop', 'Beachfront apartment balcony view', TRUE, 1);

-- Sample bookings
INSERT INTO bookings (id, property_id, guest_id, check_in_date, check_out_date, total_amount, booking_status, payment_status, guest_count, special_requests) VALUES
('booking-1', 'prop-1', 'user-guest-1', '2024-07-15', '2024-07-20', 12800.00, 'completed', 'paid', 6, 'Early check-in if possible'),
('booking-2', 'prop-2', 'user-guest-1', '2024-08-10', '2024-08-15', 4150.00, 'confirmed', 'paid', 2, 'Honeymoon stay - please prepare something special'),
('booking-3', 'prop-3', 'user-guest-1', '2024-09-01', '2024-09-05', 5000.00, 'pending', 'pending', 4, 'Traveling with elderly parents - ground floor preferred');

-- Sample reviews
INSERT INTO reviews (id, booking_id, reviewer_id, property_id, rating, comment) VALUES
('review-1', 'booking-1', 'user-guest-1', 'prop-1', 5, 'Absolutely stunning property! The villa exceeded all our expectations. The ocean views were breathtaking, and the private beach access made our stay truly special. The host was incredibly responsive and helpful. We will definitely be back!'),
('review-2', 'booking-2', 'user-guest-1', 'prop-2', 4, 'Lovely cottage in a great location. Very clean and well-maintained. The garden was beautiful and perfect for morning coffee. Only minor issue was the WiFi was a bit slow, but overall a wonderful stay.');

-- Create indexes for better performance
CREATE INDEX idx_properties_location_price ON properties(location, price_per_night);
CREATE INDEX idx_bookings_property_dates ON bookings(property_id, check_in_date, check_out_date);
CREATE INDEX idx_reviews_property_rating ON reviews(property_id, rating);

-- Full-text search index for property search
ALTER TABLE properties ADD FULLTEXT(title, description, location);

COMMIT;
