import { useEffect, useCallback } from 'react';
import { useLocation } from 'react-router-dom';
import {
  initializeAnalytics,
  trackPageView,
  trackEvent,
  PropertyAnalytics,
  BookingAnalytics,
  UserAnalytics,
  PerformanceAnalytics,
  setUserId,
  setUserProperties
} from '@/lib/analytics';

// Main analytics hook
export const useAnalytics = () => {
  const location = useLocation();

  // Initialize analytics on mount
  useEffect(() => {
    initializeAnalytics();
  }, []);

  // Track page views on route changes
  useEffect(() => {
    const path = location.pathname + location.search;
    trackPageView(path);
  }, [location]);

  // Return analytics functions
  return {
    trackEvent,
    trackPageView,
    setUserId,
    setUserProperties,
    property: PropertyAnalytics,
    booking: BookingAnalytics,
    user: UserAnalytics,
    performance: PerformanceAnalytics,
  };
};

// Hook for property-related analytics
export const usePropertyAnalytics = () => {
  const trackPropertyView = useCallback((propertyData: {
    property_id: string;
    property_name: string;
    property_type: string;
    location: string;
    price: number;
    rating?: number;
  }) => {
    PropertyAnalytics.trackPropertyView(propertyData);
  }, []);

  const trackSearch = useCallback((searchParams: {
    location?: string;
    checkIn?: string;
    checkOut?: string;
    guests?: number;
    propertyType?: string;
    priceRange?: { min: number; max: number };
  }) => {
    PropertyAnalytics.trackSearch(searchParams);
  }, []);

  const trackAddToWishlist = useCallback((propertyData: {
    property_id: string;
    property_name: string;
    property_type: string;
    price: number;
  }) => {
    PropertyAnalytics.trackAddToWishlist(propertyData);
  }, []);

  const trackRemoveFromWishlist = useCallback((propertyData: {
    property_id: string;
    property_name: string;
  }) => {
    PropertyAnalytics.trackRemoveFromWishlist(propertyData);
  }, []);

  const trackPropertyComparison = useCallback((propertyIds: string[]) => {
    PropertyAnalytics.trackPropertyComparison(propertyIds);
  }, []);

  return {
    trackPropertyView,
    trackSearch,
    trackAddToWishlist,
    trackRemoveFromWishlist,
    trackPropertyComparison,
  };
};

// Hook for booking-related analytics
export const useBookingAnalytics = () => {
  const trackBeginCheckout = useCallback((bookingData: {
    property_id: string;
    property_name: string;
    check_in: string;
    check_out: string;
    guests: number;
    nights: number;
    total_price: number;
  }) => {
    BookingAnalytics.trackBeginCheckout(bookingData);
  }, []);

  const trackBookingComplete = useCallback((bookingData: {
    booking_id: string;
    property_id: string;
    property_name: string;
    property_type: string;
    check_in: string;
    check_out: string;
    guests: number;
    nights: number;
    base_price: number;
    fees: number;
    total_price: number;
    payment_method?: string;
  }) => {
    BookingAnalytics.trackBookingComplete(bookingData);
  }, []);

  const trackBookingCancellation = useCallback((bookingData: {
    booking_id: string;
    property_name: string;
    cancellation_reason?: string;
    refund_amount?: number;
  }) => {
    BookingAnalytics.trackBookingCancellation(bookingData);
  }, []);

  return {
    trackBeginCheckout,
    trackBookingComplete,
    trackBookingCancellation,
  };
};

// Hook for user interaction analytics
export const useUserAnalytics = () => {
  const trackRegistration = useCallback((method: string = 'email') => {
    UserAnalytics.trackRegistration(method);
  }, []);

  const trackLogin = useCallback((method: string = 'email') => {
    UserAnalytics.trackLogin(method);
  }, []);

  const trackProfileCompletion = useCallback((completionPercentage: number) => {
    UserAnalytics.trackProfileCompletion(completionPercentage);
  }, []);

  const trackContactSupport = useCallback((method: string, topic?: string) => {
    UserAnalytics.trackContactSupport(method, topic);
  }, []);

  return {
    trackRegistration,
    trackLogin,
    trackProfileCompletion,
    trackContactSupport,
  };
};

// Hook for performance analytics
export const usePerformanceAnalytics = () => {
  const trackPageLoadTime = useCallback((pageName: string, loadTime: number) => {
    PerformanceAnalytics.trackPageLoadTime(pageName, loadTime);
  }, []);

  const trackSearchPerformance = useCallback((searchTime: number, resultCount: number) => {
    PerformanceAnalytics.trackSearchPerformance(searchTime, resultCount);
  }, []);

  const trackError = useCallback((errorType: string, errorMessage: string, page?: string) => {
    PerformanceAnalytics.trackError(errorType, errorMessage, page);
  }, []);

  // Automatically track page load performance
  useEffect(() => {
    const measurePageLoad = () => {
      if (typeof window !== 'undefined' && window.performance) {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        if (navigation) {
          const loadTime = navigation.loadEventEnd - navigation.fetchStart;
          const pageName = window.location.pathname;
          trackPageLoadTime(pageName, loadTime);
        }
      }
    };

    // Measure after page is fully loaded
    if (document.readyState === 'complete') {
      measurePageLoad();
    } else {
      window.addEventListener('load', measurePageLoad);
      return () => window.removeEventListener('load', measurePageLoad);
    }
  }, [trackPageLoadTime]);

  return {
    trackPageLoadTime,
    trackSearchPerformance,
    trackError,
  };
};

// Hook for conversion tracking
export const useConversionTracking = () => {
  const trackConversion = useCallback((conversionType: string, value?: number, currency = 'ZAR') => {
    trackEvent('conversion', 'ecommerce', conversionType, value, {
      currency,
      conversion_type: conversionType,
    });
  }, []);

  const trackSignupConversion = useCallback(() => {
    trackConversion('signup');
  }, [trackConversion]);

  const trackBookingConversion = useCallback((value: number) => {
    trackConversion('booking', value);
  }, [trackConversion]);

  const trackNewsletterSignup = useCallback(() => {
    trackConversion('newsletter_signup');
  }, [trackConversion]);

  return {
    trackConversion,
    trackSignupConversion,
    trackBookingConversion,
    trackNewsletterSignup,
  };
};

// Hook for A/B testing and experiments
export const useExperimentTracking = () => {
  const trackExperiment = useCallback((experimentId: string, variantId: string) => {
    trackEvent('experiment_view', 'experiment', experimentId, undefined, {
      experiment_id: experimentId,
      variant_id: variantId,
    });
  }, []);

  const trackExperimentConversion = useCallback((experimentId: string, variantId: string, conversionType: string) => {
    trackEvent('experiment_conversion', 'experiment', experimentId, undefined, {
      experiment_id: experimentId,
      variant_id: variantId,
      conversion_type: conversionType,
    });
  }, []);

  return {
    trackExperiment,
    trackExperimentConversion,
  };
};

// Hook for custom event tracking with common patterns
export const useCustomAnalytics = () => {
  const trackButtonClick = useCallback((buttonName: string, location: string) => {
    trackEvent('button_click', 'interaction', buttonName, undefined, {
      button_name: buttonName,
      button_location: location,
    });
  }, []);

  const trackFormSubmission = useCallback((formName: string, success: boolean) => {
    trackEvent('form_submit', 'interaction', formName, undefined, {
      form_name: formName,
      success: success,
    });
  }, []);

  const trackVideoPlay = useCallback((videoTitle: string, duration?: number) => {
    trackEvent('video_play', 'engagement', videoTitle, duration, {
      video_title: videoTitle,
      video_duration: duration,
    });
  }, []);

  const trackFileDownload = useCallback((fileName: string, fileType: string) => {
    trackEvent('file_download', 'engagement', fileName, undefined, {
      file_name: fileName,
      file_type: fileType,
    });
  }, []);

  const trackSocialShare = useCallback((platform: string, contentType: string, contentId?: string) => {
    trackEvent('share', 'social', platform, undefined, {
      platform: platform,
      content_type: contentType,
      content_id: contentId,
    });
  }, []);

  return {
    trackButtonClick,
    trackFormSubmission,
    trackVideoPlay,
    trackFileDownload,
    trackSocialShare,
  };
};
