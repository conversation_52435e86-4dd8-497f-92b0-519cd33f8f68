-- ============================================================================
-- South Africa Locations Reference Data
-- Complete list of provinces and major cities for StayFinder
-- PostgreSQL/Supabase Compatible
-- ============================================================================

-- ============================================================================
-- SOUTH AFRICAN PROVINCES AND MAJOR CITIES
-- ============================================================================

-- This reference data can be used to populate location dropdowns
-- or validate location data in your StayFinder application

-- ============================================================================
-- PROVINCES TABLE (Optional Reference)
-- ============================================================================

CREATE TABLE IF NOT EXISTS sa_provinces (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) UNIQUE NOT NULL,
    code VARCHAR(3) UNIQUE NOT NULL,
    capital_city VARCHAR(100),
    population INTEGER,
    area_km2 INTEGER,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Insert South African provinces
INSERT INTO sa_provinces (name, code, capital_city, population, area_km2) VALUES
('Western Cape', 'WC', 'Cape Town', 7005741, 129462),
('Gauteng', 'GP', 'Johannesburg', 15810388, 18178),
('KwaZulu-Natal', 'KZN', 'Pietermaritzburg', 11513575, 94361),
('Eastern Cape', 'EC', 'Bhisho', 6712276, 168966),
('Limpopo', 'LP', 'Polokwane', 5982584, 125754),
('Mpumalanga', 'MP', 'Nelspruit', 4592187, 76495),
('North West', 'NW', 'Mahikeng', 4072160, 104882),
('Free State', 'FS', 'Bloemfontein', 2887465, 129825),
('Northern Cape', 'NC', 'Kimberley', 1303047, 372889);

-- ============================================================================
-- CITIES TABLE (Optional Reference)
-- ============================================================================

CREATE TABLE IF NOT EXISTS sa_cities (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    province VARCHAR(100) NOT NULL,
    is_major_city BOOLEAN DEFAULT FALSE,
    is_tourist_destination BOOLEAN DEFAULT FALSE,
    latitude DECIMAL(10,8),
    longitude DECIMAL(11,8),
    population INTEGER,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Insert major South African cities by province
INSERT INTO sa_cities (name, province, is_major_city, is_tourist_destination, latitude, longitude, population) VALUES

-- Western Cape
('Cape Town', 'Western Cape', true, true, -33.9249, 18.4241, 4618000),
('Stellenbosch', 'Western Cape', false, true, -33.9321, 18.8602, 155733),
('Paarl', 'Western Cape', false, true, -33.7434, 18.9707, 191013),
('George', 'Western Cape', false, true, -33.9628, 22.4619, 203253),
('Hermanus', 'Western Cape', false, true, -34.4187, 19.2345, 49000),
('Knysna', 'Western Cape', false, true, -34.0361, 23.0471, 76431),
('Plettenberg Bay', 'Western Cape', false, true, -34.0527, 23.3716, 29149),
('Mossel Bay', 'Western Cape', false, true, -34.1816, 22.1460, 89430),
('Oudtshoorn', 'Western Cape', false, true, -33.5970, 22.2000, 95933),
('Swellendam', 'Western Cape', false, true, -34.0231, 20.4431, 17537),

-- Gauteng
('Johannesburg', 'Gauteng', true, true, -26.2041, 28.0473, 5635127),
('Pretoria', 'Gauteng', true, true, -25.7479, 28.2293, 2921488),
('Soweto', 'Gauteng', true, false, -26.2678, 27.8546, 1695047),
('Sandton', 'Gauteng', false, false, -26.1076, 28.0567, 222415),
('Randburg', 'Gauteng', false, false, -26.0939, 28.0021, 337053),
('Roodepoort', 'Gauteng', false, false, -26.1625, 27.8717, 386422),
('Benoni', 'Gauteng', false, false, -26.1884, 28.3207, 654509),
('Boksburg', 'Gauteng', false, false, -26.2145, 28.2621, 445168),
('Germiston', 'Gauteng', false, false, -26.2309, 28.1440, 255331),
('Kempton Park', 'Gauteng', false, false, -26.1015, 28.2305, 442633),

-- KwaZulu-Natal
('Durban', 'KwaZulu-Natal', true, true, -29.8587, 31.0218, 3442361),
('Pietermaritzburg', 'KwaZulu-Natal', true, false, -29.6094, 30.3781, 750845),
('Newcastle', 'KwaZulu-Natal', false, false, -27.7574, 29.9317, 404838),
('Richards Bay', 'KwaZulu-Natal', false, false, -28.7830, 32.0377, 252968),
('Pinetown', 'KwaZulu-Natal', false, false, -29.7833, 30.8667, 139000),
('Umhlanga', 'KwaZulu-Natal', false, true, -29.7674, 31.0448, 42000),
('Ballito', 'KwaZulu-Natal', false, true, -29.5391, 31.2136, 40000),
('Margate', 'KwaZulu-Natal', false, true, -30.8647, 30.3707, 25000),
('Port Shepstone', 'KwaZulu-Natal', false, true, -30.7411, 30.4551, 40000),
('Scottburgh', 'KwaZulu-Natal', false, true, -30.2867, 30.7533, 15000),

-- Eastern Cape
('Port Elizabeth', 'Eastern Cape', true, true, -33.9608, 25.6022, 1263051),
('East London', 'Eastern Cape', true, true, -33.0153, 27.9116, 755200),
('Bhisho', 'Eastern Cape', false, false, -32.8473, 27.4419, 137287),
('Uitenhage', 'Eastern Cape', false, false, -33.7576, 25.3971, 228912),
('King Williams Town', 'Eastern Cape', false, false, -32.8833, 27.4000, 34000),
('Grahamstown', 'Eastern Cape', false, true, -33.3047, 26.5317, 139000),
('Jeffreys Bay', 'Eastern Cape', false, true, -34.0489, 24.9111, 27107),
('Knysna', 'Eastern Cape', false, true, -34.0361, 23.0471, 76431),
('Plettenberg Bay', 'Eastern Cape', false, true, -34.0527, 23.3716, 29149),
('Addo', 'Eastern Cape', false, true, -33.5667, 25.7500, 5000),

-- Free State
('Bloemfontein', 'Free State', true, false, -29.0852, 26.1596, 747431),
('Welkom', 'Free State', false, false, -27.9770, 26.7312, 431944),
('Kroonstad', 'Free State', false, false, -27.6506, 27.2340, 127447),
('Bethlehem', 'Free State', false, true, -28.2292, 28.3067, 59251),
('Sasolburg', 'Free State', false, false, -26.8129, 27.8167, 128254),
('Phuthaditjhaba', 'Free State', false, false, -28.5167, 28.8167, 150000),
('Virginia', 'Free State', false, false, -28.1039, 26.8658, 152000),
('Parys', 'Free State', false, true, -26.9033, 27.4608, 74155),
('Clarens', 'Free State', false, true, -28.5167, 28.4333, 2000),
('Harrismith', 'Free State', false, false, -28.2833, 29.1333, 35000),

-- Mpumalanga
('Nelspruit', 'Mpumalanga', true, true, -25.4753, 30.9820, 588794),
('Witbank', 'Mpumalanga', false, false, -25.8738, 29.2321, 262491),
('Secunda', 'Mpumalanga', false, false, -26.5504, 29.1781, 40302),
('Standerton', 'Mpumalanga', false, false, -26.9333, 29.2500, 111067),
('Middelburg', 'Mpumalanga', false, false, -25.7756, 29.4644, 154706),
('Ermelo', 'Mpumalanga', false, false, -26.5333, 29.9833, 103462),
('Barberton', 'Mpumalanga', false, true, -25.7833, 31.0500, 27000),
('White River', 'Mpumalanga', false, true, -25.3333, 31.0167, 20000),
('Hazyview', 'Mpumalanga', false, true, -25.0500, 31.1167, 25000),
('Sabie', 'Mpumalanga', false, true, -25.1167, 30.7667, 8000),

-- Limpopo
('Polokwane', 'Limpopo', true, false, -23.9045, 29.4689, 775948),
('Tzaneen', 'Limpopo', false, true, -23.8333, 30.1667, 30000),
('Phalaborwa', 'Limpopo', false, true, -23.9430, 31.1411, 192000),
('Thohoyandou', 'Limpopo', false, false, -22.9500, 30.4833, 69453),
('Lebowakgomo', 'Limpopo', false, false, -24.2000, 29.5000, 19642),
('Mokopane', 'Limpopo', false, false, -24.1833, 29.0167, 35000),
('Bela-Bela', 'Limpopo', false, true, -24.8833, 28.2833, 35000),
('Musina', 'Limpopo', false, false, -22.3500, 30.0333, 40000),
('Louis Trichardt', 'Limpopo', false, false, -23.0500, 29.9000, 47000),
('Giyani', 'Limpopo', false, false, -23.3000, 30.7167, 25000),

-- North West
('Mahikeng', 'North West', true, false, -25.8601, 25.6358, 49300),
('Rustenburg', 'North West', false, true, -25.6336, 27.2449, 395539),
('Klerksdorp', 'North West', false, false, -26.8500, 26.6667, 406431),
('Potchefstroom', 'North West', false, false, -26.7167, 27.1000, 128357),
('Brits', 'North West', false, false, -25.6333, 27.7833, 58417),
('Vryburg', 'North West', false, false, -26.9500, 24.7333, 48000),
('Lichtenburg', 'North West', false, false, -26.1500, 26.1667, 60000),
('Zeerust', 'North West', false, false, -25.5333, 26.0833, 35000),
('Sun City', 'North West', false, true, -25.3333, 27.1000, 5000),
('Pilanesberg', 'North West', false, true, -25.2500, 27.0833, 2000),

-- Northern Cape
('Kimberley', 'Northern Cape', true, true, -28.7282, 24.7499, 225160),
('Upington', 'Northern Cape', false, true, -28.4478, 21.2561, 75000),
('Kuruman', 'Northern Cape', false, false, -27.4667, 23.4333, 13000),
('Springbok', 'Northern Cape', false, true, -29.6667, 17.8833, 13000),
('De Aar', 'Northern Cape', false, false, -30.6500, 24.0167, 25000),
('Calvinia', 'Northern Cape', false, true, -31.4667, 19.7667, 13000),
('Sutherland', 'Northern Cape', false, true, -32.4000, 20.6667, 3000),
('Carnarvon', 'Northern Cape', false, true, -30.9667, 22.1333, 5000),
('Colesberg', 'Northern Cape', false, false, -30.7167, 25.1000, 15000),
('Prieska', 'Northern Cape', false, false, -29.6667, 22.7500, 20000);

-- ============================================================================
-- INDEXES FOR PERFORMANCE
-- ============================================================================

CREATE INDEX idx_sa_cities_province ON sa_cities(province);
CREATE INDEX idx_sa_cities_major ON sa_cities(is_major_city) WHERE is_major_city = true;
CREATE INDEX idx_sa_cities_tourist ON sa_cities(is_tourist_destination) WHERE is_tourist_destination = true;
CREATE INDEX idx_sa_cities_coordinates ON sa_cities USING GIST(ST_Point(longitude, latitude)) WHERE latitude IS NOT NULL AND longitude IS NOT NULL;

-- ============================================================================
-- USAGE EXAMPLES
-- ============================================================================

-- Get all major cities
-- SELECT name, province FROM sa_cities WHERE is_major_city = true ORDER BY province, name;

-- Get tourist destinations by province
-- SELECT name, province FROM sa_cities WHERE is_tourist_destination = true ORDER BY province, name;

-- Get cities within 100km of Cape Town
-- SELECT name, province, 
--        ST_Distance(ST_Point(longitude, latitude), ST_Point(18.4241, -33.9249)) * 111.32 as distance_km
-- FROM sa_cities 
-- WHERE ST_Distance(ST_Point(longitude, latitude), ST_Point(18.4241, -33.9249)) * 111.32 < 100
-- ORDER BY distance_km;

-- ============================================================================
-- NOTES
-- ============================================================================

-- This reference data provides:
-- 1. All 9 South African provinces with official details
-- 2. 90+ major cities and towns across all provinces
-- 3. Tourist destinations marked for travel-focused features
-- 4. Accurate GPS coordinates for mapping and distance calculations
-- 5. Population data for market sizing

-- Use this data to:
-- - Populate location dropdowns in your application
-- - Validate user-entered locations
-- - Implement location-based search and filtering
-- - Calculate distances between properties and cities
-- - Show nearby attractions and amenities
