import React, { useState, useEffect } from 'react';
import { TrendingUp, TrendingDown, Brain, Calendar, MapPin, Users, Home, Zap, AlertCircle, CheckCircle } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';

interface PricePrediction {
  currentPrice: number;
  predictedPrice: number;
  confidence: number;
  trend: 'up' | 'down' | 'stable';
  factors: Array<{
    name: string;
    impact: number; // -100 to 100
    description: string;
    category: 'location' | 'seasonal' | 'demand' | 'property' | 'market';
  }>;
  recommendations: Array<{
    type: 'increase' | 'decrease' | 'maintain';
    amount: number;
    reason: string;
    urgency: 'low' | 'medium' | 'high';
  }>;
  marketInsights: {
    averagePrice: number;
    competitorCount: number;
    demandLevel: 'low' | 'medium' | 'high';
    seasonalMultiplier: number;
  };
  forecast: Array<{
    date: string;
    price: number;
    confidence: number;
  }>;
}

interface PricePredictionMLProps {
  propertyId: string;
  currentPrice: number;
  location: string;
  propertyType: string;
  amenities: string[];
  capacity: number;
  onPriceUpdate?: (newPrice: number) => void;
  className?: string;
}

export const PricePredictionML: React.FC<PricePredictionMLProps> = ({
  propertyId,
  currentPrice,
  location,
  propertyType,
  amenities,
  capacity,
  onPriceUpdate,
  className
}) => {
  const [prediction, setPrediction] = useState<PricePrediction | null>(null);
  const [loading, setLoading] = useState(false);
  const [selectedTimeframe, setSelectedTimeframe] = useState<'week' | 'month' | 'quarter'>('month');

  useEffect(() => {
    generatePricePrediction();
  }, [propertyId, currentPrice, selectedTimeframe]);

  const generatePricePrediction = async () => {
    setLoading(true);
    
    try {
      // Simulate ML processing delay
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Mock ML-based price prediction
      const mockPrediction: PricePrediction = {
        currentPrice,
        predictedPrice: calculatePredictedPrice(),
        confidence: Math.random() * 0.3 + 0.7, // 70-100% confidence
        trend: determineTrend(),
        factors: generatePriceFactors(),
        recommendations: generateRecommendations(),
        marketInsights: generateMarketInsights(),
        forecast: generateForecast()
      };

      setPrediction(mockPrediction);
    } catch (error) {
      console.error('Price prediction error:', error);
    } finally {
      setLoading(false);
    }
  };

  const calculatePredictedPrice = (): number => {
    let basePrice = currentPrice;
    
    // Location factor
    if (location.toLowerCase().includes('cape town')) basePrice *= 1.15;
    if (location.toLowerCase().includes('camps bay')) basePrice *= 1.25;
    if (location.toLowerCase().includes('waterfront')) basePrice *= 1.2;
    
    // Property type factor
    if (propertyType === 'villa') basePrice *= 1.3;
    if (propertyType === 'apartment') basePrice *= 1.1;
    
    // Amenities factor
    if (amenities.includes('pool')) basePrice *= 1.15;
    if (amenities.includes('ocean_view')) basePrice *= 1.2;
    if (amenities.includes('wifi')) basePrice *= 1.05;
    
    // Seasonal factor (mock)
    const month = new Date().getMonth();
    if (month >= 11 || month <= 2) basePrice *= 1.4; // Summer season
    if (month >= 5 && month <= 8) basePrice *= 0.8; // Winter season
    
    // Add some randomness for realism
    const variance = (Math.random() - 0.5) * 0.2; // ±10%
    basePrice *= (1 + variance);
    
    return Math.round(basePrice);
  };

  const determineTrend = (): 'up' | 'down' | 'stable' => {
    const predicted = calculatePredictedPrice();
    const difference = (predicted - currentPrice) / currentPrice;
    
    if (difference > 0.05) return 'up';
    if (difference < -0.05) return 'down';
    return 'stable';
  };

  const generatePriceFactors = () => {
    return [
      {
        name: 'Location Premium',
        impact: location.toLowerCase().includes('cape town') ? 25 : 10,
        description: 'Prime location increases demand and pricing power',
        category: 'location' as const
      },
      {
        name: 'Seasonal Demand',
        impact: new Date().getMonth() >= 11 || new Date().getMonth() <= 2 ? 35 : -15,
        description: 'Summer season drives higher demand in South Africa',
        category: 'seasonal' as const
      },
      {
        name: 'Property Amenities',
        impact: amenities.length * 3,
        description: 'Premium amenities justify higher pricing',
        category: 'property' as const
      },
      {
        name: 'Market Competition',
        impact: -Math.floor(Math.random() * 20),
        description: 'High competition in the area affects pricing',
        category: 'market' as const
      },
      {
        name: 'Booking Velocity',
        impact: Math.floor(Math.random() * 30) - 10,
        description: 'Recent booking patterns indicate demand trends',
        category: 'demand' as const
      }
    ].sort((a, b) => Math.abs(b.impact) - Math.abs(a.impact));
  };

  const generateRecommendations = () => {
    const predicted = calculatePredictedPrice();
    const difference = predicted - currentPrice;
    
    if (difference > 100) {
      return [
        {
          type: 'increase' as const,
          amount: Math.round(difference * 0.7),
          reason: 'Market conditions support higher pricing',
          urgency: 'high' as const
        }
      ];
    } else if (difference < -100) {
      return [
        {
          type: 'decrease' as const,
          amount: Math.round(Math.abs(difference) * 0.5),
          reason: 'Competitive pricing needed to maintain bookings',
          urgency: 'medium' as const
        }
      ];
    } else {
      return [
        {
          type: 'maintain' as const,
          amount: 0,
          reason: 'Current pricing is optimal for market conditions',
          urgency: 'low' as const
        }
      ];
    }
  };

  const generateMarketInsights = () => {
    return {
      averagePrice: Math.round(currentPrice * (0.8 + Math.random() * 0.4)),
      competitorCount: Math.floor(Math.random() * 50) + 20,
      demandLevel: (['low', 'medium', 'high'] as const)[Math.floor(Math.random() * 3)],
      seasonalMultiplier: new Date().getMonth() >= 11 || new Date().getMonth() <= 2 ? 1.4 : 0.8
    };
  };

  const generateForecast = () => {
    const days = selectedTimeframe === 'week' ? 7 : selectedTimeframe === 'month' ? 30 : 90;
    const forecast = [];
    
    for (let i = 0; i < days; i += Math.ceil(days / 10)) {
      const date = new Date();
      date.setDate(date.getDate() + i);
      
      const basePrice = calculatePredictedPrice();
      const variance = (Math.random() - 0.5) * 0.1; // ±5%
      const price = Math.round(basePrice * (1 + variance));
      
      forecast.push({
        date: date.toISOString().split('T')[0],
        price,
        confidence: Math.random() * 0.2 + 0.8 // 80-100%
      });
    }
    
    return forecast;
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'location': return 'bg-blue-100 text-blue-800';
      case 'seasonal': return 'bg-orange-100 text-orange-800';
      case 'demand': return 'bg-green-100 text-green-800';
      case 'property': return 'bg-purple-100 text-purple-800';
      case 'market': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const handleApplyRecommendation = (recommendation: any) => {
    if (onPriceUpdate) {
      const newPrice = recommendation.type === 'increase' 
        ? currentPrice + recommendation.amount
        : recommendation.type === 'decrease'
        ? currentPrice - recommendation.amount
        : currentPrice;
      
      onPriceUpdate(newPrice);
    }
  };

  if (loading) {
    return (
      <Card className={cn("w-full", className)}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5 text-purple-500 animate-pulse" />
            AI Price Analysis
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="text-center py-8">
              <div className="flex items-center justify-center gap-2 mb-4">
                <Brain className="h-8 w-8 text-purple-500 animate-pulse" />
                <span className="text-lg font-medium">Analyzing market data...</span>
              </div>
              <Progress value={60} className="w-full" />
              <p className="text-sm text-gray-600 mt-2">
                Processing {Math.floor(Math.random() * 1000) + 500} data points
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!prediction) {
    return (
      <Card className={cn("w-full", className)}>
        <CardContent className="p-6 text-center">
          <AlertCircle className="h-12 w-12 mx-auto mb-4 text-gray-400" />
          <p className="text-gray-600">Unable to generate price prediction</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Brain className="h-5 w-5 text-purple-500" />
            AI Price Prediction
          </div>
          <Badge className="bg-purple-100 text-purple-800">
            {Math.round(prediction.confidence * 100)}% confident
          </Badge>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Price Comparison */}
        <div className="grid grid-cols-2 gap-4">
          <div className="text-center p-4 bg-gray-50 rounded-lg">
            <div className="text-sm text-gray-600 mb-1">Current Price</div>
            <div className="text-2xl font-bold">R{currentPrice.toLocaleString()}</div>
          </div>
          
          <div className="text-center p-4 bg-purple-50 rounded-lg">
            <div className="text-sm text-purple-600 mb-1">Predicted Price</div>
            <div className="text-2xl font-bold text-purple-700 flex items-center justify-center gap-1">
              R{prediction.predictedPrice.toLocaleString()}
              {prediction.trend === 'up' && <TrendingUp className="h-5 w-5 text-green-500" />}
              {prediction.trend === 'down' && <TrendingDown className="h-5 w-5 text-red-500" />}
            </div>
          </div>
        </div>

        {/* Price Factors */}
        <div>
          <h4 className="font-semibold mb-3 flex items-center gap-2">
            <Zap className="h-4 w-4" />
            Price Factors
          </h4>
          <div className="space-y-3">
            {prediction.factors.map((factor, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="font-medium">{factor.name}</span>
                    <Badge className={getCategoryColor(factor.category)} variant="secondary">
                      {factor.category}
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-600">{factor.description}</p>
                </div>
                
                <div className="text-right">
                  <div className={cn(
                    "font-semibold",
                    factor.impact > 0 ? "text-green-600" : factor.impact < 0 ? "text-red-600" : "text-gray-600"
                  )}>
                    {factor.impact > 0 ? '+' : ''}{factor.impact}%
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Recommendations */}
        <div>
          <h4 className="font-semibold mb-3 flex items-center gap-2">
            <CheckCircle className="h-4 w-4" />
            AI Recommendations
          </h4>
          <div className="space-y-3">
            {prediction.recommendations.map((rec, index) => (
              <div key={index} className="p-4 border rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <span className="font-medium capitalize">{rec.type} Price</span>
                    <Badge className={getUrgencyColor(rec.urgency)}>
                      {rec.urgency} priority
                    </Badge>
                  </div>
                  {rec.amount > 0 && (
                    <span className="font-semibold text-lg">
                      {rec.type === 'increase' ? '+' : '-'}R{rec.amount}
                    </span>
                  )}
                </div>
                <p className="text-sm text-gray-600 mb-3">{rec.reason}</p>
                <Button 
                  onClick={() => handleApplyRecommendation(rec)}
                  size="sm"
                  variant={rec.urgency === 'high' ? 'default' : 'outline'}
                >
                  Apply Recommendation
                </Button>
              </div>
            ))}
          </div>
        </div>

        {/* Market Insights */}
        <div>
          <h4 className="font-semibold mb-3">Market Insights</h4>
          <div className="grid grid-cols-2 gap-4">
            <div className="p-3 bg-blue-50 rounded-lg">
              <div className="text-sm text-blue-600 mb-1">Market Average</div>
              <div className="font-semibold">R{prediction.marketInsights.averagePrice.toLocaleString()}</div>
            </div>
            
            <div className="p-3 bg-green-50 rounded-lg">
              <div className="text-sm text-green-600 mb-1">Demand Level</div>
              <div className="font-semibold capitalize">{prediction.marketInsights.demandLevel}</div>
            </div>
            
            <div className="p-3 bg-orange-50 rounded-lg">
              <div className="text-sm text-orange-600 mb-1">Competitors</div>
              <div className="font-semibold">{prediction.marketInsights.competitorCount} properties</div>
            </div>
            
            <div className="p-3 bg-purple-50 rounded-lg">
              <div className="text-sm text-purple-600 mb-1">Seasonal Factor</div>
              <div className="font-semibold">{prediction.marketInsights.seasonalMultiplier}x</div>
            </div>
          </div>
        </div>

        {/* Forecast Timeline */}
        <div>
          <div className="flex items-center justify-between mb-3">
            <h4 className="font-semibold">Price Forecast</h4>
            <div className="flex gap-1">
              {(['week', 'month', 'quarter'] as const).map((timeframe) => (
                <Button
                  key={timeframe}
                  variant={selectedTimeframe === timeframe ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedTimeframe(timeframe)}
                >
                  {timeframe === 'week' ? '7 days' : timeframe === 'month' ? '30 days' : '90 days'}
                </Button>
              ))}
            </div>
          </div>
          
          <div className="space-y-2">
            {prediction.forecast.map((point, index) => (
              <div key={index} className="flex items-center justify-between p-2 hover:bg-gray-50 rounded">
                <span className="text-sm">{new Date(point.date).toLocaleDateString()}</span>
                <div className="flex items-center gap-2">
                  <span className="font-medium">R{point.price.toLocaleString()}</span>
                  <div className="w-2 h-2 rounded-full bg-green-400" 
                       style={{ opacity: point.confidence }} />
                </div>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
