import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  MapPin, 
  Phone, 
  Mail, 
  Facebook, 
  Twitter, 
  Instagram, 
  Linkedin,
  Heart,
  Star,
  Award,
  Shield,
  Zap,
  Globe,
  ArrowUp,
  Send
} from 'lucide-react';
import { 
  SlideIn, 
  HoverAnimation, 
  StaggeredAnimation 
} from '@/components/ui/page-transitions';

export const ModernFooter: React.FC = () => {
  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const footerLinks = {
    company: [
      { label: 'About Us', href: '/about' },
      { label: 'Our Story', href: '/story' },
      { label: 'Careers', href: '/careers' },
      { label: 'Press', href: '/press' },
      { label: 'Blog', href: '/blog' }
    ],
    support: [
      { label: 'Help Center', href: '/help' },
      { label: 'Contact Us', href: '/contact' },
      { label: 'Safety', href: '/safety' },
      { label: 'Cancellation', href: '/cancellation' },
      { label: 'Community', href: '/community' }
    ],
    hosting: [
      { label: 'List Your Property', href: '/host' },
      { label: 'Host Resources', href: '/host-resources' },
      { label: 'Host Community', href: '/host-community' },
      { label: 'Responsible Hosting', href: '/responsible-hosting' },
      { label: 'Host Protection', href: '/host-protection' }
    ],
    legal: [
      { label: 'Terms of Service', href: '/terms' },
      { label: 'Privacy Policy', href: '/privacy' },
      { label: 'Cookie Policy', href: '/cookies' },
      { label: 'Accessibility', href: '/accessibility' },
      { label: 'Sitemap', href: '/sitemap' }
    ]
  };

  const socialLinks = [
    { icon: Facebook, href: 'https://facebook.com/stayfinder', label: 'Facebook' },
    { icon: Twitter, href: 'https://twitter.com/stayfinder', label: 'Twitter' },
    { icon: Instagram, href: 'https://instagram.com/stayfinder', label: 'Instagram' },
    { icon: Linkedin, href: 'https://linkedin.com/company/stayfinder', label: 'LinkedIn' }
  ];

  const stats = [
    { icon: Star, value: '4.8/5', label: 'Average Rating' },
    { icon: Shield, value: '100%', label: 'Secure Booking' },
    { icon: Award, value: '#1', label: 'Top Rated Platform' },
    { icon: Heart, value: '10k+', label: 'Happy Guests' }
  ];

  return (
    <footer className="bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-0 left-0 w-full h-full bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.1"%3E%3Ccircle cx="30" cy="30" r="2"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')]"></div>
      </div>

      {/* Newsletter Section */}
      <SlideIn direction="up" delay={100}>
        <div className="relative border-b border-gray-700">
          <div className="container mx-auto px-4 py-16">
            <div className="max-w-4xl mx-auto text-center">
              <div className="inline-flex items-center gap-2 bg-sea-green-500/20 backdrop-blur-sm border border-sea-green-500/30 rounded-full px-6 py-2 mb-6">
                <Zap className="h-4 w-4 text-sea-green-400" />
                <span className="text-sm font-medium text-sea-green-300">Stay Updated</span>
              </div>
              
              <h2 className="text-4xl md:text-5xl font-bold mb-4 bg-gradient-to-r from-white via-gray-100 to-white bg-clip-text text-transparent">
                Never Miss a Perfect Stay
              </h2>
              <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
                Get exclusive deals, new property alerts, and travel inspiration delivered to your inbox
              </p>
              
              <div className="flex flex-col md:flex-row gap-4 max-w-md mx-auto">
                <div className="flex-1">
                  <input
                    type="email"
                    placeholder="Enter your email address"
                    className="w-full px-6 py-4 rounded-2xl bg-white/10 backdrop-blur-sm border border-white/20 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-sea-green-500 focus:border-transparent"
                  />
                </div>
                <HoverAnimation type="scale">
                  <Button className="bg-gradient-to-r from-sea-green-500 to-ocean-blue-500 hover:from-sea-green-600 hover:to-ocean-blue-600 text-white px-8 py-4 rounded-2xl font-semibold shadow-lg">
                    <Send className="h-5 w-5 mr-2" />
                    Subscribe
                  </Button>
                </HoverAnimation>
              </div>
            </div>
          </div>
        </div>
      </SlideIn>

      {/* Stats Section */}
      <SlideIn direction="up" delay={200}>
        <div className="relative border-b border-gray-700">
          <div className="container mx-auto px-4 py-12">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
              <StaggeredAnimation delay={100}>
                {stats.map((stat, index) => {
                  const Icon = stat.icon;
                  return (
                    <HoverAnimation key={index} type="lift">
                      <div className="text-center group">
                        <div className="w-16 h-16 bg-gradient-to-br from-sea-green-500/20 to-ocean-blue-500/20 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                          <Icon className="h-8 w-8 text-sea-green-400" />
                        </div>
                        <div className="text-2xl font-bold text-white mb-1">{stat.value}</div>
                        <div className="text-sm text-gray-400">{stat.label}</div>
                      </div>
                    </HoverAnimation>
                  );
                })}
              </StaggeredAnimation>
            </div>
          </div>
        </div>
      </SlideIn>

      {/* Main Footer Content */}
      <SlideIn direction="up" delay={300}>
        <div className="relative">
          <div className="container mx-auto px-4 py-16">
            <div className="grid grid-cols-1 lg:grid-cols-6 gap-12">
              {/* Brand Section */}
              <div className="lg:col-span-2">
                <div className="mb-6">
                  <h3 className="text-2xl font-bold bg-gradient-to-r from-sea-green-400 to-ocean-blue-400 bg-clip-text text-transparent mb-4">
                    StayFinder
                  </h3>
                  <p className="text-gray-300 leading-relaxed mb-6">
                    Discover unique accommodations across South Africa's most beautiful destinations. 
                    From Cape Town's coastline to Kruger's wilderness, find your perfect getaway.
                  </p>
                  
                  <div className="flex items-center gap-2 mb-4">
                    <Badge className="bg-green-500/20 text-green-400 border-green-500/30">
                      <Shield className="h-3 w-3 mr-1" />
                      Verified Platform
                    </Badge>
                    <Badge className="bg-blue-500/20 text-blue-400 border-blue-500/30">
                      <Award className="h-3 w-3 mr-1" />
                      Top Rated
                    </Badge>
                  </div>
                </div>

                {/* Contact Info */}
                <div className="space-y-3 mb-6">
                  <div className="flex items-center gap-3 text-gray-300">
                    <MapPin className="h-4 w-4 text-sea-green-400" />
                    <span>Cape Town, South Africa</span>
                  </div>
                  <div className="flex items-center gap-3 text-gray-300">
                    <Phone className="h-4 w-4 text-sea-green-400" />
                    <span>+27 21 123 4567</span>
                  </div>
                  <div className="flex items-center gap-3 text-gray-300">
                    <Mail className="h-4 w-4 text-sea-green-400" />
                    <span><EMAIL></span>
                  </div>
                </div>

                {/* Social Links */}
                <div className="flex gap-3">
                  {socialLinks.map((social, index) => {
                    const Icon = social.icon;
                    return (
                      <HoverAnimation key={index} type="scale">
                        <a
                          href={social.href}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="w-10 h-10 bg-white/10 hover:bg-white/20 rounded-xl flex items-center justify-center transition-all duration-300 group"
                          aria-label={social.label}
                        >
                          <Icon className="h-5 w-5 text-gray-400 group-hover:text-white" />
                        </a>
                      </HoverAnimation>
                    );
                  })}
                </div>
              </div>

              {/* Links Sections */}
              <div className="lg:col-span-4 grid grid-cols-2 md:grid-cols-4 gap-8">
                <div>
                  <h4 className="font-semibold text-white mb-4">Company</h4>
                  <ul className="space-y-3">
                    {footerLinks.company.map((link, index) => (
                      <li key={index}>
                        <a
                          href={link.href}
                          className="text-gray-400 hover:text-white transition-colors duration-200"
                        >
                          {link.label}
                        </a>
                      </li>
                    ))}
                  </ul>
                </div>

                <div>
                  <h4 className="font-semibold text-white mb-4">Support</h4>
                  <ul className="space-y-3">
                    {footerLinks.support.map((link, index) => (
                      <li key={index}>
                        <a
                          href={link.href}
                          className="text-gray-400 hover:text-white transition-colors duration-200"
                        >
                          {link.label}
                        </a>
                      </li>
                    ))}
                  </ul>
                </div>

                <div>
                  <h4 className="font-semibold text-white mb-4">Hosting</h4>
                  <ul className="space-y-3">
                    {footerLinks.hosting.map((link, index) => (
                      <li key={index}>
                        <a
                          href={link.href}
                          className="text-gray-400 hover:text-white transition-colors duration-200"
                        >
                          {link.label}
                        </a>
                      </li>
                    ))}
                  </ul>
                </div>

                <div>
                  <h4 className="font-semibold text-white mb-4">Legal</h4>
                  <ul className="space-y-3">
                    {footerLinks.legal.map((link, index) => (
                      <li key={index}>
                        <a
                          href={link.href}
                          className="text-gray-400 hover:text-white transition-colors duration-200"
                        >
                          {link.label}
                        </a>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </SlideIn>

      {/* Bottom Bar */}
      <SlideIn direction="up" delay={400}>
        <div className="relative border-t border-gray-700">
          <div className="container mx-auto px-4 py-6">
            <div className="flex flex-col md:flex-row justify-between items-center gap-4">
              <div className="flex items-center gap-4 text-gray-400 text-sm">
                <span>© 2024 StayFinder. All rights reserved.</span>
                <div className="flex items-center gap-1">
                  <Globe className="h-4 w-4" />
                  <span>English (ZA)</span>
                </div>
              </div>
              
              <HoverAnimation type="scale">
                <Button
                  onClick={scrollToTop}
                  size="sm"
                  variant="outline"
                  className="border-gray-600 text-gray-400 hover:text-white hover:border-gray-500"
                >
                  <ArrowUp className="h-4 w-4 mr-2" />
                  Back to Top
                </Button>
              </HoverAnimation>
            </div>
          </div>
        </div>
      </SlideIn>
    </footer>
  );
};
