#!/usr/bin/env node

/**
 * Test RLS Policies for StayFinder
 * Verifies that Row Level Security policies work correctly
 */

import { createClient } from '@supabase/supabase-js';
import { config } from 'dotenv';
config();

// ANSI color codes
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, colors.green);
}

function logError(message) {
  log(`❌ ${message}`, colors.red);
}

function logWarning(message) {
  log(`⚠️  ${message}`, colors.yellow);
}

function logInfo(message) {
  log(`ℹ️  ${message}`, colors.blue);
}

// Initialize Supabase admin client
const supabaseAdmin = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

// Test RLS policies
async function testRLSPolicies() {
  log('🔒 TESTING RLS POLICIES', colors.bright);
  log('='.repeat(50), colors.cyan);
  
  const results = {
    functionsExist: false,
    rlsEnabled: false,
    policiesActive: false,
    testsPassed: 0,
    totalTests: 0
  };
  
  try {
    // Test 1: Check if helper functions exist
    logInfo('Testing helper functions...');
    
    const functionTests = [
      'public.is_admin()',
      'public.is_host()',
      'public.current_user_id()'
    ];
    
    let functionsWorking = 0;
    
    for (const func of functionTests) {
      try {
        const { data, error } = await supabaseAdmin.rpc('exec_sql', {
          sql: `SELECT ${func};`
        });
        
        if (!error) {
          functionsWorking++;
          logSuccess(`Function ${func} exists and callable`);
        } else {
          logWarning(`Function ${func} issue: ${error.message}`);
        }
      } catch (err) {
        logWarning(`Function ${func} test failed: ${err.message}`);
      }
    }
    
    results.functionsExist = functionsWorking === functionTests.length;
    results.totalTests += functionTests.length;
    results.testsPassed += functionsWorking;
    
    // Test 2: Check if RLS is enabled on tables
    logInfo('Checking RLS status on tables...');
    
    const tables = [
      'users', 'properties', 'bookings', 'reviews', 
      'messages', 'notifications', 'user_preferences'
    ];
    
    let rlsEnabledCount = 0;
    
    for (const table of tables) {
      try {
        const { data, error } = await supabaseAdmin
          .from('pg_tables')
          .select('rowsecurity')
          .eq('tablename', table)
          .eq('schemaname', 'public')
          .single();
        
        if (!error && data?.rowsecurity) {
          rlsEnabledCount++;
          logSuccess(`RLS enabled on ${table}`);
        } else {
          logWarning(`RLS not enabled on ${table}`);
        }
      } catch (err) {
        logWarning(`Could not check RLS for ${table}: ${err.message}`);
      }
    }
    
    results.rlsEnabled = rlsEnabledCount > 0;
    results.totalTests += tables.length;
    results.testsPassed += rlsEnabledCount;
    
    // Test 3: Check if policies exist
    logInfo('Checking if policies exist...');
    
    try {
      const { data, error } = await supabaseAdmin
        .from('pg_policies')
        .select('policyname, tablename')
        .eq('schemaname', 'public');
      
      if (!error && data && data.length > 0) {
        results.policiesActive = true;
        results.testsPassed++;
        logSuccess(`${data.length} RLS policies found`);
        
        // Group policies by table
        const policiesByTable = {};
        data.forEach(policy => {
          if (!policiesByTable[policy.tablename]) {
            policiesByTable[policy.tablename] = [];
          }
          policiesByTable[policy.tablename].push(policy.policyname);
        });
        
        // Show policy summary
        Object.keys(policiesByTable).forEach(table => {
          logInfo(`  ${table}: ${policiesByTable[table].length} policies`);
        });
      } else {
        logWarning('No RLS policies found');
      }
      
      results.totalTests++;
    } catch (err) {
      logError(`Could not check policies: ${err.message}`);
      results.totalTests++;
    }
    
    // Test 4: Test basic auth.uid() function
    logInfo('Testing auth.uid() function...');
    
    try {
      const { data, error } = await supabaseAdmin.rpc('exec_sql', {
        sql: 'SELECT auth.uid() IS NOT NULL as has_auth_function;'
      });
      
      if (!error) {
        logSuccess('auth.uid() function is accessible');
        results.testsPassed++;
      } else {
        logWarning(`auth.uid() test failed: ${error.message}`);
      }
      
      results.totalTests++;
    } catch (err) {
      logWarning(`auth.uid() test error: ${err.message}`);
      results.totalTests++;
    }
    
  } catch (error) {
    logError(`RLS testing failed: ${error.message}`);
  }
  
  return results;
}

// Generate test report
async function generateTestReport(results) {
  log('\n' + '='.repeat(50), colors.cyan);
  log('🔒 RLS POLICIES TEST REPORT', colors.bright);
  log('='.repeat(50), colors.cyan);
  
  // Overall status
  if (results.functionsExist) {
    logSuccess('Helper Functions: ALL WORKING');
  } else {
    logWarning('Helper Functions: SOME ISSUES');
  }
  
  if (results.rlsEnabled) {
    logSuccess('Row Level Security: ENABLED');
  } else {
    logError('Row Level Security: NOT ENABLED');
  }
  
  if (results.policiesActive) {
    logSuccess('Security Policies: ACTIVE');
  } else {
    logError('Security Policies: NOT FOUND');
  }
  
  // Test summary
  const passRate = results.totalTests > 0 ? (results.testsPassed / results.totalTests * 100).toFixed(1) : 0;
  
  log(`\n📊 Test Summary:`, colors.cyan);
  logInfo(`Tests Passed: ${results.testsPassed}/${results.totalTests} (${passRate}%)`);
  
  // Recommendations
  log('\n📋 Recommendations:', colors.blue);
  
  if (!results.functionsExist) {
    logWarning('1. Re-run create-rls-policies.sql to create helper functions');
  }
  
  if (!results.rlsEnabled) {
    logWarning('2. Enable RLS on tables using: ALTER TABLE table_name ENABLE ROW LEVEL SECURITY;');
  }
  
  if (!results.policiesActive) {
    logWarning('3. Create RLS policies by running the complete create-rls-policies.sql file');
  }
  
  if (results.functionsExist && results.rlsEnabled && results.policiesActive) {
    logSuccess('✨ RLS is properly configured and ready for use!');
    log('\n🎯 Next Steps:', colors.blue);
    log('1. Test with actual user authentication', colors.blue);
    log('2. Verify policies work with different user roles', colors.blue);
    log('3. Test data access restrictions', colors.blue);
  } else {
    logWarning('⚠️  RLS needs additional configuration');
    log('\n🔧 Fix Steps:', colors.yellow);
    log('1. Run create-rls-policies.sql in Supabase SQL Editor', colors.yellow);
    log('2. Verify all functions are created successfully', colors.yellow);
    log('3. Check that RLS is enabled on all tables', colors.yellow);
    log('4. Re-run this test to verify fixes', colors.yellow);
  }
  
  return results;
}

// Main function
async function main() {
  const results = await testRLSPolicies();
  await generateTestReport(results);
  
  const success = results.functionsExist && results.rlsEnabled && results.policiesActive;
  process.exit(success ? 0 : 1);
}

// Run the test
main().catch(error => {
  logError(`RLS test error: ${error.message}`);
  console.error(error);
  process.exit(1);
});
