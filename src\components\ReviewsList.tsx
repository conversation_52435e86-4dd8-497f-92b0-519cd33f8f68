import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ReviewCard } from './ReviewCard';
import { ReviewForm } from './ReviewForm';
import { RatingDisplay, RatingBreakdown } from './StarRating';
import { useReviews } from '../contexts/ReviewsContext';
import { useAuth } from '../contexts/AuthContext';
import { 
  Loader2, 
  MessageSquare, 
  Plus,
  Filter,
  SortAsc,
  SortDesc,
  Star
} from 'lucide-react';

interface ReviewsListProps {
  propertyId: string;
  propertyTitle?: string;
  userBookingId?: string; // If user has a booking, they can write a review
  showWriteReview?: boolean;
}

export const ReviewsList: React.FC<ReviewsListProps> = ({
  propertyId,
  propertyTitle,
  userBookingId,
  showWriteReview = false
}) => {
  const [showReviewForm, setShowReviewForm] = useState(false);
  const [editingReview, setEditingReview] = useState<any>(null);
  const [sortBy, setSortBy] = useState<'newest' | 'oldest' | 'highest' | 'lowest'>('newest');
  const [filterRating, setFilterRating] = useState<number | null>(null);
  const [propertyStats, setPropertyStats] = useState<any>(null);

  const { 
    propertyReviews, 
    getPropertyReviews, 
    getPropertyStats,
    canUserReview,
    loading, 
    error 
  } = useReviews();
  
  const { isAuthenticated, user } = useAuth();

  const [canWriteReview, setCanWriteReview] = useState(false);

  useEffect(() => {
    if (propertyId) {
      getPropertyReviews(propertyId);
      getPropertyStats(propertyId).then(setPropertyStats).catch(console.error);
    }
  }, [propertyId, getPropertyReviews, getPropertyStats]);

  useEffect(() => {
    if (isAuthenticated && userBookingId) {
      canUserReview(propertyId, userBookingId).then(setCanWriteReview);
    }
  }, [isAuthenticated, propertyId, userBookingId, canUserReview]);

  const handleWriteReview = () => {
    setShowReviewForm(true);
    setEditingReview(null);
  };

  const handleEditReview = (review: any) => {
    setEditingReview(review);
    setShowReviewForm(true);
  };

  const handleReviewSuccess = () => {
    setShowReviewForm(false);
    setEditingReview(null);
    // Refresh reviews and stats
    getPropertyReviews(propertyId);
    getPropertyStats(propertyId).then(setPropertyStats).catch(console.error);
  };

  const handleCancelReview = () => {
    setShowReviewForm(false);
    setEditingReview(null);
  };

  const handleDeleteReview = () => {
    // Refresh reviews and stats after deletion
    getPropertyReviews(propertyId);
    getPropertyStats(propertyId).then(setPropertyStats).catch(console.error);
  };

  const getSortedAndFilteredReviews = () => {
    let filtered = [...propertyReviews];

    // Filter by rating
    if (filterRating !== null) {
      filtered = filtered.filter(review => review.rating === filterRating);
    }

    // Sort reviews
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'newest':
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        case 'oldest':
          return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
        case 'highest':
          return b.rating - a.rating;
        case 'lowest':
          return a.rating - b.rating;
        default:
          return 0;
      }
    });

    return filtered;
  };

  const filteredReviews = getSortedAndFilteredReviews();
  const userReview = propertyReviews.find(review => review.guestId === user?.id);

  return (
    <div className="space-y-6">
      {/* Reviews Header with Stats */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            Reviews
            {propertyReviews.length > 0 && (
              <span className="text-gray-500 font-normal">
                ({propertyReviews.length})
              </span>
            )}
          </CardTitle>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* Overall Rating */}
          {propertyStats && propertyStats.averageRating > 0 && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="text-center md:text-left">
                <div className="flex items-center justify-center md:justify-start gap-2 mb-2">
                  <span className="text-3xl font-bold text-gray-900">
                    {propertyStats.averageRating.toFixed(1)}
                  </span>
                  <Star className="h-6 w-6 fill-yellow-400 text-yellow-400" />
                </div>
                <p className="text-gray-600">
                  Based on {propertyStats.totalReviews} review{propertyStats.totalReviews !== 1 ? 's' : ''}
                </p>
              </div>
              
              {/* Rating Breakdown */}
              {propertyStats.ratingBreakdown && (
                <RatingBreakdown
                  ratings={propertyStats.ratingBreakdown}
                  totalReviews={propertyStats.totalReviews}
                />
              )}
            </div>
          )}

          {/* Write Review Button */}
          {showWriteReview && isAuthenticated && canWriteReview && !userReview && (
            <div className="text-center">
              <Button
                onClick={handleWriteReview}
                className="bg-sea-green-500 hover:bg-sea-green-600 text-white"
              >
                <Plus className="mr-2 h-4 w-4" />
                Write a Review
              </Button>
            </div>
          )}

          {/* User's existing review notice */}
          {userReview && (
            <div className="bg-blue-50 p-4 rounded-lg">
              <p className="text-blue-800 text-sm">
                You have already reviewed this property. You can edit or delete your review below.
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Review Form */}
      {showReviewForm && (
        <ReviewForm
          propertyId={propertyId}
          bookingId={userBookingId || ''}
          propertyTitle={propertyTitle}
          existingReview={editingReview}
          onSuccess={handleReviewSuccess}
          onCancel={handleCancelReview}
        />
      )}

      {/* Reviews List */}
      {propertyReviews.length > 0 && (
        <Card>
          <CardHeader>
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <h3 className="text-lg font-semibold">All Reviews</h3>
              
              {/* Filters and Sorting */}
              <div className="flex flex-wrap gap-2">
                {/* Rating Filter */}
                <select
                  value={filterRating || ''}
                  onChange={(e) => setFilterRating(e.target.value ? parseInt(e.target.value) : null)}
                  className="px-3 py-1 border border-gray-300 rounded text-sm"
                >
                  <option value="">All Ratings</option>
                  <option value="5">5 Stars</option>
                  <option value="4">4 Stars</option>
                  <option value="3">3 Stars</option>
                  <option value="2">2 Stars</option>
                  <option value="1">1 Star</option>
                </select>

                {/* Sort Options */}
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value as any)}
                  className="px-3 py-1 border border-gray-300 rounded text-sm"
                >
                  <option value="newest">Newest First</option>
                  <option value="oldest">Oldest First</option>
                  <option value="highest">Highest Rated</option>
                  <option value="lowest">Lowest Rated</option>
                </select>
              </div>
            </div>
          </CardHeader>

          <CardContent>
            {/* Loading State */}
            {loading && (
              <div className="text-center py-8">
                <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-sea-green-600" />
                <p className="text-gray-600">Loading reviews...</p>
              </div>
            )}

            {/* Error State */}
            {error && (
              <div className="text-center py-8">
                <p className="text-red-600 mb-4">{error}</p>
                <Button onClick={() => getPropertyReviews(propertyId)} variant="outline">
                  Try Again
                </Button>
              </div>
            )}

            {/* Reviews */}
            {!loading && !error && (
              <div className="space-y-4">
                {filteredReviews.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    {filterRating ? (
                      <p>No reviews found with {filterRating} star{filterRating !== 1 ? 's' : ''}.</p>
                    ) : (
                      <p>No reviews yet. Be the first to review this property!</p>
                    )}
                  </div>
                ) : (
                  filteredReviews.map((review) => (
                    <ReviewCard
                      key={review.id}
                      review={review}
                      showActions={review.guestId === user?.id}
                      onEdit={handleEditReview}
                      onDelete={handleDeleteReview}
                    />
                  ))
                )}
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* No Reviews State */}
      {!loading && !error && propertyReviews.length === 0 && (
        <Card>
          <CardContent className="text-center py-12">
            <MessageSquare className="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Reviews Yet</h3>
            <p className="text-gray-600 mb-4">
              This property hasn't received any reviews yet.
            </p>
            {showWriteReview && isAuthenticated && canWriteReview && (
              <Button
                onClick={handleWriteReview}
                className="bg-sea-green-500 hover:bg-sea-green-600 text-white"
              >
                <Plus className="mr-2 h-4 w-4" />
                Be the First to Review
              </Button>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
};
