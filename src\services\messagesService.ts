interface Conversation {
  id: string;
  property_id: string;
  host_id: string;
  guest_id: string;
  booking_id?: string;
  status: 'active' | 'archived' | 'blocked';
  last_message_at?: string;
  created_at: string;
  property_title: string;
  property_images: string[];
  other_user_name: string;
  other_user_avatar?: string;
  other_user_id: string;
  unread_count: number;
  last_message?: string;
  last_message_type?: 'text' | 'image' | 'file' | 'system' | 'booking_update';
  is_host: boolean;
}

interface Message {
  id: string;
  conversation_id: string;
  sender_id: string;
  message_type: 'text' | 'image' | 'file' | 'system' | 'booking_update';
  content: string;
  attachment_url?: string;
  attachment_name?: string;
  attachment_size?: number;
  is_read: boolean;
  read_at?: string;
  created_at: string;
  sender_name: string;
  profile_picture?: string;
  is_own_message: boolean;
}

interface CreateConversationData {
  property_id: string;
  host_id: string;
  guest_id: string;
  booking_id?: string;
}

interface SendMessageData {
  conversation_id: string;
  content: string;
  message_type?: 'text' | 'image' | 'file' | 'system' | 'booking_update';
  attachment_url?: string;
  attachment_name?: string;
  attachment_size?: number;
}

interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

class MessagesService {
  private baseUrl = 'http://localhost/stayfinder/api/messages.php';

  private async makeRequest<T>(url: string, options: RequestInit = {}): Promise<ApiResponse<T>> {
    try {
      const token = localStorage.getItem('auth_token');
      const headers: HeadersInit = {
        'Content-Type': 'application/json',
        ...options.headers,
      };

      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const response = await fetch(url, {
        ...options,
        headers,
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Messages API error:', error);
      throw error;
    }
  }

  async getConversations(options: {
    limit?: number;
    offset?: number;
  } = {}): Promise<Conversation[]> {
    try {
      const params = new URLSearchParams({
        action: 'conversations',
        ...(options.limit && { limit: options.limit.toString() }),
        ...(options.offset && { offset: options.offset.toString() }),
      });

      const response = await this.makeRequest<Conversation[]>(`${this.baseUrl}?${params}`);
      
      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to fetch conversations');
      }
    } catch (error) {
      console.error('Error fetching conversations:', error);
      // Return mock data for testing
      return this.getMockConversations();
    }
  }

  async getMessages(conversationId: string, options: {
    limit?: number;
    offset?: number;
  } = {}): Promise<Message[]> {
    try {
      const params = new URLSearchParams({
        action: 'messages',
        conversation_id: conversationId,
        ...(options.limit && { limit: options.limit.toString() }),
        ...(options.offset && { offset: options.offset.toString() }),
      });

      const response = await this.makeRequest<Message[]>(`${this.baseUrl}?${params}`);
      
      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to fetch messages');
      }
    } catch (error) {
      console.error('Error fetching messages:', error);
      // Return mock data for testing
      return this.getMockMessages(conversationId);
    }
  }

  async createConversation(conversationData: CreateConversationData): Promise<string> {
    try {
      const params = new URLSearchParams({ action: 'create_conversation' });
      
      const response = await this.makeRequest<{ conversation_id: string }>(`${this.baseUrl}?${params}`, {
        method: 'POST',
        body: JSON.stringify(conversationData),
      });
      
      if (response.success && response.data) {
        return response.data.conversation_id;
      } else {
        throw new Error(response.error || 'Failed to create conversation');
      }
    } catch (error) {
      console.error('Error creating conversation:', error);
      throw error;
    }
  }

  async sendMessage(messageData: SendMessageData): Promise<string> {
    try {
      const params = new URLSearchParams({ action: 'send_message' });
      
      const response = await this.makeRequest<{ message_id: string }>(`${this.baseUrl}?${params}`, {
        method: 'POST',
        body: JSON.stringify(messageData),
      });
      
      if (response.success && response.data) {
        return response.data.message_id;
      } else {
        throw new Error(response.error || 'Failed to send message');
      }
    } catch (error) {
      console.error('Error sending message:', error);
      throw error;
    }
  }

  async markMessagesAsRead(conversationId: string): Promise<number> {
    try {
      const params = new URLSearchParams({ 
        action: 'mark_read',
        conversation_id: conversationId 
      });
      
      const response = await this.makeRequest<{ updated_count: number }>(`${this.baseUrl}?${params}`, {
        method: 'PUT',
      });
      
      if (response.success && response.data) {
        return response.data.updated_count;
      } else {
        throw new Error(response.error || 'Failed to mark messages as read');
      }
    } catch (error) {
      console.error('Error marking messages as read:', error);
      throw error;
    }
  }

  // Helper methods
  formatMessageTime(dateString: string): string {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMs = now.getTime() - date.getTime();
    const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
    const diffInHours = Math.floor(diffInMinutes / 60);
    const diffInDays = Math.floor(diffInHours / 24);
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInHours < 24) return `${diffInHours}h ago`;
    if (diffInDays < 7) return `${diffInDays}d ago`;
    
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    });
  }

  formatLastMessageTime(dateString: string): string {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMs = now.getTime() - date.getTime();
    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));
    
    if (diffInDays === 0) {
      return date.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
      });
    } else if (diffInDays < 7) {
      return date.toLocaleDateString('en-US', { weekday: 'short' });
    } else {
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric'
      });
    }
  }

  truncateMessage(message: string, maxLength: number = 50): string {
    if (message.length <= maxLength) return message;
    return message.substring(0, maxLength) + '...';
  }

  getMessageTypeIcon(messageType: string): string {
    switch (messageType) {
      case 'image': return '📷';
      case 'file': return '📎';
      case 'system': return '🔔';
      case 'booking_update': return '📅';
      default: return '';
    }
  }

  // Mock data for testing
  getMockConversations(): Conversation[] {
    return [
      {
        id: 'conv-1',
        property_id: 'prop-1',
        host_id: 'host-1',
        guest_id: 'guest-1',
        status: 'active',
        last_message_at: '2024-06-21T10:30:00Z',
        created_at: '2024-06-20T09:00:00Z',
        property_title: 'Luxury Beachfront Villa in Margate',
        property_images: ['https://images.unsplash.com/photo-1721322800607-8c38375eef04?w=400&h=300&fit=crop'],
        other_user_name: 'Sarah Johnson',
        other_user_avatar: '/placeholder.svg',
        other_user_id: 'guest-1',
        unread_count: 1,
        last_message: 'Great! The villa has a private pool, full kitchen, WiFi, and parking.',
        last_message_type: 'text',
        is_host: true
      },
      {
        id: 'conv-2',
        property_id: 'prop-2',
        host_id: 'host-1',
        guest_id: 'guest-2',
        booking_id: 'booking-1',
        status: 'active',
        last_message_at: '2024-06-21T14:15:00Z',
        created_at: '2024-06-19T16:30:00Z',
        property_title: 'Mountain Retreat Cabin',
        property_images: ['https://images.unsplash.com/photo-1566073771259-6a8506099945?w=400&h=300&fit=crop'],
        other_user_name: 'Mike Wilson',
        other_user_id: 'guest-2',
        unread_count: 0,
        last_message: 'Welcome! I\'m excited to host you at the mountain cabin.',
        last_message_type: 'text',
        is_host: true
      }
    ];
  }

  getMockMessages(conversationId: string): Message[] {
    const baseMessages = [
      {
        id: 'msg-1',
        conversation_id: conversationId,
        sender_id: 'guest-1',
        message_type: 'text' as const,
        content: 'Hi! I\'m interested in booking your beautiful villa for next weekend. Is it available?',
        is_read: true,
        read_at: '2024-06-20T09:15:00Z',
        created_at: '2024-06-20T09:00:00Z',
        sender_name: 'Sarah Johnson',
        profile_picture: '/placeholder.svg',
        is_own_message: false
      },
      {
        id: 'msg-2',
        conversation_id: conversationId,
        sender_id: 'host-1',
        message_type: 'text' as const,
        content: 'Hello! Thank you for your interest. Yes, the villa is available for next weekend. I\'d be happy to help you with the booking. How many guests will be staying?',
        is_read: true,
        read_at: '2024-06-20T10:00:00Z',
        created_at: '2024-06-20T09:30:00Z',
        sender_name: 'Jane Smith',
        is_own_message: true
      },
      {
        id: 'msg-3',
        conversation_id: conversationId,
        sender_id: 'guest-1',
        message_type: 'text' as const,
        content: 'Perfect! We\'ll be 4 adults. Could you tell me more about the amenities and check-in process?',
        is_read: true,
        read_at: '2024-06-20T11:00:00Z',
        created_at: '2024-06-20T10:30:00Z',
        sender_name: 'Sarah Johnson',
        profile_picture: '/placeholder.svg',
        is_own_message: false
      },
      {
        id: 'msg-4',
        conversation_id: conversationId,
        sender_id: 'host-1',
        message_type: 'text' as const,
        content: 'Great! The villa has a private pool, full kitchen, WiFi, and parking. Check-in is at 3 PM and I\'ll meet you personally to show you around. The beach is just a 2-minute walk away!',
        is_read: false,
        created_at: '2024-06-21T10:30:00Z',
        sender_name: 'Jane Smith',
        is_own_message: true
      }
    ];

    return baseMessages;
  }
}

export const messagesService = new MessagesService();
export type { Conversation, Message, CreateConversationData, SendMessageData, ApiResponse };
