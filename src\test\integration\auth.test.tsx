import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, userEvent, waitFor, setupCommonMocks } from '@/test/utils/test-utils';
import { server } from '@/test/setup';
import { http, HttpResponse } from 'msw';
import App from '@/App';

describe('Authentication Integration Tests', () => {
  beforeEach(() => {
    setupCommonMocks();
    localStorage.clear();
    sessionStorage.clear();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('allows user to register successfully', async () => {
    const user = userEvent.setup();
    
    render(<App />);
    
    // Navigate to register
    const registerLink = screen.getByRole('link', { name: /sign up/i });
    await user.click(registerLink);
    
    // Fill registration form
    const emailInput = screen.getByLabelText(/email/i);
    const passwordInput = screen.getByLabelText(/password/i);
    const confirmPasswordInput = screen.getByLabelText(/confirm password/i);
    const firstNameInput = screen.getByLabelText(/first name/i);
    const lastNameInput = screen.getByLabelText(/last name/i);
    
    await user.type(emailInput, '<EMAIL>');
    await user.type(passwordInput, 'password123');
    await user.type(confirmPasswordInput, 'password123');
    await user.type(firstNameInput, 'John');
    await user.type(lastNameInput, 'Doe');
    
    // Submit form
    const submitButton = screen.getByRole('button', { name: /sign up/i });
    await user.click(submitButton);
    
    // Should redirect to dashboard or show success message
    await waitFor(() => {
      expect(screen.getByText(/welcome/i) || screen.getByText(/dashboard/i)).toBeInTheDocument();
    });
  });

  it('shows validation errors for invalid registration', async () => {
    const user = userEvent.setup();
    
    render(<App />);
    
    // Navigate to register
    const registerLink = screen.getByRole('link', { name: /sign up/i });
    await user.click(registerLink);
    
    // Submit form without filling required fields
    const submitButton = screen.getByRole('button', { name: /sign up/i });
    await user.click(submitButton);
    
    // Should show validation errors
    await waitFor(() => {
      expect(screen.getByText(/email is required/i)).toBeInTheDocument();
      expect(screen.getByText(/password is required/i)).toBeInTheDocument();
    });
  });

  it('allows user to login successfully', async () => {
    const user = userEvent.setup();
    
    render(<App />);
    
    // Navigate to login
    const loginLink = screen.getByRole('link', { name: /sign in/i });
    await user.click(loginLink);
    
    // Fill login form
    const emailInput = screen.getByLabelText(/email/i);
    const passwordInput = screen.getByLabelText(/password/i);
    
    await user.type(emailInput, '<EMAIL>');
    await user.type(passwordInput, 'password123');
    
    // Submit form
    const submitButton = screen.getByRole('button', { name: /sign in/i });
    await user.click(submitButton);
    
    // Should redirect to dashboard
    await waitFor(() => {
      expect(screen.getByText(/dashboard/i) || screen.getByText(/welcome/i)).toBeInTheDocument();
    });
  });

  it('shows error for invalid login credentials', async () => {
    const user = userEvent.setup();
    
    // Mock failed login response
    server.use(
      http.post('/api/auth/login', () => {
        return HttpResponse.json(
          { error: 'Invalid credentials' },
          { status: 401 }
        );
      })
    );
    
    render(<App />);
    
    // Navigate to login
    const loginLink = screen.getByRole('link', { name: /sign in/i });
    await user.click(loginLink);
    
    // Fill login form with invalid credentials
    const emailInput = screen.getByLabelText(/email/i);
    const passwordInput = screen.getByLabelText(/password/i);
    
    await user.type(emailInput, '<EMAIL>');
    await user.type(passwordInput, 'wrongpassword');
    
    // Submit form
    const submitButton = screen.getByRole('button', { name: /sign in/i });
    await user.click(submitButton);
    
    // Should show error message
    await waitFor(() => {
      expect(screen.getByText(/invalid credentials/i)).toBeInTheDocument();
    });
  });

  it('allows user to logout', async () => {
    const user = userEvent.setup();
    
    // Mock authenticated state
    localStorage.setItem('auth_token', 'mock-token');
    
    render(<App />);
    
    // Should show user menu
    const userMenu = screen.getByRole('button', { name: /user menu/i });
    await user.click(userMenu);
    
    // Click logout
    const logoutButton = screen.getByRole('button', { name: /logout/i });
    await user.click(logoutButton);
    
    // Should redirect to home page
    await waitFor(() => {
      expect(screen.getByRole('link', { name: /sign in/i })).toBeInTheDocument();
    });
    
    // Token should be removed
    expect(localStorage.getItem('auth_token')).toBeNull();
  });

  it('redirects unauthenticated users to login', async () => {
    render(<App />);
    
    // Try to access protected route
    window.history.pushState({}, '', '/dashboard');
    
    // Should redirect to login
    await waitFor(() => {
      expect(screen.getByText(/sign in/i)).toBeInTheDocument();
    });
  });

  it('persists authentication state on page refresh', async () => {
    // Mock authenticated state
    localStorage.setItem('auth_token', 'mock-token');
    
    render(<App />);
    
    // Should maintain authenticated state
    await waitFor(() => {
      expect(screen.getByText(/dashboard/i) || screen.getByRole('button', { name: /user menu/i })).toBeInTheDocument();
    });
  });

  it('handles token expiration', async () => {
    const user = userEvent.setup();
    
    // Mock expired token response
    server.use(
      http.get('/api/auth/me', () => {
        return HttpResponse.json(
          { error: 'Token expired' },
          { status: 401 }
        );
      })
    );
    
    localStorage.setItem('auth_token', 'expired-token');
    
    render(<App />);
    
    // Should redirect to login
    await waitFor(() => {
      expect(screen.getByText(/sign in/i)).toBeInTheDocument();
    });
    
    // Token should be removed
    expect(localStorage.getItem('auth_token')).toBeNull();
  });

  it('shows loading state during authentication', async () => {
    const user = userEvent.setup();
    
    // Mock slow login response
    server.use(
      http.post('/api/auth/login', async () => {
        await new Promise(resolve => setTimeout(resolve, 1000));
        return HttpResponse.json({
          user: { id: '1', email: '<EMAIL>' },
          token: 'mock-token'
        });
      })
    );
    
    render(<App />);
    
    // Navigate to login
    const loginLink = screen.getByRole('link', { name: /sign in/i });
    await user.click(loginLink);
    
    // Fill and submit form
    const emailInput = screen.getByLabelText(/email/i);
    const passwordInput = screen.getByLabelText(/password/i);
    
    await user.type(emailInput, '<EMAIL>');
    await user.type(passwordInput, 'password123');
    
    const submitButton = screen.getByRole('button', { name: /sign in/i });
    await user.click(submitButton);
    
    // Should show loading state
    expect(screen.getByText(/signing in/i) || screen.getByTestId('loading-spinner')).toBeInTheDocument();
  });

  it('handles password reset flow', async () => {
    const user = userEvent.setup();
    
    render(<App />);
    
    // Navigate to login
    const loginLink = screen.getByRole('link', { name: /sign in/i });
    await user.click(loginLink);
    
    // Click forgot password
    const forgotPasswordLink = screen.getByRole('link', { name: /forgot password/i });
    await user.click(forgotPasswordLink);
    
    // Fill email for password reset
    const emailInput = screen.getByLabelText(/email/i);
    await user.type(emailInput, '<EMAIL>');
    
    // Submit reset request
    const submitButton = screen.getByRole('button', { name: /reset password/i });
    await user.click(submitButton);
    
    // Should show success message
    await waitFor(() => {
      expect(screen.getByText(/password reset email sent/i)).toBeInTheDocument();
    });
  });

  it('validates password strength during registration', async () => {
    const user = userEvent.setup();
    
    render(<App />);
    
    // Navigate to register
    const registerLink = screen.getByRole('link', { name: /sign up/i });
    await user.click(registerLink);
    
    // Enter weak password
    const passwordInput = screen.getByLabelText(/password/i);
    await user.type(passwordInput, '123');
    
    // Should show password strength indicator
    expect(screen.getByText(/password too weak/i) || screen.getByTestId('password-strength')).toBeInTheDocument();
  });

  it('handles social authentication', async () => {
    const user = userEvent.setup();
    
    render(<App />);
    
    // Navigate to login
    const loginLink = screen.getByRole('link', { name: /sign in/i });
    await user.click(loginLink);
    
    // Click Google sign in
    const googleButton = screen.getByRole('button', { name: /continue with google/i });
    await user.click(googleButton);
    
    // Should initiate OAuth flow (implementation dependent)
    expect(googleButton).toBeInTheDocument();
  });
});
