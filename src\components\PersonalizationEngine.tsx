import React, { useState, useEffect } from 'react';
import { User, Heart, MapPin, Calendar, DollarSign, Star, TrendingUp, Settings, Eye, EyeOff } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { toast } from '@/components/ui/use-toast';
import { cn } from '@/lib/utils';

interface UserPreferences {
  budget: {
    min: number;
    max: number;
    currency: string;
  };
  locations: string[];
  propertyTypes: string[];
  amenities: string[];
  travelStyle: 'budget' | 'mid-range' | 'luxury' | 'mixed';
  groupSize: number;
  bookingLead: number; // days in advance
  stayDuration: {
    min: number;
    max: number;
  };
  accessibility: string[];
  dietaryRestrictions: string[];
}

interface UserBehavior {
  searchHistory: Array<{
    query: string;
    location: string;
    date: Date;
    filters: Record<string, any>;
  }>;
  viewedProperties: Array<{
    propertyId: string;
    viewTime: number;
    date: Date;
  }>;
  bookingHistory: Array<{
    propertyId: string;
    location: string;
    price: number;
    rating: number;
    date: Date;
  }>;
  wishlistItems: string[];
  interactions: Array<{
    type: 'click' | 'share' | 'save' | 'compare';
    target: string;
    date: Date;
  }>;
}

interface PersonalizationSettings {
  enableRecommendations: boolean;
  enablePriceAlerts: boolean;
  enableLocationSuggestions: boolean;
  enableBehaviorTracking: boolean;
  dataRetention: number; // days
  privacyLevel: 'minimal' | 'balanced' | 'full';
}

interface PersonalizationEngineProps {
  userId: string;
  onPreferencesUpdate: (preferences: UserPreferences) => void;
  onSettingsUpdate: (settings: PersonalizationSettings) => void;
  className?: string;
}

export const PersonalizationEngine: React.FC<PersonalizationEngineProps> = ({
  userId,
  onPreferencesUpdate,
  onSettingsUpdate,
  className
}) => {
  const [preferences, setPreferences] = useState<UserPreferences>({
    budget: { min: 500, max: 2000, currency: 'ZAR' },
    locations: ['Cape Town', 'Johannesburg'],
    propertyTypes: ['apartment', 'house'],
    amenities: ['wifi', 'parking'],
    travelStyle: 'mid-range',
    groupSize: 2,
    bookingLead: 14,
    stayDuration: { min: 2, max: 7 },
    accessibility: [],
    dietaryRestrictions: []
  });

  const [settings, setSettings] = useState<PersonalizationSettings>({
    enableRecommendations: true,
    enablePriceAlerts: true,
    enableLocationSuggestions: true,
    enableBehaviorTracking: true,
    dataRetention: 365,
    privacyLevel: 'balanced'
  });

  const [behavior, setBehavior] = useState<UserBehavior>({
    searchHistory: [],
    viewedProperties: [],
    bookingHistory: [],
    wishlistItems: [],
    interactions: []
  });

  const [insights, setInsights] = useState({
    favoriteLocations: ['Cape Town', 'Stellenbosch'],
    averageStayDuration: 4,
    preferredPriceRange: [800, 1500],
    topAmenities: ['WiFi', 'Pool', 'Parking'],
    bookingPatterns: 'Weekend getaways',
    seasonalPreferences: 'Summer coastal, Winter inland'
  });

  const propertyTypes = [
    { value: 'apartment', label: 'Apartment' },
    { value: 'house', label: 'House' },
    { value: 'villa', label: 'Villa' },
    { value: 'cottage', label: 'Cottage' },
    { value: 'guesthouse', label: 'Guesthouse' },
    { value: 'hotel', label: 'Hotel' }
  ];

  const amenities = [
    { value: 'wifi', label: 'WiFi' },
    { value: 'parking', label: 'Parking' },
    { value: 'pool', label: 'Swimming Pool' },
    { value: 'kitchen', label: 'Kitchen' },
    { value: 'aircon', label: 'Air Conditioning' },
    { value: 'pets', label: 'Pet Friendly' },
    { value: 'gym', label: 'Gym' },
    { value: 'spa', label: 'Spa' }
  ];

  const updatePreference = <K extends keyof UserPreferences>(
    key: K,
    value: UserPreferences[K]
  ) => {
    const updated = { ...preferences, [key]: value };
    setPreferences(updated);
    onPreferencesUpdate(updated);
  };

  const updateSetting = <K extends keyof PersonalizationSettings>(
    key: K,
    value: PersonalizationSettings[K]
  ) => {
    const updated = { ...settings, [key]: value };
    setSettings(updated);
    onSettingsUpdate(updated);
  };

  const generateRecommendations = () => {
    // Mock recommendation generation based on preferences and behavior
    const recommendations = [
      {
        type: 'location',
        title: 'Hermanus',
        reason: 'Based on your coastal preferences',
        confidence: 0.85
      },
      {
        type: 'property',
        title: 'Luxury Apartment in V&A Waterfront',
        reason: 'Matches your budget and amenity preferences',
        confidence: 0.92
      },
      {
        type: 'timing',
        title: 'Book 2 weeks in advance',
        reason: 'Best prices for your preferred dates',
        confidence: 0.78
      }
    ];

    return recommendations;
  };

  const exportData = () => {
    const data = {
      preferences,
      behavior: settings.privacyLevel === 'full' ? behavior : {},
      insights,
      exportDate: new Date().toISOString()
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `stayfinder-data-${userId}.json`;
    a.click();
    URL.revokeObjectURL(url);

    toast({
      title: "Data exported",
      description: "Your personalization data has been downloaded",
    });
  };

  const clearData = () => {
    setBehavior({
      searchHistory: [],
      viewedProperties: [],
      bookingHistory: [],
      wishlistItems: [],
      interactions: []
    });

    toast({
      title: "Data cleared",
      description: "Your behavioral data has been cleared",
    });
  };

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Personalization Settings
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-600 mb-4">
            Customize your StayFinder experience with personalized recommendations and preferences.
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-sea-green-600">{behavior.searchHistory.length}</div>
              <div className="text-sm text-gray-600">Searches Tracked</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-sea-green-600">{behavior.viewedProperties.length}</div>
              <div className="text-sm text-gray-600">Properties Viewed</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-sea-green-600">{behavior.bookingHistory.length}</div>
              <div className="text-sm text-gray-600">Bookings Made</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Privacy Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5" />
            Privacy & Data Control
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <label className="font-medium">Enable Personalized Recommendations</label>
              <p className="text-sm text-gray-600">Get property suggestions based on your preferences</p>
            </div>
            <Switch
              checked={settings.enableRecommendations}
              onCheckedChange={(checked) => updateSetting('enableRecommendations', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <label className="font-medium">Price Alerts</label>
              <p className="text-sm text-gray-600">Notify when prices drop for saved properties</p>
            </div>
            <Switch
              checked={settings.enablePriceAlerts}
              onCheckedChange={(checked) => updateSetting('enablePriceAlerts', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <label className="font-medium">Behavior Tracking</label>
              <p className="text-sm text-gray-600">Track browsing to improve recommendations</p>
            </div>
            <Switch
              checked={settings.enableBehaviorTracking}
              onCheckedChange={(checked) => updateSetting('enableBehaviorTracking', checked)}
            />
          </div>

          <div>
            <label className="block font-medium mb-2">Privacy Level</label>
            <Select 
              value={settings.privacyLevel} 
              onValueChange={(value) => updateSetting('privacyLevel', value as any)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="minimal">Minimal - Basic preferences only</SelectItem>
                <SelectItem value="balanced">Balanced - Preferences + anonymous analytics</SelectItem>
                <SelectItem value="full">Full - All features enabled</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex gap-2">
            <Button variant="outline" onClick={exportData}>
              Export My Data
            </Button>
            <Button variant="outline" onClick={clearData}>
              Clear Behavioral Data
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Travel Preferences */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MapPin className="h-5 w-5" />
            Travel Preferences
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Budget Range */}
          <div>
            <label className="block font-medium mb-3">Budget Range (per night)</label>
            <div className="px-3">
              <Slider
                value={[preferences.budget.min, preferences.budget.max]}
                onValueChange={([min, max]) => 
                  updatePreference('budget', { ...preferences.budget, min, max })
                }
                max={5000}
                min={100}
                step={50}
                className="mb-4"
              />
              <div className="flex justify-between text-sm text-gray-600">
                <span>R{preferences.budget.min.toLocaleString()}</span>
                <span>R{preferences.budget.max.toLocaleString()}</span>
              </div>
            </div>
          </div>

          <Separator />

          {/* Travel Style */}
          <div>
            <label className="block font-medium mb-3">Travel Style</label>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
              {['budget', 'mid-range', 'luxury', 'mixed'].map((style) => (
                <Button
                  key={style}
                  variant={preferences.travelStyle === style ? 'default' : 'outline'}
                  onClick={() => updatePreference('travelStyle', style as any)}
                  className="capitalize"
                >
                  {style.replace('-', ' ')}
                </Button>
              ))}
            </div>
          </div>

          <Separator />

          {/* Property Types */}
          <div>
            <label className="block font-medium mb-3">Preferred Property Types</label>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
              {propertyTypes.map((type) => (
                <Button
                  key={type.value}
                  variant={preferences.propertyTypes.includes(type.value) ? 'default' : 'outline'}
                  onClick={() => {
                    const updated = preferences.propertyTypes.includes(type.value)
                      ? preferences.propertyTypes.filter(t => t !== type.value)
                      : [...preferences.propertyTypes, type.value];
                    updatePreference('propertyTypes', updated);
                  }}
                  size="sm"
                >
                  {type.label}
                </Button>
              ))}
            </div>
          </div>

          <Separator />

          {/* Amenities */}
          <div>
            <label className="block font-medium mb-3">Important Amenities</label>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
              {amenities.map((amenity) => (
                <Button
                  key={amenity.value}
                  variant={preferences.amenities.includes(amenity.value) ? 'default' : 'outline'}
                  onClick={() => {
                    const updated = preferences.amenities.includes(amenity.value)
                      ? preferences.amenities.filter(a => a !== amenity.value)
                      : [...preferences.amenities, amenity.value];
                    updatePreference('amenities', updated);
                  }}
                  size="sm"
                >
                  {amenity.label}
                </Button>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Insights */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Your Travel Insights
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-2">Favorite Locations</h4>
              <div className="flex flex-wrap gap-1">
                {insights.favoriteLocations.map((location) => (
                  <Badge key={location} variant="secondary">{location}</Badge>
                ))}
              </div>
            </div>

            <div>
              <h4 className="font-medium mb-2">Top Amenities</h4>
              <div className="flex flex-wrap gap-1">
                {insights.topAmenities.map((amenity) => (
                  <Badge key={amenity} variant="secondary">{amenity}</Badge>
                ))}
              </div>
            </div>

            <div>
              <h4 className="font-medium mb-2">Booking Pattern</h4>
              <p className="text-sm text-gray-600">{insights.bookingPatterns}</p>
            </div>

            <div>
              <h4 className="font-medium mb-2">Seasonal Preferences</h4>
              <p className="text-sm text-gray-600">{insights.seasonalPreferences}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Recommendations */}
      {settings.enableRecommendations && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Star className="h-5 w-5" />
              Personalized Recommendations
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {generateRecommendations().map((rec, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <h4 className="font-medium">{rec.title}</h4>
                    <p className="text-sm text-gray-600">{rec.reason}</p>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="text-sm text-gray-500">
                      {Math.round(rec.confidence * 100)}% match
                    </div>
                    <div 
                      className="w-3 h-3 rounded-full"
                      style={{ backgroundColor: `hsl(${rec.confidence * 120}, 70%, 50%)` }}
                    />
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
