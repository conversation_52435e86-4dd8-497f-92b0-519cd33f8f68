-- ============================================================================
-- StayFinder Database Seed Data - All South Africa
-- PostgreSQL/Supabase Compatible
-- Version: 1.0
-- Created: July 16, 2025
-- Coverage: All 9 provinces of South Africa
-- ============================================================================

-- ============================================================================
-- AMENITIES SEED DATA
-- ============================================================================

INSERT INTO amenities (name, description, category, icon, sort_order) VALUES
-- Basic amenities
('WiFi', 'Wireless internet access', 'basic', 'wifi', 1),
('Air Conditioning', 'Climate control system', 'basic', 'ac', 2),
('Heating', 'Heating system for cold weather', 'basic', 'heating', 3),
('Hot Water', '24/7 hot water supply', 'basic', 'hot-water', 4),
('Electricity', 'Reliable electricity supply', 'basic', 'electricity', 5),
('Bed Linen', 'Clean bed sheets and pillowcases', 'basic', 'bed-linen', 6),
('Towels', 'Bath and hand towels provided', 'basic', 'towels', 7),
('Toiletries', 'Basic toiletries provided', 'basic', 'toiletries', 8),

-- Kitchen amenities
('Full Kitchen', 'Complete kitchen with all appliances', 'kitchen', 'kitchen', 10),
('Kitchenette', 'Basic kitchen facilities', 'kitchen', 'kitchenette', 11),
('Refrigerator', 'Full-size refrigerator', 'kitchen', 'fridge', 12),
('Microwave', 'Microwave oven', 'kitchen', 'microwave', 13),
('Stove/Oven', 'Cooking stove and oven', 'kitchen', 'stove', 14),
('Dishwasher', 'Automatic dishwasher', 'kitchen', 'dishwasher', 15),
('Coffee Maker', 'Coffee making facilities', 'kitchen', 'coffee', 16),
('Kettle', 'Electric kettle', 'kitchen', 'kettle', 17),
('Toaster', 'Bread toaster', 'kitchen', 'toaster', 18),
('Cookware', 'Pots, pans, and cooking utensils', 'kitchen', 'cookware', 19),
('Dishes & Cutlery', 'Plates, bowls, glasses, and cutlery', 'kitchen', 'dishes', 20),

-- Bathroom amenities
('Private Bathroom', 'Dedicated private bathroom', 'bathroom', 'bathroom', 25),
('Shared Bathroom', 'Shared bathroom facilities', 'bathroom', 'shared-bathroom', 26),
('Bathtub', 'Bathtub for relaxing baths', 'bathroom', 'bathtub', 27),
('Shower', 'Shower facilities', 'bathroom', 'shower', 28),
('Hair Dryer', 'Hair drying facilities', 'bathroom', 'hair-dryer', 29),

-- Entertainment amenities
('TV', 'Television with local channels', 'entertainment', 'tv', 35),
('Cable/Satellite TV', 'Extended TV channels', 'entertainment', 'cable-tv', 36),
('Netflix', 'Netflix streaming access', 'entertainment', 'netflix', 37),
('Sound System', 'Music and audio system', 'entertainment', 'sound-system', 38),
('Books & Games', 'Reading materials and games', 'entertainment', 'books-games', 39),
('Board Games', 'Family board games', 'entertainment', 'board-games', 40),

-- Outdoor amenities
('Balcony', 'Private balcony or terrace', 'outdoor', 'balcony', 45),
('Patio', 'Outdoor patio area', 'outdoor', 'patio', 46),
('Garden', 'Garden or yard access', 'outdoor', 'garden', 47),
('BBQ/Braai', 'Barbecue facilities', 'outdoor', 'bbq', 48),
('Outdoor Furniture', 'Outdoor seating and tables', 'outdoor', 'outdoor-furniture', 49),
('Pool', 'Swimming pool access', 'outdoor', 'pool', 50),
('Beach Access', 'Direct beach access', 'outdoor', 'beach', 51),
('Sea View', 'Ocean or sea view', 'outdoor', 'sea-view', 52),
('Mountain View', 'Mountain view', 'outdoor', 'mountain-view', 53),
('Parking', 'Dedicated parking space', 'outdoor', 'parking', 54),
('Garage', 'Covered garage parking', 'outdoor', 'garage', 55),

-- Safety amenities
('Smoke Detector', 'Smoke detection system', 'safety', 'smoke-detector', 60),
('Carbon Monoxide Detector', 'CO detection system', 'safety', 'co-detector', 61),
('Fire Extinguisher', 'Fire safety equipment', 'safety', 'fire-extinguisher', 62),
('First Aid Kit', 'Basic first aid supplies', 'safety', 'first-aid', 63),
('Security System', 'Property security system', 'safety', 'security', 64),
('Safe', 'In-room safe for valuables', 'safety', 'safe', 65),
('Gated Community', 'Secure gated community', 'safety', 'gated', 66),

-- Accessibility amenities
('Wheelchair Accessible', 'Wheelchair accessible entrance and facilities', 'accessibility', 'wheelchair', 70),
('Step-Free Access', 'No steps to entrance', 'accessibility', 'step-free', 71),
('Wide Doorways', 'Doorways suitable for wheelchairs', 'accessibility', 'wide-doors', 72),
('Accessible Bathroom', 'Bathroom with accessibility features', 'accessibility', 'accessible-bathroom', 73),
('Grab Rails', 'Safety grab rails in bathroom', 'accessibility', 'grab-rails', 74);

-- ============================================================================
-- SAMPLE USERS (FOR DEVELOPMENT/TESTING) - ALL PROVINCES
-- ============================================================================

-- Note: In production, users will be created through Supabase Auth
-- These are sample users for development and testing purposes across all SA provinces

INSERT INTO users (
    id, email, first_name, last_name, phone, user_type, is_verified, is_active,
    city, province, country, bio, created_at
) VALUES
-- System Admin
(
    gen_random_uuid(),
    '<EMAIL>',
    'System',
    'Administrator',
    '+27123456789',
    'admin',
    true,
    true,
    'Cape Town',
    'Western Cape',
    'South Africa',
    'System administrator for StayFinder platform covering all of South Africa',
    NOW()
),
-- Western Cape Host
(
    gen_random_uuid(),
    '<EMAIL>',
    'Sarah',
    'Johnson',
    '+***********',
    'host',
    true,
    true,
    'Cape Town',
    'Western Cape',
    'South Africa',
    'Experienced host with properties in Cape Town and surrounding areas. Specializing in luxury accommodations with mountain and ocean views.',
    NOW()
),
-- Gauteng Host
(
    gen_random_uuid(),
    '<EMAIL>',
    'Michael',
    'Smith',
    '+***********',
    'host',
    true,
    true,
    'Johannesburg',
    'Gauteng',
    'South Africa',
    'Business-focused host offering corporate and leisure accommodations in Johannesburg and Pretoria.',
    NOW()
),
-- KwaZulu-Natal Host
(
    gen_random_uuid(),
    '<EMAIL>',
    'Priya',
    'Patel',
    '+***********',
    'host',
    true,
    true,
    'Durban',
    'KwaZulu-Natal',
    'South Africa',
    'Coastal property specialist with beachfront and city accommodations throughout KZN.',
    NOW()
),
-- Eastern Cape Host
(
    gen_random_uuid(),
    '<EMAIL>',
    'David',
    'Williams',
    '+***********',
    'host',
    true,
    true,
    'Port Elizabeth',
    'Eastern Cape',
    'South Africa',
    'Adventure tourism host offering properties near game reserves and coastal attractions.',
    NOW()
),
-- Free State Host
(
    gen_random_uuid(),
    '<EMAIL>',
    'Maria',
    'van der Merwe',
    '+***********',
    'host',
    true,
    true,
    'Bloemfontein',
    'Free State',
    'South Africa',
    'Rural and city accommodation specialist in the heart of South Africa.',
    NOW()
),
-- Mpumalanga Host
(
    gen_random_uuid(),
    '<EMAIL>',
    'James',
    'Mthembu',
    '+27137890123',
    'host',
    true,
    true,
    'Nelspruit',
    'Mpumalanga',
    'South Africa',
    'Safari and nature lodge host near Kruger National Park and scenic routes.',
    NOW()
),
-- Limpopo Host
(
    gen_random_uuid(),
    '<EMAIL>',
    'Anna',
    'Botha',
    '+27154567890',
    'host',
    true,
    true,
    'Polokwane',
    'Limpopo',
    'South Africa',
    'Cultural and wildlife tourism host offering authentic South African experiences.',
    NOW()
),
-- North West Host
(
    gen_random_uuid(),
    '<EMAIL>',
    'Peter',
    'Molefe',
    '+27187890123',
    'host',
    true,
    true,
    'Rustenburg',
    'North West',
    'South Africa',
    'Mining town and casino resort area host with diverse accommodation options.',
    NOW()
),
-- Northern Cape Host
(
    gen_random_uuid(),
    '<EMAIL>',
    'Susan',
    'Adams',
    '+27534567890',
    'host',
    true,
    true,
    'Kimberley',
    'Northern Cape',
    'South Africa',
    'Desert and diamond route specialist offering unique Karoo experiences.',
    NOW()
),
-- Sample Guests from different provinces
(
    gen_random_uuid(),
    '<EMAIL>',
    'Emma',
    'Wilson',
    '+27789123456',
    'guest',
    true,
    true,
    'Johannesburg',
    'Gauteng',
    'South Africa',
    'Travel enthusiast who loves exploring all of South Africa from coast to mountains.',
    NOW()
),
(
    gen_random_uuid(),
    '<EMAIL>',
    'Thabo',
    'Ndlovu',
    '+27823456789',
    'guest',
    true,
    true,
    'Cape Town',
    'Western Cape',
    'South Africa',
    'Adventure seeker and photographer documenting the beauty of South Africa.',
    NOW()
),
(
    gen_random_uuid(),
    '<EMAIL>',
    'Lisa',
    'Roberts',
    '+27845678901',
    'guest',
    true,
    true,
    'Durban',
    'KwaZulu-Natal',
    'South Africa',
    'Beach lover and cultural explorer enjoying South African hospitality.',
    NOW()
);

-- ============================================================================
-- SAMPLE PROPERTIES (FOR DEVELOPMENT/TESTING) - ALL PROVINCES
-- ============================================================================

-- Get host IDs for sample properties across all provinces
DO $$
DECLARE
    host_wc_id UUID;
    host_gp_id UUID;
    host_kzn_id UUID;
    host_ec_id UUID;
    host_fs_id UUID;
    host_mp_id UUID;
    host_lp_id UUID;
    host_nw_id UUID;
    host_nc_id UUID;
    property1_id UUID;
    property2_id UUID;
    property3_id UUID;
    property4_id UUID;
    property5_id UUID;
    property6_id UUID;
    property7_id UUID;
    property8_id UUID;
    property9_id UUID;
BEGIN
    -- Get host IDs from all provinces
    SELECT id INTO host_wc_id FROM users WHERE email = '<EMAIL>';
    SELECT id INTO host_gp_id FROM users WHERE email = '<EMAIL>';
    SELECT id INTO host_kzn_id FROM users WHERE email = '<EMAIL>';
    SELECT id INTO host_ec_id FROM users WHERE email = '<EMAIL>';
    SELECT id INTO host_fs_id FROM users WHERE email = '<EMAIL>';
    SELECT id INTO host_mp_id FROM users WHERE email = '<EMAIL>';
    SELECT id INTO host_lp_id FROM users WHERE email = '<EMAIL>';
    SELECT id INTO host_nw_id FROM users WHERE email = '<EMAIL>';
    SELECT id INTO host_nc_id FROM users WHERE email = '<EMAIL>';
    
    -- Insert sample properties from all provinces
    INSERT INTO properties (
        id, host_id, title, description, property_type,
        address_line1, city, province, postal_code, country,
        latitude, longitude, max_guests, bedrooms, bathrooms, beds,
        price_per_night, cleaning_fee, minimum_stay_nights,
        check_in_time, check_out_time, cancellation_policy,
        instant_book, status, featured
    ) VALUES
    -- Western Cape Properties
    (
        gen_random_uuid(),
        host_wc_id,
        'Luxury Villa with Table Mountain Views',
        'Stunning luxury villa in Camps Bay with breathtaking views of Table Mountain and the Atlantic Ocean. This 5-bedroom villa features a private pool, wine cellar, and world-class amenities. Perfect for discerning guests seeking the ultimate Cape Town experience with easy access to beaches, restaurants, and attractions.',
        'villa',
        '15 Victoria Road',
        'Cape Town',
        'Western Cape',
        '8005',
        'South Africa',
        -33.9249,
        18.4241,
        10,
        5,
        4.0,
        8,
        4500.00,
        800.00,
        3,
        '15:00',
        '11:00',
        'moderate',
        true,
        'active',
        true
    ),
    -- Gauteng Properties
    (
        gen_random_uuid(),
        host_gp_id,
        'Executive Apartment in Sandton',
        'Modern executive apartment in the heart of Sandton business district. This sophisticated 2-bedroom apartment offers luxury amenities, high-speed WiFi, and stunning city views. Ideal for business travelers and urban explorers. Walking distance to Sandton City Mall and Gautrain station.',
        'apartment',
        '88 Maude Street',
        'Johannesburg',
        'Gauteng',
        '2196',
        'South Africa',
        -26.1076,
        28.0567,
        4,
        2,
        2.0,
        3,
        2200.00,
        400.00,
        1,
        '14:00',
        '11:00',
        'flexible',
        true,
        'active',
        true
    ),
    -- KwaZulu-Natal Properties
    (
        gen_random_uuid(),
        host_kzn_id,
        'Beachfront Villa with Ocean Views',
        'Experience the ultimate coastal getaway in this luxurious beachfront villa in Umhlanga. Wake up to breathtaking ocean views and fall asleep to the sound of waves. This spacious 4-bedroom villa features modern amenities, a private pool, and direct beach access. Perfect for families or groups.',
        'villa',
        '123 Marine Drive',
        'Durban',
        'KwaZulu-Natal',
        '4319',
        'South Africa',
        -29.7674,
        31.0448,
        8,
        4,
        3.0,
        6,
        2800.00,
        500.00,
        2,
        '15:00',
        '11:00',
        'moderate',
        true,
        'active',
        true
    ),
    -- Eastern Cape Properties
    (
        gen_random_uuid(),
        host_ec_id,
        'Safari Lodge near Addo Elephant Park',
        'Authentic African safari lodge experience just minutes from Addo Elephant National Park. This 3-bedroom lodge offers luxury tented accommodation with game viewing deck, traditional boma, and guided safari options. Perfect for wildlife enthusiasts and adventure seekers.',
        'house',
        '45 Safari Road',
        'Port Elizabeth',
        'Eastern Cape',
        '6001',
        'South Africa',
        -33.7963,
        25.4829,
        6,
        3,
        2.0,
        4,
        3200.00,
        600.00,
        2,
        '14:00',
        '10:00',
        'strict',
        false,
        'active',
        true
    ),
    -- Free State Properties
    (
        gen_random_uuid(),
        host_fs_id,
        'Historic Guesthouse in Bloemfontein',
        'Charming historic guesthouse in the heart of Bloemfontein. This beautifully restored Victorian house offers comfortable accommodation with period features and modern amenities. Perfect for exploring the judicial capital and surrounding attractions.',
        'guesthouse',
        '67 Church Street',
        'Bloemfontein',
        'Free State',
        '9300',
        'South Africa',
        -29.0852,
        26.1596,
        8,
        4,
        3.0,
        6,
        1800.00,
        300.00,
        1,
        '14:00',
        '10:00',
        'flexible',
        false,
        'active',
        false
    ),
    -- Mpumalanga Properties
    (
        gen_random_uuid(),
        host_mp_id,
        'Kruger Park Safari Lodge',
        'Exclusive safari lodge on the border of Kruger National Park. This luxury 4-bedroom lodge offers unparalleled wildlife viewing opportunities with private game drives, spa services, and gourmet dining. The ultimate African safari experience.',
        'house',
        '12 Kruger Gate Road',
        'Nelspruit',
        'Mpumalanga',
        '1200',
        'South Africa',
        -25.4753,
        30.9820,
        8,
        4,
        3.0,
        6,
        5500.00,
        1000.00,
        3,
        '15:00',
        '11:00',
        'strict',
        false,
        'active',
        true
    ),
    -- Limpopo Properties
    (
        gen_random_uuid(),
        host_lp_id,
        'Cultural Village Experience Lodge',
        'Authentic cultural experience lodge offering traditional accommodation and cultural activities. Learn about local customs, enjoy traditional cuisine, and experience the warmth of African hospitality. Perfect for cultural tourism and educational visits.',
        'guesthouse',
        '34 Heritage Road',
        'Polokwane',
        'Limpopo',
        '0699',
        'South Africa',
        -23.9045,
        29.4689,
        12,
        6,
        4.0,
        8,
        2500.00,
        400.00,
        2,
        '14:00',
        '10:00',
        'moderate',
        false,
        'active',
        false
    ),
    -- North West Properties
    (
        gen_random_uuid(),
        host_nw_id,
        'Resort Apartment near Sun City',
        'Luxury resort apartment near the famous Sun City entertainment complex. This modern 3-bedroom apartment offers resort amenities including pools, golf course access, and casino entertainment. Perfect for leisure and entertainment seekers.',
        'apartment',
        '56 Resort Boulevard',
        'Rustenburg',
        'North West',
        '0300',
        'South Africa',
        -25.6336,
        27.2449,
        6,
        3,
        2.0,
        4,
        2800.00,
        500.00,
        2,
        '15:00',
        '11:00',
        'moderate',
        true,
        'active',
        false
    ),
    -- Northern Cape Properties
    (
        gen_random_uuid(),
        host_nc_id,
        'Desert Retreat in the Karoo',
        'Unique desert retreat offering spectacular stargazing and Karoo landscape experiences. This eco-friendly 2-bedroom cottage features solar power, traditional architecture, and guided desert tours. Perfect for nature lovers and astronomy enthusiasts.',
        'cottage',
        '78 Desert View Road',
        'Kimberley',
        'Northern Cape',
        '8300',
        'South Africa',
        -28.7282,
        24.7499,
        4,
        2,
        1.0,
        3,
        1500.00,
        250.00,
        2,
        '14:00',
        '10:00',
        'flexible',
        false,
        'active',
        false
    );
    
    -- Get the property IDs for adding amenities and images
    SELECT id INTO property1_id FROM properties WHERE title = 'Luxury Villa with Table Mountain Views';
    SELECT id INTO property2_id FROM properties WHERE title = 'Executive Apartment in Sandton';
    SELECT id INTO property3_id FROM properties WHERE title = 'Beachfront Villa with Ocean Views';
    SELECT id INTO property4_id FROM properties WHERE title = 'Safari Lodge near Addo Elephant Park';
    SELECT id INTO property5_id FROM properties WHERE title = 'Historic Guesthouse in Bloemfontein';
    SELECT id INTO property6_id FROM properties WHERE title = 'Kruger Park Safari Lodge';
    SELECT id INTO property7_id FROM properties WHERE title = 'Cultural Village Experience Lodge';
    SELECT id INTO property8_id FROM properties WHERE title = 'Resort Apartment near Sun City';
    SELECT id INTO property9_id FROM properties WHERE title = 'Desert Retreat in the Karoo';

    -- Add amenities to properties across all provinces
    -- Western Cape Villa amenities (Luxury)
    INSERT INTO property_amenities (property_id, amenity_id)
    SELECT property1_id, id FROM amenities
    WHERE name IN ('WiFi', 'Air Conditioning', 'Full Kitchen', 'Pool', 'Mountain View', 'Sea View', 'Parking', 'BBQ/Braai', 'TV', 'Private Bathroom', 'Netflix', 'Sound System');

    -- Gauteng Apartment amenities (Business)
    INSERT INTO property_amenities (property_id, amenity_id)
    SELECT property2_id, id FROM amenities
    WHERE name IN ('WiFi', 'Air Conditioning', 'Full Kitchen', 'TV', 'Private Bathroom', 'Parking', 'Security System', 'Netflix');

    -- KZN Villa amenities (Beach)
    INSERT INTO property_amenities (property_id, amenity_id)
    SELECT property3_id, id FROM amenities
    WHERE name IN ('WiFi', 'Air Conditioning', 'Full Kitchen', 'Pool', 'Beach Access', 'Sea View', 'Parking', 'BBQ/Braai', 'TV', 'Private Bathroom');

    -- Eastern Cape Safari Lodge amenities
    INSERT INTO property_amenities (property_id, amenity_id)
    SELECT property4_id, id FROM amenities
    WHERE name IN ('WiFi', 'Full Kitchen', 'BBQ/Braai', 'TV', 'Private Bathroom', 'Parking', 'Security System', 'First Aid Kit');

    -- Free State Guesthouse amenities
    INSERT INTO property_amenities (property_id, amenity_id)
    SELECT property5_id, id FROM amenities
    WHERE name IN ('WiFi', 'Full Kitchen', 'TV', 'Private Bathroom', 'Parking', 'Garden', 'BBQ/Braai');

    -- Mpumalanga Safari Lodge amenities (Luxury Safari)
    INSERT INTO property_amenities (property_id, amenity_id)
    SELECT property6_id, id FROM amenities
    WHERE name IN ('WiFi', 'Air Conditioning', 'Full Kitchen', 'Pool', 'TV', 'Private Bathroom', 'Parking', 'Security System', 'BBQ/Braai', 'First Aid Kit');

    -- Limpopo Cultural Lodge amenities
    INSERT INTO property_amenities (property_id, amenity_id)
    SELECT property7_id, id FROM amenities
    WHERE name IN ('WiFi', 'Full Kitchen', 'TV', 'Private Bathroom', 'Parking', 'Garden', 'BBQ/Braai', 'Books & Games');

    -- North West Resort Apartment amenities
    INSERT INTO property_amenities (property_id, amenity_id)
    SELECT property8_id, id FROM amenities
    WHERE name IN ('WiFi', 'Air Conditioning', 'Full Kitchen', 'Pool', 'TV', 'Private Bathroom', 'Parking', 'Netflix', 'Balcony');

    -- Northern Cape Desert Retreat amenities
    INSERT INTO property_amenities (property_id, amenity_id)
    SELECT property9_id, id FROM amenities
    WHERE name IN ('WiFi', 'Full Kitchen', 'TV', 'Private Bathroom', 'Parking', 'Garden', 'BBQ/Braai', 'Books & Games');
    
    -- Add sample property images (placeholder URLs) for all provinces
    INSERT INTO property_images (property_id, image_url, alt_text, sort_order, is_primary)
    VALUES
    -- Western Cape Villa images
    (property1_id, 'https://example.com/wc-villa-main.jpg', 'Luxury villa with Table Mountain view', 1, true),
    (property1_id, 'https://example.com/wc-villa-living.jpg', 'Spacious living room with mountain view', 2, false),
    (property1_id, 'https://example.com/wc-villa-pool.jpg', 'Private pool with city view', 3, false),

    -- Gauteng Apartment images
    (property2_id, 'https://example.com/gp-apartment-main.jpg', 'Executive apartment in Sandton', 1, true),
    (property2_id, 'https://example.com/gp-apartment-living.jpg', 'Modern living room with city view', 2, false),
    (property2_id, 'https://example.com/gp-apartment-bedroom.jpg', 'Executive bedroom suite', 3, false),

    -- KZN Villa images
    (property3_id, 'https://example.com/kzn-villa-main.jpg', 'Beachfront villa exterior view', 1, true),
    (property3_id, 'https://example.com/kzn-villa-ocean.jpg', 'Stunning ocean view from villa', 2, false),
    (property3_id, 'https://example.com/kzn-villa-pool.jpg', 'Private pool with ocean view', 3, false),

    -- Eastern Cape Safari Lodge images
    (property4_id, 'https://example.com/ec-safari-main.jpg', 'Safari lodge with wildlife view', 1, true),
    (property4_id, 'https://example.com/ec-safari-deck.jpg', 'Game viewing deck', 2, false),
    (property4_id, 'https://example.com/ec-safari-boma.jpg', 'Traditional boma area', 3, false),

    -- Free State Guesthouse images
    (property5_id, 'https://example.com/fs-guesthouse-main.jpg', 'Historic guesthouse exterior', 1, true),
    (property5_id, 'https://example.com/fs-guesthouse-interior.jpg', 'Victorian interior design', 2, false),

    -- Mpumalanga Safari Lodge images
    (property6_id, 'https://example.com/mp-safari-main.jpg', 'Luxury safari lodge', 1, true),
    (property6_id, 'https://example.com/mp-safari-wildlife.jpg', 'Wildlife viewing area', 2, false),
    (property6_id, 'https://example.com/mp-safari-suite.jpg', 'Luxury safari suite', 3, false),

    -- Limpopo Cultural Lodge images
    (property7_id, 'https://example.com/lp-cultural-main.jpg', 'Traditional cultural lodge', 1, true),
    (property7_id, 'https://example.com/lp-cultural-village.jpg', 'Cultural village experience', 2, false),

    -- North West Resort Apartment images
    (property8_id, 'https://example.com/nw-resort-main.jpg', 'Resort apartment complex', 1, true),
    (property8_id, 'https://example.com/nw-resort-amenities.jpg', 'Resort amenities and pools', 2, false),

    -- Northern Cape Desert Retreat images
    (property9_id, 'https://example.com/nc-desert-main.jpg', 'Desert retreat cottage', 1, true),
    (property9_id, 'https://example.com/nc-desert-landscape.jpg', 'Spectacular Karoo landscape', 2, false),
    (property9_id, 'https://example.com/nc-desert-stars.jpg', 'Stargazing setup', 3, false);
    
END $$;

-- ============================================================================
-- SAMPLE AVAILABILITY DATA - ALL PROVINCES
-- ============================================================================

-- Block some dates for maintenance across different properties
INSERT INTO property_availability (property_id, date, is_available, reason, notes)
SELECT
    p.id,
    generate_series(
        CURRENT_DATE + INTERVAL '30 days',
        CURRENT_DATE + INTERVAL '35 days',
        INTERVAL '1 day'
    )::DATE,
    false,
    'maintenance',
    'Annual maintenance and deep cleaning'
FROM properties p
WHERE p.title IN ('Luxury Villa with Table Mountain Views', 'Kruger Park Safari Lodge');

-- Block dates for personal use (hosts using their own properties)
INSERT INTO property_availability (property_id, date, is_available, reason, notes)
SELECT
    p.id,
    generate_series(
        CURRENT_DATE + INTERVAL '45 days',
        CURRENT_DATE + INTERVAL '48 days',
        INTERVAL '1 day'
    )::DATE,
    false,
    'personal_use',
    'Host family vacation'
FROM properties p
WHERE p.title IN ('Desert Retreat in the Karoo', 'Cultural Village Experience Lodge');

-- Set special pricing for peak season (December holidays)
INSERT INTO property_availability (property_id, date, is_available, price_override)
SELECT
    p.id,
    generate_series(
        CURRENT_DATE + INTERVAL '60 days',
        CURRENT_DATE + INTERVAL '90 days',
        INTERVAL '1 day'
    )::DATE,
    true,
    p.price_per_night * 1.5  -- 50% increase for peak season
FROM properties p
WHERE p.status = 'active' AND p.province IN ('Western Cape', 'KwaZulu-Natal', 'Eastern Cape');

-- Set special pricing for business season (Gauteng properties)
INSERT INTO property_availability (property_id, date, is_available, price_override)
SELECT
    p.id,
    generate_series(
        CURRENT_DATE + INTERVAL '15 days',
        CURRENT_DATE + INTERVAL '45 days',
        INTERVAL '1 day'
    )::DATE,
    true,
    p.price_per_night * 1.2  -- 20% increase for business season
FROM properties p
WHERE p.status = 'active' AND p.province = 'Gauteng';

-- ============================================================================
-- DEVELOPMENT NOTES - ALL SOUTH AFRICA
-- ============================================================================

-- This seed data provides comprehensive coverage for all of South Africa:
-- 1. Complete amenities list for the South African market (50+ amenities)
-- 2. Sample users from all 9 provinces (hosts, guests, admin)
-- 3. Sample properties representing all provinces with realistic locations:
--    - Western Cape: Luxury villa in Cape Town
--    - Gauteng: Executive apartment in Johannesburg
--    - KwaZulu-Natal: Beachfront villa in Durban
--    - Eastern Cape: Safari lodge near Port Elizabeth
--    - Free State: Historic guesthouse in Bloemfontein
--    - Mpumalanga: Safari lodge near Kruger Park
--    - Limpopo: Cultural village experience in Polokwane
--    - North West: Resort apartment near Sun City
--    - Northern Cape: Desert retreat in Kimberley
-- 4. Property-amenity relationships tailored to each property type
-- 5. Sample property images (placeholder URLs for all provinces)
-- 6. Sample availability data with regional pricing strategies

-- Provincial Coverage:
-- - All 9 provinces represented
-- - Major cities and tourist destinations included
-- - Diverse property types (villa, apartment, house, cottage, guesthouse)
-- - Price ranges from R1,500 to R5,500 per night
-- - Different target markets (luxury, business, safari, cultural, adventure)

-- To use this data:
-- 1. Run this script after creating the main schema
-- 2. Update image URLs to point to actual images in your Supabase storage
-- 3. Replace sample user data with real users in production
-- 4. Adjust property data and coordinates for specific locations
-- 5. Update pricing based on current market rates
-- 6. Add more properties as your platform grows

-- Geographic Coordinates:
-- All coordinates are realistic and represent actual locations in South Africa
-- Coordinates can be used for map displays and location-based searches

-- Remember to update the auth_user_id fields when integrating with Supabase Auth
