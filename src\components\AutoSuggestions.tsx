import React, { useState, useEffect, useRef } from 'react';
import { Search, MapPin, Clock, Star, TrendingUp, User, Calendar, DollarSign } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

interface Suggestion {
  id: string;
  type: 'location' | 'property' | 'search' | 'price' | 'date' | 'amenity';
  title: string;
  subtitle?: string;
  value: string;
  icon?: React.ReactNode;
  confidence: number;
  metadata?: {
    propertyCount?: number;
    averagePrice?: number;
    rating?: number;
    popularity?: number;
  };
}

interface AutoSuggestionsProps {
  inputValue: string;
  onSuggestionSelect: (suggestion: Suggestion) => void;
  onInputChange: (value: string) => void;
  placeholder?: string;
  maxSuggestions?: number;
  enableSmartCompletion?: boolean;
  context?: 'search' | 'location' | 'property' | 'general';
  className?: string;
}

export const AutoSuggestions: React.FC<AutoSuggestionsProps> = ({
  inputValue,
  onSuggestionSelect,
  onInputChange,
  placeholder = "Search for locations, properties, or amenities...",
  maxSuggestions = 8,
  enableSmartCompletion = true,
  context = 'general',
  className
}) => {
  const [suggestions, setSuggestions] = useState<Suggestion[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const inputRef = useRef<HTMLInputElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);

  // Mock data - in real implementation, this would come from APIs
  const mockSuggestions: Suggestion[] = [
    {
      id: '1',
      type: 'location',
      title: 'Cape Town',
      subtitle: 'Western Cape, South Africa',
      value: 'cape-town',
      icon: <MapPin className="h-4 w-4" />,
      confidence: 0.95,
      metadata: { propertyCount: 1250, averagePrice: 850 }
    },
    {
      id: '2',
      type: 'location',
      title: 'Johannesburg',
      subtitle: 'Gauteng, South Africa',
      value: 'johannesburg',
      icon: <MapPin className="h-4 w-4" />,
      confidence: 0.92,
      metadata: { propertyCount: 980, averagePrice: 720 }
    },
    {
      id: '3',
      type: 'property',
      title: 'Luxury Oceanview Villa',
      subtitle: 'Camps Bay, Cape Town',
      value: 'luxury-oceanview-villa',
      icon: <Star className="h-4 w-4" />,
      confidence: 0.88,
      metadata: { rating: 4.9, averagePrice: 2500 }
    },
    {
      id: '4',
      type: 'amenity',
      title: 'Swimming Pool',
      subtitle: 'Properties with pools',
      value: 'swimming-pool',
      icon: <TrendingUp className="h-4 w-4" />,
      confidence: 0.85,
      metadata: { propertyCount: 450 }
    },
    {
      id: '5',
      type: 'search',
      title: 'Pet-friendly accommodations',
      subtitle: 'Recent popular search',
      value: 'pet-friendly',
      icon: <Clock className="h-4 w-4" />,
      confidence: 0.82,
      metadata: { popularity: 78 }
    }
  ];

  useEffect(() => {
    if (inputValue.length < 2) {
      setSuggestions([]);
      setShowSuggestions(false);
      return;
    }

    setIsLoading(true);
    
    // Simulate API call with debouncing
    const timeoutId = setTimeout(() => {
      const filtered = mockSuggestions
        .filter(suggestion => 
          suggestion.title.toLowerCase().includes(inputValue.toLowerCase()) ||
          suggestion.subtitle?.toLowerCase().includes(inputValue.toLowerCase()) ||
          suggestion.value.toLowerCase().includes(inputValue.toLowerCase())
        )
        .sort((a, b) => b.confidence - a.confidence)
        .slice(0, maxSuggestions);

      // Add smart completions if enabled
      if (enableSmartCompletion && filtered.length < maxSuggestions) {
        const smartSuggestions = generateSmartSuggestions(inputValue, context);
        filtered.push(...smartSuggestions.slice(0, maxSuggestions - filtered.length));
      }

      setSuggestions(filtered);
      setShowSuggestions(filtered.length > 0);
      setIsLoading(false);
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [inputValue, maxSuggestions, enableSmartCompletion, context]);

  const generateSmartSuggestions = (input: string, ctx: string): Suggestion[] => {
    const smartSuggestions: Suggestion[] = [];
    
    // Price-based suggestions
    if (/\d+/.test(input)) {
      const price = parseInt(input.match(/\d+/)?.[0] || '0');
      if (price > 100) {
        smartSuggestions.push({
          id: `price-${price}`,
          type: 'price',
          title: `Properties under R${price}`,
          subtitle: `Find accommodations within your budget`,
          value: `max-price-${price}`,
          icon: <DollarSign className="h-4 w-4" />,
          confidence: 0.75,
          metadata: { propertyCount: Math.floor(Math.random() * 200) + 50 }
        });
      }
    }

    // Date-based suggestions
    if (/\b(today|tomorrow|weekend|next week)\b/i.test(input)) {
      smartSuggestions.push({
        id: 'date-suggestion',
        type: 'date',
        title: 'Available this weekend',
        subtitle: 'Properties with weekend availability',
        value: 'weekend-available',
        icon: <Calendar className="h-4 w-4" />,
        confidence: 0.78,
        metadata: { propertyCount: 156 }
      });
    }

    // Guest count suggestions
    if (/\b(\d+)\s*(guest|people|person)\b/i.test(input)) {
      const match = input.match(/\b(\d+)\s*(guest|people|person)\b/i);
      const guestCount = match ? parseInt(match[1]) : 2;
      smartSuggestions.push({
        id: `guests-${guestCount}`,
        type: 'search',
        title: `Properties for ${guestCount} guests`,
        subtitle: `Accommodations suitable for your group`,
        value: `guests-${guestCount}`,
        icon: <User className="h-4 w-4" />,
        confidence: 0.80,
        metadata: { propertyCount: Math.floor(Math.random() * 300) + 100 }
      });
    }

    return smartSuggestions;
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!showSuggestions) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev < suggestions.length - 1 ? prev + 1 : prev
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => prev > 0 ? prev - 1 : -1);
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0 && suggestions[selectedIndex]) {
          handleSuggestionClick(suggestions[selectedIndex]);
        }
        break;
      case 'Escape':
        setShowSuggestions(false);
        setSelectedIndex(-1);
        break;
    }
  };

  const handleSuggestionClick = (suggestion: Suggestion) => {
    onSuggestionSelect(suggestion);
    onInputChange(suggestion.title);
    setShowSuggestions(false);
    setSelectedIndex(-1);
  };

  const getSuggestionIcon = (suggestion: Suggestion) => {
    if (suggestion.icon) return suggestion.icon;
    
    switch (suggestion.type) {
      case 'location': return <MapPin className="h-4 w-4" />;
      case 'property': return <Star className="h-4 w-4" />;
      case 'search': return <Clock className="h-4 w-4" />;
      case 'price': return <DollarSign className="h-4 w-4" />;
      case 'date': return <Calendar className="h-4 w-4" />;
      case 'amenity': return <TrendingUp className="h-4 w-4" />;
      default: return <Search className="h-4 w-4" />;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'location': return 'bg-blue-100 text-blue-800';
      case 'property': return 'bg-green-100 text-green-800';
      case 'search': return 'bg-purple-100 text-purple-800';
      case 'price': return 'bg-orange-100 text-orange-800';
      case 'date': return 'bg-red-100 text-red-800';
      case 'amenity': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className={cn("relative w-full", className)}>
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
        <Input
          ref={inputRef}
          value={inputValue}
          onChange={(e) => onInputChange(e.target.value)}
          onKeyDown={handleKeyDown}
          onFocus={() => inputValue.length >= 2 && setShowSuggestions(true)}
          placeholder={placeholder}
          className="pl-9 pr-4"
        />
        {isLoading && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            <div className="animate-spin h-4 w-4 border-2 border-sea-green-600 border-t-transparent rounded-full" />
          </div>
        )}
      </div>

      {showSuggestions && suggestions.length > 0 && (
        <Card className="absolute top-full mt-1 w-full z-50 shadow-lg max-h-96 overflow-y-auto">
          <CardContent className="p-0">
            {suggestions.map((suggestion, index) => (
              <div
                key={suggestion.id}
                onClick={() => handleSuggestionClick(suggestion)}
                className={cn(
                  "flex items-center gap-3 p-3 cursor-pointer transition-colors border-b border-gray-100 last:border-b-0",
                  index === selectedIndex ? "bg-sea-green-50" : "hover:bg-gray-50"
                )}
              >
                <div className="flex-shrink-0 text-gray-500">
                  {getSuggestionIcon(suggestion)}
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="font-medium text-gray-900 truncate">
                      {suggestion.title}
                    </span>
                    <Badge 
                      variant="secondary" 
                      className={cn("text-xs", getTypeColor(suggestion.type))}
                    >
                      {suggestion.type}
                    </Badge>
                  </div>
                  
                  {suggestion.subtitle && (
                    <p className="text-sm text-gray-600 truncate">
                      {suggestion.subtitle}
                    </p>
                  )}
                  
                  {suggestion.metadata && (
                    <div className="flex items-center gap-3 mt-1 text-xs text-gray-500">
                      {suggestion.metadata.propertyCount && (
                        <span>{suggestion.metadata.propertyCount} properties</span>
                      )}
                      {suggestion.metadata.averagePrice && (
                        <span>Avg: R{suggestion.metadata.averagePrice}</span>
                      )}
                      {suggestion.metadata.rating && (
                        <span className="flex items-center gap-1">
                          <Star className="h-3 w-3 fill-current text-yellow-400" />
                          {suggestion.metadata.rating}
                        </span>
                      )}
                      {suggestion.metadata.popularity && (
                        <span>{suggestion.metadata.popularity}% popular</span>
                      )}
                    </div>
                  )}
                </div>
                
                <div className="flex-shrink-0">
                  <div className="w-2 h-2 rounded-full bg-gray-300" 
                       style={{ 
                         backgroundColor: `hsl(${suggestion.confidence * 120}, 70%, 50%)`,
                         opacity: suggestion.confidence 
                       }} 
                  />
                </div>
              </div>
            ))}
            
            {enableSmartCompletion && (
              <div className="p-3 bg-gray-50 border-t">
                <p className="text-xs text-gray-600 text-center">
                  💡 Smart suggestions powered by AI
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
};

// Hook for using auto-suggestions
export const useAutoSuggestions = (initialValue: string = '') => {
  const [inputValue, setInputValue] = useState(initialValue);
  const [selectedSuggestion, setSelectedSuggestion] = useState<Suggestion | null>(null);

  const handleSuggestionSelect = (suggestion: Suggestion) => {
    setSelectedSuggestion(suggestion);
  };

  const handleInputChange = (value: string) => {
    setInputValue(value);
    if (selectedSuggestion && value !== selectedSuggestion.title) {
      setSelectedSuggestion(null);
    }
  };

  const reset = () => {
    setInputValue('');
    setSelectedSuggestion(null);
  };

  return {
    inputValue,
    selectedSuggestion,
    handleSuggestionSelect,
    handleInputChange,
    reset
  };
};

// Smart form completion component
export const SmartFormCompletion: React.FC<{
  fields: Array<{
    name: string;
    label: string;
    type: 'text' | 'email' | 'phone' | 'location';
    value: string;
    onChange: (value: string) => void;
  }>;
  onComplete: (data: Record<string, string>) => void;
}> = ({ fields, onComplete }) => {
  const [completedFields, setCompletedFields] = useState<Set<string>>(new Set());

  const handleFieldComplete = (fieldName: string, value: string) => {
    setCompletedFields(prev => new Set([...prev, fieldName]));
    
    // Auto-suggest related fields
    if (fieldName === 'location' && value) {
      // Could auto-fill country, timezone, etc.
    }
    
    if (completedFields.size === fields.length - 1) {
      const data = fields.reduce((acc, field) => {
        acc[field.name] = field.value;
        return acc;
      }, {} as Record<string, string>);
      onComplete(data);
    }
  };

  return (
    <div className="space-y-4">
      {fields.map((field) => (
        <div key={field.name}>
          <label className="block text-sm font-medium mb-1">
            {field.label}
          </label>
          <AutoSuggestions
            inputValue={field.value}
            onInputChange={field.onChange}
            onSuggestionSelect={(suggestion) => {
              field.onChange(suggestion.value);
              handleFieldComplete(field.name, suggestion.value);
            }}
            context={field.type === 'location' ? 'location' : 'general'}
            placeholder={`Enter ${field.label.toLowerCase()}...`}
          />
        </div>
      ))}
    </div>
  );
};
