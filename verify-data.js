#!/usr/bin/env node

/**
 * Verify StayFinder Data After Schema Deployment
 * Checks if all expected data was loaded correctly
 */

import { createClient } from '@supabase/supabase-js';
import { config } from 'dotenv';
config();

// ANSI color codes
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, colors.green);
}

function logError(message) {
  log(`❌ ${message}`, colors.red);
}

function logWarning(message) {
  log(`⚠️  ${message}`, colors.yellow);
}

function logInfo(message) {
  log(`ℹ️  ${message}`, colors.blue);
}

// Initialize Supabase admin client
const supabaseAdmin = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

// Expected data counts
const expectedData = {
  amenities: 50, // Should have 50+ amenities
  users: 13,     // Should have 13 sample users
  properties: 9, // Should have 9 properties (one per province)
  provinces: 9   // Should represent all 9 SA provinces
};

async function verifyData() {
  log('🔍 VERIFYING STAYFINDER DATA', colors.bright);
  log('='.repeat(50), colors.cyan);
  
  const results = {
    tablesExist: false,
    dataLoaded: false,
    provinceCoverage: false,
    issues: []
  };
  
  try {
    // Check 1: Verify amenities data
    logInfo('Checking amenities data...');
    
    const { data: amenities, error: amenitiesError } = await supabaseAdmin
      .from('amenities')
      .select('*');
    
    if (amenitiesError) {
      logError(`Amenities table error: ${amenitiesError.message}`);
      results.issues.push('Amenities table not accessible');
    } else if (!amenities || amenities.length === 0) {
      logWarning('Amenities table is empty - seed data may not have loaded');
      results.issues.push('No amenities data found');
    } else {
      logSuccess(`Found ${amenities.length} amenities`);
      if (amenities.length >= expectedData.amenities) {
        logSuccess('✓ Amenities data looks complete');
      } else {
        logWarning(`Expected ${expectedData.amenities}+ amenities, found ${amenities.length}`);
      }
      
      // Show amenity categories
      const categories = [...new Set(amenities.map(a => a.category))];
      logInfo(`Amenity categories: ${categories.join(', ')}`);
    }
    
    // Check 2: Verify users data
    logInfo('Checking users data...');
    
    const { data: users, error: usersError } = await supabaseAdmin
      .from('users')
      .select('*');
    
    if (usersError) {
      logError(`Users table error: ${usersError.message}`);
      results.issues.push('Users table not accessible');
    } else if (!users || users.length === 0) {
      logWarning('Users table is empty - seed data may not have loaded');
      results.issues.push('No users data found');
    } else {
      logSuccess(`Found ${users.length} users`);
      
      // Check user types
      const userTypes = users.reduce((acc, user) => {
        acc[user.user_type] = (acc[user.user_type] || 0) + 1;
        return acc;
      }, {});
      
      logInfo(`User types: ${Object.entries(userTypes).map(([type, count]) => `${type}(${count})`).join(', ')}`);
      
      // Check provinces represented in users
      const userProvinces = [...new Set(users.map(u => u.province).filter(Boolean))];
      logInfo(`User provinces: ${userProvinces.join(', ')}`);
    }
    
    // Check 3: Verify properties data
    logInfo('Checking properties data...');
    
    const { data: properties, error: propertiesError } = await supabaseAdmin
      .from('properties')
      .select('*');
    
    if (propertiesError) {
      logError(`Properties table error: ${propertiesError.message}`);
      results.issues.push('Properties table not accessible');
    } else if (!properties || properties.length === 0) {
      logWarning('Properties table is empty - seed data may not have loaded');
      results.issues.push('No properties data found');
    } else {
      logSuccess(`Found ${properties.length} properties`);
      
      // Check provinces represented in properties
      const propertyProvinces = [...new Set(properties.map(p => p.province).filter(Boolean))];
      logInfo(`Property provinces: ${propertyProvinces.join(', ')}`);
      
      if (propertyProvinces.length >= expectedData.provinces) {
        logSuccess(`✓ All ${propertyProvinces.length} provinces represented in properties`);
        results.provinceCoverage = true;
      } else {
        logWarning(`Expected ${expectedData.provinces} provinces, found ${propertyProvinces.length}`);
        results.issues.push(`Missing provinces in properties: ${expectedData.provinces - propertyProvinces.length}`);
      }
      
      // Show property details
      properties.forEach(property => {
        logInfo(`  ${property.title} - ${property.city}, ${property.province} (R${property.price_per_night}/night)`);
      });
    }
    
    // Check 4: Verify property amenities relationships
    logInfo('Checking property amenities relationships...');
    
    const { data: propertyAmenities, error: paError } = await supabaseAdmin
      .from('property_amenities')
      .select('*');
    
    if (paError) {
      logWarning(`Property amenities error: ${paError.message}`);
    } else if (propertyAmenities && propertyAmenities.length > 0) {
      logSuccess(`Found ${propertyAmenities.length} property-amenity relationships`);
    } else {
      logWarning('No property-amenity relationships found');
    }
    
    // Check 5: Verify property images
    logInfo('Checking property images...');
    
    const { data: propertyImages, error: piError } = await supabaseAdmin
      .from('property_images')
      .select('*');
    
    if (piError) {
      logWarning(`Property images error: ${piError.message}`);
    } else if (propertyImages && propertyImages.length > 0) {
      logSuccess(`Found ${propertyImages.length} property images`);
    } else {
      logWarning('No property images found');
    }
    
    // Overall assessment
    results.tablesExist = !amenitiesError && !usersError && !propertiesError;
    results.dataLoaded = amenities?.length > 0 && users?.length > 0 && properties?.length > 0;
    
  } catch (error) {
    logError(`Data verification failed: ${error.message}`);
    results.issues.push(`Verification error: ${error.message}`);
  }
  
  return results;
}

// Generate verification report
async function generateReport(results) {
  log('\n' + '='.repeat(50), colors.cyan);
  log('📊 DATA VERIFICATION REPORT', colors.bright);
  log('='.repeat(50), colors.cyan);
  
  // Overall status
  if (results.tablesExist) {
    logSuccess('Database Tables: ACCESSIBLE');
  } else {
    logError('Database Tables: ISSUES FOUND');
  }
  
  if (results.dataLoaded) {
    logSuccess('Sample Data: LOADED');
  } else {
    logError('Sample Data: MISSING OR INCOMPLETE');
  }
  
  if (results.provinceCoverage) {
    logSuccess('Provincial Coverage: COMPLETE (All 9 provinces)');
  } else {
    logWarning('Provincial Coverage: INCOMPLETE');
  }
  
  // Issues summary
  if (results.issues.length > 0) {
    log('\n⚠️  Issues Found:', colors.yellow);
    results.issues.forEach((issue, index) => {
      logWarning(`${index + 1}. ${issue}`);
    });
  }
  
  // Recommendations
  log('\n📋 Recommendations:', colors.blue);
  
  if (!results.dataLoaded) {
    logWarning('1. Re-run seed-data.sql in Supabase SQL Editor');
    logWarning('2. Check for any SQL errors during seed data execution');
    logWarning('3. Verify all INSERT statements completed successfully');
  }
  
  if (!results.provinceCoverage) {
    logWarning('4. Ensure all 9 South African provinces are represented');
    logWarning('5. Check property data for missing provinces');
  }
  
  if (results.tablesExist && results.dataLoaded && results.provinceCoverage) {
    logSuccess('✨ All data loaded successfully!');
    log('\n🎯 Your StayFinder database is ready with:', colors.green);
    log('• Complete amenities for South African market', colors.green);
    log('• Sample users from all provinces', colors.green);
    log('• Properties representing all 9 provinces', colors.green);
    log('• Property-amenity relationships', colors.green);
    log('• Sample images and availability data', colors.green);
  }
  
  return results;
}

// Main function
async function main() {
  const results = await verifyData();
  await generateReport(results);
  
  const success = results.tablesExist && results.dataLoaded;
  process.exit(success ? 0 : 1);
}

// Run verification
main().catch(error => {
  logError(`Verification error: ${error.message}`);
  console.error(error);
  process.exit(1);
});
