import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { StarRating } from './StarRating';
import { 
  Star, 
  MessageSquare, 
  Calendar,
  MapPin,
  Edit,
  Trash2,
  Send,
  X,
  AlertCircle,
  CheckCircle,
  Clock
} from 'lucide-react';

interface Review {
  id: string;
  property_id: string;
  property_title: string;
  property_location: string;
  property_image: string;
  booking_id: string;
  rating: number;
  comment: string;
  response?: string;
  created_at: string;
  updated_at: string;
  can_edit: boolean;
  host_name: string;
  stay_date: string;
}

interface ReviewStats {
  total_reviews: number;
  average_rating: number;
  pending_reviews: number;
  reviews_with_response: number;
}

interface ReviewManagementProps {
  className?: string;
  limit?: number;
  showStats?: boolean;
}

export const ReviewManagement: React.FC<ReviewManagementProps> = ({
  className,
  limit,
  showStats = true
}) => {
  const [reviews, setReviews] = useState<Review[]>([]);
  const [stats, setStats] = useState<ReviewStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [editingReview, setEditingReview] = useState<Review | null>(null);
  const [editRating, setEditRating] = useState(0);
  const [editComment, setEditComment] = useState('');
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    fetchReviews();
    if (showStats) {
      fetchStats();
    }
  }, []);

  const fetchReviews = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Mock data for testing - replace with actual API call
      const mockReviews: Review[] = [
        {
          id: 'review-1',
          property_id: 'prop-1',
          property_title: 'Luxury Villa in Margate',
          property_location: 'Margate, KwaZulu-Natal',
          property_image: '/placeholder.svg',
          booking_id: 'booking-1',
          rating: 5,
          comment: 'Amazing stay! The villa was exactly as described and the host was incredibly helpful. The ocean views were breathtaking and we loved the private pool. Would definitely stay here again!',
          response: 'Thank you so much for the wonderful review! We\'re thrilled you enjoyed your stay and hope to welcome you back soon.',
          created_at: '2024-05-20T10:30:00Z',
          updated_at: '2024-05-20T10:30:00Z',
          can_edit: true,
          host_name: 'Jane Smith',
          stay_date: '2024-05-15'
        },
        {
          id: 'review-2',
          property_id: 'prop-2',
          property_title: 'Beachfront Cottage in Hibberdene',
          property_location: 'Hibberdene, KwaZulu-Natal',
          property_image: '/placeholder.svg',
          booking_id: 'booking-2',
          rating: 4,
          comment: 'Great location right on the beach. The cottage was clean and comfortable. Only minor issue was the WiFi was a bit slow, but overall a fantastic experience.',
          created_at: '2024-04-25T14:15:00Z',
          updated_at: '2024-04-25T14:15:00Z',
          can_edit: false,
          host_name: 'Mike Johnson',
          stay_date: '2024-04-20'
        },
        {
          id: 'review-3',
          property_id: 'prop-3',
          property_title: 'Mountain Retreat in Drakensberg',
          property_location: 'Drakensberg, KwaZulu-Natal',
          property_image: '/placeholder.svg',
          booking_id: 'booking-3',
          rating: 5,
          comment: 'Absolutely stunning location with incredible mountain views. The cabin was well-equipped and perfect for our family getaway. Highly recommend!',
          response: 'We\'re so happy you enjoyed the mountain views and had a great family time. Thank you for choosing our retreat!',
          created_at: '2024-03-10T09:45:00Z',
          updated_at: '2024-03-10T09:45:00Z',
          can_edit: false,
          host_name: 'Sarah Wilson',
          stay_date: '2024-03-05'
        }
      ];
      
      const limitedReviews = limit ? mockReviews.slice(0, limit) : mockReviews;
      setReviews(limitedReviews);
    } catch (err) {
      console.error('Error fetching reviews:', err);
      setError('Failed to load reviews');
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      // Mock stats for testing - replace with actual API call
      const mockStats: ReviewStats = {
        total_reviews: 8,
        average_rating: 4.6,
        pending_reviews: 2,
        reviews_with_response: 5
      };
      setStats(mockStats);
    } catch (err) {
      console.error('Error fetching review stats:', err);
    }
  };

  const handleEditReview = (review: Review) => {
    setEditingReview(review);
    setEditRating(review.rating);
    setEditComment(review.comment);
  };

  const handleSaveEdit = async () => {
    if (!editingReview) return;

    try {
      setSaving(true);
      
      // Mock API call - replace with actual implementation
      const updatedReview = {
        ...editingReview,
        rating: editRating,
        comment: editComment,
        updated_at: new Date().toISOString()
      };

      setReviews(prev => prev.map(r => r.id === editingReview.id ? updatedReview : r));
      setEditingReview(null);
      setEditRating(0);
      setEditComment('');
    } catch (err) {
      console.error('Error updating review:', err);
      setError('Failed to update review');
    } finally {
      setSaving(false);
    }
  };

  const handleCancelEdit = () => {
    setEditingReview(null);
    setEditRating(0);
    setEditComment('');
  };

  const handleDeleteReview = async (reviewId: string) => {
    if (!confirm('Are you sure you want to delete this review?')) return;

    try {
      // Mock API call - replace with actual implementation
      setReviews(prev => prev.filter(r => r.id !== reviewId));
    } catch (err) {
      console.error('Error deleting review:', err);
      setError('Failed to delete review');
    }
  };

  const renderStars = (rating: number) => {
    return (
      <div className="flex items-center gap-1">
        {[...Array(5)].map((_, index) => (
          <Star
            key={index}
            className={`h-4 w-4 ${
              index < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
            }`}
          />
        ))}
      </div>
    );
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="animate-pulse space-y-4">
            <div className="h-6 bg-gray-200 rounded w-1/3"></div>
            {[...Array(3)].map((_, index) => (
              <div key={index} className="space-y-3">
                <div className="h-4 bg-gray-200 rounded"></div>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Stats Overview */}
      {showStats && stats && (
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-gray-900">{stats.total_reviews}</div>
              <div className="text-sm text-gray-600">Total Reviews</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-yellow-600 flex items-center justify-center gap-1">
                <Star className="h-5 w-5 fill-current" />
                {stats.average_rating.toFixed(1)}
              </div>
              <div className="text-sm text-gray-600">Average Rating</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-orange-600">{stats.pending_reviews}</div>
              <div className="text-sm text-gray-600">Pending Reviews</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-green-600">{stats.reviews_with_response}</div>
              <div className="text-sm text-gray-600">Host Responses</div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Reviews List */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            My Reviews
            <Badge variant="secondary">{reviews.length}</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {error && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-lg mb-4">
              <div className="flex items-center gap-2 text-red-700">
                <AlertCircle className="h-4 w-4" />
                <span className="text-sm">{error}</span>
              </div>
            </div>
          )}

          {reviews.length === 0 ? (
            <div className="text-center py-8">
              <MessageSquare className="h-12 w-12 mx-auto mb-4 text-gray-400" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No Reviews Yet
              </h3>
              <p className="text-gray-600 mb-4">
                Complete your stays to leave reviews and help other travelers
              </p>
            </div>
          ) : (
            <div className="space-y-6">
              {reviews.map((review) => (
                <div key={review.id} className="border rounded-lg p-4">
                  {editingReview?.id === review.id ? (
                    // Edit Mode
                    <div className="space-y-4">
                      <div className="flex items-start gap-4">
                        <img
                          src={review.property_image || '/placeholder.svg'}
                          alt={review.property_title}
                          className="w-16 h-16 rounded-lg object-cover"
                        />
                        <div className="flex-1">
                          <h4 className="font-semibold text-gray-900">{review.property_title}</h4>
                          <div className="flex items-center gap-1 text-gray-600 text-sm">
                            <MapPin className="h-3 w-3" />
                            {review.property_location}
                          </div>
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Rating
                        </label>
                        <StarRating
                          rating={editRating}
                          onRatingChange={setEditRating}
                          size="lg"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Review
                        </label>
                        <Textarea
                          value={editComment}
                          onChange={(e) => setEditComment(e.target.value)}
                          placeholder="Share your experience..."
                          rows={4}
                        />
                      </div>

                      <div className="flex items-center gap-2">
                        <Button
                          onClick={handleSaveEdit}
                          disabled={saving || !editComment.trim() || editRating === 0}
                          className="bg-sea-green-500 hover:bg-sea-green-600"
                        >
                          {saving ? (
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                          ) : (
                            <Send className="h-4 w-4 mr-2" />
                          )}
                          Save Changes
                        </Button>
                        <Button
                          variant="outline"
                          onClick={handleCancelEdit}
                          disabled={saving}
                        >
                          <X className="h-4 w-4 mr-2" />
                          Cancel
                        </Button>
                      </div>
                    </div>
                  ) : (
                    // View Mode
                    <div className="space-y-4">
                      <div className="flex items-start justify-between">
                        <div className="flex items-start gap-4">
                          <img
                            src={review.property_image || '/placeholder.svg'}
                            alt={review.property_title}
                            className="w-16 h-16 rounded-lg object-cover"
                          />
                          <div>
                            <h4 className="font-semibold text-gray-900">{review.property_title}</h4>
                            <div className="flex items-center gap-1 text-gray-600 text-sm mb-2">
                              <MapPin className="h-3 w-3" />
                              {review.property_location}
                            </div>
                            <div className="flex items-center gap-2 text-sm text-gray-600">
                              <Calendar className="h-3 w-3" />
                              Stayed {new Date(review.stay_date).toLocaleDateString()}
                            </div>
                          </div>
                        </div>

                        <div className="flex items-center gap-2">
                          {review.can_edit && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleEditReview(review)}
                            >
                              <Edit className="h-3 w-3 mr-1" />
                              Edit
                            </Button>
                          )}
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDeleteReview(review.id)}
                            className="text-red-600 border-red-200 hover:bg-red-50"
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>

                      <div className="space-y-3">
                        <div className="flex items-center gap-2">
                          {renderStars(review.rating)}
                          <span className="text-sm text-gray-600">
                            {new Date(review.created_at).toLocaleDateString()}
                          </span>
                        </div>

                        <p className="text-gray-700">{review.comment}</p>

                        {review.response && (
                          <div className="bg-gray-50 p-3 rounded-lg border-l-4 border-sea-green-500">
                            <div className="flex items-center gap-2 mb-2">
                              <CheckCircle className="h-4 w-4 text-sea-green-500" />
                              <span className="text-sm font-medium text-gray-900">
                                Response from {review.host_name}
                              </span>
                            </div>
                            <p className="text-gray-700 text-sm">{review.response}</p>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
