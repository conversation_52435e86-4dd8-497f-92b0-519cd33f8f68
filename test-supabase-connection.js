#!/usr/bin/env node

/**
 * Comprehensive Supabase Connection Test Suite
 * Tests all Supabase services and configurations
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { config } from 'dotenv';
config();

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

// Test results tracking
const testResults = {
  passed: 0,
  failed: 0,
  warnings: 0,
  details: []
};

// Helper functions
function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, colors.green);
  testResults.passed++;
}

function logError(message) {
  log(`❌ ${message}`, colors.red);
  testResults.failed++;
}

function logWarning(message) {
  log(`⚠️  ${message}`, colors.yellow);
  testResults.warnings++;
}

function logInfo(message) {
  log(`ℹ️  ${message}`, colors.blue);
}

function addTestDetail(test, status, message, error = null) {
  testResults.details.push({
    test,
    status,
    message,
    error: error ? error.message : null,
    timestamp: new Date().toISOString()
  });
}

// Initialize Supabase clients
let supabase, supabaseAdmin;

try {
  // Public client (anon key)
  supabase = createClient(
    process.env.SUPABASE_URL,
    process.env.SUPABASE_ANON_KEY
  );

  // Admin client (service role key)
  supabaseAdmin = createClient(
    process.env.SUPABASE_URL,
    process.env.SUPABASE_SERVICE_ROLE_KEY
  );

  logSuccess('Supabase clients initialized successfully');
} catch (error) {
  logError(`Failed to initialize Supabase clients: ${error.message}`);
  process.exit(1);
}

// Test 1: Environment Variables Validation
async function testEnvironmentVariables() {
  log('\n🔧 Testing Environment Variables...', colors.cyan);
  
  const requiredVars = [
    'SUPABASE_URL',
    'SUPABASE_ANON_KEY',
    'SUPABASE_SERVICE_ROLE_KEY',
    'SUPABASE_JWT_SECRET',
    'DATABASE_URL'
  ];

  const optionalVars = [
    'SUPABASE_STORAGE_BUCKET_PROPERTIES',
    'SUPABASE_STORAGE_BUCKET_AVATARS',
    'SUPABASE_STORAGE_BUCKET_DOCUMENTS'
  ];

  // Check required variables
  for (const varName of requiredVars) {
    if (process.env[varName]) {
      logSuccess(`${varName} is set`);
      addTestDetail('Environment Variables', 'PASS', `${varName} is configured`);
    } else {
      logError(`${varName} is missing`);
      addTestDetail('Environment Variables', 'FAIL', `${varName} is not configured`);
    }
  }

  // Check optional variables
  for (const varName of optionalVars) {
    if (process.env[varName]) {
      logSuccess(`${varName} is set`);
    } else {
      logWarning(`${varName} is not set (optional)`);
    }
  }

  // Validate URL format
  if (process.env.SUPABASE_URL) {
    const urlPattern = /^https:\/\/[a-z0-9]+\.supabase\.co$/;
    if (urlPattern.test(process.env.SUPABASE_URL)) {
      logSuccess('Supabase URL format is valid');
    } else {
      logError('Supabase URL format is invalid');
      addTestDetail('Environment Variables', 'FAIL', 'Invalid Supabase URL format');
    }
  }
}

// Test 2: Database Connection
async function testDatabaseConnection() {
  log('\n🗄️  Testing Database Connection...', colors.cyan);
  
  try {
    // Test basic connection with a simple query
    const { data, error } = await supabaseAdmin
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .limit(1);

    if (error) {
      logError(`Database connection failed: ${error.message}`);
      addTestDetail('Database Connection', 'FAIL', 'Cannot connect to database', error);
      return false;
    }

    logSuccess('Database connection successful');
    addTestDetail('Database Connection', 'PASS', 'Successfully connected to PostgreSQL database');
    return true;
  } catch (error) {
    logError(`Database connection error: ${error.message}`);
    addTestDetail('Database Connection', 'FAIL', 'Database connection error', error);
    return false;
  }
}

// Test 3: Database Schema Validation
async function testDatabaseSchema() {
  log('\n📋 Testing Database Schema...', colors.cyan);
  
  const expectedTables = ['users', 'properties', 'bookings', 'reviews', 'messages'];
  
  try {
    const { data: tables, error } = await supabaseAdmin
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public');

    if (error) {
      logError(`Schema validation failed: ${error.message}`);
      addTestDetail('Database Schema', 'FAIL', 'Cannot retrieve schema information', error);
      return false;
    }

    const tableNames = tables.map(t => t.table_name);
    
    for (const expectedTable of expectedTables) {
      if (tableNames.includes(expectedTable)) {
        logSuccess(`Table '${expectedTable}' exists`);
        addTestDetail('Database Schema', 'PASS', `Table '${expectedTable}' found`);
      } else {
        logWarning(`Table '${expectedTable}' not found - may need to be created`);
        addTestDetail('Database Schema', 'WARNING', `Table '${expectedTable}' missing`);
      }
    }

    return true;
  } catch (error) {
    logError(`Schema validation error: ${error.message}`);
    addTestDetail('Database Schema', 'FAIL', 'Schema validation error', error);
    return false;
  }
}

// Test 4: Authentication Service
async function testAuthentication() {
  log('\n🔐 Testing Authentication Service...', colors.cyan);
  
  try {
    // Test 1: Check if auth is enabled
    const { data: { user }, error: sessionError } = await supabase.auth.getUser();
    
    if (sessionError && sessionError.message !== 'Invalid JWT') {
      logError(`Auth service error: ${sessionError.message}`);
      addTestDetail('Authentication', 'FAIL', 'Auth service not accessible', sessionError);
      return false;
    }

    logSuccess('Authentication service is accessible');

    // Test 2: Test user registration (with cleanup)
    const testEmail = `test-${Date.now()}@stayfinder-test.com`;
    const testPassword = 'TestPassword123!';

    const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
      email: testEmail,
      password: testPassword,
      options: {
        data: {
          first_name: 'Test',
          last_name: 'User'
        }
      }
    });

    if (signUpError) {
      if (signUpError.message.includes('Email rate limit exceeded')) {
        logWarning('Email rate limit exceeded - auth service is working but rate limited');
        addTestDetail('Authentication', 'WARNING', 'Rate limited but service is functional');
      } else {
        logError(`User registration failed: ${signUpError.message}`);
        addTestDetail('Authentication', 'FAIL', 'User registration failed', signUpError);
        return false;
      }
    } else {
      logSuccess('User registration test successful');
      addTestDetail('Authentication', 'PASS', 'User registration works');

      // Cleanup: Delete test user if created
      if (signUpData.user) {
        try {
          await supabaseAdmin.auth.admin.deleteUser(signUpData.user.id);
          logInfo('Test user cleaned up');
        } catch (cleanupError) {
          logWarning('Could not cleanup test user');
        }
      }
    }

    return true;
  } catch (error) {
    logError(`Authentication test error: ${error.message}`);
    addTestDetail('Authentication', 'FAIL', 'Authentication test error', error);
    return false;
  }
}

// Test 5: Storage Buckets
async function testStorageBuckets() {
  log('\n📁 Testing Storage Buckets...', colors.cyan);
  
  const buckets = [
    process.env.SUPABASE_STORAGE_BUCKET_PROPERTIES || 'property-images',
    process.env.SUPABASE_STORAGE_BUCKET_AVATARS || 'user-avatars',
    process.env.SUPABASE_STORAGE_BUCKET_DOCUMENTS || 'documents'
  ];

  try {
    // List all buckets
    const { data: allBuckets, error: listError } = await supabaseAdmin.storage.listBuckets();
    
    if (listError) {
      logError(`Storage service error: ${listError.message}`);
      addTestDetail('Storage', 'FAIL', 'Cannot access storage service', listError);
      return false;
    }

    logSuccess('Storage service is accessible');
    
    const existingBuckets = allBuckets.map(b => b.name);
    
    for (const bucketName of buckets) {
      if (existingBuckets.includes(bucketName)) {
        logSuccess(`Bucket '${bucketName}' exists`);
        addTestDetail('Storage', 'PASS', `Bucket '${bucketName}' found`);
      } else {
        logWarning(`Bucket '${bucketName}' not found - needs to be created`);
        addTestDetail('Storage', 'WARNING', `Bucket '${bucketName}' missing`);
      }
    }

    return true;
  } catch (error) {
    logError(`Storage test error: ${error.message}`);
    addTestDetail('Storage', 'FAIL', 'Storage test error', error);
    return false;
  }
}

// Test 6: File Upload/Download
async function testFileOperations() {
  log('\n📤 Testing File Operations...', colors.cyan);
  
  const bucketName = process.env.SUPABASE_STORAGE_BUCKET_PROPERTIES || 'property-images';
  const testFileName = `test-${Date.now()}.txt`;
  const testContent = 'This is a test file for Supabase storage validation.';

  try {
    // Create test file buffer
    const fileBuffer = Buffer.from(testContent, 'utf8');

    // Test upload
    const { data: uploadData, error: uploadError } = await supabaseAdmin.storage
      .from(bucketName)
      .upload(testFileName, fileBuffer, {
        contentType: 'text/plain'
      });

    if (uploadError) {
      if (uploadError.message.includes('Bucket not found')) {
        logWarning(`Bucket '${bucketName}' not found - file operations cannot be tested`);
        addTestDetail('File Operations', 'WARNING', 'Bucket not found for file operations');
        return false;
      } else {
        logError(`File upload failed: ${uploadError.message}`);
        addTestDetail('File Operations', 'FAIL', 'File upload failed', uploadError);
        return false;
      }
    }

    logSuccess('File upload successful');
    addTestDetail('File Operations', 'PASS', 'File upload works');

    // Test download
    const { data: downloadData, error: downloadError } = await supabaseAdmin.storage
      .from(bucketName)
      .download(testFileName);

    if (downloadError) {
      logError(`File download failed: ${downloadError.message}`);
      addTestDetail('File Operations', 'FAIL', 'File download failed', downloadError);
    } else {
      logSuccess('File download successful');
      addTestDetail('File Operations', 'PASS', 'File download works');
    }

    // Cleanup: Delete test file
    const { error: deleteError } = await supabaseAdmin.storage
      .from(bucketName)
      .remove([testFileName]);

    if (deleteError) {
      logWarning(`Could not cleanup test file: ${deleteError.message}`);
    } else {
      logInfo('Test file cleaned up');
    }

    return true;
  } catch (error) {
    logError(`File operations test error: ${error.message}`);
    addTestDetail('File Operations', 'FAIL', 'File operations test error', error);
    return false;
  }
}

// Main test runner
async function runAllTests() {
  log('🚀 Starting Supabase Integration Tests...', colors.bright);
  log('='.repeat(50), colors.cyan);

  const startTime = Date.now();

  // Run all tests
  await testEnvironmentVariables();
  await testDatabaseConnection();
  await testDatabaseSchema();
  await testAuthentication();
  await testStorageBuckets();
  await testFileOperations();

  const endTime = Date.now();
  const duration = ((endTime - startTime) / 1000).toFixed(2);

  // Print summary
  log('\n' + '='.repeat(50), colors.cyan);
  log('📊 Test Summary', colors.bright);
  log('='.repeat(50), colors.cyan);
  
  logSuccess(`Passed: ${testResults.passed}`);
  logError(`Failed: ${testResults.failed}`);
  logWarning(`Warnings: ${testResults.warnings}`);
  logInfo(`Duration: ${duration}s`);

  // Save detailed results
  const reportPath = path.join(__dirname, 'supabase-test-report.json');
  const report = {
    timestamp: new Date().toISOString(),
    duration: `${duration}s`,
    summary: {
      passed: testResults.passed,
      failed: testResults.failed,
      warnings: testResults.warnings,
      total: testResults.passed + testResults.failed + testResults.warnings
    },
    details: testResults.details,
    environment: {
      supabaseUrl: process.env.SUPABASE_URL,
      nodeVersion: process.version,
      platform: process.platform
    }
  };

  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  logInfo(`Detailed report saved to: ${reportPath}`);

  // Exit with appropriate code
  if (testResults.failed > 0) {
    log('\n❌ Some tests failed. Please check the issues above.', colors.red);
    process.exit(1);
  } else if (testResults.warnings > 0) {
    log('\n⚠️  All critical tests passed, but there are warnings to address.', colors.yellow);
    process.exit(0);
  } else {
    log('\n✅ All tests passed! Supabase integration is working correctly.', colors.green);
    process.exit(0);
  }
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runAllTests().catch(error => {
    logError(`Test runner error: ${error.message}`);
    console.error(error);
    process.exit(1);
  });
}

export {
  runAllTests,
  testResults
};
