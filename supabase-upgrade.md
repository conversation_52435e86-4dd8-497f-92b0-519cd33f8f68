# 🚀 Supabase Migration Guide for StayFinder

## Overview

This document outlines the complete migration from MySQL to Supabase for the StayFinder application. Supabase provides a modern PostgreSQL database with real-time capabilities, built-in authentication, and powerful APIs.

## 📋 Migration Benefits

### Why Supabase?
- **PostgreSQL Database**: More powerful than MySQL with JSON support, full-text search, and advanced indexing
- **Real-time Subscriptions**: Built-in real-time updates for bookings, messages, and property changes
- **Built-in Authentication**: Reduces custom auth code and provides social login options
- **Auto-generated APIs**: REST and GraphQL APIs generated automatically
- **Row Level Security (RLS)**: Database-level security policies
- **Edge Functions**: Serverless functions for custom business logic
- **Storage**: Built-in file storage for property images
- **Dashboard**: Web-based database management interface

## 🔧 Environment Configuration

### Required Environment Variables

Add these variables to your `.env` files:

```env
# Supabase Configuration
SUPABASE_URL=https://your-project-ref.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Database Configuration (for direct connections if needed)
DATABASE_URL=postgresql://postgres:[password]@db.[project-ref].supabase.co:5432/postgres

# Storage Configuration
SUPABASE_STORAGE_BUCKET=property-images

# Real-time Configuration
SUPABASE_REALTIME_ENABLED=true

# Authentication Configuration
SUPABASE_AUTH_ENABLED=true
SUPABASE_AUTH_PROVIDERS=email,google,facebook

# Edge Functions (optional)
SUPABASE_FUNCTIONS_URL=https://your-project-ref.functions.supabase.co
```

### Frontend Environment Variables (.env.local)

```env
# Supabase Public Configuration
VITE_SUPABASE_URL=https://your-project-ref.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key

# Feature Flags
VITE_ENABLE_REALTIME=true
VITE_ENABLE_SUPABASE_AUTH=true
VITE_ENABLE_SUPABASE_STORAGE=true
```

### Backend Environment Variables (.env)

```env
# Supabase Server Configuration
SUPABASE_URL=https://your-project-ref.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
SUPABASE_JWT_SECRET=your-jwt-secret

# Database Direct Connection (for migrations)
DATABASE_URL=postgresql://postgres:[password]@db.[project-ref].supabase.co:5432/postgres

# Legacy MySQL (during transition)
MYSQL_ENABLED=false
DB_HOST=localhost
DB_PORT=3306
DB_NAME=stayfinder_dev
DB_USER=root
DB_PASSWORD=
```

## 📊 Database Schema Migration

### 1. Table Structure Conversion

The existing MySQL schema will be converted to PostgreSQL with enhancements:

#### Users Table
```sql
-- Enhanced users table with Supabase auth integration
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    role user_role DEFAULT 'guest',
    avatar_url TEXT,
    email_verified BOOLEAN DEFAULT FALSE,
    auth_user_id UUID REFERENCES auth.users(id),
    preferences JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create custom enum for user roles
CREATE TYPE user_role AS ENUM ('guest', 'host', 'admin');
```

#### Properties Table
```sql
-- Enhanced properties table with PostgreSQL features
CREATE TABLE properties (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    owner_id UUID REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    property_type property_type_enum NOT NULL,
    location VARCHAR(255) NOT NULL,
    coordinates POINT, -- PostgreSQL geometric type
    max_guests INTEGER NOT NULL,
    bedrooms INTEGER NOT NULL,
    bathrooms INTEGER NOT NULL,
    price_per_night DECIMAL(10,2) NOT NULL,
    cleaning_fee DECIMAL(10,2) DEFAULT 0,
    amenities JSONB DEFAULT '[]', -- JSON array for flexible amenities
    house_rules TEXT[],  -- PostgreSQL array type
    images JSONB DEFAULT '[]', -- JSON array for image URLs
    availability_calendar JSONB DEFAULT '{}', -- JSON for calendar data
    check_in_time TIME DEFAULT '15:00:00',
    check_out_time TIME DEFAULT '11:00:00',
    status property_status DEFAULT 'active',
    search_vector TSVECTOR, -- Full-text search
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create enums
CREATE TYPE property_type_enum AS ENUM ('apartment', 'house', 'villa', 'guesthouse', 'cottage');
CREATE TYPE property_status AS ENUM ('active', 'inactive', 'maintenance', 'pending_approval');

-- Create indexes for performance
CREATE INDEX idx_properties_location ON properties USING GIN(to_tsvector('english', location));
CREATE INDEX idx_properties_search ON properties USING GIN(search_vector);
CREATE INDEX idx_properties_coordinates ON properties USING GIST(coordinates);
CREATE INDEX idx_properties_amenities ON properties USING GIN(amenities);
```

#### Bookings Table
```sql
-- Enhanced bookings table
CREATE TABLE bookings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    property_id UUID REFERENCES properties(id) ON DELETE CASCADE,
    guest_id UUID REFERENCES users(id) ON DELETE CASCADE,
    check_in_date DATE NOT NULL,
    check_out_date DATE NOT NULL,
    guest_count INTEGER NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    booking_status booking_status_enum DEFAULT 'pending',
    payment_status payment_status_enum DEFAULT 'pending',
    payment_intent_id VARCHAR(255), -- Stripe payment intent
    special_requests TEXT,
    cancellation_reason TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_dates CHECK (check_out_date > check_in_date),
    CONSTRAINT positive_amount CHECK (total_amount > 0),
    CONSTRAINT positive_guests CHECK (guest_count > 0)
);

-- Create enums
CREATE TYPE booking_status_enum AS ENUM ('pending', 'confirmed', 'cancelled', 'completed', 'no_show');
CREATE TYPE payment_status_enum AS ENUM ('pending', 'paid', 'failed', 'refunded', 'partially_refunded');
```

### 2. Migration Scripts

#### Data Migration Script
```sql
-- Migration script to transfer data from MySQL to Supabase
-- This would be run after setting up the new schema

-- Example for users migration
INSERT INTO users (id, email, first_name, last_name, phone, role, created_at, updated_at)
SELECT 
    id::UUID,
    email,
    first_name,
    last_name,
    phone,
    role::user_role,
    created_at,
    updated_at
FROM mysql_users_backup;
```

## 🔐 Authentication Migration

### 1. Supabase Auth Integration

Replace custom JWT authentication with Supabase Auth:

#### Frontend Auth Service
```typescript
// src/services/supabaseAuth.ts
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

export const authService = {
  // Sign up with email
  async signUp(email: string, password: string, userData: any) {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: userData
      }
    })
    return { data, error }
  },

  // Sign in with email
  async signIn(email: string, password: string) {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    })
    return { data, error }
  },

  // Sign out
  async signOut() {
    const { error } = await supabase.auth.signOut()
    return { error }
  },

  // Get current session
  async getSession() {
    const { data: { session } } = await supabase.auth.getSession()
    return session
  },

  // Listen to auth changes
  onAuthStateChange(callback: (event: string, session: any) => void) {
    return supabase.auth.onAuthStateChange(callback)
  }
}
```

### 2. Row Level Security (RLS) Policies

```sql
-- Enable RLS on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE properties ENABLE ROW LEVEL SECURITY;
ALTER TABLE bookings ENABLE ROW LEVEL SECURITY;
ALTER TABLE reviews ENABLE ROW LEVEL SECURITY;

-- Users can read their own data
CREATE POLICY "Users can read own data" ON users
    FOR SELECT USING (auth.uid() = auth_user_id);

-- Users can update their own data
CREATE POLICY "Users can update own data" ON users
    FOR UPDATE USING (auth.uid() = auth_user_id);

-- Properties policies
CREATE POLICY "Anyone can read active properties" ON properties
    FOR SELECT USING (status = 'active');

CREATE POLICY "Owners can manage their properties" ON properties
    FOR ALL USING (auth.uid() = (SELECT auth_user_id FROM users WHERE id = owner_id));

-- Bookings policies
CREATE POLICY "Users can read their bookings" ON bookings
    FOR SELECT USING (
        auth.uid() = (SELECT auth_user_id FROM users WHERE id = guest_id) OR
        auth.uid() = (SELECT auth_user_id FROM users WHERE id = (SELECT owner_id FROM properties WHERE id = property_id))
    );
```

## 📡 API Migration

### 1. Replace Custom Database Queries

#### Before (MySQL)
```javascript
// backend/src/utils/database.js
const mysql = require('mysql2/promise');

const findOne = async (query, params) => {
  const connection = await mysql.createConnection(dbConfig);
  const [rows] = await connection.execute(query, params);
  await connection.end();
  return rows[0];
};
```

#### After (Supabase)
```javascript
// backend/src/utils/supabase.js
const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

const findOne = async (table, filters) => {
  const { data, error } = await supabase
    .from(table)
    .select('*')
    .match(filters)
    .single();
  
  if (error) throw error;
  return data;
};
```

### 2. Real-time Subscriptions

```typescript
// Real-time booking updates
const subscribeToBookings = (userId: string, callback: Function) => {
  return supabase
    .channel('bookings')
    .on(
      'postgres_changes',
      {
        event: '*',
        schema: 'public',
        table: 'bookings',
        filter: `guest_id=eq.${userId}`
      },
      callback
    )
    .subscribe()
}
```

## 📁 File Storage Migration

### 1. Supabase Storage Setup

```typescript
// src/services/supabaseStorage.ts
export const storageService = {
  async uploadPropertyImage(file: File, propertyId: string) {
    const fileName = `${propertyId}/${Date.now()}-${file.name}`
    
    const { data, error } = await supabase.storage
      .from('property-images')
      .upload(fileName, file)
    
    if (error) throw error
    
    const { data: { publicUrl } } = supabase.storage
      .from('property-images')
      .getPublicUrl(fileName)
    
    return publicUrl
  },

  async deletePropertyImage(path: string) {
    const { error } = await supabase.storage
      .from('property-images')
      .remove([path])
    
    if (error) throw error
  }
}
```

## 🔄 Migration Steps

### Phase 1: Setup (Week 1)
1. Create Supabase project
2. Set up environment variables
3. Create database schema
4. Set up RLS policies
5. Configure storage buckets

### Phase 2: Backend Migration (Week 2)
1. Install Supabase client libraries
2. Replace database connection logic
3. Update API endpoints to use Supabase
4. Migrate authentication system
5. Update file upload logic

### Phase 3: Frontend Migration (Week 3)
1. Install Supabase client
2. Update auth context to use Supabase Auth
3. Replace API calls with Supabase queries
4. Implement real-time subscriptions
5. Update file upload components

### Phase 4: Data Migration (Week 4)
1. Export data from MySQL
2. Transform data for PostgreSQL
3. Import data to Supabase
4. Verify data integrity
5. Update application configuration

### Phase 5: Testing & Deployment (Week 5)
1. Run comprehensive tests
2. Performance testing
3. Security audit
4. Gradual rollout
5. Monitor and optimize

## 📦 Package Updates

### Frontend Dependencies
```json
{
  "dependencies": {
    "@supabase/supabase-js": "^2.38.0",
    "@supabase/auth-helpers-react": "^0.4.2",
    "@supabase/realtime-js": "^2.8.4"
  }
}
```

### Backend Dependencies
```json
{
  "dependencies": {
    "@supabase/supabase-js": "^2.38.0",
    "pg": "^8.11.3"
  },
  "devDependencies": {
    "@types/pg": "^8.10.7"
  }
}
```

## 🚨 Breaking Changes

### 1. Authentication
- JWT tokens now managed by Supabase
- User IDs change from string to UUID
- Session management handled by Supabase

### 2. Database
- MySQL-specific queries need conversion
- Date handling differences
- JSON column syntax changes

### 3. File Storage
- File URLs change to Supabase storage URLs
- Upload logic completely different
- Need to migrate existing files

## 📊 Performance Improvements

### Expected Benefits
- **Faster queries**: PostgreSQL performance optimizations
- **Real-time updates**: Instant UI updates without polling
- **Better caching**: Built-in query caching
- **CDN integration**: Global file delivery
- **Reduced server load**: Client-side queries

### Monitoring
- Use Supabase dashboard for query performance
- Monitor real-time connection usage
- Track storage bandwidth usage
- Set up alerts for rate limits

## 🔒 Security Enhancements

### Row Level Security
- Database-level security policies
- Automatic user isolation
- Fine-grained permissions

### Authentication Security
- Built-in rate limiting
- Social login options
- Email verification
- Password reset flows

## �️ Implementation Examples

### 1. Supabase Client Setup

#### Frontend Client (src/lib/supabase.ts)
```typescript
import { createClient } from '@supabase/supabase-js'
import { Database } from './database.types'

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY

export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  },
  realtime: {
    params: {
      eventsPerSecond: 10
    }
  }
})

// Type-safe database access
export type Tables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Row']
export type Enums<T extends keyof Database['public']['Enums']> = Database['public']['Enums'][T]
```

#### Backend Client (backend/src/lib/supabase.js)
```javascript
const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = process.env.SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

// Service role client for backend operations
const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

module.exports = { supabaseAdmin }
```

### 2. Database Type Generation

Generate TypeScript types from your Supabase schema:

```bash
# Install Supabase CLI
npm install -g supabase

# Login to Supabase
supabase login

# Generate types
supabase gen types typescript --project-id your-project-ref > src/lib/database.types.ts
```

### 3. Updated API Services

#### Property Service (src/services/propertyService.ts)
```typescript
import { supabase } from '@/lib/supabase'
import type { Tables } from '@/lib/database.types'

type Property = Tables<'properties'>
type PropertyInsert = Database['public']['Tables']['properties']['Insert']

export const propertyService = {
  // Get all properties with filters
  async getProperties(filters: {
    location?: string
    minPrice?: number
    maxPrice?: number
    propertyType?: string
    guests?: number
    page?: number
    limit?: number
  }) {
    let query = supabase
      .from('properties')
      .select(`
        *,
        users:owner_id (
          first_name,
          last_name,
          avatar_url
        )
      `)
      .eq('status', 'active')

    // Apply filters
    if (filters.location) {
      query = query.ilike('location', `%${filters.location}%`)
    }

    if (filters.minPrice) {
      query = query.gte('price_per_night', filters.minPrice)
    }

    if (filters.maxPrice) {
      query = query.lte('price_per_night', filters.maxPrice)
    }

    if (filters.propertyType) {
      query = query.eq('property_type', filters.propertyType)
    }

    if (filters.guests) {
      query = query.gte('max_guests', filters.guests)
    }

    // Pagination
    const page = filters.page || 1
    const limit = filters.limit || 12
    const from = (page - 1) * limit
    const to = from + limit - 1

    query = query.range(from, to)

    const { data, error, count } = await query

    if (error) throw error

    return {
      properties: data,
      total: count,
      page,
      limit,
      totalPages: Math.ceil((count || 0) / limit)
    }
  },

  // Create new property
  async createProperty(propertyData: PropertyInsert) {
    const { data, error } = await supabase
      .from('properties')
      .insert(propertyData)
      .select()
      .single()

    if (error) throw error
    return data
  },

  // Update property
  async updateProperty(id: string, updates: Partial<Property>) {
    const { data, error } = await supabase
      .from('properties')
      .update(updates)
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data
  },

  // Delete property
  async deleteProperty(id: string) {
    const { error } = await supabase
      .from('properties')
      .delete()
      .eq('id', id)

    if (error) throw error
  },

  // Search properties with full-text search
  async searchProperties(searchTerm: string) {
    const { data, error } = await supabase
      .from('properties')
      .select('*')
      .textSearch('search_vector', searchTerm)
      .eq('status', 'active')

    if (error) throw error
    return data
  }
}
```

### 4. Real-time Subscriptions

#### Booking Updates (src/hooks/useRealtimeBookings.ts)
```typescript
import { useEffect, useState } from 'react'
import { supabase } from '@/lib/supabase'
import type { Tables } from '@/lib/database.types'

type Booking = Tables<'bookings'>

export const useRealtimeBookings = (userId: string) => {
  const [bookings, setBookings] = useState<Booking[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Initial fetch
    const fetchBookings = async () => {
      const { data, error } = await supabase
        .from('bookings')
        .select('*')
        .eq('guest_id', userId)
        .order('created_at', { ascending: false })

      if (!error && data) {
        setBookings(data)
      }
      setLoading(false)
    }

    fetchBookings()

    // Set up real-time subscription
    const subscription = supabase
      .channel('bookings')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'bookings',
          filter: `guest_id=eq.${userId}`
        },
        (payload) => {
          if (payload.eventType === 'INSERT') {
            setBookings(prev => [payload.new as Booking, ...prev])
          } else if (payload.eventType === 'UPDATE') {
            setBookings(prev =>
              prev.map(booking =>
                booking.id === payload.new.id ? payload.new as Booking : booking
              )
            )
          } else if (payload.eventType === 'DELETE') {
            setBookings(prev =>
              prev.filter(booking => booking.id !== payload.old.id)
            )
          }
        }
      )
      .subscribe()

    return () => {
      subscription.unsubscribe()
    }
  }, [userId])

  return { bookings, loading }
}
```

### 5. File Storage Implementation

#### Image Upload Service (src/services/storageService.ts)
```typescript
import { supabase } from '@/lib/supabase'

export const storageService = {
  async uploadPropertyImages(files: File[], propertyId: string) {
    const uploadPromises = files.map(async (file, index) => {
      const fileExt = file.name.split('.').pop()
      const fileName = `${propertyId}/${Date.now()}-${index}.${fileExt}`

      const { data, error } = await supabase.storage
        .from('property-images')
        .upload(fileName, file, {
          cacheControl: '3600',
          upsert: false
        })

      if (error) throw error

      const { data: { publicUrl } } = supabase.storage
        .from('property-images')
        .getPublicUrl(fileName)

      return {
        path: data.path,
        url: publicUrl
      }
    })

    return Promise.all(uploadPromises)
  },

  async deletePropertyImage(path: string) {
    const { error } = await supabase.storage
      .from('property-images')
      .remove([path])

    if (error) throw error
  },

  async uploadUserAvatar(file: File, userId: string) {
    const fileExt = file.name.split('.').pop()
    const fileName = `${userId}/avatar.${fileExt}`

    const { data, error } = await supabase.storage
      .from('user-avatars')
      .upload(fileName, file, {
        cacheControl: '3600',
        upsert: true
      })

    if (error) throw error

    const { data: { publicUrl } } = supabase.storage
      .from('user-avatars')
      .getPublicUrl(fileName)

    return publicUrl
  }
}
```

## 🔄 Migration Checklist

### Pre-Migration
- [ ] Create Supabase project
- [ ] Set up environment variables
- [ ] Install Supabase CLI
- [ ] Generate TypeScript types
- [ ] Set up storage buckets
- [ ] Configure authentication providers

### Database Migration
- [ ] Create PostgreSQL schema
- [ ] Set up Row Level Security policies
- [ ] Create database functions and triggers
- [ ] Set up full-text search indexes
- [ ] Import data from MySQL
- [ ] Verify data integrity

### Code Migration
- [ ] Update database connection logic
- [ ] Replace authentication system
- [ ] Update API endpoints
- [ ] Implement real-time subscriptions
- [ ] Update file upload logic
- [ ] Update frontend services

### Testing
- [ ] Unit tests pass
- [ ] Integration tests pass
- [ ] E2E tests pass
- [ ] Performance testing
- [ ] Security audit
- [ ] Load testing

### Deployment
- [ ] Update production environment variables
- [ ] Deploy backend changes
- [ ] Deploy frontend changes
- [ ] Monitor error rates
- [ ] Verify all features working

## �📚 Additional Resources

- [Supabase Documentation](https://supabase.com/docs)
- [PostgreSQL Migration Guide](https://supabase.com/docs/guides/database/migrating-to-supabase)
- [Real-time Subscriptions](https://supabase.com/docs/guides/realtime)
- [Row Level Security](https://supabase.com/docs/guides/auth/row-level-security)
- [Supabase CLI Reference](https://supabase.com/docs/reference/cli)
- [TypeScript Support](https://supabase.com/docs/guides/api/generating-types)

---

**Next Steps**: Follow the migration phases and update the codebase incrementally to ensure a smooth transition from MySQL to Supabase.
