import React, { useEffect, useState } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { Header } from '../components/Header';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  XCircle, 
  AlertTriangle,
  Calendar,
  MapPin,
  Users,
  DollarSign,
  RefreshCw,
  ArrowLeft,
  Home,
  Clock
} from 'lucide-react';

export const PaymentCancel: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [bookingDetails, setBookingDetails] = useState<any>(null);

  useEffect(() => {
    // Get booking details from URL params or localStorage
    const bookingId = searchParams.get('booking_id');
    
    // For demonstration, use mock data
    setBookingDetails({
      id: bookingId || 'booking-1',
      property_title: 'Beachfront Villa in Margate',
      property_location: 'Margate, KZN',
      check_in_date: '2024-06-25',
      check_out_date: '2024-06-28',
      guest_count: 4,
      total_amount: 1200.00,
      payment_status: 'cancelled'
    });
  }, [searchParams]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
      minimumFractionDigits: 2,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-ZA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const handleRetryPayment = () => {
    // Navigate back to booking with payment form
    navigate(`/property/${bookingDetails.property_id}?booking=${bookingDetails.id}`);
  };

  const handleViewBookings = () => {
    navigate('/bookings');
  };

  const handleBackToHome = () => {
    navigate('/');
  };

  const handleSearchAgain = () => {
    navigate('/search');
  };

  if (!bookingDetails) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-2xl mx-auto">
            <Card>
              <CardContent className="p-8 text-center">
                <div className="animate-pulse space-y-4">
                  <div className="h-12 w-12 bg-gray-200 rounded-full mx-auto"></div>
                  <div className="h-6 bg-gray-200 rounded w-3/4 mx-auto"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/2 mx-auto"></div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto space-y-6">
          {/* Cancel Header */}
          <Card className="border-orange-200 bg-orange-50">
            <CardContent className="p-8 text-center">
              <div className="text-orange-600 mb-4">
                <XCircle className="h-16 w-16 mx-auto" />
              </div>
              <h1 className="text-2xl font-bold text-orange-800 mb-2">
                Payment Cancelled
              </h1>
              <p className="text-orange-700 mb-4">
                Your payment was cancelled and no charges were made to your account.
              </p>
              <Badge className="bg-orange-100 text-orange-800 px-4 py-2">
                No Payment Processed
              </Badge>
            </CardContent>
          </Card>

          {/* Booking Details */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MapPin className="h-5 w-5" />
                Booking Details
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="font-semibold text-lg text-gray-900">
                  {bookingDetails.property_title}
                </h3>
                {bookingDetails.property_location && (
                  <p className="text-gray-600">{bookingDetails.property_location}</p>
                )}
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-gray-600">Check-in</p>
                    <p className="font-medium">{formatDate(bookingDetails.check_in_date)}</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-gray-600">Check-out</p>
                    <p className="font-medium">{formatDate(bookingDetails.check_out_date)}</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-gray-600">Guests</p>
                    <p className="font-medium">{bookingDetails.guest_count}</p>
                  </div>
                </div>
              </div>
              
              <div className="border-t pt-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Total Amount</span>
                  <span className="text-xl font-bold">{formatCurrency(bookingDetails.total_amount)}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* What Happened */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5 text-orange-600" />
                What Happened?
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex items-start gap-3">
                  <XCircle className="h-5 w-5 text-red-600 mt-0.5" />
                  <div>
                    <h4 className="font-medium">Payment Cancelled</h4>
                    <p className="text-sm text-gray-600">
                      You cancelled the payment process before completion.
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start gap-3">
                  <Clock className="h-5 w-5 text-blue-600 mt-0.5" />
                  <div>
                    <h4 className="font-medium">Booking Status</h4>
                    <p className="text-sm text-gray-600">
                      Your booking is still pending and requires payment to be confirmed.
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start gap-3">
                  <AlertTriangle className="h-5 w-5 text-orange-600 mt-0.5" />
                  <div>
                    <h4 className="font-medium">Time Limit</h4>
                    <p className="text-sm text-gray-600">
                      You have 24 hours to complete payment before the booking expires.
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Next Steps */}
          <Card>
            <CardHeader>
              <CardTitle>What Would You Like to Do?</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="p-4 border border-green-200 rounded-lg bg-green-50">
                  <h4 className="font-medium text-green-800 mb-2">Complete Your Booking</h4>
                  <p className="text-sm text-green-700 mb-3">
                    Return to payment to secure your accommodation.
                  </p>
                  <Button 
                    onClick={handleRetryPayment}
                    className="w-full bg-green-600 hover:bg-green-700 text-white"
                  >
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Complete Payment
                  </Button>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <div className="p-4 border border-gray-200 rounded-lg">
                    <h4 className="font-medium text-gray-800 mb-2">View My Bookings</h4>
                    <p className="text-sm text-gray-600 mb-3">
                      Check all your current bookings.
                    </p>
                    <Button 
                      variant="outline" 
                      onClick={handleViewBookings}
                      className="w-full"
                    >
                      <Calendar className="h-4 w-4 mr-2" />
                      My Bookings
                    </Button>
                  </div>
                  
                  <div className="p-4 border border-gray-200 rounded-lg">
                    <h4 className="font-medium text-gray-800 mb-2">Search Again</h4>
                    <p className="text-sm text-gray-600 mb-3">
                      Find other available properties.
                    </p>
                    <Button 
                      variant="outline" 
                      onClick={handleSearchAgain}
                      className="w-full"
                    >
                      <MapPin className="h-4 w-4 mr-2" />
                      Search Properties
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Support Information */}
          <Card className="border-blue-200 bg-blue-50">
            <CardContent className="p-6">
              <div className="text-center">
                <h4 className="font-medium text-blue-800 mb-2">Need Help?</h4>
                <p className="text-sm text-blue-700 mb-4">
                  If you're experiencing issues with payment, our support team is here to help.
                </p>
                <div className="flex flex-col sm:flex-row gap-3 justify-center">
                  <Button variant="outline" size="sm">
                    Contact Support
                  </Button>
                  <Button variant="outline" size="sm">
                    Payment FAQ
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Back to Home */}
          <div className="text-center">
            <Button
              variant="ghost"
              onClick={handleBackToHome}
              className="text-gray-600 hover:text-gray-800"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Home
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};
