<?php
// Quick Database Restore Script for StayFinder
// Run this in browser: http://localhost/stayfinder/restore_database.php

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔧 StayFinder Database Restore</h1>";
echo "<p>Restoring your StayFinder database...</p>";

try {
    // Database connection
    $host = 'localhost';
    $username = 'root';
    $password = '';
    
    // Connect to MySQL
    $pdo = new PDO("mysql:host=$host", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p>✅ Connected to MySQL server</p>";
    
    // Read the SQL file
    $sqlFile = __DIR__ . '/database_init.sql';
    if (!file_exists($sqlFile)) {
        throw new Exception("Database initialization file not found: $sqlFile");
    }
    
    $sql = file_get_contents($sqlFile);
    echo "<p>✅ Read database initialization file</p>";
    
    // Split SQL into individual statements
    $statements = array_filter(
        array_map('trim', explode(';', $sql)),
        function($stmt) {
            return !empty($stmt) && !preg_match('/^--/', $stmt);
        }
    );
    
    echo "<p>📊 Found " . count($statements) . " SQL statements to execute</p>";
    
    // Execute each statement
    $executed = 0;
    foreach ($statements as $statement) {
        if (!empty(trim($statement))) {
            try {
                $pdo->exec($statement);
                $executed++;
            } catch (PDOException $e) {
                // Skip errors for statements that might already exist
                if (strpos($e->getMessage(), 'already exists') === false && 
                    strpos($e->getMessage(), 'Duplicate entry') === false) {
                    echo "<p>⚠️ Warning: " . htmlspecialchars($e->getMessage()) . "</p>";
                }
            }
        }
    }
    
    echo "<p>✅ Executed $executed SQL statements successfully</p>";
    
    // Verify database and tables
    $pdo->exec("USE stayfinder_dev");
    $result = $pdo->query("SHOW TABLES");
    $tables = $result->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<h3>📋 Database Tables Created:</h3>";
    echo "<ul>";
    foreach ($tables as $table) {
        echo "<li>✅ $table</li>";
    }
    echo "</ul>";
    
    // Check sample data
    echo "<h3>📈 Sample Data Loaded:</h3>";
    $dataTables = ['users', 'properties', 'property_images', 'bookings', 'reviews'];
    
    foreach ($dataTables as $table) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
            $count = $stmt->fetchColumn();
            echo "<p>✅ $table: $count records</p>";
        } catch (PDOException $e) {
            echo "<p>❌ $table: table not found</p>";
        }
    }
    
    echo "<h3>🎉 Database Restore Complete!</h3>";
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>✅ Your StayFinder database has been restored successfully!</h4>";
    echo "<p><strong>Database:</strong> stayfinder_dev</p>";
    echo "<p><strong>Test User Accounts:</strong></p>";
    echo "<ul>";
    echo "<li>Guest: <EMAIL> (password: password123)</li>";
    echo "<li>Host: <EMAIL> (password: password123)</li>";
    echo "<li>Admin: <EMAIL> (password: password123)</li>";
    echo "</ul>";
    echo "<p><strong>Sample Properties:</strong> 5 properties loaded with images and reviews</p>";
    echo "</div>";
    
    echo "<p><a href='http://localhost:5173' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🚀 Go to StayFinder Site</a></p>";
    echo "<p><a href='http://localhost/phpmyadmin' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>📊 View in phpMyAdmin</a></p>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>❌ Database Restore Failed</h4>";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>Manual Setup Instructions:</strong></p>";
    echo "<ol>";
    echo "<li>Open <a href='http://localhost/phpmyadmin'>phpMyAdmin</a></li>";
    echo "<li>Click 'Import' tab</li>";
    echo "<li>Choose file: database_init.sql</li>";
    echo "<li>Click 'Go' to import</li>";
    echo "</ol>";
    echo "</div>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background: #f8f9fa;
}
h1 { color: #007bff; }
h3 { color: #28a745; }
p { margin: 10px 0; }
ul { margin: 10px 0; }
li { margin: 5px 0; }
</style>
