import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  MapPin,
  Star,
  Coffee,
  ShoppingBag,
  Utensils,
  Car,
  Waves,
  TreePine,
  Building,
  Camera,
  Clock,
  Navigation,
  Walking,
  Hospital,
  GraduationCap,
  Shield,
  Zap,
  Mountain,
  Trees,
  Plane,
  Train,
  Bus,
  Target,
  TrendingUp
} from 'lucide-react';
import {
  HoverAnimation,
  SlideIn,
  StaggeredAnimation
} from '@/components/ui/page-transitions';
import { QuickTooltip } from '@/components/ui/enhanced-tooltip';

interface PointOfInterest {
  id: string;
  name: string;
  type: 'restaurant' | 'attraction' | 'shopping' | 'beach' | 'nature' | 'transport';
  distance: string;
  rating?: number;
  description: string;
  walkTime?: string;
  driveTime?: string;
}

interface NeighborhoodData {
  name: string;
  description: string;
  highlights: string[];
  walkScore?: number;
  safetyRating?: number;
  pointsOfInterest: PointOfInterest[];
  demographics?: {
    familyFriendly: boolean;
    nightlife: boolean;
    quiet: boolean;
    touristy: boolean;
  };
}

interface NeighborhoodInfoProps {
  location: string;
  className?: string;
}

// Mock neighborhood data for South African coastal towns
const NEIGHBORHOOD_DATA: Record<string, NeighborhoodData> = {
  'Margate': {
    name: 'Margate',
    description: 'A vibrant coastal town known for its beautiful beaches, family-friendly atmosphere, and excellent seafood restaurants. Popular with both locals and tourists.',
    highlights: [
      'Blue Flag beach status',
      'Family-friendly attractions',
      'Excellent seafood restaurants',
      'Water sports activities',
      'Shopping centers nearby'
    ],
    walkScore: 75,
    safetyRating: 4.2,
    demographics: {
      familyFriendly: true,
      nightlife: false,
      quiet: false,
      touristy: true
    },
    pointsOfInterest: [
      {
        id: '1',
        name: 'Margate Main Beach',
        type: 'beach',
        distance: '0.2 km',
        rating: 4.5,
        description: 'Blue Flag beach with lifeguards and facilities',
        walkTime: '3 min',
        driveTime: '1 min'
      },
      {
        id: '2',
        name: 'Margate Spur',
        type: 'restaurant',
        distance: '0.5 km',
        rating: 4.2,
        description: 'Family restaurant with great steaks and burgers',
        walkTime: '6 min',
        driveTime: '2 min'
      },
      {
        id: '3',
        name: 'Margate Shopping Centre',
        type: 'shopping',
        distance: '1.2 km',
        rating: 4.0,
        description: 'Local shopping center with groceries and shops',
        walkTime: '15 min',
        driveTime: '4 min'
      },
      {
        id: '4',
        name: 'Ski Boat Club',
        type: 'attraction',
        distance: '0.8 km',
        rating: 4.3,
        description: 'Water sports and boat launching facilities',
        walkTime: '10 min',
        driveTime: '3 min'
      }
    ]
  },
  'Scottburgh': {
    name: 'Scottburgh',
    description: 'A charming seaside town with a rich history, beautiful beaches, and a relaxed atmosphere. Known for its lighthouse and excellent fishing spots.',
    highlights: [
      'Historic lighthouse',
      'Excellent fishing spots',
      'Quiet beaches',
      'Golf course nearby',
      'Local markets'
    ],
    walkScore: 65,
    safetyRating: 4.0,
    demographics: {
      familyFriendly: true,
      nightlife: false,
      quiet: true,
      touristy: false
    },
    pointsOfInterest: [
      {
        id: '1',
        name: 'Scottburgh Beach',
        type: 'beach',
        distance: '0.3 km',
        rating: 4.3,
        description: 'Peaceful beach perfect for swimming and sunbathing',
        walkTime: '4 min',
        driveTime: '1 min'
      },
      {
        id: '2',
        name: 'Scottburgh Lighthouse',
        type: 'attraction',
        distance: '0.6 km',
        rating: 4.4,
        description: 'Historic lighthouse with panoramic ocean views',
        walkTime: '8 min',
        driveTime: '2 min'
      },
      {
        id: '3',
        name: 'Scottburgh Golf Club',
        type: 'attraction',
        distance: '2.1 km',
        rating: 4.1,
        description: '18-hole golf course with ocean views',
        walkTime: '25 min',
        driveTime: '6 min'
      }
    ]
  }
};

const getTypeIcon = (type: string) => {
  switch (type) {
    case 'restaurant': return <Utensils className="h-4 w-4" />;
    case 'attraction': return <Camera className="h-4 w-4" />;
    case 'shopping': return <ShoppingBag className="h-4 w-4" />;
    case 'beach': return <Waves className="h-4 w-4" />;
    case 'nature': return <TreePine className="h-4 w-4" />;
    case 'transport': return <Car className="h-4 w-4" />;
    default: return <MapPin className="h-4 w-4" />;
  }
};

const getTypeColor = (type: string) => {
  switch (type) {
    case 'restaurant': return 'bg-orange-100 text-orange-700 border-orange-200';
    case 'attraction': return 'bg-purple-100 text-purple-700 border-purple-200';
    case 'shopping': return 'bg-pink-100 text-pink-700 border-pink-200';
    case 'beach': return 'bg-blue-100 text-blue-700 border-blue-200';
    case 'nature': return 'bg-green-100 text-green-700 border-green-200';
    case 'transport': return 'bg-gray-100 text-gray-700 border-gray-200';
    default: return 'bg-gray-100 text-gray-700 border-gray-200';
  }
};

export const NeighborhoodInfo: React.FC<NeighborhoodInfoProps> = ({
  location,
  className
}) => {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  
  const neighborhoodData = NEIGHBORHOOD_DATA[location];

  if (!neighborhoodData) {
    return (
      <Card className={className}>
        <CardContent className="p-6 text-center">
          <MapPin className="h-12 w-12 mx-auto mb-3 text-gray-400" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Neighborhood information not available
          </h3>
          <p className="text-gray-600">
            We're working on adding detailed information for {location}.
          </p>
        </CardContent>
      </Card>
    );
  }

  const categories = [
    { id: 'all', label: 'All', icon: MapPin },
    { id: 'beach', label: 'Beaches', icon: Waves },
    { id: 'restaurant', label: 'Dining', icon: Utensils },
    { id: 'attraction', label: 'Attractions', icon: Camera },
    { id: 'shopping', label: 'Shopping', icon: ShoppingBag }
  ];

  const filteredPOIs = selectedCategory === 'all' 
    ? neighborhoodData.pointsOfInterest
    : neighborhoodData.pointsOfInterest.filter(poi => poi.type === selectedCategory);

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Neighborhood Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MapPin className="h-5 w-5" />
            About {neighborhoodData.name}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-gray-700">{neighborhoodData.description}</p>
          
          {/* Highlights */}
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Neighborhood Highlights</h4>
            <div className="flex flex-wrap gap-2">
              {neighborhoodData.highlights.map((highlight, index) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {highlight}
                </Badge>
              ))}
            </div>
          </div>

          {/* Scores */}
          <div className="grid grid-cols-2 gap-4">
            {neighborhoodData.walkScore && (
              <div className="text-center p-3 bg-green-50 rounded-lg border border-green-200">
                <div className="text-2xl font-bold text-green-600">
                  {neighborhoodData.walkScore}
                </div>
                <div className="text-sm text-green-700">Walk Score</div>
              </div>
            )}
            {neighborhoodData.safetyRating && (
              <div className="text-center p-3 bg-blue-50 rounded-lg border border-blue-200">
                <div className="flex items-center justify-center gap-1">
                  <Star className="h-4 w-4 text-yellow-400 fill-current" />
                  <span className="text-2xl font-bold text-blue-600">
                    {neighborhoodData.safetyRating}
                  </span>
                </div>
                <div className="text-sm text-blue-700">Safety Rating</div>
              </div>
            )}
          </div>

          {/* Demographics */}
          {neighborhoodData.demographics && (
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Area Characteristics</h4>
              <div className="flex flex-wrap gap-2">
                {neighborhoodData.demographics.familyFriendly && (
                  <Badge className="bg-green-100 text-green-700 border-green-200">
                    Family Friendly
                  </Badge>
                )}
                {neighborhoodData.demographics.quiet && (
                  <Badge className="bg-blue-100 text-blue-700 border-blue-200">
                    Quiet Area
                  </Badge>
                )}
                {neighborhoodData.demographics.touristy && (
                  <Badge className="bg-orange-100 text-orange-700 border-orange-200">
                    Tourist Area
                  </Badge>
                )}
                {neighborhoodData.demographics.nightlife && (
                  <Badge className="bg-purple-100 text-purple-700 border-purple-200">
                    Nightlife
                  </Badge>
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Points of Interest */}
      <Card>
        <CardHeader>
          <CardTitle>Nearby Points of Interest</CardTitle>
          <div className="flex flex-wrap gap-2">
            {categories.map((category) => (
              <Button
                key={category.id}
                variant={selectedCategory === category.id ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedCategory(category.id)}
                className="flex items-center gap-1"
              >
                <category.icon className="h-3 w-3" />
                {category.label}
              </Button>
            ))}
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {filteredPOIs.map((poi) => (
              <div key={poi.id} className="flex items-start gap-3 p-3 border rounded-lg hover:bg-gray-50 transition-colors">
                <div className={`p-2 rounded-lg border ${getTypeColor(poi.type)}`}>
                  {getTypeIcon(poi.type)}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-start justify-between">
                    <div>
                      <h4 className="font-medium text-gray-900">{poi.name}</h4>
                      <p className="text-sm text-gray-600 mt-1">{poi.description}</p>
                    </div>
                    <div className="text-right ml-4">
                      <div className="text-sm font-medium text-gray-900">{poi.distance}</div>
                      {poi.rating && (
                        <div className="flex items-center gap-1 mt-1">
                          <Star className="h-3 w-3 text-yellow-400 fill-current" />
                          <span className="text-xs text-gray-600">{poi.rating}</span>
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center gap-4 mt-2 text-xs text-gray-500">
                    {poi.walkTime && (
                      <div className="flex items-center gap-1">
                        <Navigation className="h-3 w-3" />
                        {poi.walkTime} walk
                      </div>
                    )}
                    {poi.driveTime && (
                      <div className="flex items-center gap-1">
                        <Car className="h-3 w-3" />
                        {poi.driveTime} drive
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {filteredPOIs.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <MapPin className="h-12 w-12 mx-auto mb-2 opacity-50" />
              <p>No {selectedCategory === 'all' ? 'points of interest' : categories.find(c => c.id === selectedCategory)?.label.toLowerCase()} found</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
