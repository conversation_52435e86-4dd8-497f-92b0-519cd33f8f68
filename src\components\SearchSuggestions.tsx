import React, { useState, useEffect, useRef } from 'react';
import { advancedSearchService, SearchSuggestion } from '../services/advancedSearchService';
import { 
  Search, 
  MapPin, 
  Home, 
  Star,
  Clock,
  TrendingUp
} from 'lucide-react';

interface SearchSuggestionsProps {
  query: string;
  category?: 'location' | 'property_type' | 'amenity' | 'general';
  onSuggestionSelect: (suggestion: string) => void;
  onClose?: () => void;
  className?: string;
  placeholder?: string;
}

export const SearchSuggestions: React.FC<SearchSuggestionsProps> = ({
  query,
  category,
  onSuggestionSelect,
  onClose,
  className,
  placeholder = "Search locations, property types, amenities..."
}) => {
  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const suggestionsRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (query.trim().length >= 2) {
      fetchSuggestions();
    } else {
      setSuggestions([]);
    }
    setSelectedIndex(-1);
  }, [query, category]);

  const fetchSuggestions = async () => {
    try {
      setLoading(true);
      const data = await advancedSearchService.getSearchSuggestions(query, category, 8);
      setSuggestions(data);
    } catch (error) {
      console.error('Error fetching suggestions:', error);
      setSuggestions([]);
    } finally {
      setLoading(false);
    }
  };

  const handleSuggestionClick = async (suggestion: SearchSuggestion) => {
    // Update suggestion count
    await advancedSearchService.updateSearchSuggestion(suggestion.term, suggestion.category);
    
    onSuggestionSelect(suggestion.term);
    onClose?.();
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (suggestions.length === 0) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev < suggestions.length - 1 ? prev + 1 : 0
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev > 0 ? prev - 1 : suggestions.length - 1
        );
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0 && selectedIndex < suggestions.length) {
          handleSuggestionClick(suggestions[selectedIndex]);
        }
        break;
      case 'Escape':
        onClose?.();
        break;
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'location':
        return <MapPin className="h-4 w-4 text-blue-500" />;
      case 'property_type':
        return <Home className="h-4 w-4 text-green-500" />;
      case 'amenity':
        return <Star className="h-4 w-4 text-yellow-500" />;
      default:
        return <Search className="h-4 w-4 text-gray-500" />;
    }
  };

  const getCategoryLabel = (category: string) => {
    switch (category) {
      case 'location':
        return 'Location';
      case 'property_type':
        return 'Property Type';
      case 'amenity':
        return 'Amenity';
      default:
        return 'General';
    }
  };

  const highlightMatch = (text: string, query: string) => {
    if (!query.trim()) return text;
    
    const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
    const parts = text.split(regex);
    
    return parts.map((part, index) => 
      regex.test(part) ? (
        <span key={index} className="font-semibold text-sea-green-600">
          {part}
        </span>
      ) : (
        part
      )
    );
  };

  if (!query.trim() || query.length < 2) {
    return null;
  }

  return (
    <div 
      ref={suggestionsRef}
      className={`absolute top-full left-0 right-0 bg-white border border-gray-200 rounded-md shadow-lg z-50 max-h-80 overflow-y-auto ${className}`}
      onKeyDown={handleKeyDown}
    >
      {loading ? (
        <div className="p-4 text-center">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-sea-green-500 mx-auto"></div>
          <p className="text-sm text-gray-500 mt-2">Searching...</p>
        </div>
      ) : suggestions.length > 0 ? (
        <div className="py-2">
          {suggestions.map((suggestion, index) => (
            <div
              key={`${suggestion.term}-${suggestion.category}`}
              onClick={() => handleSuggestionClick(suggestion)}
              className={`px-4 py-3 cursor-pointer transition-colors ${
                index === selectedIndex 
                  ? 'bg-sea-green-50 border-l-4 border-sea-green-500' 
                  : 'hover:bg-gray-50'
              }`}
            >
              <div className="flex items-center gap-3">
                {getCategoryIcon(suggestion.category)}
                
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium text-gray-900">
                      {highlightMatch(suggestion.term, query)}
                    </span>
                    <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                      {getCategoryLabel(suggestion.category)}
                    </span>
                  </div>
                  
                  <div className="flex items-center gap-2 mt-1">
                    <TrendingUp className="h-3 w-3 text-gray-400" />
                    <span className="text-xs text-gray-500">
                      {suggestion.search_count} searches
                    </span>
                  </div>
                </div>
                
                <div className="text-xs text-gray-400">
                  <Search className="h-3 w-3" />
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="p-4 text-center">
          <Search className="h-8 w-8 mx-auto mb-2 text-gray-400" />
          <p className="text-sm text-gray-500">No suggestions found</p>
          <p className="text-xs text-gray-400 mt-1">
            Try searching for locations, property types, or amenities
          </p>
        </div>
      )}
    </div>
  );
};

// Enhanced Search Input with Suggestions
interface SearchInputWithSuggestionsProps {
  value: string;
  onChange: (value: string) => void;
  onSearch?: (value: string) => void;
  category?: 'location' | 'property_type' | 'amenity' | 'general';
  placeholder?: string;
  className?: string;
}

export const SearchInputWithSuggestions: React.FC<SearchInputWithSuggestionsProps> = ({
  value,
  onChange,
  onSearch,
  category,
  placeholder = "Search...",
  className
}) => {
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setShowSuggestions(false);
        setIsFocused(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    onChange(newValue);
    setShowSuggestions(newValue.length >= 2);
  };

  const handleInputFocus = () => {
    setIsFocused(true);
    if (value.length >= 2) {
      setShowSuggestions(true);
    }
  };

  const handleSuggestionSelect = (suggestion: string) => {
    onChange(suggestion);
    setShowSuggestions(false);
    inputRef.current?.blur();
    onSearch?.(suggestion);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      setShowSuggestions(false);
      onSearch?.(value);
    }
  };

  return (
    <div ref={containerRef} className={`relative ${className}`}>
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
        <input
          ref={inputRef}
          type="text"
          value={value}
          onChange={handleInputChange}
          onFocus={handleInputFocus}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          className={`w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md text-gray-900 bg-white placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-sea-green-500 focus:border-transparent transition-all duration-200 ${
            isFocused ? 'ring-2 ring-sea-green-500 border-transparent' : ''
          }`}
        />
      </div>
      
      {showSuggestions && (
        <SearchSuggestions
          query={value}
          category={category}
          onSuggestionSelect={handleSuggestionSelect}
          onClose={() => setShowSuggestions(false)}
        />
      )}
    </div>
  );
};
