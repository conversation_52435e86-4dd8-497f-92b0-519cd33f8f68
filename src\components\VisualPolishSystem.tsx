import React, { useState, useEffect } from 'react';
import { Palette, Type, Layers, Zap, Eye, Contrast, Sun, Moon } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { cn } from '@/lib/utils';

interface VisualTheme {
  name: string;
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    foreground: string;
  };
  typography: {
    fontFamily: string;
    fontSize: number;
    fontWeight: number;
    lineHeight: number;
  };
  spacing: {
    scale: number;
    borderRadius: number;
  };
  shadows: {
    intensity: number;
    blur: number;
  };
}

interface AccessibilitySettings {
  highContrast: boolean;
  reducedMotion: boolean;
  largeText: boolean;
  focusIndicators: boolean;
}

export const VisualPolishSystem: React.FC = () => {
  const [currentTheme, setCurrentTheme] = useState<VisualTheme>({
    name: 'StayFinder Default',
    colors: {
      primary: '#14b8a6',
      secondary: '#3b82f6',
      accent: '#f59e0b',
      background: '#ffffff',
      foreground: '#1f2937'
    },
    typography: {
      fontFamily: 'Inter',
      fontSize: 16,
      fontWeight: 400,
      lineHeight: 1.5
    },
    spacing: {
      scale: 1,
      borderRadius: 8
    },
    shadows: {
      intensity: 0.1,
      blur: 10
    }
  });

  const [accessibility, setAccessibility] = useState<AccessibilitySettings>({
    highContrast: false,
    reducedMotion: false,
    largeText: false,
    focusIndicators: true
  });

  const [isDarkMode, setIsDarkMode] = useState(false);

  useEffect(() => {
    applyTheme();
  }, [currentTheme, accessibility, isDarkMode]);

  const applyTheme = () => {
    const root = document.documentElement;
    
    // Apply color scheme
    if (isDarkMode) {
      root.style.setProperty('--background', '#0f172a');
      root.style.setProperty('--foreground', '#f8fafc');
      root.style.setProperty('--card', '#1e293b');
      root.style.setProperty('--card-foreground', '#f8fafc');
    } else {
      root.style.setProperty('--background', currentTheme.colors.background);
      root.style.setProperty('--foreground', currentTheme.colors.foreground);
      root.style.setProperty('--card', '#ffffff');
      root.style.setProperty('--card-foreground', currentTheme.colors.foreground);
    }

    // Apply primary colors
    root.style.setProperty('--primary', currentTheme.colors.primary);
    root.style.setProperty('--secondary', currentTheme.colors.secondary);
    root.style.setProperty('--accent', currentTheme.colors.accent);

    // Apply typography
    root.style.setProperty('--font-family', currentTheme.typography.fontFamily);
    root.style.setProperty('--font-size', `${currentTheme.typography.fontSize}px`);
    root.style.setProperty('--font-weight', currentTheme.typography.fontWeight.toString());
    root.style.setProperty('--line-height', currentTheme.typography.lineHeight.toString());

    // Apply spacing
    root.style.setProperty('--spacing-scale', currentTheme.spacing.scale.toString());
    root.style.setProperty('--border-radius', `${currentTheme.spacing.borderRadius}px`);

    // Apply shadows
    const shadowColor = isDarkMode ? '0, 0, 0' : '0, 0, 0';
    root.style.setProperty('--shadow-sm', `0 1px 2px 0 rgba(${shadowColor}, ${currentTheme.shadows.intensity})`);
    root.style.setProperty('--shadow-md', `0 4px 6px -1px rgba(${shadowColor}, ${currentTheme.shadows.intensity}), 0 2px 4px -2px rgba(${shadowColor}, ${currentTheme.shadows.intensity})`);
    root.style.setProperty('--shadow-lg', `0 10px 15px -3px rgba(${shadowColor}, ${currentTheme.shadows.intensity}), 0 4px 6px -4px rgba(${shadowColor}, ${currentTheme.shadows.intensity})`);

    // Apply accessibility settings
    if (accessibility.highContrast) {
      root.style.setProperty('--primary', '#000000');
      root.style.setProperty('--background', '#ffffff');
      root.style.setProperty('--foreground', '#000000');
    }

    if (accessibility.reducedMotion) {
      root.style.setProperty('--animation-duration', '0.01ms');
      root.style.setProperty('--transition-duration', '0.01ms');
    } else {
      root.style.setProperty('--animation-duration', '300ms');
      root.style.setProperty('--transition-duration', '150ms');
    }

    if (accessibility.largeText) {
      root.style.setProperty('--font-size', `${currentTheme.typography.fontSize * 1.25}px`);
    }

    if (accessibility.focusIndicators) {
      root.style.setProperty('--focus-ring', `2px solid ${currentTheme.colors.primary}`);
      root.style.setProperty('--focus-ring-offset', '2px');
    }
  };

  const presetThemes: VisualTheme[] = [
    {
      name: 'Ocean Breeze',
      colors: {
        primary: '#0ea5e9',
        secondary: '#06b6d4',
        accent: '#f0f9ff',
        background: '#ffffff',
        foreground: '#0f172a'
      },
      typography: {
        fontFamily: 'Inter',
        fontSize: 16,
        fontWeight: 400,
        lineHeight: 1.6
      },
      spacing: {
        scale: 1,
        borderRadius: 12
      },
      shadows: {
        intensity: 0.08,
        blur: 12
      }
    },
    {
      name: 'Forest Green',
      colors: {
        primary: '#059669',
        secondary: '#10b981',
        accent: '#d1fae5',
        background: '#ffffff',
        foreground: '#064e3b'
      },
      typography: {
        fontFamily: 'Inter',
        fontSize: 16,
        fontWeight: 400,
        lineHeight: 1.5
      },
      spacing: {
        scale: 1,
        borderRadius: 8
      },
      shadows: {
        intensity: 0.1,
        blur: 10
      }
    },
    {
      name: 'Sunset Orange',
      colors: {
        primary: '#ea580c',
        secondary: '#f97316',
        accent: '#fed7aa',
        background: '#ffffff',
        foreground: '#9a3412'
      },
      typography: {
        fontFamily: 'Inter',
        fontSize: 16,
        fontWeight: 500,
        lineHeight: 1.4
      },
      spacing: {
        scale: 1.1,
        borderRadius: 16
      },
      shadows: {
        intensity: 0.12,
        blur: 8
      }
    }
  ];

  const updateThemeProperty = (category: keyof VisualTheme, property: string, value: any) => {
    setCurrentTheme(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        [property]: value
      }
    }));
  };

  const updateAccessibility = (property: keyof AccessibilitySettings, value: boolean) => {
    setAccessibility(prev => ({
      ...prev,
      [property]: value
    }));
  };

  const exportTheme = () => {
    const themeData = {
      theme: currentTheme,
      accessibility,
      darkMode: isDarkMode
    };
    
    const blob = new Blob([JSON.stringify(themeData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `stayfinder-theme-${currentTheme.name.toLowerCase().replace(/\s+/g, '-')}.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const resetToDefault = () => {
    setCurrentTheme(presetThemes[0]);
    setAccessibility({
      highContrast: false,
      reducedMotion: false,
      largeText: false,
      focusIndicators: true
    });
    setIsDarkMode(false);
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-6xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Visual Polish System</h1>
          <p className="text-gray-600">Customize the visual appearance and accessibility of StayFinder</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Theme Controls */}
          <div className="lg:col-span-2 space-y-6">
            <Tabs defaultValue="colors" className="w-full">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="colors">Colors</TabsTrigger>
                <TabsTrigger value="typography">Typography</TabsTrigger>
                <TabsTrigger value="spacing">Spacing</TabsTrigger>
                <TabsTrigger value="effects">Effects</TabsTrigger>
              </TabsList>

              <TabsContent value="colors" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Palette className="h-5 w-5" />
                      Color Scheme
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span>Dark Mode</span>
                      <Switch checked={isDarkMode} onCheckedChange={setIsDarkMode} />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium">Primary Color</label>
                        <input
                          type="color"
                          value={currentTheme.colors.primary}
                          onChange={(e) => updateThemeProperty('colors', 'primary', e.target.value)}
                          className="w-full h-10 rounded border"
                        />
                      </div>
                      <div>
                        <label className="text-sm font-medium">Secondary Color</label>
                        <input
                          type="color"
                          value={currentTheme.colors.secondary}
                          onChange={(e) => updateThemeProperty('colors', 'secondary', e.target.value)}
                          className="w-full h-10 rounded border"
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <h4 className="font-medium">Preset Themes</h4>
                      <div className="grid grid-cols-3 gap-2">
                        {presetThemes.map((theme, index) => (
                          <button
                            key={index}
                            onClick={() => setCurrentTheme(theme)}
                            className="p-3 border rounded-lg hover:shadow-md transition-shadow"
                          >
                            <div className="flex gap-1 mb-2">
                              <div className="w-4 h-4 rounded" style={{ backgroundColor: theme.colors.primary }} />
                              <div className="w-4 h-4 rounded" style={{ backgroundColor: theme.colors.secondary }} />
                              <div className="w-4 h-4 rounded" style={{ backgroundColor: theme.colors.accent }} />
                            </div>
                            <div className="text-xs font-medium">{theme.name}</div>
                          </button>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="typography" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Type className="h-5 w-5" />
                      Typography
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <label className="text-sm font-medium">Font Size: {currentTheme.typography.fontSize}px</label>
                      <Slider
                        value={[currentTheme.typography.fontSize]}
                        onValueChange={([value]) => updateThemeProperty('typography', 'fontSize', value)}
                        min={12}
                        max={24}
                        step={1}
                        className="mt-2"
                      />
                    </div>

                    <div>
                      <label className="text-sm font-medium">Font Weight: {currentTheme.typography.fontWeight}</label>
                      <Slider
                        value={[currentTheme.typography.fontWeight]}
                        onValueChange={([value]) => updateThemeProperty('typography', 'fontWeight', value)}
                        min={300}
                        max={700}
                        step={100}
                        className="mt-2"
                      />
                    </div>

                    <div>
                      <label className="text-sm font-medium">Line Height: {currentTheme.typography.lineHeight}</label>
                      <Slider
                        value={[currentTheme.typography.lineHeight]}
                        onValueChange={([value]) => updateThemeProperty('typography', 'lineHeight', value)}
                        min={1.2}
                        max={2.0}
                        step={0.1}
                        className="mt-2"
                      />
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="spacing" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Layers className="h-5 w-5" />
                      Spacing & Layout
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <label className="text-sm font-medium">Spacing Scale: {currentTheme.spacing.scale}x</label>
                      <Slider
                        value={[currentTheme.spacing.scale]}
                        onValueChange={([value]) => updateThemeProperty('spacing', 'scale', value)}
                        min={0.8}
                        max={1.5}
                        step={0.1}
                        className="mt-2"
                      />
                    </div>

                    <div>
                      <label className="text-sm font-medium">Border Radius: {currentTheme.spacing.borderRadius}px</label>
                      <Slider
                        value={[currentTheme.spacing.borderRadius]}
                        onValueChange={([value]) => updateThemeProperty('spacing', 'borderRadius', value)}
                        min={0}
                        max={24}
                        step={2}
                        className="mt-2"
                      />
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="effects" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Zap className="h-5 w-5" />
                      Visual Effects
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <label className="text-sm font-medium">Shadow Intensity: {Math.round(currentTheme.shadows.intensity * 100)}%</label>
                      <Slider
                        value={[currentTheme.shadows.intensity]}
                        onValueChange={([value]) => updateThemeProperty('shadows', 'intensity', value)}
                        min={0}
                        max={0.3}
                        step={0.01}
                        className="mt-2"
                      />
                    </div>

                    <div>
                      <label className="text-sm font-medium">Shadow Blur: {currentTheme.shadows.blur}px</label>
                      <Slider
                        value={[currentTheme.shadows.blur]}
                        onValueChange={([value]) => updateThemeProperty('shadows', 'blur', value)}
                        min={0}
                        max={20}
                        step={1}
                        className="mt-2"
                      />
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>

            {/* Accessibility Settings */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Eye className="h-5 w-5" />
                  Accessibility Settings
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium">High Contrast</div>
                    <div className="text-sm text-gray-600">Increase contrast for better visibility</div>
                  </div>
                  <Switch 
                    checked={accessibility.highContrast} 
                    onCheckedChange={(value) => updateAccessibility('highContrast', value)} 
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium">Reduced Motion</div>
                    <div className="text-sm text-gray-600">Minimize animations and transitions</div>
                  </div>
                  <Switch 
                    checked={accessibility.reducedMotion} 
                    onCheckedChange={(value) => updateAccessibility('reducedMotion', value)} 
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium">Large Text</div>
                    <div className="text-sm text-gray-600">Increase text size for better readability</div>
                  </div>
                  <Switch 
                    checked={accessibility.largeText} 
                    onCheckedChange={(value) => updateAccessibility('largeText', value)} 
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium">Focus Indicators</div>
                    <div className="text-sm text-gray-600">Show focus outlines for keyboard navigation</div>
                  </div>
                  <Switch 
                    checked={accessibility.focusIndicators} 
                    onCheckedChange={(value) => updateAccessibility('focusIndicators', value)} 
                  />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Preview Panel */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Live Preview</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {/* Sample UI Elements */}
                  <div className="p-4 border rounded-lg" style={{ 
                    backgroundColor: currentTheme.colors.background,
                    color: currentTheme.colors.foreground,
                    borderRadius: `${currentTheme.spacing.borderRadius}px`
                  }}>
                    <h3 className="font-semibold mb-2" style={{ 
                      fontSize: `${currentTheme.typography.fontSize * 1.25}px`,
                      fontWeight: currentTheme.typography.fontWeight + 200,
                      lineHeight: currentTheme.typography.lineHeight
                    }}>
                      Sample Property Card
                    </h3>
                    <p className="text-sm mb-3" style={{ 
                      fontSize: `${currentTheme.typography.fontSize}px`,
                      lineHeight: currentTheme.typography.lineHeight
                    }}>
                      Beautiful oceanview apartment in Cape Town with modern amenities.
                    </p>
                    <div className="flex gap-2">
                      <Badge style={{ backgroundColor: currentTheme.colors.primary, color: 'white' }}>
                        Featured
                      </Badge>
                      <Badge style={{ backgroundColor: currentTheme.colors.secondary, color: 'white' }}>
                        Ocean View
                      </Badge>
                    </div>
                  </div>

                  <Button 
                    className="w-full"
                    style={{ 
                      backgroundColor: currentTheme.colors.primary,
                      borderRadius: `${currentTheme.spacing.borderRadius}px`
                    }}
                  >
                    Book Now
                  </Button>

                  <div className="text-center text-sm text-gray-600">
                    Theme: {currentTheme.name}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <Button onClick={exportTheme} variant="outline" className="w-full">
                  Export Theme
                </Button>
                <Button onClick={resetToDefault} variant="outline" className="w-full">
                  Reset to Default
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};
