import React from 'react';
import { Star, MessageSquare } from 'lucide-react';

interface ReviewSummaryProps {
  averageRating?: number;
  reviewCount?: number;
  size?: 'sm' | 'md' | 'lg';
  showCount?: boolean;
  className?: string;
}

export const ReviewSummary: React.FC<ReviewSummaryProps> = ({
  averageRating = 0,
  reviewCount = 0,
  size = 'md',
  showCount = true,
  className = ''
}) => {
  if (reviewCount === 0) {
    return (
      <div className={`flex items-center gap-1 text-gray-500 ${className}`}>
        <MessageSquare className={`${size === 'sm' ? 'h-3 w-3' : size === 'lg' ? 'h-5 w-5' : 'h-4 w-4'}`} />
        <span className={`${size === 'sm' ? 'text-xs' : size === 'lg' ? 'text-base' : 'text-sm'}`}>
          No reviews yet
        </span>
      </div>
    );
  }

  const starSize = size === 'sm' ? 'h-3 w-3' : size === 'lg' ? 'h-5 w-5' : 'h-4 w-4';
  const textSize = size === 'sm' ? 'text-xs' : size === 'lg' ? 'text-base' : 'text-sm';

  return (
    <div className={`flex items-center gap-1 ${className}`}>
      <Star className={`${starSize} text-yellow-400 fill-current`} />
      <span className={`font-medium ${textSize}`}>
        {averageRating.toFixed(1)}
      </span>
      {showCount && (
        <span className={`text-gray-600 ${textSize}`}>
          ({reviewCount} review{reviewCount !== 1 ? 's' : ''})
        </span>
      )}
    </div>
  );
};
