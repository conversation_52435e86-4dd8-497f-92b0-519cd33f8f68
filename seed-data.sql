-- ============================================================================
-- StayFinder Database Seed Data
-- PostgreSQL/Supabase Compatible
-- Version: 1.0
-- Created: July 16, 2025
-- ============================================================================

-- ============================================================================
-- AMENITIES SEED DATA
-- ============================================================================

INSERT INTO amenities (name, description, category, icon, sort_order) VALUES
-- Basic amenities
('WiFi', 'Wireless internet access', 'basic', 'wifi', 1),
('Air Conditioning', 'Climate control system', 'basic', 'ac', 2),
('Heating', 'Heating system for cold weather', 'basic', 'heating', 3),
('Hot Water', '24/7 hot water supply', 'basic', 'hot-water', 4),
('Electricity', 'Reliable electricity supply', 'basic', 'electricity', 5),
('Bed Linen', 'Clean bed sheets and pillowcases', 'basic', 'bed-linen', 6),
('Towels', 'Bath and hand towels provided', 'basic', 'towels', 7),
('Toiletries', 'Basic toiletries provided', 'basic', 'toiletries', 8),

-- Kitchen amenities
('Full Kitchen', 'Complete kitchen with all appliances', 'kitchen', 'kitchen', 10),
('Kitchenette', 'Basic kitchen facilities', 'kitchen', 'kitchenette', 11),
('Refrigerator', 'Full-size refrigerator', 'kitchen', 'fridge', 12),
('Microwave', 'Microwave oven', 'kitchen', 'microwave', 13),
('Stove/Oven', 'Cooking stove and oven', 'kitchen', 'stove', 14),
('Dishwasher', 'Automatic dishwasher', 'kitchen', 'dishwasher', 15),
('Coffee Maker', 'Coffee making facilities', 'kitchen', 'coffee', 16),
('Kettle', 'Electric kettle', 'kitchen', 'kettle', 17),
('Toaster', 'Bread toaster', 'kitchen', 'toaster', 18),
('Cookware', 'Pots, pans, and cooking utensils', 'kitchen', 'cookware', 19),
('Dishes & Cutlery', 'Plates, bowls, glasses, and cutlery', 'kitchen', 'dishes', 20),

-- Bathroom amenities
('Private Bathroom', 'Dedicated private bathroom', 'bathroom', 'bathroom', 25),
('Shared Bathroom', 'Shared bathroom facilities', 'bathroom', 'shared-bathroom', 26),
('Bathtub', 'Bathtub for relaxing baths', 'bathroom', 'bathtub', 27),
('Shower', 'Shower facilities', 'bathroom', 'shower', 28),
('Hair Dryer', 'Hair drying facilities', 'bathroom', 'hair-dryer', 29),

-- Entertainment amenities
('TV', 'Television with local channels', 'entertainment', 'tv', 35),
('Cable/Satellite TV', 'Extended TV channels', 'entertainment', 'cable-tv', 36),
('Netflix', 'Netflix streaming access', 'entertainment', 'netflix', 37),
('Sound System', 'Music and audio system', 'entertainment', 'sound-system', 38),
('Books & Games', 'Reading materials and games', 'entertainment', 'books-games', 39),
('Board Games', 'Family board games', 'entertainment', 'board-games', 40),

-- Outdoor amenities
('Balcony', 'Private balcony or terrace', 'outdoor', 'balcony', 45),
('Patio', 'Outdoor patio area', 'outdoor', 'patio', 46),
('Garden', 'Garden or yard access', 'outdoor', 'garden', 47),
('BBQ/Braai', 'Barbecue facilities', 'outdoor', 'bbq', 48),
('Outdoor Furniture', 'Outdoor seating and tables', 'outdoor', 'outdoor-furniture', 49),
('Pool', 'Swimming pool access', 'outdoor', 'pool', 50),
('Beach Access', 'Direct beach access', 'outdoor', 'beach', 51),
('Sea View', 'Ocean or sea view', 'outdoor', 'sea-view', 52),
('Mountain View', 'Mountain view', 'outdoor', 'mountain-view', 53),
('Parking', 'Dedicated parking space', 'outdoor', 'parking', 54),
('Garage', 'Covered garage parking', 'outdoor', 'garage', 55),

-- Safety amenities
('Smoke Detector', 'Smoke detection system', 'safety', 'smoke-detector', 60),
('Carbon Monoxide Detector', 'CO detection system', 'safety', 'co-detector', 61),
('Fire Extinguisher', 'Fire safety equipment', 'safety', 'fire-extinguisher', 62),
('First Aid Kit', 'Basic first aid supplies', 'safety', 'first-aid', 63),
('Security System', 'Property security system', 'safety', 'security', 64),
('Safe', 'In-room safe for valuables', 'safety', 'safe', 65),
('Gated Community', 'Secure gated community', 'safety', 'gated', 66),

-- Accessibility amenities
('Wheelchair Accessible', 'Wheelchair accessible entrance and facilities', 'accessibility', 'wheelchair', 70),
('Step-Free Access', 'No steps to entrance', 'accessibility', 'step-free', 71),
('Wide Doorways', 'Doorways suitable for wheelchairs', 'accessibility', 'wide-doors', 72),
('Accessible Bathroom', 'Bathroom with accessibility features', 'accessibility', 'accessible-bathroom', 73),
('Grab Rails', 'Safety grab rails in bathroom', 'accessibility', 'grab-rails', 74);

-- ============================================================================
-- SAMPLE USERS (FOR DEVELOPMENT/TESTING)
-- ============================================================================

-- Note: In production, users will be created through Supabase Auth
-- These are sample users for development and testing purposes

INSERT INTO users (
    id, email, first_name, last_name, phone, user_type, is_verified, is_active,
    city, province, country, bio, created_at
) VALUES
(
    gen_random_uuid(),
    '<EMAIL>',
    'System',
    'Administrator',
    '+27123456789',
    'admin',
    true,
    true,
    'Durban',
    'KwaZulu-Natal',
    'South Africa',
    'System administrator for StayFinder platform',
    NOW()
),
(
    gen_random_uuid(),
    '<EMAIL>',
    'Sarah',
    'Johnson',
    '+27987654321',
    'host',
    true,
    true,
    'Margate',
    'KwaZulu-Natal',
    'South Africa',
    'Experienced host with multiple properties on the KZN South Coast. Passionate about providing exceptional guest experiences.',
    NOW()
),
(
    gen_random_uuid(),
    '<EMAIL>',
    'Michael',
    'Smith',
    '+27456789123',
    'host',
    true,
    true,
    'Scottburgh',
    'KwaZulu-Natal',
    'South Africa',
    'Local property owner specializing in beachfront accommodations.',
    NOW()
),
(
    gen_random_uuid(),
    '<EMAIL>',
    'Emma',
    'Wilson',
    '+27789123456',
    'guest',
    true,
    true,
    'Johannesburg',
    'Gauteng',
    'South Africa',
    'Travel enthusiast who loves exploring the South African coast.',
    NOW()
);

-- ============================================================================
-- SAMPLE PROPERTIES (FOR DEVELOPMENT/TESTING)
-- ============================================================================

-- Get host IDs for sample properties
DO $$
DECLARE
    host1_id UUID;
    host2_id UUID;
    property1_id UUID;
    property2_id UUID;
    property3_id UUID;
BEGIN
    -- Get host IDs
    SELECT id INTO host1_id FROM users WHERE email = '<EMAIL>';
    SELECT id INTO host2_id FROM users WHERE email = '<EMAIL>';
    
    -- Insert sample properties
    INSERT INTO properties (
        id, host_id, title, description, property_type,
        address_line1, city, province, postal_code, country,
        latitude, longitude, max_guests, bedrooms, bathrooms, beds,
        price_per_night, cleaning_fee, minimum_stay_nights,
        check_in_time, check_out_time, cancellation_policy,
        instant_book, status, featured
    ) VALUES
    (
        gen_random_uuid(),
        host1_id,
        'Beachfront Villa with Stunning Ocean Views',
        'Experience the ultimate coastal getaway in this luxurious beachfront villa. Wake up to breathtaking ocean views and fall asleep to the sound of waves. This spacious 4-bedroom villa features modern amenities, a private pool, and direct beach access. Perfect for families or groups looking for an unforgettable South Coast experience.',
        'villa',
        '123 Marine Drive',
        'Margate',
        'KwaZulu-Natal',
        '4275',
        'South Africa',
        -30.8647,
        30.3707,
        8,
        4,
        3.0,
        6,
        2500.00,
        500.00,
        2,
        '15:00',
        '11:00',
        'moderate',
        true,
        'active',
        true
    ),
    (
        gen_random_uuid(),
        host1_id,
        'Cozy Cottage Near Uvongo Beach',
        'Charming 2-bedroom cottage just a short walk from the beautiful Uvongo Beach. This comfortable retreat features a fully equipped kitchen, cozy living area, and a lovely garden with braai facilities. Ideal for couples or small families seeking a peaceful coastal escape.',
        'cottage',
        '45 Lighthouse Road',
        'Uvongo',
        'KwaZulu-Natal',
        '4270',
        'South Africa',
        -30.8294,
        30.3889,
        4,
        2,
        1.0,
        3,
        1200.00,
        200.00,
        1,
        '14:00',
        '10:00',
        'flexible',
        false,
        'active',
        false
    ),
    (
        gen_random_uuid(),
        host2_id,
        'Modern Apartment with Sea Views',
        'Stylish modern apartment offering spectacular sea views from every room. Located in the heart of Scottburgh, this 3-bedroom apartment is perfect for beach lovers. Features include a fully equipped kitchen, spacious living areas, and a balcony overlooking the ocean. Walking distance to restaurants, shops, and the main beach.',
        'apartment',
        '78 Ocean Terrace',
        'Scottburgh',
        'KwaZulu-Natal',
        '4180',
        'South Africa',
        -30.2867,
        30.7533,
        6,
        3,
        2.0,
        4,
        1800.00,
        300.00,
        2,
        '15:00',
        '11:00',
        'strict',
        true,
        'active',
        true
    ) RETURNING id INTO property1_id;
    
    -- Get the property IDs for adding amenities and images
    SELECT id INTO property1_id FROM properties WHERE title = 'Beachfront Villa with Stunning Ocean Views';
    SELECT id INTO property2_id FROM properties WHERE title = 'Cozy Cottage Near Uvongo Beach';
    SELECT id INTO property3_id FROM properties WHERE title = 'Modern Apartment with Sea Views';
    
    -- Add amenities to properties
    -- Villa amenities
    INSERT INTO property_amenities (property_id, amenity_id)
    SELECT property1_id, id FROM amenities 
    WHERE name IN ('WiFi', 'Air Conditioning', 'Full Kitchen', 'Pool', 'Beach Access', 'Sea View', 'Parking', 'BBQ/Braai', 'TV', 'Private Bathroom');
    
    -- Cottage amenities
    INSERT INTO property_amenities (property_id, amenity_id)
    SELECT property2_id, id FROM amenities 
    WHERE name IN ('WiFi', 'Full Kitchen', 'Garden', 'BBQ/Braai', 'TV', 'Private Bathroom', 'Parking');
    
    -- Apartment amenities
    INSERT INTO property_amenities (property_id, amenity_id)
    SELECT property3_id, id FROM amenities 
    WHERE name IN ('WiFi', 'Air Conditioning', 'Full Kitchen', 'Sea View', 'Balcony', 'TV', 'Private Bathroom', 'Parking');
    
    -- Add sample property images (placeholder URLs)
    INSERT INTO property_images (property_id, image_url, alt_text, sort_order, is_primary)
    VALUES
    (property1_id, 'https://example.com/villa1-main.jpg', 'Beachfront villa exterior view', 1, true),
    (property1_id, 'https://example.com/villa1-living.jpg', 'Spacious living room with ocean view', 2, false),
    (property1_id, 'https://example.com/villa1-bedroom.jpg', 'Master bedroom with sea view', 3, false),
    (property1_id, 'https://example.com/villa1-pool.jpg', 'Private pool area', 4, false),
    
    (property2_id, 'https://example.com/cottage1-main.jpg', 'Cozy cottage exterior', 1, true),
    (property2_id, 'https://example.com/cottage1-living.jpg', 'Comfortable living area', 2, false),
    (property2_id, 'https://example.com/cottage1-garden.jpg', 'Beautiful garden with braai area', 3, false),
    
    (property3_id, 'https://example.com/apartment1-main.jpg', 'Modern apartment building', 1, true),
    (property3_id, 'https://example.com/apartment1-living.jpg', 'Modern living room with sea view', 2, false),
    (property3_id, 'https://example.com/apartment1-balcony.jpg', 'Balcony with ocean view', 3, false);
    
END $$;

-- ============================================================================
-- SAMPLE AVAILABILITY DATA
-- ============================================================================

-- Block some dates for maintenance and personal use
INSERT INTO property_availability (property_id, date, is_available, reason, notes)
SELECT 
    p.id,
    generate_series(
        CURRENT_DATE + INTERVAL '30 days',
        CURRENT_DATE + INTERVAL '35 days',
        INTERVAL '1 day'
    )::DATE,
    false,
    'maintenance',
    'Annual maintenance and deep cleaning'
FROM properties p
WHERE p.title = 'Beachfront Villa with Stunning Ocean Views';

-- Set special pricing for peak season
INSERT INTO property_availability (property_id, date, is_available, price_override)
SELECT 
    p.id,
    generate_series(
        CURRENT_DATE + INTERVAL '60 days',
        CURRENT_DATE + INTERVAL '90 days',
        INTERVAL '1 day'
    )::DATE,
    true,
    p.price_per_night * 1.5  -- 50% increase for peak season
FROM properties p
WHERE p.status = 'active';

-- ============================================================================
-- DEVELOPMENT NOTES
-- ============================================================================

-- This seed data provides:
-- 1. Complete amenities list for the South African market
-- 2. Sample users (admin, hosts, guest) for testing
-- 3. Sample properties with realistic South Coast locations
-- 4. Property-amenity relationships
-- 5. Sample property images (placeholder URLs)
-- 6. Sample availability data with blocked dates and special pricing

-- To use this data:
-- 1. Run this script after creating the main schema
-- 2. Update image URLs to point to actual images in your Supabase storage
-- 3. Replace sample user data with real users in production
-- 4. Adjust property data as needed for your specific use case

-- Remember to update the auth_user_id fields when integrating with Supabase Auth
