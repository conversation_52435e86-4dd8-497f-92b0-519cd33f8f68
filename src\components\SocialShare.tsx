import React, { useState } from 'react';
import { Share2, Facebook, Twitter, Instagram, Linkedin, Link, Co<PERSON>, Check, MessageCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { toast } from '@/components/ui/use-toast';
import { cn } from '@/lib/utils';

interface SocialShareProps {
  url: string;
  title: string;
  description: string;
  imageUrl?: string;
  hashtags?: string[];
  className?: string;
  variant?: 'buttons' | 'dropdown' | 'modal';
  size?: 'sm' | 'md' | 'lg';
}

interface SocialPlatform {
  id: string;
  name: string;
  icon: React.ComponentType<{ className?: string }>;
  color: string;
  shareUrl: (params: ShareParams) => string;
}

interface ShareParams {
  url: string;
  title: string;
  description: string;
  hashtags?: string[];
  imageUrl?: string;
}

const socialPlatforms: SocialPlatform[] = [
  {
    id: 'facebook',
    name: 'Facebook',
    icon: Facebook,
    color: 'bg-blue-600 hover:bg-blue-700',
    shareUrl: ({ url, title, description }) => 
      `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}&quote=${encodeURIComponent(`${title} - ${description}`)}`
  },
  {
    id: 'twitter',
    name: 'Twitter',
    icon: Twitter,
    color: 'bg-sky-500 hover:bg-sky-600',
    shareUrl: ({ url, title, hashtags }) => {
      const text = title;
      const hashtagString = hashtags ? hashtags.map(tag => `#${tag}`).join(' ') : '';
      return `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(url)}&hashtags=${encodeURIComponent(hashtagString)}`;
    }
  },
  {
    id: 'linkedin',
    name: 'LinkedIn',
    icon: Linkedin,
    color: 'bg-blue-700 hover:bg-blue-800',
    shareUrl: ({ url, title, description }) => 
      `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}&title=${encodeURIComponent(title)}&summary=${encodeURIComponent(description)}`
  },
  {
    id: 'whatsapp',
    name: 'WhatsApp',
    icon: MessageCircle,
    color: 'bg-green-600 hover:bg-green-700',
    shareUrl: ({ url, title, description }) => 
      `https://wa.me/?text=${encodeURIComponent(`${title}\n${description}\n${url}`)}`
  },
  {
    id: 'instagram',
    name: 'Instagram',
    icon: Instagram,
    color: 'bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600',
    shareUrl: ({ url }) => 
      `https://www.instagram.com/` // Instagram doesn't support direct sharing, opens app
  }
];

export const SocialShare: React.FC<SocialShareProps> = ({
  url,
  title,
  description,
  imageUrl,
  hashtags = [],
  className,
  variant = 'buttons',
  size = 'md'
}) => {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isCopied, setIsCopied] = useState(false);

  const shareParams: ShareParams = {
    url,
    title,
    description,
    hashtags,
    imageUrl
  };

  const handleShare = (platform: SocialPlatform) => {
    const shareUrl = platform.shareUrl(shareParams);
    
    if (platform.id === 'instagram') {
      // Instagram doesn't support web sharing, show message
      toast({
        title: "Instagram Sharing",
        description: "Copy the link and share it in your Instagram story or post!",
      });
      handleCopyLink();
      return;
    }

    // Open share URL in new window
    const width = 600;
    const height = 400;
    const left = (window.innerWidth - width) / 2;
    const top = (window.innerHeight - height) / 2;
    
    window.open(
      shareUrl,
      'share',
      `width=${width},height=${height},left=${left},top=${top},resizable=yes,scrollbars=yes`
    );

    // Track sharing event
    if (typeof gtag !== 'undefined') {
      gtag('event', 'share', {
        method: platform.name,
        content_type: 'property',
        item_id: url
      });
    }
  };

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(url);
      setIsCopied(true);
      toast({
        title: "Link copied!",
        description: "The property link has been copied to your clipboard.",
      });
      
      setTimeout(() => setIsCopied(false), 2000);
    } catch (error) {
      toast({
        title: "Copy failed",
        description: "Failed to copy link. Please try again.",
        variant: "destructive"
      });
    }
  };

  const handleNativeShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title,
          text: description,
          url
        });
      } catch (error) {
        // User cancelled or error occurred
        console.log('Share cancelled or failed:', error);
      }
    }
  };

  const sizeClasses = {
    sm: 'h-8 w-8',
    md: 'h-10 w-10',
    lg: 'h-12 w-12'
  };

  const iconSizes = {
    sm: 'h-4 w-4',
    md: 'h-5 w-5',
    lg: 'h-6 w-6'
  };

  if (variant === 'buttons') {
    return (
      <div className={cn("flex items-center gap-2", className)}>
        {/* Native Share (if supported) */}
        {navigator.share && (
          <Button
            onClick={handleNativeShare}
            variant="outline"
            size={size}
            className={sizeClasses[size]}
          >
            <Share2 className={iconSizes[size]} />
          </Button>
        )}

        {/* Social Platform Buttons */}
        {socialPlatforms.map((platform) => {
          const IconComponent = platform.icon;
          return (
            <Button
              key={platform.id}
              onClick={() => handleShare(platform)}
              className={cn(
                sizeClasses[size],
                platform.color,
                "text-white border-0"
              )}
              size={size}
            >
              <IconComponent className={iconSizes[size]} />
            </Button>
          );
        })}

        {/* Copy Link Button */}
        <Button
          onClick={handleCopyLink}
          variant="outline"
          size={size}
          className={cn(
            sizeClasses[size],
            isCopied && "bg-green-50 border-green-200 text-green-700"
          )}
        >
          {isCopied ? (
            <Check className={iconSizes[size]} />
          ) : (
            <Copy className={iconSizes[size]} />
          )}
        </Button>
      </div>
    );
  }

  if (variant === 'dropdown') {
    return (
      <div className={cn("relative", className)}>
        <Button
          onClick={() => setIsDropdownOpen(!isDropdownOpen)}
          variant="outline"
          size={size}
          className="flex items-center gap-2"
        >
          <Share2 className={iconSizes[size]} />
          Share
        </Button>

        {isDropdownOpen && (
          <>
            <div
              className="fixed inset-0 z-10"
              onClick={() => setIsDropdownOpen(false)}
            />
            <Card className="absolute top-full mt-2 right-0 z-20 w-64 shadow-lg">
              <CardContent className="p-4">
                <div className="space-y-3">
                  <h4 className="font-medium text-sm">Share this property</h4>
                  
                  {/* Copy Link */}
                  <div className="flex items-center gap-2">
                    <Input
                      value={url}
                      readOnly
                      className="text-xs"
                    />
                    <Button
                      onClick={handleCopyLink}
                      size="sm"
                      variant="outline"
                      className={cn(
                        "flex-shrink-0",
                        isCopied && "bg-green-50 border-green-200 text-green-700"
                      )}
                    >
                      {isCopied ? (
                        <Check className="h-4 w-4" />
                      ) : (
                        <Copy className="h-4 w-4" />
                      )}
                    </Button>
                  </div>

                  {/* Social Platforms */}
                  <div className="grid grid-cols-2 gap-2">
                    {socialPlatforms.map((platform) => {
                      const IconComponent = platform.icon;
                      return (
                        <Button
                          key={platform.id}
                          onClick={() => {
                            handleShare(platform);
                            setIsDropdownOpen(false);
                          }}
                          variant="outline"
                          size="sm"
                          className="flex items-center gap-2 justify-start"
                        >
                          <IconComponent className="h-4 w-4" />
                          {platform.name}
                        </Button>
                      );
                    })}
                  </div>

                  {/* Native Share */}
                  {navigator.share && (
                    <Button
                      onClick={() => {
                        handleNativeShare();
                        setIsDropdownOpen(false);
                      }}
                      variant="outline"
                      size="sm"
                      className="w-full flex items-center gap-2"
                    >
                      <Share2 className="h-4 w-4" />
                      More options
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          </>
        )}
      </div>
    );
  }

  // Modal variant would be implemented here
  return null;
};

// Utility component for property sharing
export const PropertySocialShare: React.FC<{
  propertyId: string;
  propertyName: string;
  propertyDescription: string;
  propertyImage?: string;
  location: string;
  price: number;
  className?: string;
}> = ({
  propertyId,
  propertyName,
  propertyDescription,
  propertyImage,
  location,
  price,
  className
}) => {
  const url = `${window.location.origin}/property/${propertyId}`;
  const title = `${propertyName} - ${location}`;
  const description = `${propertyDescription} Starting from R${price.toLocaleString()} per night on StayFinder.`;
  const hashtags = ['StayFinder', 'SouthAfrica', 'Travel', 'Accommodation', location.replace(/\s+/g, '')];

  return (
    <SocialShare
      url={url}
      title={title}
      description={description}
      imageUrl={propertyImage}
      hashtags={hashtags}
      className={className}
      variant="dropdown"
    />
  );
};

// Hook for social sharing functionality
export const useSocialShare = () => {
  const shareProperty = (property: {
    id: string;
    name: string;
    description: string;
    location: string;
    price: number;
    image?: string;
  }) => {
    const url = `${window.location.origin}/property/${property.id}`;
    const title = `${property.name} - ${property.location}`;
    const description = `${property.description} Starting from R${property.price.toLocaleString()} per night.`;

    return {
      url,
      title,
      description,
      imageUrl: property.image,
      hashtags: ['StayFinder', 'SouthAfrica', 'Travel']
    };
  };

  const shareSearch = (searchParams: {
    location: string;
    checkIn: string;
    checkOut: string;
    guests: number;
  }) => {
    const url = `${window.location.origin}/search?location=${encodeURIComponent(searchParams.location)}&checkIn=${searchParams.checkIn}&checkOut=${searchParams.checkOut}&guests=${searchParams.guests}`;
    const title = `Find accommodation in ${searchParams.location}`;
    const description = `Discover amazing places to stay in ${searchParams.location} for ${searchParams.guests} guests from ${searchParams.checkIn} to ${searchParams.checkOut}.`;

    return {
      url,
      title,
      description,
      hashtags: ['StayFinder', 'Travel', searchParams.location.replace(/\s+/g, '')]
    };
  };

  return {
    shareProperty,
    shareSearch
  };
};
