const { executeQuery, findOne, findMany, getClient } = require('../utils/database');

class Property {
  static async create(propertyData) {
    const query = `
      INSERT INTO properties (
        id, owner_id, title, description, property_type, location, 
        latitude, longitude, max_guests, bedrooms, bathrooms, 
        price_per_night, cleaning_fee, amenities, house_rules, 
        check_in_time, check_out_time, status
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    const params = [
      propertyData.id,
      propertyData.owner_id,
      propertyData.title,
      propertyData.description,
      propertyData.property_type,
      propertyData.location,
      propertyData.latitude,
      propertyData.longitude,
      propertyData.max_guests,
      propertyData.bedrooms,
      propertyData.bathrooms,
      propertyData.price_per_night,
      propertyData.cleaning_fee || 0,
      JSON.stringify(propertyData.amenities || []),
      propertyData.house_rules,
      propertyData.check_in_time || '15:00:00',
      propertyData.check_out_time || '11:00:00',
      propertyData.status || 'pending'
    ];
    
    return await executeQuery(query, params);
  }

  static async findById(id) {
    const query = `
      SELECT 
        p.*,
        u.first_name as owner_first_name,
        u.last_name as owner_last_name,
        u.email as owner_email,
        u.phone as owner_phone,
        COALESCE(AVG(r.rating), 0) as average_rating,
        COUNT(r.id) as review_count
      FROM properties p
      LEFT JOIN users u ON p.owner_id = u.id
      LEFT JOIN reviews r ON p.id = r.property_id
      WHERE p.id = ? AND p.status = 'active'
      GROUP BY p.id
    `;
    
    return await findOne(query, [id]);
  }

  static async search(filters = {}) {
    let whereConditions = ['p.status = ?'];
    let queryParams = ['active'];
    
    if (filters.location) {
      whereConditions.push('p.location LIKE ?');
      queryParams.push(`%${filters.location}%`);
    }
    
    if (filters.minPrice) {
      whereConditions.push('p.price_per_night >= ?');
      queryParams.push(parseFloat(filters.minPrice));
    }
    
    if (filters.maxPrice) {
      whereConditions.push('p.price_per_night <= ?');
      queryParams.push(parseFloat(filters.maxPrice));
    }
    
    if (filters.guests) {
      whereConditions.push('p.max_guests >= ?');
      queryParams.push(parseInt(filters.guests));
    }
    
    if (filters.propertyType) {
      whereConditions.push('p.property_type = ?');
      queryParams.push(filters.propertyType);
    }
    
    const whereClause = whereConditions.join(' AND ');
    const offset = ((filters.page || 1) - 1) * (filters.limit || 12);
    
    const query = `
      SELECT 
        p.*,
        u.first_name as owner_first_name,
        u.last_name as owner_last_name,
        pi.image_url as primary_image,
        COALESCE(AVG(r.rating), 0) as average_rating,
        COUNT(DISTINCT r.id) as review_count
      FROM properties p
      LEFT JOIN users u ON p.owner_id = u.id
      LEFT JOIN property_images pi ON p.id = pi.property_id AND pi.is_primary = true
      LEFT JOIN reviews r ON p.id = r.property_id
      WHERE ${whereClause}
      GROUP BY p.id
      ORDER BY p.created_at DESC
      LIMIT ? OFFSET ?
    `;
    
    return await findMany(query, [...queryParams, parseInt(filters.limit || 12), offset]);
  }

  static async updateById(id, updateData) {
    const fields = Object.keys(updateData);
    const values = Object.values(updateData);
    const setClause = fields.map(field => `${field} = ?`).join(', ');
    
    const query = `UPDATE properties SET ${setClause} WHERE id = ?`;
    return await executeQuery(query, [...values, id]);
  }

  static async deleteById(id) {
    const query = 'DELETE FROM properties WHERE id = ?';
    return await executeQuery(query, [id]);
  }

  static async getCount(filters = {}) {
    let whereConditions = ['p.status = ?'];
    let queryParams = ['active'];
    
    if (filters.location) {
      whereConditions.push('p.location LIKE ?');
      queryParams.push(`%${filters.location}%`);
    }
    
    if (filters.minPrice) {
      whereConditions.push('p.price_per_night >= ?');
      queryParams.push(parseFloat(filters.minPrice));
    }
    
    if (filters.maxPrice) {
      whereConditions.push('p.price_per_night <= ?');
      queryParams.push(parseFloat(filters.maxPrice));
    }
    
    if (filters.guests) {
      whereConditions.push('p.max_guests >= ?');
      queryParams.push(parseInt(filters.guests));
    }
    
    if (filters.propertyType) {
      whereConditions.push('p.property_type = ?');
      queryParams.push(filters.propertyType);
    }
    
    const whereClause = whereConditions.join(' AND ');
    
    const query = `
      SELECT COUNT(DISTINCT p.id) as total
      FROM properties p
      WHERE ${whereClause}
    `;
    
    const result = await findOne(query, queryParams);
    return result ? result.total : 0;
  }

  // Supabase-compatible methods
  static async getAllSupabase(filters = {}) {
    try {
      const supabase = getClient();
      let query = supabase.from('properties').select('*', { count: 'exact' });

      // Apply filters only if they have values
      if (filters.location && filters.location.trim()) {
        query = query.ilike('location', `%${filters.location}%`);
      }

      if (filters.minPrice && !isNaN(filters.minPrice)) {
        query = query.gte('price_per_night', filters.minPrice);
      }

      if (filters.maxPrice && !isNaN(filters.maxPrice)) {
        query = query.lte('price_per_night', filters.maxPrice);
      }

      if (filters.guests && !isNaN(filters.guests)) {
        query = query.gte('max_guests', filters.guests);
      }

      if (filters.propertyType && filters.propertyType.trim()) {
        query = query.eq('property_type', filters.propertyType);
      }

      // Pagination
      const page = filters.page || 1;
      const limit = filters.limit || 10;
      const from = (page - 1) * limit;
      const to = from + limit - 1;

      query = query.range(from, to);
      query = query.order('created_at', { ascending: false });

      const { data, error, count } = await query;

      if (error) {
        console.error('Supabase query error:', error);
        throw error;
      }

      return {
        properties: data || [],
        total: count || 0,
        page,
        limit,
        totalPages: Math.ceil((count || 0) / limit)
      };
    } catch (error) {
      console.error('Error fetching properties from Supabase:', error);
      throw error;
    }
  }
}

module.exports = Property;
