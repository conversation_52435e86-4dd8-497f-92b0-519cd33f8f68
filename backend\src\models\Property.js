const { executeQuery, findOne, findMany } = require('../utils/database');

class Property {
  static async create(propertyData) {
    const query = `
      INSERT INTO properties (
        id, owner_id, title, description, property_type, location, 
        latitude, longitude, max_guests, bedrooms, bathrooms, 
        price_per_night, cleaning_fee, amenities, house_rules, 
        check_in_time, check_out_time, status
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    const params = [
      propertyData.id,
      propertyData.owner_id,
      propertyData.title,
      propertyData.description,
      propertyData.property_type,
      propertyData.location,
      propertyData.latitude,
      propertyData.longitude,
      propertyData.max_guests,
      propertyData.bedrooms,
      propertyData.bathrooms,
      propertyData.price_per_night,
      propertyData.cleaning_fee || 0,
      JSON.stringify(propertyData.amenities || []),
      propertyData.house_rules,
      propertyData.check_in_time || '15:00:00',
      propertyData.check_out_time || '11:00:00',
      propertyData.status || 'pending'
    ];
    
    return await executeQuery(query, params);
  }

  static async findById(id) {
    const query = `
      SELECT 
        p.*,
        u.first_name as owner_first_name,
        u.last_name as owner_last_name,
        u.email as owner_email,
        u.phone as owner_phone,
        COALESCE(AVG(r.rating), 0) as average_rating,
        COUNT(r.id) as review_count
      FROM properties p
      LEFT JOIN users u ON p.owner_id = u.id
      LEFT JOIN reviews r ON p.id = r.property_id
      WHERE p.id = ? AND p.status = 'active'
      GROUP BY p.id
    `;
    
    return await findOne(query, [id]);
  }

  static async search(filters = {}) {
    let whereConditions = ['p.status = ?'];
    let queryParams = ['active'];
    
    if (filters.location) {
      whereConditions.push('p.location LIKE ?');
      queryParams.push(`%${filters.location}%`);
    }
    
    if (filters.minPrice) {
      whereConditions.push('p.price_per_night >= ?');
      queryParams.push(parseFloat(filters.minPrice));
    }
    
    if (filters.maxPrice) {
      whereConditions.push('p.price_per_night <= ?');
      queryParams.push(parseFloat(filters.maxPrice));
    }
    
    if (filters.guests) {
      whereConditions.push('p.max_guests >= ?');
      queryParams.push(parseInt(filters.guests));
    }
    
    if (filters.propertyType) {
      whereConditions.push('p.property_type = ?');
      queryParams.push(filters.propertyType);
    }
    
    const whereClause = whereConditions.join(' AND ');
    const offset = ((filters.page || 1) - 1) * (filters.limit || 12);
    
    const query = `
      SELECT 
        p.*,
        u.first_name as owner_first_name,
        u.last_name as owner_last_name,
        pi.image_url as primary_image,
        COALESCE(AVG(r.rating), 0) as average_rating,
        COUNT(DISTINCT r.id) as review_count
      FROM properties p
      LEFT JOIN users u ON p.owner_id = u.id
      LEFT JOIN property_images pi ON p.id = pi.property_id AND pi.is_primary = true
      LEFT JOIN reviews r ON p.id = r.property_id
      WHERE ${whereClause}
      GROUP BY p.id
      ORDER BY p.created_at DESC
      LIMIT ? OFFSET ?
    `;
    
    return await findMany(query, [...queryParams, parseInt(filters.limit || 12), offset]);
  }

  static async updateById(id, updateData) {
    const fields = Object.keys(updateData);
    const values = Object.values(updateData);
    const setClause = fields.map(field => `${field} = ?`).join(', ');
    
    const query = `UPDATE properties SET ${setClause} WHERE id = ?`;
    return await executeQuery(query, [...values, id]);
  }

  static async deleteById(id) {
    const query = 'DELETE FROM properties WHERE id = ?';
    return await executeQuery(query, [id]);
  }

  static async getCount(filters = {}) {
    let whereConditions = ['p.status = ?'];
    let queryParams = ['active'];
    
    if (filters.location) {
      whereConditions.push('p.location LIKE ?');
      queryParams.push(`%${filters.location}%`);
    }
    
    if (filters.minPrice) {
      whereConditions.push('p.price_per_night >= ?');
      queryParams.push(parseFloat(filters.minPrice));
    }
    
    if (filters.maxPrice) {
      whereConditions.push('p.price_per_night <= ?');
      queryParams.push(parseFloat(filters.maxPrice));
    }
    
    if (filters.guests) {
      whereConditions.push('p.max_guests >= ?');
      queryParams.push(parseInt(filters.guests));
    }
    
    if (filters.propertyType) {
      whereConditions.push('p.property_type = ?');
      queryParams.push(filters.propertyType);
    }
    
    const whereClause = whereConditions.join(' AND ');
    
    const query = `
      SELECT COUNT(DISTINCT p.id) as total
      FROM properties p
      WHERE ${whereClause}
    `;
    
    const result = await findOne(query, queryParams);
    return result ? result.total : 0;
  }
}

module.exports = Property;
