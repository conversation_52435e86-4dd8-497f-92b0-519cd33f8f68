// Performance monitoring and optimization service
export interface PerformanceMetrics {
  // Core Web Vitals
  lcp?: number; // Largest Contentful Paint
  fid?: number; // First Input Delay
  cls?: number; // Cumulative Layout Shift
  fcp?: number; // First Contentful Paint
  ttfb?: number; // Time to First Byte
  
  // Custom metrics
  pageLoadTime?: number;
  domContentLoaded?: number;
  resourceLoadTime?: number;
  apiResponseTime?: number;
  imageLoadTime?: number;
  
  // User experience metrics
  timeToInteractive?: number;
  totalBlockingTime?: number;
  
  // Memory usage
  usedJSHeapSize?: number;
  totalJSHeapSize?: number;
  jsHeapSizeLimit?: number;
  
  // Network information
  connectionType?: string;
  effectiveType?: string;
  downlink?: number;
  rtt?: number;
}

export interface PerformanceEntry {
  id: string;
  timestamp: number;
  url: string;
  userAgent: string;
  metrics: PerformanceMetrics;
  errors?: string[];
  warnings?: string[];
}

class PerformanceService {
  private metrics: PerformanceMetrics = {};
  private observers: PerformanceObserver[] = [];
  private isMonitoring = false;
  private entries: PerformanceEntry[] = [];

  constructor() {
    this.initializeMonitoring();
  }

  // Initialize performance monitoring
  private initializeMonitoring(): void {
    if (typeof window === 'undefined') return;

    this.isMonitoring = true;
    this.setupPerformanceObservers();
    this.measureInitialMetrics();
    this.setupNetworkMonitoring();
    this.setupMemoryMonitoring();
  }

  // Setup performance observers for Core Web Vitals
  private setupPerformanceObservers(): void {
    // Largest Contentful Paint (LCP)
    if ('PerformanceObserver' in window) {
      try {
        const lcpObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const lastEntry = entries[entries.length - 1] as any;
          this.metrics.lcp = lastEntry.startTime;
          this.logMetric('LCP', lastEntry.startTime);
        });
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
        this.observers.push(lcpObserver);
      } catch (e) {
        console.warn('LCP observer not supported');
      }

      // First Input Delay (FID)
      try {
        const fidObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry: any) => {
            this.metrics.fid = entry.processingStart - entry.startTime;
            this.logMetric('FID', this.metrics.fid);
          });
        });
        fidObserver.observe({ entryTypes: ['first-input'] });
        this.observers.push(fidObserver);
      } catch (e) {
        console.warn('FID observer not supported');
      }

      // Cumulative Layout Shift (CLS)
      try {
        let clsValue = 0;
        const clsObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry: any) => {
            if (!entry.hadRecentInput) {
              clsValue += entry.value;
              this.metrics.cls = clsValue;
            }
          });
          this.logMetric('CLS', clsValue);
        });
        clsObserver.observe({ entryTypes: ['layout-shift'] });
        this.observers.push(clsObserver);
      } catch (e) {
        console.warn('CLS observer not supported');
      }

      // Navigation timing
      try {
        const navigationObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry: any) => {
            this.metrics.fcp = entry.firstContentfulPaint;
            this.metrics.domContentLoaded = entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart;
            this.metrics.pageLoadTime = entry.loadEventEnd - entry.loadEventStart;
            this.logMetric('Navigation', entry);
          });
        });
        navigationObserver.observe({ entryTypes: ['navigation'] });
        this.observers.push(navigationObserver);
      } catch (e) {
        console.warn('Navigation observer not supported');
      }

      // Resource timing
      try {
        const resourceObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry: any) => {
            if (entry.initiatorType === 'img') {
              this.metrics.imageLoadTime = entry.responseEnd - entry.startTime;
            }
            this.logResourceTiming(entry);
          });
        });
        resourceObserver.observe({ entryTypes: ['resource'] });
        this.observers.push(resourceObserver);
      } catch (e) {
        console.warn('Resource observer not supported');
      }
    }
  }

  // Measure initial performance metrics
  private measureInitialMetrics(): void {
    if (typeof window === 'undefined') return;

    // Time to First Byte
    if (performance.timing) {
      this.metrics.ttfb = performance.timing.responseStart - performance.timing.navigationStart;
    }

    // Page load time
    window.addEventListener('load', () => {
      if (performance.timing) {
        this.metrics.pageLoadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
        this.logMetric('Page Load Time', this.metrics.pageLoadTime);
      }
    });

    // DOM Content Loaded
    document.addEventListener('DOMContentLoaded', () => {
      if (performance.timing) {
        this.metrics.domContentLoaded = performance.timing.domContentLoadedEventEnd - performance.timing.navigationStart;
        this.logMetric('DOM Content Loaded', this.metrics.domContentLoaded);
      }
    });
  }

  // Setup network monitoring
  private setupNetworkMonitoring(): void {
    if ('navigator' in window && 'connection' in navigator) {
      const connection = (navigator as any).connection;
      this.metrics.connectionType = connection.type;
      this.metrics.effectiveType = connection.effectiveType;
      this.metrics.downlink = connection.downlink;
      this.metrics.rtt = connection.rtt;

      connection.addEventListener('change', () => {
        this.metrics.connectionType = connection.type;
        this.metrics.effectiveType = connection.effectiveType;
        this.metrics.downlink = connection.downlink;
        this.metrics.rtt = connection.rtt;
        this.logMetric('Network Change', this.metrics);
      });
    }
  }

  // Setup memory monitoring
  private setupMemoryMonitoring(): void {
    if ('memory' in performance) {
      const updateMemoryMetrics = () => {
        const memory = (performance as any).memory;
        this.metrics.usedJSHeapSize = memory.usedJSHeapSize;
        this.metrics.totalJSHeapSize = memory.totalJSHeapSize;
        this.metrics.jsHeapSizeLimit = memory.jsHeapSizeLimit;
      };

      updateMemoryMetrics();
      setInterval(updateMemoryMetrics, 30000); // Update every 30 seconds
    }
  }

  // Measure API response time
  public measureApiCall<T>(
    apiCall: () => Promise<T>,
    endpoint: string
  ): Promise<T> {
    const startTime = performance.now();
    
    return apiCall()
      .then((result) => {
        const endTime = performance.now();
        const responseTime = endTime - startTime;
        this.metrics.apiResponseTime = responseTime;
        this.logMetric(`API Call: ${endpoint}`, responseTime);
        return result;
      })
      .catch((error) => {
        const endTime = performance.now();
        const responseTime = endTime - startTime;
        this.logMetric(`API Call Failed: ${endpoint}`, responseTime);
        throw error;
      });
  }

  // Measure component render time
  public measureComponentRender(componentName: string, renderFn: () => void): void {
    const startTime = performance.now();
    renderFn();
    const endTime = performance.now();
    const renderTime = endTime - startTime;
    this.logMetric(`Component Render: ${componentName}`, renderTime);
  }

  // Get current metrics
  public getMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }

  // Get performance score
  public getPerformanceScore(): number {
    const { lcp, fid, cls } = this.metrics;
    
    let score = 100;
    
    // LCP scoring (good: <2.5s, needs improvement: 2.5-4s, poor: >4s)
    if (lcp) {
      if (lcp > 4000) score -= 30;
      else if (lcp > 2500) score -= 15;
    }
    
    // FID scoring (good: <100ms, needs improvement: 100-300ms, poor: >300ms)
    if (fid) {
      if (fid > 300) score -= 25;
      else if (fid > 100) score -= 10;
    }
    
    // CLS scoring (good: <0.1, needs improvement: 0.1-0.25, poor: >0.25)
    if (cls) {
      if (cls > 0.25) score -= 25;
      else if (cls > 0.1) score -= 10;
    }
    
    return Math.max(0, score);
  }

  // Get performance recommendations
  public getRecommendations(): string[] {
    const recommendations: string[] = [];
    const { lcp, fid, cls, imageLoadTime, apiResponseTime } = this.metrics;
    
    if (lcp && lcp > 2500) {
      recommendations.push('Optimize Largest Contentful Paint by compressing images and reducing server response time');
    }
    
    if (fid && fid > 100) {
      recommendations.push('Reduce First Input Delay by minimizing JavaScript execution time');
    }
    
    if (cls && cls > 0.1) {
      recommendations.push('Improve Cumulative Layout Shift by setting dimensions for images and ads');
    }
    
    if (imageLoadTime && imageLoadTime > 1000) {
      recommendations.push('Optimize image loading with lazy loading and modern formats (WebP, AVIF)');
    }
    
    if (apiResponseTime && apiResponseTime > 500) {
      recommendations.push('Optimize API response times with caching and database optimization');
    }
    
    return recommendations;
  }

  // Log performance metric
  private logMetric(name: string, value: any): void {
    if (process.env.NODE_ENV === 'development') {
      console.log(`[Performance] ${name}:`, value);
    }
  }

  // Log resource timing
  private logResourceTiming(entry: any): void {
    if (process.env.NODE_ENV === 'development') {
      const loadTime = entry.responseEnd - entry.startTime;
      if (loadTime > 1000) { // Log slow resources
        console.warn(`[Performance] Slow resource: ${entry.name} (${loadTime.toFixed(2)}ms)`);
      }
    }
  }

  // Create performance entry
  public createEntry(): PerformanceEntry {
    const entry: PerformanceEntry = {
      id: this.generateId(),
      timestamp: Date.now(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      metrics: this.getMetrics(),
      warnings: this.getRecommendations()
    };
    
    this.entries.push(entry);
    return entry;
  }

  // Get all performance entries
  public getEntries(): PerformanceEntry[] {
    return [...this.entries];
  }

  // Clear performance data
  public clear(): void {
    this.metrics = {};
    this.entries = [];
  }

  // Generate unique ID
  private generateId(): string {
    return Math.random().toString(36).substr(2, 9);
  }

  // Cleanup observers
  public destroy(): void {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
    this.isMonitoring = false;
  }

  // Export performance data
  public exportData(): string {
    return JSON.stringify({
      metrics: this.metrics,
      entries: this.entries,
      score: this.getPerformanceScore(),
      recommendations: this.getRecommendations()
    }, null, 2);
  }

  // Send performance data to analytics
  public sendToAnalytics(): void {
    if (typeof window !== 'undefined' && 'gtag' in window) {
      const { lcp, fid, cls } = this.metrics;
      
      // Send Core Web Vitals to Google Analytics
      if (lcp) {
        (window as any).gtag('event', 'LCP', {
          event_category: 'Web Vitals',
          value: Math.round(lcp),
          non_interaction: true,
        });
      }
      
      if (fid) {
        (window as any).gtag('event', 'FID', {
          event_category: 'Web Vitals',
          value: Math.round(fid),
          non_interaction: true,
        });
      }
      
      if (cls) {
        (window as any).gtag('event', 'CLS', {
          event_category: 'Web Vitals',
          value: Math.round(cls * 1000),
          non_interaction: true,
        });
      }
    }
  }
}

// Create singleton instance
export const performanceService = new PerformanceService();

// Export utility functions
export const measurePerformance = performanceService.measureApiCall.bind(performanceService);
export const getPerformanceMetrics = performanceService.getMetrics.bind(performanceService);
export const getPerformanceScore = performanceService.getPerformanceScore.bind(performanceService);
export const getPerformanceRecommendations = performanceService.getRecommendations.bind(performanceService);
